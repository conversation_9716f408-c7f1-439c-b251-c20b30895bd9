package response

import (
	"fmt"
	"godsp/modules/admin/permission/common/consts"
	"godsp/modules/admin/permission/entity"
	"strconv"
	"strings"
)

func PermissionsMapToHTML(permissions *[]entity.PermissionEntity, permissionRole map[string]int) *string {
	if permissions == nil {
		return nil
	}
	var htmlBuilder strings.Builder

	for i, permission := range *permissions {
		rowIndex := strconv.Itoa(i + 1)
		isChecked := func(key string) string {
			if _, exists := permissionRole[key]; exists {
				return "checked"
			}
			return ""
		}

		if permission.IsApi == consts.IS_API_FALSE {
			var childrenHTMLBuilder strings.Builder
			if permission.Childrens != nil {
				for _, child := range *permission.Childrens {
					statusInput := ""
					if child.IsAcp == consts.IS_ACP_FULL_ACCESS {
						statusInput = "disabled"
					}

					childrenHTMLBuilder.WriteString(fmt.Sprintf(`
						<div class="d-flex mb-2 gap-3">
							<input class="form-check-input permission_checkbox %s_%d" type="checkbox" name="permisions" value="%s" %s %s>
							<div>
								<p class="fs-12 m-0"><strong>%s</strong></p>
								<p class="text-muted fs-10 m-0">%s</p>
							</div>
						</div>`,
						permission.RouteName,
						child.IsAcp,
						child.Path+"-"+child.Method,
						isChecked(child.Path+"-"+child.Method),
						statusInput,
						child.RouteName,
						child.Path+"-"+permission.Method,
					))
				}
			}

			htmlBuilder.WriteString(fmt.Sprintf(`
				<tr>
					<td style="text-align: center;">%s</td>
					<td>
						<p class="fs-12 m-0"><strong>%s</strong></p>
						<p class="text-muted fs-10 m-0">%s</p>
					</td>
					<td style="text-align: center;">
						<input name="permisions" type="checkbox" value="%s" class="form-check-input action-checkbox permission_checkbox" %s>
					</td>
					<td>%s</td>
				</tr>`,
				rowIndex,
				permission.RouteName,
				permission.Path+"-"+permission.Method,
				permission.Path+"-"+permission.Method,
				isChecked(permission.Path+"-"+permission.Method),
				childrenHTMLBuilder.String(),
			))
		} else {
			htmlBuilder.WriteString(fmt.Sprintf(`
				<tr>
					<td style="text-align: center;">%s</td>
					<td></td>
					<td></td>
					<td>
						<div class="d-flex mb-2 gap-3">
							<input class="form-check-input permission_checkbox" type="checkbox" name="permisions[]" value="%s" %s>
							<div>
								<p class="fs-12 m-0"><strong>%s</strong></p>
								<p class="text-muted fs-10 m-0">%s</p>
							</div>
						</div>
					</td>
				</tr>`,
				rowIndex,
				permission.Path+"-"+permission.Method,
				isChecked(permission.Path+"-"+permission.Method),
				permission.RouteName,
				permission.Path+"-"+permission.Method,
			))
		}
	}

	result := htmlBuilder.String()
	if result == "" {
		result = `<tr><td colspan='4'>No permissions available</td></tr>`
	}
	return &result
}
