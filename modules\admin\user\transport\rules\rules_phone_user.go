package rules

import (
	"godsp/modules/admin/user/common/consts"
	"regexp"
	"strings"

	"github.com/go-playground/validator/v10"
)

func RulePhoneUser(fl validator.FieldLevel) bool {
	phone := fl.Field().Interface().(string)
	var flag = false
	var phoneStr = strings.TrimSpace(phone)
	if phoneStr == "" {
		return true
	}
	phoneStr = strings.ReplaceAll(phoneStr, "(+84)", "0")
	phoneStr = strings.ReplaceAll(phoneStr, "+84", "0")
	phoneStr = strings.ReplaceAll(phoneStr, "0084", "0")
	phoneStr = strings.ReplaceAll(phoneStr, " ", "")
	if len(phoneStr) == 10 {
		firstNumber := phoneStr[:2]
		for _, validPrefix := range consts.CheckPhoneUser {
			if firstNumber == validPrefix && regexp.MustCompile(`^\d{10}$`).MatchString(phoneStr) {
				flag = true
			}
		}
	}
	return flag
}
