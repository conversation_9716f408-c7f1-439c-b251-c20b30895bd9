package mapping

import (
	"godsp/modules/admin/role/entity"
	"godsp/modules/admin/role/transport/requests"
	"godsp/pkg/gos/utils"
)

func MapperEditPermissionRole(req *requests.PayloadUpdatePermissionRole, listPermission entity.PermissionMapInt) *entity.PermissionRoleUpdate {
	timeNow := utils.TimeNowLocationHCM()

	if listPermission == nil {
		listPermission = entity.PermissionMapInt{}
	}

	if len(req.Add) > 0 {
		for _, v := range req.Add {
			if _, ok := listPermission[v]; !ok {
				listPermission[v] = 1
			}
		}
	}

	if len(req.Delete) > 0 {
		for _, v := range req.Delete {
			delete(listPermission, v)
		}
	}

	data := &entity.PermissionRoleUpdate{
		UpdatedBy:   req.UserID,
		UpdatedAt:   timeNow,
		Permissions: &listPermission,
	}

	return data
}
