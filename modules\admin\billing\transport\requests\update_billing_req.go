package requests

import (
	"github.com/go-playground/validator/v10"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"godsp/modules/admin/billing/common/errs"
	"godsp/modules/admin/common/admerrs"
	"strings"
)

type PayloadUpdateBilling struct {
	ID          string             `json:"id"`
	Status      string             `json:"status"`
	BillNumber  string             `json:"bill_number" validate:"required"`
	BillingID   string             `json:"billing_id" validate:"required"`
	Currency    string             `json:"currency" validate:"required"`
	Notes       string             `json:"notes,omitempty"`
	ClientIDStr string             `json:"client_id" validate:"required"`
	ApprovedBy  string             `json:"approved_by,omitempty"`
	UserID      primitive.ObjectID `json:"-"`
	ClientID    primitive.ObjectID `json:"-"`
}

func (req *PayloadUpdateBilling) Validate() []*string {
	validate := validator.New()
	var validationErrors []*string
	err := validate.Struct(req)
	if err != nil {
		for _, err := range err.(validator.ValidationErrors) {
			switch err.Field() {
			case "Amount":
				errName := errs.ErrAmountBillingValidate.Error()
				validationErrors = append(validationErrors, &errName)
			case "BillingID":
				errName := errs.ErrBillingIDValidate.Error()
				validationErrors = append(validationErrors, &errName)
			case "BillNumber":
				errName := errs.ErrBillNumberValidate.Error()
				validationErrors = append(validationErrors, &errName)
			case "AdvertiserID":
				errName := errs.ErrAdvertiserIDBillingValidate.Error()
				validationErrors = append(validationErrors, &errName)
			case "Currency":
				errName := errs.ErrCurrencyBillingValidate.Error()
				validationErrors = append(validationErrors, &errName)
			case "ClientIDStr":
				errName := errs.ErrClientIdBillingValidate.Error()
				validationErrors = append(validationErrors, &errName)
			case "Status":
				errStatus := admerrs.ErrStatusNotFound.Error()
				validationErrors = append(validationErrors, &errStatus)
			}
		}
	}

	if req.ClientIDStr != "" {
		objID, err := primitive.ObjectIDFromHex(req.ClientIDStr)
		if err != nil {
			errName := errs.ErrClientIdBillingValidate.Error()
			validationErrors = append(validationErrors, &errName)
		} else {
			req.ClientID = objID
		}
	}

	req.Notes = strings.TrimSpace(req.Notes)

	return validationErrors
}
