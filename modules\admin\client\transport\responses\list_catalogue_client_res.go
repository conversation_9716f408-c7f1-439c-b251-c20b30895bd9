package responses

import "go.mongodb.org/mongo-driver/bson/primitive"

type CatalogueList struct {
	ID              string  `json:"catalogue_id"`
	Name            string  `json:"name"`
	ProductCount    int32   `json:"product_count,omitempty"`
	DefaultImageURL *string `json:"default_image_url,omitempty"`
}

type CatalogueListEditClient struct {
	ID              string               `json:"catalogue_id"`
	Name            string               `json:"name"`
	ProductCount    int32                `json:"product_count,omitempty"`
	DefaultImageURL *string              `json:"default_image_url,omitempty"`
	ListUserIDs     []primitive.ObjectID `json:"list_user_ids" bson:"list_user_ids"`
	ClientIDs       []primitive.ObjectID `json:"client_ids" bson:"client_ids"`
}
