package mapping

import (
	"encoding/json"
	"fmt"
	"godsp/modules/facebook/ad_creative/transport/requests"
	v20 "godsp/pkg/fb-marketing/marketing/v20"
	"strings"
)

func MapperAdCreativeReqToFB(payload *requests.CreateAdCreativeReq) *v20.AdCreative {
	jsonData, err := json.MarshalIndent(payload, "", "  ")
	if err != nil {
		fmt.Println("Error marshaling struct:", err)
	} else {
		fmt.Println("\n-------- payload -------> \n", string(jsonData))
	}
	UpdateAdCreativeReqWelcomeMessage(payload)
	adCreative := v20.AdCreative{
		AccountID: payload.AdAccountID,
		ActorID:   payload.ActorID,
		URLTags:   payload.URLTags,
	}
	if payload.Status != nil {
		adCreative.Status = *payload.Status
	}

	if payload.ObjectStorySpec != nil {

		adCreative.ObjectStorySpec = payload.ObjectStorySpec
	}
	if payload.AssetFeedSpec != nil {
		adCreative.AssetFeedSpec = payload.AssetFeedSpec
	}

	if payload.ObjectStoryID != nil {
		adCreative.ObjectStoryID = *payload.ObjectStoryID
	}

	if payload.CallToActionType != "" {
		adCreative.CallToActionType = payload.CallToActionType
	}

	if payload.ContextualMultiAds != nil {
		adCreative.ContextualMultiAds = payload.ContextualMultiAds
	}

	if payload.DegreesOfFreedomSpec != nil {
		adCreative.DegreesOfFreedomSpec = payload.DegreesOfFreedomSpec
	}

	if payload.ProductSetID != nil {
		adCreative.ProductSetID = *payload.ProductSetID
	}

	if payload.TemplateURLSpec != nil {
		adCreative.TemplateURLSpec = *payload.TemplateURLSpec
	}

	if payload.ApplinkTreatment != "" {
		adCreative.ApplinkTreatment = payload.ApplinkTreatment
	}

	return &adCreative
}

func UpdateAdCreativeReqWelcomeMessage(payload *requests.CreateAdCreativeReq) {
	pageWelcomeMessage, isExistPageWelcomeMessage := GetPageWelcomeMessage(payload.ObjectStorySpec)
	if !isExistPageWelcomeMessage {
		return
	}

	var welcomeData map[string]interface{}
	err := json.Unmarshal([]byte(pageWelcomeMessage), &welcomeData)
	if err != nil {
		fmt.Println("Lỗi unmarshal pageWelcomeMessage JSON:", err)
		return
	}

	mediaTypeIf, ok := welcomeData["media_type"]
	if !ok {
		fmt.Println("INFO: Không tìm thấy key 'media_type' trong pageWelcomeMessage.")
		return
	}
	mediaType, ok := mediaTypeIf.(string)
	if !ok {
		fmt.Println("INFO: Giá trị 'media_type' trong pageWelcomeMessage không phải là chuỗi.")
		return
	}

	prefix := payload.AdAccountID + ":"

	switch mediaType {
	case "image":
		updatedWelcomeData := processImageWelcomeMessageAndUpdate(welcomeData, prefix)
		if updatedWelcomeData != nil {
			updatedBytes, err := json.Marshal(updatedWelcomeData)
			if err != nil {
				fmt.Println("Lỗi marshal updated welcomeData JSON:", err)
				return
			}
			updatePayloadWelcomeMessage(payload.ObjectStorySpec, string(updatedBytes))
		}
	case "video":
		// Tương tự cho video, gọi hàm xử lý video và cập nhật payload
	case "text":
		// Tương tự cho text, gọi hàm xử lý text và cập nhật payload
	default:
		fmt.Printf("INFO: media_type '%s' không được hỗ trợ.\n", mediaType)
	}
}

func processImageWelcomeMessageAndUpdate(welcomeData map[string]interface{}, prefix string) map[string]interface{} {
	var element map[string]interface{}
	var currentHash string
	var foundHash bool

	func() {
		defer func() {
			if r := recover(); r != nil {
				fmt.Println("Panic recovered in processImageWelcomeMessageAndUpdate:", r)
			}
		}()

		imageFormatIf, ok := welcomeData["image_format"].(map[string]interface{})
		if !ok {
			fmt.Println("INFO: Không tìm thấy key 'image_format'.")
			return
		}
		messageIf, ok := imageFormatIf["message"].(map[string]interface{})
		if !ok {
			fmt.Println("INFO: Không tìm thấy key 'message' trong image_format.")
			return
		}
		attachmentIf, ok := messageIf["attachment"].(map[string]interface{})
		if !ok {
			fmt.Println("INFO: Không tìm thấy key 'attachment' trong message.")
			return
		}
		payloadIf, ok := attachmentIf["payload"].(map[string]interface{})
		if !ok {
			fmt.Println("INFO: Không tìm thấy key 'payload' trong attachment.")
			return
		}
		elementsIf, ok := payloadIf["elements"].([]interface{})
		if !ok || len(elementsIf) == 0 {
			fmt.Println("INFO: Không tìm thấy key 'elements' hoặc elements rỗng trong payload.")
			return
		}
		elementIf := elementsIf[0]
		element, ok = elementIf.(map[string]interface{})
		if !ok {
			fmt.Println("INFO: Phần tử đầu tiên trong elements không phải là map.")
			return
		}
		currentHashIf, ok := element["image_hash"]
		if !ok {
			fmt.Println("INFO: Không tìm thấy key 'image_hash' trong element.")
			return
		}
		currentHash, ok = currentHashIf.(string)
		if !ok {
			fmt.Println("INFO: Giá trị 'image_hash' không phải là chuỗi.")
			return
		}
		foundHash = true
	}()

	if foundHash {
		if !strings.HasPrefix(currentHash, prefix) {
			newHash := prefix + currentHash
			element["image_hash"] = newHash
			fmt.Println("INFO: Đã thêm prefix Ad Account ID vào image_hash.")
			return welcomeData // Trả về map đã cập nhật
		} else {
			fmt.Println("INFO: image_hash đã có sẵn prefix Ad Account ID.")
		}
	} else {
		fmt.Println("Lỗi: Không tìm thấy 'image_hash' hoặc cấu trúc JSON không đúng.")
	}
	return nil // Trả về nil nếu không có cập nhật hoặc có lỗi
}

func updatePayloadWelcomeMessage(spec *v20.ObjectStorySpec, updatedMessage string) {
	if spec.PhotoData != nil {
		spec.PhotoData.PageWelcomeMessage = updatedMessage
		return
	}

	if spec.VideoData != nil {
		spec.VideoData.PageWelcomeMessage = updatedMessage
		return
	}

	if spec.LinkData != nil {
		spec.LinkData.PageWelcomeMessage = updatedMessage
		return
	}
}

func GetPageWelcomeMessage(spec *v20.ObjectStorySpec) (string, bool) {
	if spec.PhotoData != nil {
		return spec.PhotoData.PageWelcomeMessage, true
	}

	if spec.VideoData != nil {
		return spec.VideoData.PageWelcomeMessage, true
	}

	if spec.LinkData != nil {
		return spec.LinkData.PageWelcomeMessage, true
	}

	return "", false
}
