name: Deploy to local-dev-2
on:
  push:
    branches:
      - dev
jobs:
  deploy:
    runs-on: self-hosted
    steps:
      - name: Checkout Code
        uses: actions/checkout@v2
      - name: SSH Deploy
        uses: appleboy/ssh-action@master
        with:
          host: ${{ secrets.LOCAL_DEV_2_HOST }}
          USERNAME: ${{ secrets.LOCAL_DEV_2_USERNAME }}
          PORT: ${{ secrets.LOCAL_DEV_2_PORT }}
          KEY: ${{ secrets.LOCAL_DEV_2_PRIV_KEY }}
          script: |
            systemctl stop godsp     
            destination_dir="/home/<USER>/godsp"              
            cd $destination_dir
            git pull
            systemctl restart godsp
          timeout: 240s
