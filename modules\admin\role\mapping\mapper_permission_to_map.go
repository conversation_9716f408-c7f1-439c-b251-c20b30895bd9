package mapping

// import (
// 	"godsp/modules/admin/common/admfunc"
// 	"godsp/modules/admin/permission/entity"
// 	"strconv"

// 	"strings"
// )

// func MapperPermissionToMap(permissions *[]entity.PermissionEntity) map[string]interface{} {
// 	if permissions == nil {
// 		return nil
// 	}
// 	data := make(map[string]interface{})

// 	var modules []string
// 	for _, permission := range *permissions {
// 		action := processAction(permission.RouteName, permission.Path)
// 		if _, ok := data[permission.Module]; !ok {
// 			modules = append(modules, permission.Module+"_"+permission.Group)
// 			newAction := createNewAction(&permission)
// 			data[permission.Module] = map[string]interface{}{
// 				action: newAction,
// 			}
// 		} else {
// 			module := data[permission.Module].(map[string]interface{})
// 			if _, exists := module[action]; !exists {
// 				newAction := createNewAction(&permission)
// 				module[action] = newAction
// 			}
// 		}
// 	}
// 	data["modules_groups"] = modules

// 	return data
// }

// func createNewAction(permission *entity.PermissionEntity) map[string]interface{} {
// 	routeName := permission.RouteName
// 	if routeName == "" {
// 		routeName = permission.Path
// 	}
// 	newAction := map[string]interface{}{
// 		"path":        admfunc.ProcessMethodPathPermission(permission.Method, permission.Path),
// 		"description": permission.Description,
// 		"is_acp":      permission.IsAcp,
// 		"routeName":   routeName,
// 		// "group":       permission.Group,
// 	}

// 	if len(permission.Childrens) > 0 {
// 		childrens := make([]map[string]interface{}, 0)
// 		for _, v := range permission.Childrens {
// 			childrens = append(childrens, map[string]interface{}{
// 				"path":        admfunc.ProcessMethodPathPermission(v.Method, v.Path),
// 				"description": v.Description,
// 				"routeName":   processAction(v.RouteName, v.Path),
// 				"is_acp":      strconv.Itoa(v.IsAcp),
// 			})
// 		}
// 		newAction["childrens"] = childrens
// 	}

// 	return newAction
// }

// func processAction(routeName, path string) string {
// 	strs := strings.Split(routeName, ".")
// 	if len(strs) == 2 {
// 		return strs[1]
// 	}
// 	return path
// }
