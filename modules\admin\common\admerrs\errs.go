package admerrs

import "errors"

var (
	ErrStatusNotFound = errors.New("status not found")
	ErrIsUnvalid      = errors.New("id are not valid")

	//login
	ErrValidateLogin         = errors.New("login validate email, password not empty")
	ErrValidateLoginEmail    = errors.New("login email invalidate")
	ErrValidateLoginPassword = errors.New("login password is not valid")

	ErrLoginFailed         = errors.New("email and password are not valid")
	ErrLoginEmailFailed    = errors.New("email are not valid")
	ErrLoginPasswordFailed = errors.New("password are not valid")
	ErrPermissionDenied    = errors.New("access denied - contact Admin for assistance")

	ErrPermissionDataFailed = errors.New("email and password not set data permission failed")
	ErrPasswordIsTheSame    = errors.New("password new and password old is the same")
	ErrOldPasswordDontMatch = errors.New("old password don't match")

	ErrClientIdEmpty    = errors.New("client ID is required")
	ErrAdAccountIdEmpty = errors.New("account ID is required")
	ErrUserIdEmpty      = errors.New("user ID is required")
	ErrUserIdIsNotFound = errors.New("user is not found")
)
