package enums

// GMV Max Campaign Primary Status Enums
const (
	STATUS_DELIVERY_OK = "STATUS_DELIVERY_OK" // Active
	STATUS_DISABLE     = "STATUS_DISABLE"     // Inactive  
	STATUS_DELETE      = "STATUS_DELETE"      // Deleted
)

var GMVCampaignStatus = map[string]string{
	STATUS_DELIVERY_OK: "Active",
	STATUS_DISABLE:     "Inactive", 
	STATUS_DELETE:      "Deleted",
}

var GMVCampaignStatusReverse = map[string]string{
	"active":   STATUS_DELIVERY_OK,
	"inactive": STATUS_DISABLE,
	"deleted":  STATUS_DELETE,
}

// GetGMVStatusDisplay returns the display name for a primary status
func GetGMVStatusDisplay(primaryStatus string) string {
	if display, exists := GMVCampaignStatus[primaryStatus]; exists {
		return display
	}
	return "Unknown"
}

// GetGMVStatusPrimary returns the primary status for a display value
func GetGMVStatusPrimary(displayValue string) string {
	if primary, exists := GMVCampaignStatusReverse[displayValue]; exists {
		return primary
	}
	return ""
}

// GetAllGMVStatuses returns all available GMV campaign statuses
func GetAllGMVStatuses() map[string]string {
	return GMVCampaignStatus
}
