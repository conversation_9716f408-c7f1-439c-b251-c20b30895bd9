package mapping

import (
	"godsp/modules/admin/user/transport/requests"
	"time"

	"go.mongodb.org/mongo-driver/bson"
)

func MapperUpdateUserBson(req *requests.PayloadBsonUserUpdate) *bson.M {
	data := bson.M{}

	if req.ClientID != nil {
		data["client_id"] = req.ClientID
	} else {
		data["client_id"] = nil
	}

	data["updated_by"] = req.UpdatedBy
	data["updated_at"] = time.Now()

	update := bson.M{
		"$set": data,
	}

	return &update
}
