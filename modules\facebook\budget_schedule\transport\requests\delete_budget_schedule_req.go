package requests

import (
	"godsp/modules/facebook/budget_schedule/common/errs"

	"github.com/go-playground/validator/v10"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

type DeleteBudgetScheduleReq struct {
	CampaignId          string   `json:"campaign_id,omitempty" validate:"omitempty,numeric,gt=0"`
	AdsetId             string   `json:"adset_id,omitempty" validate:"omitempty,numeric,gt=0"`
	HighDemandPeriodIds []string `json:"high_demand_period_id" validate:"required,dive,numeric,gt=0"`

	UserId primitive.ObjectID `json:"-"`
}

func (req *DeleteBudgetScheduleReq) Validate() []string {
	validate := validator.New()
	var validationErrors []string
	if req.CampaignId == "" && req.AdsetId == "" {
		validationErrors = append(validationErrors, errs.ErrCampaignIdAdsetBudgetSchedule.Error())
	}

	err := validate.Struct(req)
	if err != nil {
		for _, err := range err.(validator.ValidationErrors) {
			switch err.Field() {
			case "CampaignId":
				validationErrors = append(validationErrors, errs.ErrCampaignIdBudgetSchedule.Error())
			case "HighDemandPeriodId":
				validationErrors = append(validationErrors, errs.ErrHighDemandPeriodId.Error())
			}
		}
	}
	return validationErrors
}
