package usecase

import (
	"context"
	"fmt"
	v20 "godsp/pkg/fb-marketing/marketing/v20"

	"github.com/dev-networldasia/dspgos/sctx"
)

type fbAdPixelUsc struct {
	fbService *v20.Service
	logger    sctx.Logger
}

func NewFBMarketingApi(fbService *v20.Service, logger sctx.Logger) *fbAdPixelUsc {
	return &fbAdPixelUsc{
		fbService: fbService,
		logger:    logger,
	}
}

/**
 * Reload Ad Pixel Facebook V20
 */
func (usc *fbAdPixelUsc) ReloadAdPixelUsc(ctx context.Context, accountID string) error {
	adPixels, err := usc.fbService.AdPixels.List(accountID).Do(ctx)
	if err != nil {
		return err
	}

	for _, adc := range adPixels {
		fmt.Printf("--->> %+v /n ", adc)

	}

	return nil
}
