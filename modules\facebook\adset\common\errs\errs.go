package errs

import "errors"

var (
	ErrFBAdsetUpdateInvalidate     = errors.New("invalidate adset update")
	ErrAdsetAccountID              = errors.New("validate adset account id required")
	ErrAdsetCampaignID             = errors.New("validate adset campaign id required")
	ErrAdsetID                     = errors.New("validate adset id required")
	ErrAdsetName                   = errors.New("validate name adset required")
	ErrAdsetOptimizationGoal       = errors.New("validate optimization goal adset required")
	ErrAdsetDestinationType        = errors.New("validate conversation adset is not in the correct format")
	ErrAdsetPromotedObject         = errors.New("validate promoted object adset is not in the correct format")
	ErrAdsetDailyBudget            = errors.New("validate daily budget adset must be greater than 0")
	ErrAdsetDailyMinSpendTarget    = errors.New("validate daily min spend target adset must be greater than 0")
	ErrAdsetDailyMinCap            = errors.New("validate daily spend cap adset must be greater than 0")
	ErrAdsetLifetimeBudget         = errors.New("validate lifetime budget adset must be greater than 0")
	ErrAdsetBidAmount              = errors.New("validate bid amount  adset must be greater than 0")
	ErrAdsetBillingEvent           = errors.New("validate name billing event required")
	ErrAdsetBidStrategy            = errors.New("validate name bid strategy is not in the correct format")
	ErrAdsetLifeTimeSpendCap       = errors.New("validate Lifetime minimum is not in the correct format")
	ErrAdsetLifetimeMinSpendTarget = errors.New("validate Lifetime maximum is not in the correct format")
	ErrAdsetStatus                 = errors.New("validate status adset is not in the correct format")
	ErrAdsetStartTime              = errors.New("validate field 'time_start' must be greater than the current time")
	ErrAdsetEndTime                = errors.New("validate field 'time_end' must be greater than 'time_start' and the current time")
	ErrAdsetTargeting              = errors.New("validate targetin adset is not in the correct format")

	//reload
	ErrReloadAdsetAdsetId               = errors.New("validate adset id must be a number greater than 0")
	ErrReloadAdsetAccountId             = errors.New("validate adset account id must be a number greater than 0")
	ErrReloadAdsetCampaignId            = errors.New("validate adset campaign id must be a number greater than 0")
	ErrReloadAdsetIdAccountIdCampaignId = errors.New("validate either account id or campaign id or adset id must be provided")

	// list api
	ErrAdsetDataTableEmpty = errors.New("payload datatable not empty")

	//update api
	ErrAdsetUpdateFieldEmpty = errors.New("validate at least one field must be changed")

	// interest check list
	ErrInterestCheckValidate = errors.New("validate at data")
)
