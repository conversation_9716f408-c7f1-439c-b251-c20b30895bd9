package routes

import (
	"github.com/dev-networldasia/dspgos/sctx"

	"github.com/gofiber/fiber/v2"
)

func SetupRouterCampaign(app *fiber.App, serviceCtx sctx.ServiceContext, midds ...fiber.Handler) {
	group := app.Group("dsp/facebook/campaigns")
	{
		if len(midds) > 0 {
			for _, midd := range midds {
				group.Use(midd)
			}
		}
		comPage := ComposerCampaignService(serviceCtx)
		group.Get("/list", comPage.ListCampaignHdl()).Name("fb.campaign.list")
		group.Get("/edit", comPage.ListCampaignHdl()).Name("fb.campaign.edit")
	}

	apiGroup := app.Group("/dsp/facebook/api/campaigns")
	{
		if len(midds) > 0 {
			for _, midd := range midds {
				apiGroup.Use(midd)
			}
		}
		compApi := ComposerCampaignApiService(serviceCtx)
		apiGroup.Post("/list-datatable", compApi.ListTableCampaignApi()).Name("fb.campaign.list_datatable")
		apiGroup.Post("/list", compApi.ListCampaignApi()).Name("fb.api.campaign.list")
		apiGroup.Post("/create", compApi.CreateCampaignApi()).Name("fb.campaign.create")
		apiGroup.Patch("/reload", compApi.ReloadCampaignApi()).Name("fb.campaign.reload")
		// apiGroup.Post("/:campaign_id", compApi.GetDetailCampaignApi()).Name("fb.campaign.detail")
		apiGroup.Post("/details/:id", compApi.GetDetailAdsetAdCampaignApi()).Name("fb.campaign.details")
		apiGroup.Delete("", compApi.DeleteCampaignApi()).Name("fb.campaign.delete")
		apiGroup.Patch("/update", compApi.UpdateCampaignApi()).Name("fb.campaign.update")
		apiGroup.Put("/update", compApi.UpdateNameStatusCampaignApi()).Name("fb.campaign.update_name_status")
		apiGroup.Put("/approve", compApi.ApproveCampaignApi()).Name("fb.campaign.approve_campaigns")
	}
}
