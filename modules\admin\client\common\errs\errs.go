package errs

import "errors"

var (
	ErrIdUserInvalidate    = errors.New("user id is validate")
	ErrIdUserEmpty         = errors.New("user id empty")
	ErrGetUser             = errors.New("get user is error")
	ErrIDClientValidate    = errors.New("client not exists")
	ErrAdminIDUserValidate = errors.New("admin user not exists")

	ErrNameValidate    = errors.New("name is not empty")
	ErrCompanyValidate = errors.New("company is not empty")
	ErrBrandValidate   = errors.New("brand is not empty")

	ErrEmailDuplicate = errors.New("email already exists")
	ErrEmailValidate  = errors.New("email is not empty or invalid")

	ErrStatusValidate    = errors.New("status is required or invalid")
	ErrAdAccountIDFormat = errors.New("invalid ad account format")
	ErrPhoneValidate     = errors.New("please enter the correct phone number")

	ErrClientUserIDsEmpty = errors.New("client user list is empty or invalid")
	ErrClientIdEmpty      = errors.New("client id is required")
	ErrClientAdminIDEmpty = errors.New("admin user ID is empty or invalid")

	ErrPositionValidate = errors.New("position must be a valid string")
	ErrAddressValidate  = errors.New("address must be a valid string")
	ErrDomainValidate   = errors.New("domain must be a valid string")

	ErrEditStatusDeleteClient = errors.New("cannot be edited when the status is set to delete")

	ErrAdAccountIdNotExist = errors.New("adaccount id not exist")
	ErrAdvertiserIdNotExist = errors.New("advertiser id not exist")
	ErrPageIdNotExist      = errors.New("page id not exist")
	ErrPixelIdNotExist     = errors.New("pixel id not exist")
	ErrCatalogueIdNotExist = errors.New("catalogue id not exist")
)
