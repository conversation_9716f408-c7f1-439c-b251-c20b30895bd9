[{"_id": "67c56feccceafebe2cf7ab79", "interest_id": "1", "raw_name": "Single", "name": "Single", "type": "relationship_statuses", "path": ["Demographics", "Relationship", "Relationship status"], "description": "", "audience_size_lower_bound": 298698616, "audience_size_upper_bound": 351269573, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.38Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.428Z"}, {"_id": "67c56feccceafebe2cf7ab7b", "interest_id": "2", "raw_name": "In a relationship", "name": "In a relationship", "type": "relationship_statuses", "path": ["Demographics", "Relationship", "Relationship status"], "description": "", "audience_size_lower_bound": 146037816, "audience_size_upper_bound": 171740472, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.404Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.426Z"}, {"_id": "67c56feccceafebe2cf7ab7d", "interest_id": "7", "raw_name": "Civil partnership", "name": "Civil partnership", "type": "relationship_statuses", "path": ["Demographics", "Relationship", "Relationship status"], "description": "", "audience_size_lower_bound": 2225233, "audience_size_upper_bound": 2616875, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.405Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.425Z"}, {"_id": "67c56feccceafebe2cf7ab7f", "interest_id": "11", "raw_name": "Separated", "name": "Separated", "type": "relationship_statuses", "path": ["Demographics", "Relationship", "Relationship status"], "description": "", "audience_size_lower_bound": 4976079, "audience_size_upper_bound": 5851870, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.406Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.427Z"}, {"_id": "67c56feccceafebe2cf7ab81", "interest_id": "6", "raw_name": "Unspecified", "name": "Unspecified", "type": "relationship_statuses", "path": ["Demographics", "Relationship", "Relationship status"], "description": "", "audience_size_lower_bound": 1488182511, "audience_size_upper_bound": 1750102633, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.406Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.428Z"}, {"_id": "67c56feccceafebe2cf7ab83", "interest_id": "4", "raw_name": "Engaged", "name": "Engaged", "type": "relationship_statuses", "path": ["Demographics", "Relationship", "Relationship status"], "description": "", "audience_size_lower_bound": 26743202, "audience_size_upper_bound": 31450006, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.406Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.426Z"}, {"_id": "67c56feccceafebe2cf7ab85", "interest_id": "9", "raw_name": "Open relationship", "name": "Open relationship", "type": "relationship_statuses", "path": ["Demographics", "Relationship", "Relationship status"], "description": "", "audience_size_lower_bound": 8736659, "audience_size_upper_bound": 10274311, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.406Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.427Z"}, {"_id": "67c56feccceafebe2cf7ab87", "interest_id": "10", "raw_name": "Complicated", "name": "Complicated", "type": "relationship_statuses", "path": ["Demographics", "Relationship", "Relationship status"], "description": "", "audience_size_lower_bound": 13757495, "audience_size_upper_bound": 16178815, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.407Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.425Z"}, {"_id": "67c56feccceafebe2cf7ab89", "interest_id": "13", "raw_name": "Widowed", "name": "Widowed", "type": "relationship_statuses", "path": ["Demographics", "Relationship", "Relationship status"], "description": "", "audience_size_lower_bound": 5480374, "audience_size_upper_bound": 6444920, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.407Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.428Z"}, {"_id": "67c56feccceafebe2cf7ab8b", "interest_id": "5", "raw_name": "Some university", "name": "Some university", "type": "education_statuses", "path": ["Demographics", "Education", "Education level"], "description": "", "audience_size_lower_bound": 92354489, "audience_size_upper_bound": 108608880, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.408Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.408Z"}, {"_id": "67c56feccceafebe2cf7ab8d", "interest_id": "8", "raw_name": "Domestic partnership", "name": "Domestic partnership", "type": "relationship_statuses", "path": ["Demographics", "Relationship", "Relationship status"], "description": "", "audience_size_lower_bound": 4130924, "audience_size_upper_bound": 4857967, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.408Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.426Z"}, {"_id": "67c56feccceafebe2cf7ab8f", "interest_id": "3", "raw_name": "Married", "name": "Married", "type": "relationship_statuses", "path": ["Demographics", "Relationship", "Relationship status"], "description": "", "audience_size_lower_bound": 281018135, "audience_size_upper_bound": 330477327, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.408Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.427Z"}, {"_id": "67c56feccceafebe2cf7ab91", "interest_id": "12", "raw_name": "Divorced", "name": "Divorced", "type": "relationship_statuses", "path": ["Demographics", "Relationship", "Relationship status"], "description": "", "audience_size_lower_bound": 5822765, "audience_size_upper_bound": 6847572, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.409Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.425Z"}, {"_id": "67c56feccceafebe2cf7ab93", "interest_id": "6107813551783", "raw_name": "Household income: top 10% of ZIP codes (US)", "name": "Household income: top 10% of ZIP codes (US)", "type": "income", "path": ["Demographics", "Financial", "Income"], "description": "People who live in the top 10% of US ZIP code areas by average household income based on publicly available information", "audience_size_lower_bound": 25870450, "audience_size_upper_bound": 30423650, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.409Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.409Z"}, {"_id": "67c56feccceafebe2cf7ab95", "interest_id": "6107813553183", "raw_name": "Household income: top 10-25% of ZIP codes (US)", "name": "Household income: top 10-25% of ZIP codes (US)", "type": "income", "path": ["Demographics", "Financial", "Income"], "description": "People who live in the top 10-25% of US ZIP code areas by average household income based on publicly available information.", "audience_size_lower_bound": 33355661, "audience_size_upper_bound": 39226258, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.409Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.409Z"}, {"_id": "67c56feccceafebe2cf7ab97", "interest_id": "6107813554583", "raw_name": "Household income: top 25-50% of ZIP codes (US)", "name": "Household income: top 25-50% of ZIP codes (US)", "type": "income", "path": ["Demographics", "Financial", "Income"], "description": "People who live in the top 25-50% of US ZIP code areas by average household income based on publicly available information.", "audience_size_lower_bound": 39920074, "audience_size_upper_bound": 46946008, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.41Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.41Z"}, {"_id": "67c56feccceafebe2cf7ab99", "interest_id": "6107813079183", "raw_name": "Household income: top 5% of ZIP codes (US)", "name": "Household income: top 5% of ZIP codes (US)", "type": "income", "path": ["Demographics", "Financial", "Income"], "description": "People who live in the top 5% of US ZIP code areas by average household income based on publicly available information", "audience_size_lower_bound": 12427305, "audience_size_upper_bound": 14614511, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.41Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.41Z"}, {"_id": "67c56feccceafebe2cf7ab9b", "interest_id": "6017476616183", "raw_name": "Anniversary within 30 days", "name": "Anniversary within 30 days", "type": "life_events", "path": ["Demographics", "Life events", "Anniversary"], "description": "People with a relationship anniversary (marriage, domestic partnership etc.) occurring within the next 30 days", "audience_size_lower_bound": 5804171, "audience_size_upper_bound": 6825706, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.41Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.41Z"}, {"_id": "67c56feccceafebe2cf7ab9d", "interest_id": "6018399723983", "raw_name": "Anniversary within 31-60 days", "name": "Anniversary within 31-60 days", "type": "life_events", "path": ["Demographics", "Life events", "Anniversary"], "description": "People with a relationship anniversary (marriage, domestic partnership etc.) occurring within the next 31-60 days", "audience_size_lower_bound": 5787820, "audience_size_upper_bound": 6806477, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.411Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.411Z"}, {"_id": "67c56feccceafebe2cf7ab9f", "interest_id": "6003053857372", "raw_name": "Away from family", "name": "Away from family", "type": "life_events", "path": ["Demographics", "Life events"], "description": "People who are away from their family", "audience_size_lower_bound": 133043386, "audience_size_upper_bound": 156459022, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.411Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.411Z"}, {"_id": "67c56feccceafebe2cf7aba1", "interest_id": "6003053860372", "raw_name": "Away from home town", "name": "Away from home town", "type": "life_events", "path": ["Demographics", "Life events"], "description": "People who are away from their home towns.", "audience_size_lower_bound": 133091803, "audience_size_upper_bound": 156515961, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.412Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.412Z"}, {"_id": "67c56feccceafebe2cf7aba3", "interest_id": "6048026275783", "raw_name": "Birthday in April", "name": "Birthday in April", "type": "life_events", "path": ["Demographics", "Life events", "Date of birth", "Month of birth"], "description": "People who have a birthday in April.", "audience_size_lower_bound": 150057372, "audience_size_upper_bound": 176467470, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.412Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.412Z"}, {"_id": "67c56feccceafebe2cf7aba5", "interest_id": "6048810966183", "raw_name": "Birthday in August", "name": "Birthday in August", "type": "life_events", "path": ["Demographics", "Life events", "Date of birth", "Month of birth"], "description": "People with a birthday in August.", "audience_size_lower_bound": 163206000, "audience_size_upper_bound": 191930257, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.412Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.412Z"}, {"_id": "67c56feccceafebe2cf7aba7", "interest_id": "6048810914583", "raw_name": "Birthday in December", "name": "Birthday in December", "type": "life_events", "path": ["Demographics", "Life events", "Date of birth", "Month of birth"], "description": "People with a birthday in December.", "audience_size_lower_bound": 181444557, "audience_size_upper_bound": 213378800, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.413Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.413Z"}, {"_id": "67c56feccceafebe2cf7aba9", "interest_id": "6049083267183", "raw_name": "Birthday in February", "name": "Birthday in February", "type": "life_events", "path": ["Demographics", "Life events", "Date of birth", "Month of birth"], "description": "People who have a birthday in February.", "audience_size_lower_bound": 162193051, "audience_size_upper_bound": 190739029, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.413Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.413Z"}, {"_id": "67c56feccceafebe2cf7abab", "interest_id": "6048267235783", "raw_name": "Birthday in January", "name": "Birthday in January", "type": "life_events", "path": ["Demographics", "Life events", "Date of birth", "Month of birth"], "description": "People who have a birthday in January", "audience_size_lower_bound": 234328378, "audience_size_upper_bound": 275570173, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.413Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.413Z"}, {"_id": "67c56feccceafebe2cf7abad", "interest_id": "6048808449583", "raw_name": "Birthday in July", "name": "Birthday in July", "type": "life_events", "path": ["Demographics", "Life events", "Date of birth", "Month of birth"], "description": "People who have a birthday in July.", "audience_size_lower_bound": 159533761, "audience_size_upper_bound": 187611704, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.414Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.414Z"}, {"_id": "67c56feccceafebe2cf7abaf", "interest_id": "6048026229983", "raw_name": "Birthday in June", "name": "Birthday in June", "type": "life_events", "path": ["Demographics", "Life events", "Date of birth", "Month of birth"], "description": "People who have a birthday in June.", "audience_size_lower_bound": 157556652, "audience_size_upper_bound": 185286623, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.414Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.414Z"}, {"_id": "67c56feccceafebe2cf7abb1", "interest_id": "6048026294583", "raw_name": "Birthday in March", "name": "Birthday in March", "type": "life_events", "path": ["Demographics", "Life events", "Date of birth", "Month of birth"], "description": "People who have a birthday in March.", "audience_size_lower_bound": 155055751, "audience_size_upper_bound": 182345564, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.414Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.414Z"}, {"_id": "67c56feccceafebe2cf7abb3", "interest_id": "6048026061783", "raw_name": "Birthday in May", "name": "Birthday in May", "type": "life_events", "path": ["Demographics", "Life events", "Date of birth", "Month of birth"], "description": "People who have a birthday in May", "audience_size_lower_bound": 163296392, "audience_size_upper_bound": 192036557, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.415Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.415Z"}, {"_id": "67c56feccceafebe2cf7abb5", "interest_id": "6048810938183", "raw_name": "Birthday in November", "name": "Birthday in November", "type": "life_events", "path": ["Demographics", "Life events", "Date of birth", "Month of birth"], "description": "People with a birthday in November", "audience_size_lower_bound": 145917865, "audience_size_upper_bound": 171599410, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.415Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.415Z"}, {"_id": "67c56feccceafebe2cf7abb7", "interest_id": "6048810950583", "raw_name": "Birthday in October", "name": "Birthday in October", "type": "life_events", "path": ["Demographics", "Life events", "Date of birth", "Month of birth"], "description": "People with a birthday in October", "audience_size_lower_bound": 159108288, "audience_size_upper_bound": 187111347, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.415Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.415Z"}, {"_id": "67c56feccceafebe2cf7abb9", "interest_id": "6048810961183", "raw_name": "Birthday in September", "name": "Birthday in September", "type": "life_events", "path": ["Demographics", "Life events", "Date of birth", "Month of birth"], "description": "People with a birthday in September", "audience_size_lower_bound": 152086783, "audience_size_upper_bound": 178854057, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.415Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.415Z"}, {"_id": "67c56feccceafebe2cf7abbb", "interest_id": "6002737124172", "raw_name": "Upcoming birthday", "name": "Upcoming birthday", "type": "life_events", "path": ["Demographics", "Life events", "Date of birth"], "description": "People who are going to have their birthday within the next week.", "audience_size_lower_bound": 65279108, "audience_size_upper_bound": 76768232, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.416Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.416Z"}, {"_id": "67c56feccceafebe2cf7abbd", "interest_id": "6203621472783", "raw_name": "Friends of men with a birthday in 0-7 days", "name": "Friends of men with a birthday in 0-7 days", "type": "life_events", "path": ["Demographics", "Life events", "Friends of"], "description": "Friends of men with a birthday in 0-7 days", "audience_size_lower_bound": 1677150221, "audience_size_upper_bound": 1972328660, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.416Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.416Z"}, {"_id": "67c56feccceafebe2cf7abbf", "interest_id": "6203621119983", "raw_name": "Friends of men with a birthday in 7-30 days", "name": "Friends of men with a birthday in 7-30 days", "type": "life_events", "path": ["Demographics", "Life events", "Friends of"], "description": "Friends of men with a birthday in 7-30 days", "audience_size_lower_bound": 1907215857, "audience_size_upper_bound": 2242885849, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.416Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.416Z"}, {"_id": "67c56feccceafebe2cf7abc1", "interest_id": "6203619820983", "raw_name": "Friends of people who recently moved", "name": "Friends of people who recently moved", "type": "life_events", "path": ["Demographics", "Life events", "Friends of"], "description": "Friends of people who have moved in the past 30 days", "audience_size_lower_bound": 1089917085, "audience_size_upper_bound": 1281742492, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.417Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.417Z"}, {"_id": "67c56feccceafebe2cf7abc3", "interest_id": "6203620854183", "raw_name": "Friends of people with birthdays in a month", "name": "Friends of people with birthdays in a month", "type": "life_events", "path": ["Demographics", "Life events", "Friends of"], "description": "Friends of people with a birthday in 7-30 days", "audience_size_lower_bound": 2039934556, "audience_size_upper_bound": 2398963039, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.417Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.417Z"}, {"_id": "67c56feccceafebe2cf7abc5", "interest_id": "6203621218383", "raw_name": "Friends of people with birthdays in a week", "name": "Friends of people with birthdays in a week", "type": "life_events", "path": ["Demographics", "Life events", "Friends of"], "description": "Friends of people with a birthday in 0-7 days", "audience_size_lower_bound": 1878524994, "audience_size_upper_bound": 2209145393, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.417Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.417Z"}, {"_id": "67c56feccceafebe2cf7abc7", "interest_id": "6203621325983", "raw_name": "Friends of women with a birthday in 0-7 days", "name": "Friends of women with a birthday in 0-7 days", "type": "life_events", "path": ["Demographics", "Life events", "Friends of"], "description": "Friends of women with a birthday in 0-7 days", "audience_size_lower_bound": 1558637676, "audience_size_upper_bound": 1832957907, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.418Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.418Z"}, {"_id": "67c56feccceafebe2cf7abc9", "interest_id": "6203621025983", "raw_name": "Friends of women with a birthday in 7-30 days", "name": "Friends of women with a birthday in 7-30 days", "type": "life_events", "path": ["Demographics", "Life events", "Friends of"], "description": "Friends of women with a birthday in 7-30 days", "audience_size_lower_bound": 1817502999, "audience_size_upper_bound": 2137383527, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.418Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.418Z"}, {"_id": "67c56feccceafebe2cf7abcb", "interest_id": "6003053984972", "raw_name": "Long-distance relationship", "name": "Long-distance relationship", "type": "life_events", "path": ["Demographics", "Life events"], "description": "People who are in a long-distance relationship", "audience_size_lower_bound": 8656590, "audience_size_upper_bound": 10180150, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.418Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.418Z"}, {"_id": "67c56feccceafebe2cf7abcd", "interest_id": "6005149512172", "raw_name": "New job", "name": "New job", "type": "life_events", "path": ["Demographics", "Life events"], "description": "People who have updated their profile with a new job position in the last 6 months.", "audience_size_lower_bound": 105532, "audience_size_upper_bound": 124106, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.418Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.418Z"}, {"_id": "67c56feccceafebe2cf7abcf", "interest_id": "6005232221572", "raw_name": "New relationship", "name": "New relationship", "type": "life_events", "path": ["Demographics", "Life events"], "description": "People who have updated their profile with a new relationship in the last six months", "audience_size_lower_bound": 1830274, "audience_size_upper_bound": 2152403, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.419Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.419Z"}, {"_id": "67c56feccceafebe2cf7abd1", "interest_id": "6003050210972", "raw_name": "Newly engaged (1 year)", "name": "Newly engaged (1 year)", "type": "life_events", "path": ["Demographics", "Life events"], "description": "People who have been engaged for less than 1 year", "audience_size_lower_bound": 4307578, "audience_size_upper_bound": 5065712, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.419Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.419Z"}, {"_id": "67c56feccceafebe2cf7abd3", "interest_id": "6012631862383", "raw_name": "Newly engaged (3 months)", "name": "Newly engaged (3 months)", "type": "life_events", "path": ["Demographics", "Life events"], "description": "People who have been engaged for less than three months", "audience_size_lower_bound": 1572180, "audience_size_upper_bound": 1848884, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.419Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.419Z"}, {"_id": "67c56feccceafebe2cf7abd5", "interest_id": "6002714398772", "raw_name": "Newly engaged (6 months)", "name": "Newly engaged (6 months)", "type": "life_events", "path": ["Demographics", "Life events"], "description": "People who have been engaged for less than 6 months.", "audience_size_lower_bound": 2661204, "audience_size_upper_bound": 3129577, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.42Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.42Z"}, {"_id": "67c56feccceafebe2cf7abd7", "interest_id": "6002714398172", "raw_name": "Newlywed (1 year)", "name": "Newlywed (1 year)", "type": "life_events", "path": ["Demographics", "Life events"], "description": "People who have been married for less than a year", "audience_size_lower_bound": 15283943, "audience_size_upper_bound": 17973918, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.42Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.42Z"}, {"_id": "67c56feccceafebe2cf7abd9", "interest_id": "6013133420583", "raw_name": "Newlywed (3 months)", "name": "Newlywed (3 months)", "type": "life_events", "path": ["Demographics", "Life events"], "description": "People who have been married for less than three months", "audience_size_lower_bound": 4482533, "audience_size_upper_bound": 5271459, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.421Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.421Z"}, {"_id": "67c56feccceafebe2cf7abdb", "interest_id": "6003050226972", "raw_name": "Newlywed (6 months)", "name": "Newlywed (6 months)", "type": "life_events", "path": ["Demographics", "Life events"], "description": "People who have been married for less than six months", "audience_size_lower_bound": 8462187, "audience_size_upper_bound": 9951532, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.421Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.421Z"}, {"_id": "67c56feccceafebe2cf7abdd", "interest_id": "6003054185372", "raw_name": "Recently moved", "name": "Recently moved", "type": "life_events", "path": ["Demographics", "Life events"], "description": "People who have updated their profile with a new current city in the last 6 months", "audience_size_lower_bound": 758077, "audience_size_upper_bound": 891499, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.421Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.421Z"}, {"_id": "67c56feccceafebe2cf7abdf", "interest_id": "6002714398372", "raw_name": "Parents (All)", "name": "Parents (All)", "type": "family_statuses", "path": ["Demographics", "Parents", "All parents"], "description": "People who are likely to be parents.", "audience_size_lower_bound": 311847495, "audience_size_upper_bound": 366732655, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.422Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.422Z"}, {"_id": "67c56feccceafebe2cf7abe1", "interest_id": "6023005372383", "raw_name": "Parents (up to 12 months)", "name": "Parents (up to 12 months)", "type": "family_statuses", "path": ["Demographics", "Parents", "All parents"], "description": "People who are likely to be parents with children aged up to 12 months.", "audience_size_lower_bound": 5567947, "audience_size_upper_bound": 6547906, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.422Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.422Z"}, {"_id": "67c56feccceafebe2cf7abe3", "interest_id": "6023005718983", "raw_name": "Parents with adult children (aged 18-26)", "name": "Parents with adult children (aged 18-26)", "type": "family_statuses", "path": ["Demographics", "Parents", "All parents"], "description": "People who are likely to be parents with adult children.", "audience_size_lower_bound": 71129705, "audience_size_upper_bound": 83648534, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.422Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.422Z"}, {"_id": "67c56feccceafebe2cf7abe5", "interest_id": "6023005529383", "raw_name": "Parents with pre-schoolers (3-5 years)", "name": "Parents with pre-schoolers (3-5 years)", "type": "family_statuses", "path": ["Demographics", "Parents", "All parents"], "description": "People who are likely to be parents with pre-schoolers.", "audience_size_lower_bound": 5899580, "audience_size_upper_bound": 6937907, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.423Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.423Z"}, {"_id": "67c56feccceafebe2cf7abe7", "interest_id": "6023080302983", "raw_name": "Parents with pre-teens (aged 9-12)", "name": "Parents with pre-teens (aged 9-12)", "type": "family_statuses", "path": ["Demographics", "Parents", "All parents"], "description": "People who are likely to be parents with pre-teens.", "audience_size_lower_bound": 12436769, "audience_size_upper_bound": 14625641, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.423Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.423Z"}, {"_id": "67c56feccceafebe2cf7abe9", "interest_id": "6023005570783", "raw_name": "Parents with primary school-age children (6-8 years).", "name": "Parents with primary school-age children (6-8 years).", "type": "family_statuses", "path": ["Demographics", "Parents", "All parents"], "description": "People who are likely to be parents with early school-age children.", "audience_size_lower_bound": 10408489, "audience_size_upper_bound": 12240384, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.423Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.423Z"}, {"_id": "67c56feccceafebe2cf7abeb", "interest_id": "6023005681983", "raw_name": "Parents with teenagers (aged 13-17)", "name": "Parents with teenagers (aged 13-17)", "type": "family_statuses", "path": ["Demographics", "Parents", "All parents"], "description": "People who are likely to be parents with teenagers.", "audience_size_lower_bound": 27115409, "audience_size_upper_bound": 31887721, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.424Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.424Z"}, {"_id": "67c56feccceafebe2cf7abed", "interest_id": "6023005458383", "raw_name": "Parents with toddlers (aged 1-2)", "name": "Parents with toddlers (aged 1-2)", "type": "family_statuses", "path": ["Demographics", "Parents", "All parents"], "description": "People who are likely to be parents with toddlers.", "audience_size_lower_bound": 3584350, "audience_size_upper_bound": 4215196, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.424Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.424Z"}, {"_id": "67c56feccceafebe2cf7abfb", "interest_id": "6008888954983", "raw_name": "Administrative services", "name": "Administrative services", "type": "industries", "path": ["Demographics", "Work", "Industries"], "description": "People with roles in administration. Examples include: secretary, administrative assistant, office manager, office assistant, flat locator, personal assistant, branch manager etc.", "audience_size_lower_bound": 17147046, "audience_size_upper_bound": 20164927, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.429Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.429Z"}, {"_id": "67c56feccceafebe2cf7abfd", "interest_id": "6012903126783", "raw_name": "Architecture and engineering", "name": "Architecture and engineering", "type": "industries", "path": ["Demographics", "Work", "Industries"], "description": "People with roles in architecture and engineering. Examples include: software engineer, technician, electrician, machinist etc.", "audience_size_lower_bound": 6035268, "audience_size_upper_bound": 7097476, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.429Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.429Z"}, {"_id": "67c56feccceafebe2cf7abff", "interest_id": "6012901802383", "raw_name": "Arts, entertainment, sport and media", "name": "Arts, entertainment, sport and media", "type": "industries", "path": ["Demographics", "Work", "Industries"], "description": "People with roles in the arts, entertainment, sport and media. Examples include: photographer, artist, actor, actress, singer etc.", "audience_size_lower_bound": 9516217, "audience_size_upper_bound": ********, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.429Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.429Z"}, {"_id": "67c56feccceafebe2cf7ac01", "interest_id": "*************", "raw_name": "Business and finance", "name": "Business and finance", "type": "industries", "path": ["Demographics", "Work", "Industries"], "description": "People with roles in business and finance. Examples include: accountant, accounting manager, auditor, sales manager, financial adviser, chief financial officer, dealer, agent etc.", "audience_size_lower_bound": 7336130, "audience_size_upper_bound": 8627289, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.43Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.43Z"}, {"_id": "67c56feccceafebe2cf7ac03", "interest_id": "*************", "raw_name": "Business decision maker titles and interests", "name": "Business decision maker titles and interests", "type": "industries", "path": ["Demographics", "Work", "Industries"], "description": "Business decision maker titles and interests is a B2B audience segment that targets ads to people who are business decision makers based on their job titles and interests.", "audience_size_lower_bound": 40360, "audience_size_upper_bound": 47464, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.43Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.43Z"}, {"_id": "67c56feccceafebe2cf7ac05", "interest_id": "6262428231783", "raw_name": "Business decision makers", "name": "Business decision makers", "type": "industries", "path": ["Demographics", "Work", "Industries"], "description": "Business decision is a B2B audience segment that targets ads to people who are business decision makers in engineering/IT, operations, HR, strategy or marketing, based on their job titles.", "audience_size_lower_bound": 40406, "audience_size_upper_bound": 47518, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.43Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.43Z"}, {"_id": "67c56feccceafebe2cf7ac07", "interest_id": "6012903317583", "raw_name": "Cleaning and maintenance services", "name": "Cleaning and maintenance services", "type": "industries", "path": ["Demographics", "Work", "Industries"], "description": "People with roles in cleaning and maintenance. Examples include: caretaker, housekeeper, gardener, handyman etc.", "audience_size_lower_bound": 2489687, "audience_size_upper_bound": 2927872, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.431Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.431Z"}, {"_id": "67c56feccceafebe2cf7ac09", "interest_id": "6012903168383", "raw_name": "Community and social services", "name": "Community and social services", "type": "industries", "path": ["Demographics", "Work", "Industries"], "description": "People with roles in community and social services. Examples include: minister, social worker, counsellor, politician etc.", "audience_size_lower_bound": 7658094, "audience_size_upper_bound": 9005919, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.431Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.431Z"}, {"_id": "67c56feccceafebe2cf7ac0b", "interest_id": "6377134922183", "raw_name": "Companies founded before 2000", "name": "Companies founded before 2000", "type": "industries", "path": ["Demographics", "Work", "Industries"], "description": "People who work for companies founded before 2000.", "audience_size_lower_bound": 967875, "audience_size_upper_bound": 1138222, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.431Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.431Z"}, {"_id": "67c56feccceafebe2cf7ac0d", "interest_id": "6377408028783", "raw_name": "Companies founded between 2000 and 2009", "name": "Companies founded between 2000 and 2009", "type": "industries", "path": ["Demographics", "Work", "Industries"], "description": "People who work for companies founded between 2000 and 2009.", "audience_size_lower_bound": 665006, "audience_size_upper_bound": 782048, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.432Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.432Z"}, {"_id": "67c56feccceafebe2cf7ac0f", "interest_id": "6377168689383", "raw_name": "Companies founded between 2010 and now", "name": "Companies founded between 2010 and now", "type": "industries", "path": ["Demographics", "Work", "Industries"], "description": "People who work for companies founded between 2010 and now.", "audience_size_lower_bound": 955508, "audience_size_upper_bound": 1123678, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.432Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.432Z"}, {"_id": "67c56feccceafebe2cf7ac11", "interest_id": "6377168992983", "raw_name": "Company revenue: $1M to $10M", "name": "Company revenue: $1M to $10M", "type": "industries", "path": ["Demographics", "Work", "Industries"], "description": "People who work for companies with revenue of $1M to $10M.", "audience_size_lower_bound": 759753, "audience_size_upper_bound": 893470, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.432Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.432Z"}, {"_id": "67c56feccceafebe2cf7ac13", "interest_id": "6377169088983", "raw_name": "Company revenue: less than $1M", "name": "Company revenue: less than $1M", "type": "industries", "path": ["Demographics", "Work", "Industries"], "description": "People who work for companies with revenue of less than $1M.", "audience_size_lower_bound": 311240, "audience_size_upper_bound": 366019, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.433Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.433Z"}, {"_id": "67c56feccceafebe2cf7ac15", "interest_id": "6377408081983", "raw_name": "Company revenue: more than $10M", "name": "Company revenue: more than $10M", "type": "industries", "path": ["Demographics", "Work", "Industries"], "description": "People who work for companies with revenue of more than $10M.", "audience_size_lower_bound": 872751, "audience_size_upper_bound": 1026356, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.433Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.433Z"}, {"_id": "67c56feccceafebe2cf7ac17", "interest_id": "6377169550583", "raw_name": "Company size: 1-10 employees", "name": "Company size: 1-10 employees", "type": "industries", "path": ["Demographics", "Work", "Industries"], "description": "People who work for companies with 1 to 10 employees.", "audience_size_lower_bound": 610084, "audience_size_upper_bound": 717459, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.433Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.433Z"}, {"_id": "67c56feccceafebe2cf7ac19", "interest_id": "6377169297783", "raw_name": "Company size: 101-500 employees", "name": "Company size: 101-500 employees", "type": "industries", "path": ["Demographics", "Work", "Industries"], "description": "People who work for companies with 101 to 500 employees.", "audience_size_lower_bound": 645275, "audience_size_upper_bound": 758844, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.434Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.434Z"}, {"_id": "67c56feccceafebe2cf7ac1b", "interest_id": "6377134779583", "raw_name": "Company size: 11-100 employees", "name": "Company size: 11-100 employees", "type": "industries", "path": ["Demographics", "Work", "Industries"], "description": "People who work for companies with 11 to 100 employees.", "audience_size_lower_bound": 1178575, "audience_size_upper_bound": 1386005, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.434Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.434Z"}, {"_id": "67c56feccceafebe2cf7ac1d", "interest_id": "6377408290383", "raw_name": "Company size: more than 500 employees", "name": "Company size: more than 500 employees", "type": "industries", "path": ["Demographics", "Work", "Industries"], "description": "People who work for companies with more than 500 employees.", "audience_size_lower_bound": 739782, "audience_size_upper_bound": 869984, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.434Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.434Z"}, {"_id": "67c56feccceafebe2cf7ac1f", "interest_id": "6012903167783", "raw_name": "Computation and mathematics", "name": "Computation and mathematics", "type": "industries", "path": ["Demographics", "Work", "Industries"], "description": "People with roles in computation and mathematics. Examples include: computer scientist, analyst, mathematician etc.", "audience_size_lower_bound": 5203153, "audience_size_upper_bound": 6118908, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.435Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.435Z"}, {"_id": "67c56feccceafebe2cf7ac21", "interest_id": "6012903128783", "raw_name": "Construction and extraction", "name": "Construction and extraction", "type": "industries", "path": ["Demographics", "Work", "Industries"], "description": "People with roles in construction and extraction. Examples include: service technician, electrician, brick layer, mechanic, machine operator etc.", "audience_size_lower_bound": 3822632, "audience_size_upper_bound": 4495416, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.435Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.435Z"}, {"_id": "67c56feccceafebe2cf7ac23", "interest_id": "6008888998983", "raw_name": "Education and libraries", "name": "Education and libraries", "type": "industries", "path": ["Demographics", "Work", "Industries"], "description": "People with roles in education or libraries. Examples include: educator, instructor, teacher, professor, lecturer, research assistant, tutor, librarian, principal etc.", "audience_size_lower_bound": 10192769, "audience_size_upper_bound": 11986697, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.435Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.435Z"}, {"_id": "67c56feccceafebe2cf7ac25", "interest_id": "6012903299983", "raw_name": "Farming, fishing and forestry", "name": "Farming, fishing and forestry", "type": "industries", "path": ["Demographics", "Work", "Industries"], "description": "People with roles in farming, fishing and forestry. Examples include: farmer, rider, crew member, handyman etc.", "audience_size_lower_bound": 3760392, "audience_size_upper_bound": 4422221, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.436Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.436Z"}, {"_id": "67c56feccceafebe2cf7ac27", "interest_id": "6012903127583", "raw_name": "Food and restaurants", "name": "Food and restaurants", "type": "industries", "path": ["Demographics", "Work", "Industries"], "description": "People with roles in food and restaurants. Examples include: cashier, server, waiter, waitress, chef, barista, line cook etc.", "audience_size_lower_bound": 4054582, "audience_size_upper_bound": 4768189, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.436Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.436Z"}, {"_id": "67c56feccceafebe2cf7ac29", "interest_id": "6019621029983", "raw_name": "Government employees (global)", "name": "Government employees (global)", "type": "industries", "path": ["Demographics", "Work", "Industries"], "description": "People with roles in the government", "audience_size_lower_bound": 805733, "audience_size_upper_bound": 947543, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.436Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.436Z"}, {"_id": "67c56feccceafebe2cf7ac2b", "interest_id": "6012903159383", "raw_name": "Healthcare and medical services", "name": "Healthcare and medical services", "type": "industries", "path": ["Demographics", "Work", "Industries"], "description": "People with roles in healthcare and medical services. Examples include: physician, dentist, cardiologist etc.", "audience_size_lower_bound": 9609258, "audience_size_upper_bound": 11300488, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.436Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.436Z"}, {"_id": "67c56feccceafebe2cf7ac2d", "interest_id": "6008888961983", "raw_name": "IT and technical services", "name": "IT and technical services", "type": "industries", "path": ["Demographics", "Work", "Industries"], "description": "People with roles in IT and technical services. Examples include: IT technician, web developer, IT consultant etc.", "audience_size_lower_bound": 6328886, "audience_size_upper_bound": 7442770, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.437Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.437Z"}, {"_id": "67c56feccceafebe2cf7ac2f", "interest_id": "6262428248783", "raw_name": "IT decision makers", "name": "IT decision makers", "type": "industries", "path": ["Demographics", "Work", "Industries"], "description": "IT decision makers is a B2B audience segment that target ads to people who are IT decision makers based on their job titles.", "audience_size_lower_bound": 13676, "audience_size_upper_bound": 16083, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.437Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.437Z"}, {"_id": "67c56feccceafebe2cf7ac31", "interest_id": "6012903160983", "raw_name": "Installation and repair services", "name": "Installation and repair services", "type": "industries", "path": ["Demographics", "Work", "Industries"], "description": "People with roles in installation and repair services. Examples include: technician, operator, mechanic, welder etc.", "audience_size_lower_bound": 6132440, "audience_size_upper_bound": 7211750, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.437Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.437Z"}, {"_id": "67c56feccceafebe2cf7ac33", "interest_id": "6075565069783", "raw_name": "Large business-to-business enterprise employees (500+ employees)", "name": "Large business-to-business enterprise employees (500+ employees)", "type": "industries", "path": ["Demographics", "Work", "Industries"], "description": "A proxy for people who work in B2B companies with 501 or more employees, based on the number of users regularly logging in from the same IP location.", "audience_size_lower_bound": 181910876, "audience_size_upper_bound": 213927191, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.437Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.437Z"}, {"_id": "67c56feccceafebe2cf7ac35", "interest_id": "6008888972183", "raw_name": "Legal services", "name": "Legal services", "type": "industries", "path": ["Demographics", "Work", "Industries"], "description": "People with roles in legal services. Examples include: lawyer, corporate counsel, partner, paralegal etc.", "audience_size_lower_bound": 921537, "audience_size_upper_bound": 1083728, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.438Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.438Z"}, {"_id": "67c56feccceafebe2cf7ac37", "interest_id": "6012903167183", "raw_name": "Life, physical and social sciences", "name": "Life, physical and social sciences", "type": "industries", "path": ["Demographics", "Work", "Industries"], "description": "People with roles in life, physical and social sciences. Examples include: professor, chemist, psychologist, geologist etc.", "audience_size_lower_bound": 3914432, "audience_size_upper_bound": 4603373, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.438Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.438Z"}, {"_id": "67c56feccceafebe2cf7ac39", "interest_id": "6009003311983", "raw_name": "Management", "name": "Management", "type": "industries", "path": ["Demographics", "Work", "Industries"], "description": "People with roles in management. Examples include: manager, supervisor, director, president, chairperson etc.", "audience_size_lower_bound": 16743404, "audience_size_upper_bound": 19690244, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.438Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.438Z"}, {"_id": "67c56feccceafebe2cf7ac3b", "interest_id": "6080792228383", "raw_name": "Medium business-to-business enterprise employees (200-500 employees)", "name": "Medium business-to-business enterprise employees (200-500 employees)", "type": "industries", "path": ["Demographics", "Work", "Industries"], "description": "A proxy for people who work in B2B companies with 200-500 employees, based on the number of users regularly logging in from the same IP location.", "audience_size_lower_bound": 43302045, "audience_size_upper_bound": 50923205, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.439Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.439Z"}, {"_id": "67c56feccceafebe2cf7ac3d", "interest_id": "6012903320383", "raw_name": "Military (global)", "name": "Military (global)", "type": "industries", "path": ["Demographics", "Work", "Industries"], "description": "People with active roles in the military.", "audience_size_lower_bound": 713159, "audience_size_upper_bound": 838676, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.439Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.439Z"}, {"_id": "67c56feccceafebe2cf7ac3f", "interest_id": "6012903140583", "raw_name": "Production", "name": "Production", "type": "industries", "path": ["Demographics", "Work", "Industries"], "description": "People with roles in production. Examples include: miner, blacksmith, lumberjack etc.", "audience_size_lower_bound": 10537183, "audience_size_upper_bound": 12391728, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.439Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.439Z"}, {"_id": "67c56feccceafebe2cf7ac41", "interest_id": "6012903299783", "raw_name": "Protective services", "name": "Protective services", "type": "industries", "path": ["Demographics", "Work", "Industries"], "description": "People with roles in protective services. Examples include: security guard, sergeant, life guard, police officer, firefighter etc.", "audience_size_lower_bound": 2227144, "audience_size_upper_bound": 2619122, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.44Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.44Z"}, {"_id": "67c56feccceafebe2cf7ac43", "interest_id": "6008888980183", "raw_name": "Sales", "name": "Sales", "type": "industries", "path": ["Demographics", "Work", "Industries"], "description": "People with roles in sales. Examples include: sales assistant, retail manager, realtor, consultant, customer service representative, cashier etc.", "audience_size_lower_bound": 13329538, "audience_size_upper_bound": 15675537, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.44Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.44Z"}, {"_id": "67c56feccceafebe2cf7ac45", "interest_id": "6080792282783", "raw_name": "Small business-to-business enterprise employees (10-200 employees)", "name": "Small business-to-business enterprise employees (10-200 employees)", "type": "industries", "path": ["Demographics", "Work", "Industries"], "description": "A proxy for people who work in B2B companies with 10-199 employees, based on the number of users regularly logging in from the same IP location.", "audience_size_lower_bound": 113883692, "audience_size_upper_bound": 133927222, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.44Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.44Z"}, {"_id": "67c56feccceafebe2cf7ac47", "interest_id": "6012903320983", "raw_name": "Transport and moving", "name": "Transport and moving", "type": "industries", "path": ["Demographics", "Work", "Industries"], "description": "People with roles in transport and moving. Examples include: driver, operator, sea captain, flight attendant, pilot etc.", "audience_size_lower_bound": 5823071, "audience_size_upper_bound": 6847932, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.44Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.44Z"}, {"_id": "67c56feccceafebe2cf7ac49", "interest_id": "6016671857383", "raw_name": "Veterans (US)", "name": "Veterans (US)", "type": "industries", "path": ["Demographics", "Work", "Industries"], "description": "People who were previously employed by the US military", "audience_size_lower_bound": 1119284, "audience_size_upper_bound": 1316278, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.441Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.441Z"}, {"_id": "67c56feccceafebe2cf7ac4b", "interest_id": "6003584163107", "raw_name": "Advertising (marketing)", "name": "Advertising (marketing)", "type": "interests", "path": ["Interests", "Business and industry"], "description": "", "audience_size_lower_bound": 465943911, "audience_size_upper_bound": 547950040, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.441Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.441Z"}, {"_id": "67c56feccceafebe2cf7ac4d", "interest_id": "6003840140052", "raw_name": "Agriculture (industry)", "name": "Agriculture (industry)", "type": "interests", "path": ["Interests", "Business and industry"], "description": "", "audience_size_lower_bound": 389563392, "audience_size_upper_bound": 458126550, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.442Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.442Z"}, {"_id": "67c56feccceafebe2cf7ac4f", "interest_id": "6004140335706", "raw_name": "Architecture (architecture)", "name": "Architecture (architecture)", "type": "interests", "path": ["Interests", "Business and industry"], "description": "", "audience_size_lower_bound": *********, "audience_size_upper_bound": *********, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.442Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.442Z"}, {"_id": "67c56feccceafebe2cf7ac51", "interest_id": "600**********", "raw_name": "Aviation (air travel)", "name": "Aviation (air travel)", "type": "interests", "path": ["Interests", "Business and industry"], "description": "", "audience_size_lower_bound": *********, "audience_size_upper_bound": *********, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.442Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.442Z"}, {"_id": "67c56feccceafebe2cf7ac53", "interest_id": "*************", "raw_name": "Investment banking (banking)", "name": "Investment banking (banking)", "type": "interests", "path": ["Interests", "Business and industry", "Banking (finance)"], "description": "", "audience_size_lower_bound": ********, "audience_size_upper_bound": ********, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.442Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.442Z"}, {"_id": "67c56feccceafebe2cf7ac55", "interest_id": "*************", "raw_name": "Online banking (banking)", "name": "Online banking (banking)", "type": "interests", "path": ["Interests", "Business and industry", "Banking (finance)"], "description": "", "audience_size_lower_bound": ********, "audience_size_upper_bound": *********, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.443Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.443Z"}, {"_id": "67c56feccceafebe2cf7ac57", "interest_id": "*************", "raw_name": "Retail banking (banking)", "name": "Retail banking (banking)", "type": "interests", "path": ["Interests", "Business and industry", "Banking (finance)"], "description": "", "audience_size_lower_bound": ********, "audience_size_upper_bound": ********, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.443Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.443Z"}, {"_id": "67c56feccceafebe2cf7ac59", "interest_id": "*************", "raw_name": "Business (business & finance)", "name": "Business (business & finance)", "type": "interests", "path": ["Interests", "Business and industry"], "description": "", "audience_size_lower_bound": *********, "audience_size_upper_bound": **********, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.443Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.443Z"}, {"_id": "67c56feccceafebe2cf7ac5b", "interest_id": "*************", "raw_name": "Construction (industry)", "name": "Construction (industry)", "type": "interests", "path": ["Interests", "Business and industry"], "description": "", "audience_size_lower_bound": 466283290, "audience_size_upper_bound": 548349150, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.444Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.444Z"}, {"_id": "67c56feccceafebe2cf7ac5d", "interest_id": "6003266266843", "raw_name": "Fashion design (design)", "name": "Fashion design (design)", "type": "interests", "path": ["Interests", "Business and industry", "Design (design)"], "description": "", "audience_size_lower_bound": 347376821, "audience_size_upper_bound": 408515142, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.444Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.444Z"}, {"_id": "67c56feccceafebe2cf7ac5f", "interest_id": "6003096002658", "raw_name": "Graphic design (visual art)", "name": "Graphic design (visual art)", "type": "interests", "path": ["Interests", "Business and industry", "Design (design)"], "description": "", "audience_size_lower_bound": 315060008, "audience_size_upper_bound": 370510570, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.444Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.444Z"}, {"_id": "67c56feccceafebe2cf7ac61", "interest_id": "6002920953955", "raw_name": "Interior design (design)", "name": "Interior design (design)", "type": "interests", "path": ["Interests", "Business and industry", "Design (design)"], "description": "", "audience_size_lower_bound": 488461241, "audience_size_upper_bound": 574430420, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.445Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.445Z"}, {"_id": "67c56feccceafebe2cf7ac63", "interest_id": "6003656112304", "raw_name": "Economics (economics)", "name": "Economics (economics)", "type": "interests", "path": ["Interests", "Business and industry"], "description": "", "audience_size_lower_bound": 308751105, "audience_size_upper_bound": 363091300, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.445Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.445Z"}, {"_id": "67c56feccceafebe2cf7ac65", "interest_id": "6003252179711", "raw_name": "Engineering (science)", "name": "Engineering (science)", "type": "interests", "path": ["Interests", "Business and industry"], "description": "", "audience_size_lower_bound": 469516258, "audience_size_upper_bound": 552151120, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.445Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.445Z"}, {"_id": "67c56feccceafebe2cf7ac67", "interest_id": "6003371567474", "raw_name": "Entrepreneurship (business & finance)", "name": "Entrepreneurship (business & finance)", "type": "interests", "path": ["Interests", "Business and industry"], "description": "", "audience_size_lower_bound": 382208471, "audience_size_upper_bound": 449477162, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.445Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.445Z"}, {"_id": "67c56feccceafebe2cf7ac69", "interest_id": "6003270811593", "raw_name": "Higher education (education)", "name": "Higher education (education)", "type": "interests", "path": ["Interests", "Business and industry"], "description": "", "audience_size_lower_bound": 563361079, "audience_size_upper_bound": 662512630, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.446Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.446Z"}, {"_id": "67c56feccceafebe2cf7ac6b", "interest_id": "6004037932409", "raw_name": "Management (business & finance)", "name": "Management (business & finance)", "type": "interests", "path": ["Interests", "Business and industry"], "description": "", "audience_size_lower_bound": 332392721, "audience_size_upper_bound": 390893840, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.446Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.446Z"}, {"_id": "67c56feccceafebe2cf7ac6d", "interest_id": "6003279598823", "raw_name": "Marketing (business & finance)", "name": "Marketing (business & finance)", "type": "interests", "path": ["Interests", "Business and industry"], "description": "", "audience_size_lower_bound": 605494039, "audience_size_upper_bound": 712060990, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.446Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.446Z"}, {"_id": "67c56feccceafebe2cf7ac6f", "interest_id": "6003127206524", "raw_name": "Digital marketing (marketing)", "name": "Digital marketing (marketing)", "type": "interests", "path": ["Interests", "Business and industry", "Online (computing)"], "description": "", "audience_size_lower_bound": 149444939, "audience_size_upper_bound": 175747249, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.447Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.447Z"}, {"_id": "67c56feccceafebe2cf7ac71", "interest_id": "6003076016339", "raw_name": "Email marketing (marketing)", "name": "Email marketing (marketing)", "type": "interests", "path": ["Interests", "Business and industry", "Online (computing)"], "description": "", "audience_size_lower_bound": 14417554, "audience_size_upper_bound": 16955044, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.447Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.447Z"}, {"_id": "67c56feccceafebe2cf7ac73", "interest_id": "6003526234370", "raw_name": "Online advertising (marketing)", "name": "Online advertising (marketing)", "type": "interests", "path": ["Interests", "Business and industry", "Online (computing)"], "description": "", "audience_size_lower_bound": 156407143, "audience_size_upper_bound": 183934801, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.447Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.447Z"}, {"_id": "67c56feccceafebe2cf7ac75", "interest_id": "6003370636074", "raw_name": "Search engine optimization (software)", "name": "Search engine optimization (software)", "type": "interests", "path": ["Interests", "Business and industry", "Online (computing)"], "description": "", "audience_size_lower_bound": 36242695, "audience_size_upper_bound": 42621410, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.448Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.448Z"}, {"_id": "67c56feccceafebe2cf7ac77", "interest_id": "6004030160948", "raw_name": "Social media (online media)", "name": "Social media (online media)", "type": "interests", "path": ["Interests", "Business and industry", "Online (computing)"], "description": "", "audience_size_lower_bound": 633785255, "audience_size_upper_bound": 745331460, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.448Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.448Z"}, {"_id": "67c56feccceafebe2cf7ac79", "interest_id": "6003389760112", "raw_name": "Social media marketing (marketing)", "name": "Social media marketing (marketing)", "type": "interests", "path": ["Interests", "Business and industry", "Online (computing)"], "description": "", "audience_size_lower_bound": 84384732, "audience_size_upper_bound": 99236446, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.448Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.448Z"}, {"_id": "67c56feccceafebe2cf7ac7b", "interest_id": "6003402518839", "raw_name": "Web design (websites)", "name": "Web design (websites)", "type": "interests", "path": ["Interests", "Business and industry", "Online (computing)"], "description": "", "audience_size_lower_bound": 53903690, "audience_size_upper_bound": 63390740, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.449Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.449Z"}, {"_id": "67c56feccceafebe2cf7ac7d", "interest_id": "6003290005325", "raw_name": "Web development (websites)", "name": "Web development (websites)", "type": "interests", "path": ["Interests", "Business and industry", "Online (computing)"], "description": "", "audience_size_lower_bound": 45666865, "audience_size_upper_bound": 53704234, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.449Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.449Z"}, {"_id": "67c56feccceafebe2cf7ac7f", "interest_id": "6003387418453", "raw_name": "Web hosting (computing)", "name": "Web hosting (computing)", "type": "interests", "path": ["Interests", "Business and industry", "Online (computing)"], "description": "", "audience_size_lower_bound": ********, "audience_size_upper_bound": ********, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.449Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.449Z"}, {"_id": "67c56feccceafebe2cf7ac81", "interest_id": "*************", "raw_name": "Credit cards (credit & lending)", "name": "Credit cards (credit & lending)", "type": "interests", "path": ["Interests", "Business and industry", "Personal finance (banking)"], "description": "", "audience_size_lower_bound": *********, "audience_size_upper_bound": *********, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.45Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.45Z"}, {"_id": "67c56feccceafebe2cf7ac83", "interest_id": "*************", "raw_name": "Insurance (business & finance)", "name": "Insurance (business & finance)", "type": "interests", "path": ["Interests", "Business and industry", "Personal finance (banking)"], "description": "", "audience_size_lower_bound": *********, "audience_size_upper_bound": *********, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.45Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.45Z"}, {"_id": "67c56feccceafebe2cf7ac85", "interest_id": "*************", "raw_name": "Investment (business & finance)", "name": "Investment (business & finance)", "type": "interests", "path": ["Interests", "Business and industry", "Personal finance (banking)"], "description": "", "audience_size_lower_bound": *********, "audience_size_upper_bound": *********, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.45Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.45Z"}, {"_id": "67c56feccceafebe2cf7ac87", "interest_id": "*************", "raw_name": "Mortgage loans (banking)", "name": "Mortgage loans (banking)", "type": "interests", "path": ["Interests", "Business and industry", "Personal finance (banking)"], "description": "", "audience_size_lower_bound": *********, "audience_size_upper_bound": *********, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.451Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.451Z"}, {"_id": "67c56feccceafebe2cf7ac89", "interest_id": "*************", "raw_name": "Real estate (industry)", "name": "Real estate (industry)", "type": "interests", "path": ["Interests", "Business and industry"], "description": "", "audience_size_lower_bound": *********, "audience_size_upper_bound": *********, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.451Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.451Z"}, {"_id": "67c56feccceafebe2cf7ac8b", "interest_id": "*************", "raw_name": "Retail (industry)", "name": "Retail (industry)", "type": "interests", "path": ["Interests", "Business and industry"], "description": "", "audience_size_lower_bound": 713008299, "audience_size_upper_bound": 838497760, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.451Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.451Z"}, {"_id": "67c56feccceafebe2cf7ac8d", "interest_id": "6003074954515", "raw_name": "Sales (business & finance)", "name": "Sales (business & finance)", "type": "interests", "path": ["Interests", "Business and industry"], "description": "", "audience_size_lower_bound": 892383903, "audience_size_upper_bound": 1049443470, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.452Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.452Z"}, {"_id": "67c56feccceafebe2cf7ac8f", "interest_id": "6002866718622", "raw_name": "Science (science)", "name": "Science (science)", "type": "interests", "path": ["Interests", "Business and industry"], "description": "", "audience_size_lower_bound": 600334489, "audience_size_upper_bound": 705993360, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.452Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.452Z"}, {"_id": "67c56feccceafebe2cf7ac91", "interest_id": "6002884511422", "raw_name": "Small business (business & finance)", "name": "Small business (business & finance)", "type": "interests", "path": ["Interests", "Business and industry"], "description": "", "audience_size_lower_bound": 177101195, "audience_size_upper_bound": 208271006, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.452Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.452Z"}, {"_id": "67c56feccceafebe2cf7ac93", "interest_id": "6002971095994", "raw_name": "Action games (video games)", "name": "Action games (video games)", "type": "interests", "path": ["Interests", "Entertainment (leisure)", "Games (leisure)"], "description": "", "audience_size_lower_bound": 152967687, "audience_size_upper_bound": 179890000, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.452Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.452Z"}, {"_id": "67c56feccceafebe2cf7ac95", "interest_id": "6003342470823", "raw_name": "Board games (games)", "name": "Board games (games)", "type": "interests", "path": ["Interests", "Entertainment (leisure)", "Games (leisure)"], "description": "", "audience_size_lower_bound": 87881277, "audience_size_upper_bound": 103348382, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.453Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.453Z"}, {"_id": "67c56feccceafebe2cf7ac97", "interest_id": "6003434373937", "raw_name": "Browser games (video games)", "name": "Browser games (video games)", "type": "interests", "path": ["Interests", "Entertainment (leisure)", "Games (leisure)"], "description": "", "audience_size_lower_bound": 51808472, "audience_size_upper_bound": 60926764, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.453Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.453Z"}, {"_id": "67c56feccceafebe2cf7ac99", "interest_id": "6003647522546", "raw_name": "Card games (games)", "name": "Card games (games)", "type": "interests", "path": ["Interests", "Entertainment (leisure)", "Games (leisure)"], "description": "", "audience_size_lower_bound": 272395153, "audience_size_upper_bound": 320336700, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.453Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.453Z"}, {"_id": "67c56feccceafebe2cf7ac9b", "interest_id": "6003248338072", "raw_name": "Casino games (gambling)", "name": "Casino games (gambling)", "type": "interests", "path": ["Interests", "Entertainment (leisure)", "Games (leisure)"], "description": "", "audience_size_lower_bound": 43101736, "audience_size_upper_bound": 50687642, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.454Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.454Z"}, {"_id": "67c56feccceafebe2cf7ac9d", "interest_id": "6003059733932", "raw_name": "First-person shooter games (video games)", "name": "First-person shooter games (video games)", "type": "interests", "path": ["Interests", "Entertainment (leisure)", "Games (leisure)"], "description": "", "audience_size_lower_bound": 558766989, "audience_size_upper_bound": 657109980, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.454Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.454Z"}, {"_id": "67c56feccceafebe2cf7ac9f", "interest_id": "6003012317397", "raw_name": "Gambling (gambling)", "name": "Gambling (gambling)", "type": "interests", "path": ["Interests", "Entertainment (leisure)", "Games (leisure)"], "description": "", "audience_size_lower_bound": 331443443, "audience_size_upper_bound": 389777490, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.454Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.454Z"}, {"_id": "67c56feccceafebe2cf7aca1", "interest_id": "6003176101552", "raw_name": "Massively multiplayer online games (video games)", "name": "Massively multiplayer online games (video games)", "type": "interests", "path": ["Interests", "Entertainment (leisure)", "Games (leisure)"], "description": "", "audience_size_lower_bound": 111371562, "audience_size_upper_bound": 130972958, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.455Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.455Z"}, {"_id": "67c56feccceafebe2cf7aca3", "interest_id": "6003198370967", "raw_name": "Massively multiplayer online role-playing games (video games)", "name": "Massively multiplayer online role-playing games (video games)", "type": "interests", "path": ["Interests", "Entertainment (leisure)", "Games (leisure)"], "description": "", "audience_size_lower_bound": 109827267, "audience_size_upper_bound": 129156867, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.455Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.455Z"}, {"_id": "67c56feccceafebe2cf7aca5", "interest_id": "6003153672865", "raw_name": "Online games (video games)", "name": "Online games (video games)", "type": "interests", "path": ["Interests", "Entertainment (leisure)", "Games (leisure)"], "description": "", "audience_size_lower_bound": 595532287, "audience_size_upper_bound": 700345970, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.455Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.455Z"}, {"_id": "67c56feccceafebe2cf7aca7", "interest_id": "6003030519207", "raw_name": "Online poker (gambling)", "name": "Online poker (gambling)", "type": "interests", "path": ["Interests", "Entertainment (leisure)", "Games (leisure)"], "description": "", "audience_size_lower_bound": 151133044, "audience_size_upper_bound": 177732460, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.456Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.456Z"}, {"_id": "67c56feccceafebe2cf7aca9", "interest_id": "6003668975718", "raw_name": "Puzzle video games (video games)", "name": "Puzzle video games (video games)", "type": "interests", "path": ["Interests", "Entertainment (leisure)", "Games (leisure)"], "description": "", "audience_size_lower_bound": 288117984, "audience_size_upper_bound": 338826750, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.456Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.456Z"}, {"_id": "67c56feccceafebe2cf7acab", "interest_id": "6003385141743", "raw_name": "Racing games (video game)", "name": "Racing games (video game)", "type": "interests", "path": ["Interests", "Entertainment (leisure)", "Games (leisure)"], "description": "", "audience_size_lower_bound": 123480765, "audience_size_upper_bound": 145213380, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.456Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.456Z"}, {"_id": "67c56feccceafebe2cf7acad", "interest_id": "6003380576181", "raw_name": "Role-playing games (video games)", "name": "Role-playing games (video games)", "type": "interests", "path": ["Interests", "Entertainment (leisure)", "Games (leisure)"], "description": "", "audience_size_lower_bound": 152068967, "audience_size_upper_bound": 178833106, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.457Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.457Z"}, {"_id": "67c56feccceafebe2cf7acaf", "interest_id": "6003246168013", "raw_name": "Simulation games (video games)", "name": "Simulation games (video games)", "type": "interests", "path": ["Interests", "Entertainment (leisure)", "Games (leisure)"], "description": "", "audience_size_lower_bound": 92506020, "audience_size_upper_bound": 108787080, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.457Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.457Z"}, {"_id": "67c56feccceafebe2cf7acb1", "interest_id": "6003540150873", "raw_name": "Sports games (video games)", "name": "Sports games (video games)", "type": "interests", "path": ["Interests", "Entertainment (leisure)", "Games (leisure)"], "description": "", "audience_size_lower_bound": 167944091, "audience_size_upper_bound": 197502252, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.457Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.457Z"}, {"_id": "67c56feccceafebe2cf7acb3", "interest_id": "6003582500438", "raw_name": "Strategy games (games)", "name": "Strategy games (games)", "type": "interests", "path": ["Interests", "Entertainment (leisure)", "Games (leisure)"], "description": "", "audience_size_lower_bound": 57279099, "audience_size_upper_bound": 67360221, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.458Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.458Z"}, {"_id": "67c56feccceafebe2cf7acb5", "interest_id": "6003940339466", "raw_name": "Video games (gaming)", "name": "Video games (gaming)", "type": "interests", "path": ["Interests", "Entertainment (leisure)", "Games (leisure)"], "description": "", "audience_size_lower_bound": 946113469, "audience_size_upper_bound": 1112629440, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.458Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.458Z"}, {"_id": "67c56feccceafebe2cf7acb7", "interest_id": "6002964500317", "raw_name": "Word games (games)", "name": "Word games (games)", "type": "interests", "path": ["Interests", "Entertainment (leisure)", "Games (leisure)"], "description": "", "audience_size_lower_bound": 62499974, "audience_size_upper_bound": 73499970, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.458Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.458Z"}, {"_id": "67c56feccceafebe2cf7acb9", "interest_id": "6003247127613", "raw_name": "Ballet (dance)", "name": "Ballet (dance)", "type": "interests", "path": ["Interests", "Entertainment (leisure)", "Live events (entertainment)"], "description": "", "audience_size_lower_bound": 100333394, "audience_size_upper_bound": 117992072, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.459Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.459Z"}, {"_id": "67c56feccceafebe2cf7acbb", "interest_id": "6003156321008", "raw_name": "Bars (bars, clubs & nightlife)", "name": "Bars (bars, clubs & nightlife)", "type": "interests", "path": ["Interests", "Entertainment (leisure)", "Live events (entertainment)"], "description": "", "audience_size_lower_bound": 294046380, "audience_size_upper_bound": 345798543, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.459Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.459Z"}, {"_id": "67c56feccceafebe2cf7acbd", "interest_id": "6002970406974", "raw_name": "Concerts (music event)", "name": "Concerts (music event)", "type": "interests", "path": ["Interests", "Entertainment (leisure)", "Live events (entertainment)"], "description": "", "audience_size_lower_bound": 259957134, "audience_size_upper_bound": 305709590, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.459Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.459Z"}, {"_id": "67c56feccceafebe2cf7acbf", "interest_id": "6003247890613", "raw_name": "Dancehalls (music)", "name": "Dancehalls (music)", "type": "interests", "path": ["Interests", "Entertainment (leisure)", "Live events (entertainment)"], "description": "", "audience_size_lower_bound": 103399718, "audience_size_upper_bound": 121598069, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.46Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.46Z"}, {"_id": "67c56feccceafebe2cf7acc1", "interest_id": "6003108826384", "raw_name": "Music festivals (events)", "name": "Music festivals (events)", "type": "interests", "path": ["Interests", "Entertainment (leisure)", "Live events (entertainment)"], "description": "", "audience_size_lower_bound": 293265824, "audience_size_upper_bound": 344880610, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.46Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.46Z"}, {"_id": "67c56feccceafebe2cf7acc3", "interest_id": "6003361714600", "raw_name": "Nightclubs (bars, clubs & nightlife)", "name": "Nightclubs (bars, clubs & nightlife)", "type": "interests", "path": ["Interests", "Entertainment (leisure)", "Live events (entertainment)"], "description": "", "audience_size_lower_bound": 334577993, "audience_size_upper_bound": 393463720, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.46Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.46Z"}, {"_id": "67c56feccceafebe2cf7acc5", "interest_id": "6003147868152", "raw_name": "Parties (event)", "name": "Parties (event)", "type": "interests", "path": ["Interests", "Entertainment (leisure)", "Live events (entertainment)"], "description": "", "audience_size_lower_bound": 384128267, "audience_size_upper_bound": 451734843, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.461Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.461Z"}, {"_id": "67c56feccceafebe2cf7acc7", "interest_id": "6003417378239", "raw_name": "Plays (performing arts)", "name": "Plays (performing arts)", "type": "interests", "path": ["Interests", "Entertainment (leisure)", "Live events (entertainment)"], "description": "", "audience_size_lower_bound": 205761207, "audience_size_upper_bound": 241975180, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.461Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.461Z"}, {"_id": "67c56feccceafebe2cf7acc9", "interest_id": "6002957026250", "raw_name": "Theatre (performing arts)", "name": "Theatre (performing arts)", "type": "interests", "path": ["Interests", "Entertainment (leisure)", "Live events (entertainment)"], "description": "", "audience_size_lower_bound": 512677397, "audience_size_upper_bound": 602908620, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.461Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.461Z"}, {"_id": "67c56feccceafebe2cf7accb", "interest_id": "6003243604899", "raw_name": "Action movies (movies)", "name": "Action movies (movies)", "type": "interests", "path": ["Interests", "Entertainment (leisure)", "Movies (entertainment & media)"], "description": "", "audience_size_lower_bound": 627385552, "audience_size_upper_bound": 737805410, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.461Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.461Z"}, {"_id": "67c56feccceafebe2cf7accd", "interest_id": "6003129926917", "raw_name": "Animated movies (movies)", "name": "Animated movies (movies)", "type": "interests", "path": ["Interests", "Entertainment (leisure)", "Movies (entertainment & media)"], "description": "", "audience_size_lower_bound": 418699651, "audience_size_upper_bound": 492390790, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.462Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.462Z"}, {"_id": "67c56feccceafebe2cf7accf", "interest_id": "6003605717820", "raw_name": "Anime movies (movies)", "name": "Anime movies (movies)", "type": "interests", "path": ["Interests", "Entertainment (leisure)", "Movies (entertainment & media)"], "description": "", "audience_size_lower_bound": 385607074, "audience_size_upper_bound": 453473920, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.462Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.462Z"}, {"_id": "67c56feccceafebe2cf7acd1", "interest_id": "6003157824284", "raw_name": "Bollywood movies (movies)", "name": "Bollywood movies (movies)", "type": "interests", "path": ["Interests", "Entertainment (leisure)", "Movies (entertainment & media)"], "description": "", "audience_size_lower_bound": 374775850, "audience_size_upper_bound": 440736400, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.462Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.462Z"}, {"_id": "67c56feccceafebe2cf7acd3", "interest_id": "6003161475030", "raw_name": "Comedy movies (movies)", "name": "Comedy movies (movies)", "type": "interests", "path": ["Interests", "Entertainment (leisure)", "Movies (entertainment & media)"], "description": "", "audience_size_lower_bound": 1006576522, "audience_size_upper_bound": 1183733990, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.463Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.463Z"}, {"_id": "67c56feccceafebe2cf7acd5", "interest_id": "6003373175581", "raw_name": "Documentary movies (movies)", "name": "Documentary movies (movies)", "type": "interests", "path": ["Interests", "Entertainment (leisure)", "Movies (entertainment & media)"], "description": "", "audience_size_lower_bound": 440272559, "audience_size_upper_bound": 517760530, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.463Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.463Z"}, {"_id": "67c56feccceafebe2cf7acd7", "interest_id": "6003375422677", "raw_name": "Drama movies (movies)", "name": "Drama movies (movies)", "type": "interests", "path": ["Interests", "Entertainment (leisure)", "Movies (entertainment & media)"], "description": "", "audience_size_lower_bound": 473430323, "audience_size_upper_bound": 556754060, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.463Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.463Z"}, {"_id": "67c56feccceafebe2cf7acd9", "interest_id": "6003656922020", "raw_name": "Horror movies (movies)", "name": "Horror movies (movies)", "type": "interests", "path": ["Interests", "Entertainment (leisure)", "Movies (entertainment & media)"], "description": "", "audience_size_lower_bound": 376549897, "audience_size_upper_bound": 442822680, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.464Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.464Z"}, {"_id": "67c56feccceafebe2cf7acdb", "interest_id": "6003351312828", "raw_name": "Musical theatre (performing arts)", "name": "Musical theatre (performing arts)", "type": "interests", "path": ["Interests", "Entertainment (leisure)", "Movies (entertainment & media)"], "description": "", "audience_size_lower_bound": 99099132, "audience_size_upper_bound": 116540580, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.464Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.464Z"}, {"_id": "67c56feccceafebe2cf7acdd", "interest_id": "6003206308286", "raw_name": "Science fiction movies (movies)", "name": "Science fiction movies (movies)", "type": "interests", "path": ["Interests", "Entertainment (leisure)", "Movies (entertainment & media)"], "description": "", "audience_size_lower_bound": 363505918, "audience_size_upper_bound": 427482960, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.464Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.464Z"}, {"_id": "67c56feccceafebe2cf7acdf", "interest_id": "6003225325061", "raw_name": "Thriller movies (movies)", "name": "Thriller movies (movies)", "type": "interests", "path": ["Interests", "Entertainment (leisure)", "Movies (entertainment & media)"], "description": "", "audience_size_lower_bound": 553071904, "audience_size_upper_bound": 650412560, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.465Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.465Z"}, {"_id": "67c56feccceafebe2cf7ace1", "interest_id": "6003257757682", "raw_name": "Blues music (music)", "name": "Blues music (music)", "type": "interests", "path": ["Interests", "Entertainment (leisure)", "Music (entertainment & media)"], "description": "", "audience_size_lower_bound": 453873086, "audience_size_upper_bound": 533754750, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.465Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.465Z"}, {"_id": "67c56feccceafebe2cf7ace3", "interest_id": "6002951587955", "raw_name": "Classical music (music)", "name": "Classical music (music)", "type": "interests", "path": ["Interests", "Entertainment (leisure)", "Music (entertainment & media)"], "description": "", "audience_size_lower_bound": 324272448, "audience_size_upper_bound": 381344400, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.465Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.465Z"}, {"_id": "67c56feccceafebe2cf7ace5", "interest_id": "6003493980595", "raw_name": "Country music (music)", "name": "Country music (music)", "type": "interests", "path": ["Interests", "Entertainment (leisure)", "Music (entertainment & media)"], "description": "", "audience_size_lower_bound": 470572865, "audience_size_upper_bound": 553393690, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.466Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.466Z"}, {"_id": "67c56feccceafebe2cf7ace7", "interest_id": "6003179515414", "raw_name": "Dance music (music)", "name": "Dance music (music)", "type": "interests", "path": ["Interests", "Entertainment (leisure)", "Music (entertainment & media)"], "description": "", "audience_size_lower_bound": 285610654, "audience_size_upper_bound": 335878130, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.466Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.466Z"}, {"_id": "67c56feccceafebe2cf7ace9", "interest_id": "6003902397066", "raw_name": "Electronic music (music)", "name": "Electronic music (music)", "type": "interests", "path": ["Interests", "Entertainment (leisure)", "Music (entertainment & media)"], "description": "", "audience_size_lower_bound": 763266700, "audience_size_upper_bound": 897601640, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.466Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.466Z"}, {"_id": "67c56feccceafebe2cf7aceb", "interest_id": "6003633122583", "raw_name": "Heavy metal music (music)", "name": "Heavy metal music (music)", "type": "interests", "path": ["Interests", "Entertainment (leisure)", "Music (entertainment & media)"], "description": "", "audience_size_lower_bound": 603639736, "audience_size_upper_bound": 709880330, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.467Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.467Z"}, {"_id": "67c56feccceafebe2cf7aced", "interest_id": "6003225556345", "raw_name": "Hip hop music (music)", "name": "Hip hop music (music)", "type": "interests", "path": ["Interests", "Entertainment (leisure)", "Music (entertainment & media)"], "description": "", "audience_size_lower_bound": 824386198, "audience_size_upper_bound": 969478170, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.467Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.467Z"}, {"_id": "67c56feccceafebe2cf7acef", "interest_id": "6003146442552", "raw_name": "Jazz music (music)", "name": "Jazz music (music)", "type": "interests", "path": ["Interests", "Entertainment (leisure)", "Music (entertainment & media)"], "description": "", "audience_size_lower_bound": 462368469, "audience_size_upper_bound": 543745320, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.467Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.467Z"}, {"_id": "67c56feccceafebe2cf7acf1", "interest_id": "6003332483177", "raw_name": "Music videos (entertainment & media)", "name": "Music videos (entertainment & media)", "type": "interests", "path": ["Interests", "Entertainment (leisure)", "Music (entertainment & media)"], "description": "", "audience_size_lower_bound": 985474268, "audience_size_upper_bound": 1158917740, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.468Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.468Z"}, {"_id": "67c56feccceafebe2cf7acf3", "interest_id": "6003341579196", "raw_name": "Pop music (music)", "name": "Pop music (music)", "type": "interests", "path": ["Interests", "Entertainment (leisure)", "Music (entertainment & media)"], "description": "", "audience_size_lower_bound": 994586870, "audience_size_upper_bound": 1169634160, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.468Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.468Z"}, {"_id": "67c56feccceafebe2cf7acf5", "interest_id": "6003195554098", "raw_name": "Rhythm and blues music (music)", "name": "Rhythm and blues music (music)", "type": "interests", "path": ["Interests", "Entertainment (leisure)", "Music (entertainment & media)"], "description": "", "audience_size_lower_bound": 669180255, "audience_size_upper_bound": 786955980, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.468Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.468Z"}, {"_id": "67c56feccceafebe2cf7acf7", "interest_id": "6003582732907", "raw_name": "Rock music (music)", "name": "Rock music (music)", "type": "interests", "path": ["Interests", "Entertainment (leisure)", "Music (entertainment & media)"], "description": "", "audience_size_lower_bound": 959474906, "audience_size_upper_bound": 1128342490, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.469Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.469Z"}, {"_id": "67c56feccceafebe2cf7acf9", "interest_id": "6003107699532", "raw_name": "Soul music (music)", "name": "Soul music (music)", "type": "interests", "path": ["Interests", "Entertainment (leisure)", "Music (entertainment & media)"], "description": "", "audience_size_lower_bound": 461193307, "audience_size_upper_bound": 542363330, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.469Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.469Z"}, {"_id": "67c56feccceafebe2cf7acfb", "interest_id": "6003462707303", "raw_name": "Books (publications)", "name": "Books (publications)", "type": "interests", "path": ["Interests", "Entertainment (leisure)", "Reading (communication)"], "description": "", "audience_size_lower_bound": 581479795, "audience_size_upper_bound": 683820240, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.469Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.469Z"}, {"_id": "67c56feccceafebe2cf7acfd", "interest_id": "6003126215349", "raw_name": "Comics (comics & cartoons)", "name": "Comics (comics & cartoons)", "type": "interests", "path": ["Interests", "Entertainment (leisure)", "Reading (communication)"], "description": "", "audience_size_lower_bound": 284793789, "audience_size_upper_bound": 334917497, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.47Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.47Z"}, {"_id": "67c56feccceafebe2cf7acff", "interest_id": "6003074487739", "raw_name": "E-books (publications)", "name": "E-books (publications)", "type": "interests", "path": ["Interests", "Entertainment (leisure)", "Reading (communication)"], "description": "", "audience_size_lower_bound": 366680399, "audience_size_upper_bound": 431216150, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.47Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.47Z"}, {"_id": "67c56feccceafebe2cf7ad01", "interest_id": "6003274262708", "raw_name": "Fiction books (publications)", "name": "Fiction books (publications)", "type": "interests", "path": ["Interests", "Entertainment (leisure)", "Reading (communication)"], "description": "", "audience_size_lower_bound": 407642219, "audience_size_upper_bound": 479387250, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.47Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.47Z"}, {"_id": "67c56feccceafebe2cf7ad03", "interest_id": "6003247790075", "raw_name": "Literature (publications)", "name": "Literature (publications)", "type": "interests", "path": ["Interests", "Entertainment (leisure)", "Reading (communication)"], "description": "", "audience_size_lower_bound": 372953690, "audience_size_upper_bound": 438593540, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.471Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.471Z"}, {"_id": "67c56feccceafebe2cf7ad05", "interest_id": "6003206216430", "raw_name": "Magazines (publications)", "name": "Magazines (publications)", "type": "interests", "path": ["Interests", "Entertainment (leisure)", "Reading (communication)"], "description": "", "audience_size_lower_bound": 613485722, "audience_size_upper_bound": 721459210, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.471Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.471Z"}, {"_id": "67c56feccceafebe2cf7ad07", "interest_id": "6003083357650", "raw_name": "<PERSON><PERSON> (anime & manga)", "name": "<PERSON><PERSON> (anime & manga)", "type": "interests", "path": ["Interests", "Entertainment (leisure)", "Reading (communication)"], "description": "", "audience_size_lower_bound": 226028885, "audience_size_upper_bound": 265809969, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.471Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.471Z"}, {"_id": "67c56feccceafebe2cf7ad09", "interest_id": "6002986104968", "raw_name": "Mystery fiction (entertainment & media)", "name": "Mystery fiction (entertainment & media)", "type": "interests", "path": ["Interests", "Entertainment (leisure)", "Reading (communication)"], "description": "", "audience_size_lower_bound": 147119005, "audience_size_upper_bound": 173011950, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.472Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.472Z"}, {"_id": "67c56feccceafebe2cf7ad0b", "interest_id": "6004043913548", "raw_name": "Newspapers (publications)", "name": "Newspapers (publications)", "type": "interests", "path": ["Interests", "Entertainment (leisure)", "Reading (communication)"], "description": "", "audience_size_lower_bound": 815218979, "audience_size_upper_bound": 958697520, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.472Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.472Z"}, {"_id": "67c56feccceafebe2cf7ad0d", "interest_id": "6003420644631", "raw_name": "Non-fiction books (publications)", "name": "Non-fiction books (publications)", "type": "interests", "path": ["Interests", "Entertainment (leisure)", "Reading (communication)"], "description": "", "audience_size_lower_bound": 36691106, "audience_size_upper_bound": 43148741, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.473Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.473Z"}, {"_id": "67c56feccceafebe2cf7ad0f", "interest_id": "6003210799924", "raw_name": "Romance novels (publications)", "name": "Romance novels (publications)", "type": "interests", "path": ["Interests", "Entertainment (leisure)", "Reading (communication)"], "description": "", "audience_size_lower_bound": 229731352, "audience_size_upper_bound": 270164070, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.473Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.473Z"}, {"_id": "67c56feccceafebe2cf7ad11", "interest_id": "6003126358188", "raw_name": "TV game shows (television show)", "name": "TV game shows (television show)", "type": "interests", "path": ["Interests", "Entertainment (leisure)", "TV (movies & television)"], "description": "", "audience_size_lower_bound": 110719600, "audience_size_upper_bound": 130206250, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.473Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.473Z"}, {"_id": "67c56feccceafebe2cf7ad13", "interest_id": "6003268182136", "raw_name": "TV reality shows (movies & television)", "name": "TV reality shows (movies & television)", "type": "interests", "path": ["Interests", "Entertainment (leisure)", "TV (movies & television)"], "description": "", "audience_size_lower_bound": 520518945, "audience_size_upper_bound": 612130280, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.474Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.474Z"}, {"_id": "67c56feccceafebe2cf7ad15", "interest_id": "6003172448161", "raw_name": "TV talkshows (television show)", "name": "TV talkshows (television show)", "type": "interests", "path": ["Interests", "Entertainment (leisure)", "TV (movies & television)"], "description": "", "audience_size_lower_bound": 131425399, "audience_size_upper_bound": 154556270, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.474Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.474Z"}, {"_id": "67c56feccceafebe2cf7ad17", "interest_id": "6003476182657", "raw_name": "Family (social concept)", "name": "Family (social concept)", "type": "interests", "path": ["Interests", "Family and relationships"], "description": "", "audience_size_lower_bound": 1029906930, "audience_size_upper_bound": 1211170550, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.474Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.474Z"}, {"_id": "67c56feccceafebe2cf7ad19", "interest_id": "6003101323797", "raw_name": "Fatherhood (children & parenting)", "name": "Fatherhood (children & parenting)", "type": "interests", "path": ["Interests", "Family and relationships"], "description": "", "audience_size_lower_bound": 352672278, "audience_size_upper_bound": 414742600, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.475Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.475Z"}, {"_id": "67c56feccceafebe2cf7ad1b", "interest_id": "6004100985609", "raw_name": "Friendship (relationships)", "name": "Friendship (relationships)", "type": "interests", "path": ["Interests", "Family and relationships"], "description": "", "audience_size_lower_bound": 716429413, "audience_size_upper_bound": 842520990, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.475Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.475Z"}, {"_id": "67c56feccceafebe2cf7ad1d", "interest_id": "6003445506042", "raw_name": "Marriage (weddings)", "name": "Marriage (weddings)", "type": "interests", "path": ["Interests", "Family and relationships"], "description": "", "audience_size_lower_bound": 233638750, "audience_size_upper_bound": 274759170, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.475Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.475Z"}, {"_id": "67c56feccceafebe2cf7ad1f", "interest_id": "6002991239659", "raw_name": "Motherhood (children & parenting)", "name": "Motherhood (children & parenting)", "type": "interests", "path": ["Interests", "Family and relationships"], "description": "", "audience_size_lower_bound": 698407040, "audience_size_upper_bound": 821326680, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.476Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.476Z"}, {"_id": "67c56feccceafebe2cf7ad21", "interest_id": "6003232518610", "raw_name": "Parenting (children & parenting)", "name": "Parenting (children & parenting)", "type": "interests", "path": ["Interests", "Family and relationships"], "description": "", "audience_size_lower_bound": 284294627, "audience_size_upper_bound": 334330482, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.476Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.476Z"}, {"_id": "67c56feccceafebe2cf7ad23", "interest_id": "6003409392877", "raw_name": "Weddings (weddings)", "name": "Weddings (weddings)", "type": "interests", "path": ["Interests", "Family and relationships"], "description": "", "audience_size_lower_bound": 304885943, "audience_size_upper_bound": 358545870, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.476Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.476Z"}, {"_id": "67c56feccceafebe2cf7ad25", "interest_id": "6003648059946", "raw_name": "Bodybuilding (sport)", "name": "Bodybuilding (sport)", "type": "interests", "path": ["Interests", "Fitness and wellness (fitness)"], "description": "", "audience_size_lower_bound": 196138179, "audience_size_upper_bound": 230658499, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.477Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.477Z"}, {"_id": "67c56feccceafebe2cf7ad27", "interest_id": "6004115167424", "raw_name": "Physical exercise (fitness)", "name": "Physical exercise (fitness)", "type": "interests", "path": ["Interests", "Fitness and wellness (fitness)"], "description": "", "audience_size_lower_bound": 646978494, "audience_size_upper_bound": 760846710, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.477Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.477Z"}, {"_id": "67c56feccceafebe2cf7ad29", "interest_id": "6003277229371", "raw_name": "Physical fitness (fitness)", "name": "Physical fitness (fitness)", "type": "interests", "path": ["Interests", "Fitness and wellness (fitness)"], "description": "", "audience_size_lower_bound": 678866522, "audience_size_upper_bound": 798347030, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.478Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.478Z"}, {"_id": "67c56feccceafebe2cf7ad2b", "interest_id": "6003397496347", "raw_name": "Running (sport)", "name": "Running (sport)", "type": "interests", "path": ["Interests", "Fitness and wellness (fitness)"], "description": "", "audience_size_lower_bound": 295862363, "audience_size_upper_bound": 347934139, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.478Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.478Z"}, {"_id": "67c56feccceafebe2cf7ad2d", "interest_id": "6003473077165", "raw_name": "Weight training (weightlifting)", "name": "Weight training (weightlifting)", "type": "interests", "path": ["Interests", "Fitness and wellness (fitness)"], "description": "", "audience_size_lower_bound": 191719338, "audience_size_upper_bound": 225461942, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.478Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.478Z"}, {"_id": "67c56feccceafebe2cf7ad2f", "interest_id": "6003306084421", "raw_name": "Yoga (fitness)", "name": "Yoga (fitness)", "type": "interests", "path": ["Interests", "Fitness and wellness (fitness)"], "description": "", "audience_size_lower_bound": 382445073, "audience_size_upper_bound": 449755406, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.479Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.479Z"}, {"_id": "67c56feccceafebe2cf7ad31", "interest_id": "6003012461997", "raw_name": "Beer (alcoholic drinks)", "name": "Beer (alcoholic drinks)", "type": "interests", "path": ["Interests", "Food and drink (consumables)", "Alcoholic beverages (food & drink)"], "description": "", "audience_size_lower_bound": 333167653, "audience_size_upper_bound": 391805160, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.479Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.479Z"}, {"_id": "67c56feccceafebe2cf7ad33", "interest_id": "6003146729229", "raw_name": "Distilled beverage (liquor)", "name": "Distilled beverage (liquor)", "type": "interests", "path": ["Interests", "Food and drink (consumables)", "Alcoholic beverages (food & drink)"], "description": "", "audience_size_lower_bound": 204502551, "audience_size_upper_bound": 240495000, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.479Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.479Z"}, {"_id": "67c56feccceafebe2cf7ad35", "interest_id": "6003148544265", "raw_name": "Wine (alcoholic drinks)", "name": "Wine (alcoholic drinks)", "type": "interests", "path": ["Interests", "Food and drink (consumables)", "Alcoholic beverages (food & drink)"], "description": "", "audience_size_lower_bound": 332466045, "audience_size_upper_bound": 390980070, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.479Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.479Z"}, {"_id": "67c56feccceafebe2cf7ad37", "interest_id": "6003626773307", "raw_name": "Coffee (food & drink)", "name": "Coffee (food & drink)", "type": "interests", "path": ["Interests", "Food and drink (consumables)", "Beverages (food & drink)"], "description": "", "audience_size_lower_bound": 535770493, "audience_size_upper_bound": 630066100, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.48Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.48Z"}, {"_id": "67c56feccceafebe2cf7ad39", "interest_id": "6003392512725", "raw_name": "Energy drinks (nonalcoholic beverage)", "name": "Energy drinks (nonalcoholic beverage)", "type": "interests", "path": ["Interests", "Food and drink (consumables)", "Beverages (food & drink)"], "description": "", "audience_size_lower_bound": 97544710, "audience_size_upper_bound": 114712580, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.48Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.48Z"}, {"_id": "67c56feccceafebe2cf7ad3b", "interest_id": "6003703931713", "raw_name": "Juice (nonalcoholic beverage)", "name": "Juice (nonalcoholic beverage)", "type": "interests", "path": ["Interests", "Food and drink (consumables)", "Beverages (food & drink)"], "description": "", "audience_size_lower_bound": 223882925, "audience_size_upper_bound": 263286320, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.481Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.481Z"}, {"_id": "67c56feccceafebe2cf7ad3d", "interest_id": "6002936693259", "raw_name": "Soft drinks (nonalcoholic beverage)", "name": "Soft drinks (nonalcoholic beverage)", "type": "interests", "path": ["Interests", "Food and drink (consumables)", "Beverages (food & drink)"], "description": "", "audience_size_lower_bound": 187387091, "audience_size_upper_bound": 220367220, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.481Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.481Z"}, {"_id": "67c56feccceafebe2cf7ad3f", "interest_id": "6003491283786", "raw_name": "Tea (nonalcoholic beverage)", "name": "Tea (nonalcoholic beverage)", "type": "interests", "path": ["Interests", "Food and drink (consumables)", "Beverages (food & drink)"], "description": "", "audience_size_lower_bound": 395174719, "audience_size_upper_bound": 464725470, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.481Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.481Z"}, {"_id": "67c56feccceafebe2cf7ad41", "interest_id": "6003134986700", "raw_name": "Baking (cooking)", "name": "Baking (cooking)", "type": "interests", "path": ["Interests", "Food and drink (consumables)", "Cooking (food & drink)"], "description": "", "audience_size_lower_bound": 356463045, "audience_size_upper_bound": 419200542, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.482Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.482Z"}, {"_id": "67c56feccceafebe2cf7ad43", "interest_id": "6003385609165", "raw_name": "Recipes (food & drink)", "name": "Recipes (food & drink)", "type": "interests", "path": ["Interests", "Food and drink (consumables)", "Cooking (food & drink)"], "description": "", "audience_size_lower_bound": 480500399, "audience_size_upper_bound": 565068470, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.482Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.482Z"}, {"_id": "67c56feccceafebe2cf7ad45", "interest_id": "6003030029655", "raw_name": "Chinese cuisine (food & drink)", "name": "Chinese cuisine (food & drink)", "type": "interests", "path": ["Interests", "Food and drink (consumables)", "C<PERSON>sine (food & drink)"], "description": "", "audience_size_lower_bound": 152438027, "audience_size_upper_bound": 179267120, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.483Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.483Z"}, {"_id": "67c56feccceafebe2cf7ad47", "interest_id": "6003420024431", "raw_name": "French cuisine (food & drink)", "name": "French cuisine (food & drink)", "type": "interests", "path": ["Interests", "Food and drink (consumables)", "C<PERSON>sine (food & drink)"], "description": "", "audience_size_lower_bound": 89071692, "audience_size_upper_bound": 104748310, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.483Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.483Z"}, {"_id": "67c56feccceafebe2cf7ad49", "interest_id": "6004094205989", "raw_name": "German cuisine (food & drink)", "name": "German cuisine (food & drink)", "type": "interests", "path": ["Interests", "Food and drink (consumables)", "C<PERSON>sine (food & drink)"], "description": "", "audience_size_lower_bound": 29112772, "audience_size_upper_bound": 34236620, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.483Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.483Z"}, {"_id": "67c56feccceafebe2cf7ad4b", "interest_id": "6003306415421", "raw_name": "Greek cuisine (food & drink)", "name": "Greek cuisine (food & drink)", "type": "interests", "path": ["Interests", "Food and drink (consumables)", "C<PERSON>sine (food & drink)"], "description": "", "audience_size_lower_bound": 31516505, "audience_size_upper_bound": 37063410, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.484Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.484Z"}, {"_id": "67c56feccceafebe2cf7ad4d", "interest_id": "6003494675627", "raw_name": "Indian cuisine (food & drink)", "name": "Indian cuisine (food & drink)", "type": "interests", "path": ["Interests", "Food and drink (consumables)", "C<PERSON>sine (food & drink)"], "description": "", "audience_size_lower_bound": 91538323, "audience_size_upper_bound": 107649069, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.484Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.484Z"}, {"_id": "67c56feccceafebe2cf7ad4f", "interest_id": "6003102729234", "raw_name": "Italian cuisine (food & drink)", "name": "Italian cuisine (food & drink)", "type": "interests", "path": ["Interests", "Food and drink (consumables)", "C<PERSON>sine (food & drink)"], "description": "", "audience_size_lower_bound": 146667498, "audience_size_upper_bound": 172480978, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.485Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.485Z"}, {"_id": "67c56feccceafebe2cf7ad51", "interest_id": "6002998123892", "raw_name": "Japanese cuisine (food & drink)", "name": "Japanese cuisine (food & drink)", "type": "interests", "path": ["Interests", "Food and drink (consumables)", "C<PERSON>sine (food & drink)"], "description": "", "audience_size_lower_bound": 135928681, "audience_size_upper_bound": 159852130, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.485Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.485Z"}, {"_id": "67c56feccceafebe2cf7ad53", "interest_id": "6003343485089", "raw_name": "Korean cuisine (food & drink)", "name": "Korean cuisine (food & drink)", "type": "interests", "path": ["Interests", "Food and drink (consumables)", "C<PERSON>sine (food & drink)"], "description": "", "audience_size_lower_bound": 102158375, "audience_size_upper_bound": 120138250, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.485Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.485Z"}, {"_id": "67c56feccceafebe2cf7ad55", "interest_id": "6003102988840", "raw_name": "Latin American cuisine (food & drink)", "name": "Latin American cuisine (food & drink)", "type": "interests", "path": ["Interests", "Food and drink (consumables)", "C<PERSON>sine (food & drink)"], "description": "", "audience_size_lower_bound": 41796581, "audience_size_upper_bound": 49152780, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.486Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.486Z"}, {"_id": "67c56feccceafebe2cf7ad57", "interest_id": "6002964239317", "raw_name": "Mexican cuisine (food & drink)", "name": "Mexican cuisine (food & drink)", "type": "interests", "path": ["Interests", "Food and drink (consumables)", "C<PERSON>sine (food & drink)"], "description": "", "audience_size_lower_bound": 102614047, "audience_size_upper_bound": 120674120, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.487Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.487Z"}, {"_id": "67c56feccceafebe2cf7ad59", "interest_id": "6003200340482", "raw_name": "Middle Eastern cuisine (food & drink)", "name": "Middle Eastern cuisine (food & drink)", "type": "interests", "path": ["Interests", "Food and drink (consumables)", "C<PERSON>sine (food & drink)"], "description": "", "audience_size_lower_bound": 28441849, "audience_size_upper_bound": 33447615, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.487Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.487Z"}, {"_id": "67c56feccceafebe2cf7ad5b", "interest_id": "6003108649035", "raw_name": "Spanish cuisine (food & drink)", "name": "Spanish cuisine (food & drink)", "type": "interests", "path": ["Interests", "Food and drink (consumables)", "C<PERSON>sine (food & drink)"], "description": "", "audience_size_lower_bound": 35717153, "audience_size_upper_bound": 42003372, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.488Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.488Z"}, {"_id": "67c56feccceafebe2cf7ad5d", "interest_id": "6003283801502", "raw_name": "Thai cuisine (food & drink)", "name": "Thai cuisine (food & drink)", "type": "interests", "path": ["Interests", "Food and drink (consumables)", "C<PERSON>sine (food & drink)"], "description": "", "audience_size_lower_bound": 57123875, "audience_size_upper_bound": 67177678, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.488Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.488Z"}, {"_id": "67c56feccceafebe2cf7ad5f", "interest_id": "6003346311730", "raw_name": "Vietnamese cuisine (food & drink)", "name": "Vietnamese cuisine (food & drink)", "type": "interests", "path": ["Interests", "Food and drink (consumables)", "C<PERSON>sine (food & drink)"], "description": "", "audience_size_lower_bound": 48440493, "audience_size_upper_bound": 56966020, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.488Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.488Z"}, {"_id": "67c56feccceafebe2cf7ad61", "interest_id": "6003435096731", "raw_name": "Barbecue (cooking)", "name": "Barbecue (cooking)", "type": "interests", "path": ["Interests", "Food and drink (consumables)", "Food (food & drink)"], "description": "", "audience_size_lower_bound": 348044897, "audience_size_upper_bound": 409300800, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.489Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.489Z"}, {"_id": "67c56feccceafebe2cf7ad63", "interest_id": "6003133978408", "raw_name": "Chocolate (food & drink)", "name": "Chocolate (food & drink)", "type": "interests", "path": ["Interests", "Food and drink (consumables)", "Food (food & drink)"], "description": "", "audience_size_lower_bound": 475445765, "audience_size_upper_bound": 559124220, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.489Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.489Z"}, {"_id": "67c56feccceafebe2cf7ad65", "interest_id": "6003125948045", "raw_name": "Desserts (food & drink)", "name": "Desserts (food & drink)", "type": "interests", "path": ["Interests", "Food and drink (consumables)", "Food (food & drink)"], "description": "", "audience_size_lower_bound": 398663415, "audience_size_upper_bound": 468828177, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.489Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.489Z"}, {"_id": "67c56feccceafebe2cf7ad67", "interest_id": "6004037400009", "raw_name": "Fast food (food & drink)", "name": "Fast food (food & drink)", "type": "interests", "path": ["Interests", "Food and drink (consumables)", "Food (food & drink)"], "description": "", "audience_size_lower_bound": 445818967, "audience_size_upper_bound": 524283106, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.49Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.49Z"}, {"_id": "67c56feccceafebe2cf7ad69", "interest_id": "6002868910910", "raw_name": "Organic food (food & drink)", "name": "Organic food (food & drink)", "type": "interests", "path": ["Interests", "Food and drink (consumables)", "Food (food & drink)"], "description": "", "audience_size_lower_bound": 273132221, "audience_size_upper_bound": 321203492, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.49Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.49Z"}, {"_id": "67c56feccceafebe2cf7ad6b", "interest_id": "6003668857118", "raw_name": "Pizza (food & drink)", "name": "Pizza (food & drink)", "type": "interests", "path": ["Interests", "Food and drink (consumables)", "Food (food & drink)"], "description": "", "audience_size_lower_bound": 441634838, "audience_size_upper_bound": 519362570, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.49Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.49Z"}, {"_id": "67c56feccceafebe2cf7ad6d", "interest_id": "6003240742699", "raw_name": "Seafood (food & drink)", "name": "Seafood (food & drink)", "type": "interests", "path": ["Interests", "Food and drink (consumables)", "Food (food & drink)"], "description": "", "audience_size_lower_bound": 270206906, "audience_size_upper_bound": 317763322, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.491Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.491Z"}, {"_id": "67c56feccceafebe2cf7ad6f", "interest_id": "6003641846820", "raw_name": "Veganism (diets)", "name": "Veganism (diets)", "type": "interests", "path": ["Interests", "Food and drink (consumables)", "Food (food & drink)"], "description": "", "audience_size_lower_bound": 324563265, "audience_size_upper_bound": 381686400, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.491Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.491Z"}, {"_id": "67c56feccceafebe2cf7ad71", "interest_id": "6003155333705", "raw_name": "Vegetarianism (diets)", "name": "Vegetarianism (diets)", "type": "interests", "path": ["Interests", "Food and drink (consumables)", "Food (food & drink)"], "description": "", "audience_size_lower_bound": 207809507, "audience_size_upper_bound": 244383981, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.491Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.491Z"}, {"_id": "67c56feccceafebe2cf7ad73", "interest_id": "6003120620858", "raw_name": "Coffeehouses (coffee)", "name": "Coffeehouses (coffee)", "type": "interests", "path": ["Interests", "Food and drink (consumables)", "Restaurants (dining)"], "description": "", "audience_size_lower_bound": 411936607, "audience_size_upper_bound": 484437450, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.492Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.492Z"}, {"_id": "67c56feccceafebe2cf7ad75", "interest_id": "6003243058188", "raw_name": "Diners (restaurant)", "name": "Diners (restaurant)", "type": "interests", "path": ["Interests", "Food and drink (consumables)", "Restaurants (dining)"], "description": "", "audience_size_lower_bound": 117957833, "audience_size_upper_bound": 138718412, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.492Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.492Z"}, {"_id": "67c56feccceafebe2cf7ad77", "interest_id": "6003398056603", "raw_name": "Fast casual restaurants (restaurant)", "name": "Fast casual restaurants (restaurant)", "type": "interests", "path": ["Interests", "Food and drink (consumables)", "Restaurants (dining)"], "description": "", "audience_size_lower_bound": 127329706, "audience_size_upper_bound": 149739735, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.492Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.492Z"}, {"_id": "67c56feccceafebe2cf7ad79", "interest_id": "6003372667195", "raw_name": "Fast food restaurants (dining)", "name": "Fast food restaurants (dining)", "type": "interests", "path": ["Interests", "Food and drink (consumables)", "Restaurants (dining)"], "description": "", "audience_size_lower_bound": 169866686, "audience_size_upper_bound": 199763223, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.493Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.493Z"}, {"_id": "67c56feccceafebe2cf7ad7b", "interest_id": "6002925538921", "raw_name": "Acting (performing arts)", "name": "Acting (performing arts)", "type": "interests", "path": ["Interests", "Hobbies and activities", "Arts and music (art)"], "description": "", "audience_size_lower_bound": 195934863, "audience_size_upper_bound": 230419400, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.493Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.493Z"}, {"_id": "67c56feccceafebe2cf7ad7d", "interest_id": "6003105618835", "raw_name": "Crafts (hobbies)", "name": "Crafts (hobbies)", "type": "interests", "path": ["Interests", "Hobbies and activities", "Arts and music (art)"], "description": "", "audience_size_lower_bound": 423784727, "audience_size_upper_bound": 498370840, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.493Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.493Z"}, {"_id": "67c56feccceafebe2cf7ad7f", "interest_id": "6003423342191", "raw_name": "Dance (art)", "name": "Dance (art)", "type": "interests", "path": ["Interests", "Hobbies and activities", "Arts and music (art)"], "description": "", "audience_size_lower_bound": 545935068, "audience_size_upper_bound": 642019640, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.494Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.494Z"}, {"_id": "67c56feccceafebe2cf7ad81", "interest_id": "6003780025252", "raw_name": "Drawing (visual art)", "name": "Drawing (visual art)", "type": "interests", "path": ["Interests", "Hobbies and activities", "Arts and music (art)"], "description": "", "audience_size_lower_bound": 198107763, "audience_size_upper_bound": 232974730, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.494Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.494Z"}, {"_id": "67c56feccceafebe2cf7ad83", "interest_id": "6003387633593", "raw_name": "Drums (instruments)", "name": "Drums (instruments)", "type": "interests", "path": ["Interests", "Hobbies and activities", "Arts and music (art)"], "description": "", "audience_size_lower_bound": 116439379, "audience_size_upper_bound": 136932710, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.494Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.494Z"}, {"_id": "67c56feccceafebe2cf7ad85", "interest_id": "6003194056672", "raw_name": "Fine art (visual art)", "name": "Fine art (visual art)", "type": "interests", "path": ["Interests", "Hobbies and activities", "Arts and music (art)"], "description": "", "audience_size_lower_bound": 140074631, "audience_size_upper_bound": 164727767, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.495Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.495Z"}, {"_id": "67c56feccceafebe2cf7ad87", "interest_id": "6003302121228", "raw_name": "Guitar (instruments)", "name": "Guitar (instruments)", "type": "interests", "path": ["Interests", "Hobbies and activities", "Arts and music (art)"], "description": "", "audience_size_lower_bound": 150172134, "audience_size_upper_bound": 176602430, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.495Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.495Z"}, {"_id": "67c56feccceafebe2cf7ad89", "interest_id": "6003142974961", "raw_name": "Painting (visual art)", "name": "Painting (visual art)", "type": "interests", "path": ["Interests", "Hobbies and activities", "Arts and music (art)"], "description": "", "audience_size_lower_bound": 402405442, "audience_size_upper_bound": 473228800, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.495Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.495Z"}, {"_id": "67c56feccceafebe2cf7ad8b", "interest_id": "6003154043305", "raw_name": "Performing arts (performing arts)", "name": "Performing arts (performing arts)", "type": "interests", "path": ["Interests", "Hobbies and activities", "Arts and music (art)"], "description": "", "audience_size_lower_bound": 431488095, "audience_size_upper_bound": 507430000, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.496Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.496Z"}, {"_id": "67c56feccceafebe2cf7ad8d", "interest_id": "6003899195666", "raw_name": "Photography (visual art)", "name": "Photography (visual art)", "type": "interests", "path": ["Interests", "Hobbies and activities", "Arts and music (art)"], "description": "", "audience_size_lower_bound": 1117351836, "audience_size_upper_bound": 1314005760, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.496Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.496Z"}, {"_id": "67c56feccceafebe2cf7ad8f", "interest_id": "6003717247746", "raw_name": "Sculpture (art)", "name": "Sculpture (art)", "type": "interests", "path": ["Interests", "Hobbies and activities", "Arts and music (art)"], "description": "", "audience_size_lower_bound": 132166649, "audience_size_upper_bound": 155427980, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.496Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.496Z"}, {"_id": "67c56feccceafebe2cf7ad91", "interest_id": "6002997799844", "raw_name": "Singing (music)", "name": "Singing (music)", "type": "interests", "path": ["Interests", "Hobbies and activities", "Arts and music (art)"], "description": "", "audience_size_lower_bound": 425761938, "audience_size_upper_bound": 500696040, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.497Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.497Z"}, {"_id": "67c56feccceafebe2cf7ad93", "interest_id": "6003586608473", "raw_name": "Writing (communication)", "name": "Writing (communication)", "type": "interests", "path": ["Interests", "Hobbies and activities", "Arts and music (art)"], "description": "", "audience_size_lower_bound": 341526028, "audience_size_upper_bound": 401634610, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.497Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.497Z"}, {"_id": "67c56feccceafebe2cf7ad95", "interest_id": "6003290811111", "raw_name": "Current events (politics)", "name": "Current events (politics)", "type": "interests", "path": ["Interests", "Hobbies and activities"], "description": "", "audience_size_lower_bound": 879551573, "audience_size_upper_bound": 1034352650, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.497Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.497Z"}, {"_id": "67c56feccceafebe2cf7ad97", "interest_id": "6003470511564", "raw_name": "Do it yourself (DIY)", "name": "Do it yourself (DIY)", "type": "interests", "path": ["Interests", "Hobbies and activities", "Home and garden"], "description": "", "audience_size_lower_bound": 418387661, "audience_size_upper_bound": 492023890, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.498Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.498Z"}, {"_id": "67c56feccceafebe2cf7ad99", "interest_id": "6003132926214", "raw_name": "Furniture (home furnishings)", "name": "Furniture (home furnishings)", "type": "interests", "path": ["Interests", "Hobbies and activities", "Home and garden"], "description": "", "audience_size_lower_bound": 522148358, "audience_size_upper_bound": 614046470, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.498Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.498Z"}, {"_id": "67c56feccceafebe2cf7ad9b", "interest_id": "6003053056644", "raw_name": "Gardening (outdoor activities)", "name": "Gardening (outdoor activities)", "type": "interests", "path": ["Interests", "Hobbies and activities", "Home and garden"], "description": "", "audience_size_lower_bound": 356451726, "audience_size_upper_bound": 419187230, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.498Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.498Z"}, {"_id": "67c56feccceafebe2cf7ad9d", "interest_id": "6003343997689", "raw_name": "Home Appliances (consumer electronics)", "name": "Home Appliances (consumer electronics)", "type": "interests", "path": ["Interests", "Hobbies and activities", "Home and garden"], "description": "", "audience_size_lower_bound": 265301547, "audience_size_upper_bound": 311994620, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.499Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.499Z"}, {"_id": "67c56feccceafebe2cf7ad9f", "interest_id": "6003234413249", "raw_name": "Home improvement (home & garden)", "name": "Home improvement (home & garden)", "type": "interests", "path": ["Interests", "Hobbies and activities", "Home and garden"], "description": "", "audience_size_lower_bound": 335748624, "audience_size_upper_bound": 394840382, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.499Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.499Z"}, {"_id": "67c56feccceafebe2cf7ada1", "interest_id": "6003286289697", "raw_name": "Birds (animals)", "name": "Birds (animals)", "type": "interests", "path": ["Interests", "Hobbies and activities", "Pets (animals)"], "description": "", "audience_size_lower_bound": 353079693, "audience_size_upper_bound": 415221720, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.5Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.5Z"}, {"_id": "67c56feccceafebe2cf7ada3", "interest_id": "6003159378782", "raw_name": "Cats (animals)", "name": "Cats (animals)", "type": "interests", "path": ["Interests", "Hobbies and activities", "Pets (animals)"], "description": "", "audience_size_lower_bound": 448420365, "audience_size_upper_bound": 527342350, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.5Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.5Z"}, {"_id": "67c56feccceafebe2cf7ada5", "interest_id": "6003332344237", "raw_name": "Dogs (animals)", "name": "Dogs (animals)", "type": "interests", "path": ["Interests", "Hobbies and activities", "Pets (animals)"], "description": "", "audience_size_lower_bound": 491744251, "audience_size_upper_bound": 578291240, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.5Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.5Z"}, {"_id": "67c56feccceafebe2cf7ada7", "interest_id": "6003159413034", "raw_name": "Fish (animals)", "name": "Fish (animals)", "type": "interests", "path": ["Interests", "Hobbies and activities", "Pets (animals)"], "description": "", "audience_size_lower_bound": 321510238, "audience_size_upper_bound": 378096040, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.501Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.501Z"}, {"_id": "67c56feccceafebe2cf7ada9", "interest_id": "6003416777039", "raw_name": "Horses (animals)", "name": "Horses (animals)", "type": "interests", "path": ["Interests", "Hobbies and activities", "Pets (animals)"], "description": "", "audience_size_lower_bound": 258434243, "audience_size_upper_bound": 303918670, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.501Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.501Z"}, {"_id": "67c56feccceafebe2cf7adab", "interest_id": "6003461162225", "raw_name": "Pet food (pet supplies)", "name": "Pet food (pet supplies)", "type": "interests", "path": ["Interests", "Hobbies and activities", "Pets (animals)"], "description": "", "audience_size_lower_bound": 108416370, "audience_size_upper_bound": 127497652, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.501Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.501Z"}, {"_id": "67c56feccceafebe2cf7adad", "interest_id": "6003108411433", "raw_name": "Rabbits (animals)", "name": "Rabbits (animals)", "type": "interests", "path": ["Interests", "Hobbies and activities", "Pets (animals)"], "description": "", "audience_size_lower_bound": 114737916, "audience_size_upper_bound": 134931790, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.502Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.502Z"}, {"_id": "67c56feccceafebe2cf7adaf", "interest_id": "6003382151137", "raw_name": "Reptiles (animals)", "name": "Reptiles (animals)", "type": "interests", "path": ["Interests", "Hobbies and activities", "Pets (animals)"], "description": "", "audience_size_lower_bound": 49090909, "audience_size_upper_bound": 57730910, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.502Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.502Z"}, {"_id": "67c56feccceafebe2cf7adb1", "interest_id": "6003422719241", "raw_name": "Charity and causes (social causes)", "name": "Charity and causes (social causes)", "type": "interests", "path": ["Interests", "Hobbies and activities", "Politics and social issues (politics)"], "description": "", "audience_size_lower_bound": 64685429, "audience_size_upper_bound": 76070065, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.503Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.503Z"}, {"_id": "67c56feccceafebe2cf7adb3", "interest_id": "6003049202156", "raw_name": "Community issues (law & government)", "name": "Community issues (law & government)", "type": "interests", "path": ["Interests", "Hobbies and activities", "Politics and social issues (politics)"], "description": "", "audience_size_lower_bound": 236059226, "audience_size_upper_bound": 277605650, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.503Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.503Z"}, {"_id": "67c56feccceafebe2cf7adb5", "interest_id": "6003703762913", "raw_name": "Law (law & legal services)", "name": "Law (law & legal services)", "type": "interests", "path": ["Interests", "Hobbies and activities", "Politics and social issues (politics)"], "description": "", "audience_size_lower_bound": 477592806, "audience_size_upper_bound": 561649140, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.503Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.503Z"}, {"_id": "67c56feccceafebe2cf7adb7", "interest_id": "6003654559478", "raw_name": "Politics (politics)", "name": "Politics (politics)", "type": "interests", "path": ["Interests", "Hobbies and activities", "Politics and social issues (politics)"], "description": "", "audience_size_lower_bound": 460704668, "audience_size_upper_bound": 541788690, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.504Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.504Z"}, {"_id": "67c56feccceafebe2cf7adb9", "interest_id": "6003137105590", "raw_name": "Volunteering (social causes)", "name": "Volunteering (social causes)", "type": "interests", "path": ["Interests", "Hobbies and activities", "Politics and social issues (politics)"], "description": "", "audience_size_lower_bound": 76758137, "audience_size_upper_bound": 90267570, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.504Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.504Z"}, {"_id": "67c56feccceafebe2cf7adbb", "interest_id": "6002868021822", "raw_name": "Adventure travel (travel & tourism)", "name": "Adventure travel (travel & tourism)", "type": "interests", "path": ["Interests", "Hobbies and activities", "Travel (travel & tourism)"], "description": "", "audience_size_lower_bound": *********, "audience_size_upper_bound": *********, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.504Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.504Z"}, {"_id": "67c56feccceafebe2cf7adbd", "interest_id": "6003211401886", "raw_name": "Air travel (transportation)", "name": "Air travel (transportation)", "type": "interests", "path": ["Interests", "Hobbies and activities", "Travel (travel & tourism)"], "description": "", "audience_size_lower_bound": *********, "audience_size_upper_bound": *********, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.505Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.505Z"}, {"_id": "67c56feccceafebe2cf7adbf", "interest_id": "6003431201869", "raw_name": "Beaches (places)", "name": "Beaches (places)", "type": "interests", "path": ["Interests", "Hobbies and activities", "Travel (travel & tourism)"], "description": "", "audience_size_lower_bound": *********, "audience_size_upper_bound": *********, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.505Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.505Z"}, {"_id": "67c56feccceafebe2cf7adc1", "interest_id": "6003090714101", "raw_name": "Car rentals (transportation)", "name": "Car rentals (transportation)", "type": "interests", "path": ["Interests", "Hobbies and activities", "Travel (travel & tourism)"], "description": "", "audience_size_lower_bound": *********, "audience_size_upper_bound": *********, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.506Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.506Z"}, {"_id": "67c56feccceafebe2cf7adc3", "interest_id": "6003225930699", "raw_name": "Cruises (travel & tourism business)", "name": "Cruises (travel & tourism business)", "type": "interests", "path": ["Interests", "Hobbies and activities", "Travel (travel & tourism)"], "description": "", "audience_size_lower_bound": *********, "audience_size_upper_bound": *********, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.506Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.506Z"}, {"_id": "67c56feccceafebe2cf7adc5", "interest_id": "6003059385128", "raw_name": "Ecotourism (travel & tourism)", "name": "Ecotourism (travel & tourism)", "type": "interests", "path": ["Interests", "Hobbies and activities", "Travel (travel & tourism)"], "description": "", "audience_size_lower_bound": *********, "audience_size_upper_bound": *********, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.506Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.506Z"}, {"_id": "67c56feccceafebe2cf7adc7", "interest_id": "6003572379887", "raw_name": "Hotels (lodging)", "name": "Hotels (lodging)", "type": "interests", "path": ["Interests", "Hobbies and activities", "Travel (travel & tourism)"], "description": "", "audience_size_lower_bound": *********, "audience_size_upper_bound": *********, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.507Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.507Z"}, {"_id": "67c56feccceafebe2cf7adc9", "interest_id": "6003430600057", "raw_name": "Lakes (body of water)", "name": "Lakes (body of water)", "type": "interests", "path": ["Interests", "Hobbies and activities", "Travel (travel & tourism)"], "description": "", "audience_size_lower_bound": *********, "audience_size_upper_bound": *********, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.507Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.507Z"}, {"_id": "67c56feccceafebe2cf7adcb", "interest_id": "6003064649070", "raw_name": "Mountains (places)", "name": "Mountains (places)", "type": "interests", "path": ["Interests", "Hobbies and activities", "Travel (travel & tourism)"], "description": "", "audience_size_lower_bound": *********, "audience_size_upper_bound": *********, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.507Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.507Z"}, {"_id": "67c56feccceafebe2cf7adcd", "interest_id": "6003359996821", "raw_name": "Nature (science)", "name": "Nature (science)", "type": "interests", "path": ["Interests", "Hobbies and activities", "Travel (travel & tourism)"], "description": "", "audience_size_lower_bound": *********, "audience_size_upper_bound": *********, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.508Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.508Z"}, {"_id": "67c56feccceafebe2cf7adcf", "interest_id": "6003902462066", "raw_name": "Theme parks (leisure)", "name": "Theme parks (leisure)", "type": "interests", "path": ["Interests", "Hobbies and activities", "Travel (travel & tourism)"], "description": "", "audience_size_lower_bound": *********, "audience_size_upper_bound": *********, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.508Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.508Z"}, {"_id": "67c56feccceafebe2cf7add1", "interest_id": "6003430696269", "raw_name": "Tourism (industry)", "name": "Tourism (industry)", "type": "interests", "path": ["Interests", "Hobbies and activities", "Travel (travel & tourism)"], "description": "", "audience_size_lower_bound": *********, "audience_size_upper_bound": *********, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.509Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.509Z"}, {"_id": "67c56feccceafebe2cf7add3", "interest_id": "6002926108721", "raw_name": "Vacations (social concept)", "name": "Vacations (social concept)", "type": "interests", "path": ["Interests", "Hobbies and activities", "Travel (travel & tourism)"], "description": "", "audience_size_lower_bound": *********, "audience_size_upper_bound": *********, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.509Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.509Z"}, {"_id": "67c56feccceafebe2cf7add5", "interest_id": "6003176678152", "raw_name": "Automobiles (vehicles)", "name": "Automobiles (vehicles)", "type": "interests", "path": ["Interests", "Hobbies and activities", "Vehicles (transportation)"], "description": "", "audience_size_lower_bound": *********, "audience_size_upper_bound": *********, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.509Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.509Z"}, {"_id": "67c56feccceafebe2cf7add7", "interest_id": "6004037107009", "raw_name": "Boats (watercraft)", "name": "Boats (watercraft)", "type": "interests", "path": ["Interests", "Hobbies and activities", "Vehicles (transportation)"], "description": "", "audience_size_lower_bound": 145653103, "audience_size_upper_bound": 171288050, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.51Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.51Z"}, {"_id": "67c56feccceafebe2cf7add9", "interest_id": "6003125064949", "raw_name": "Electric vehicle (vehicle)", "name": "Electric vehicle (vehicle)", "type": "interests", "path": ["Interests", "Hobbies and activities", "Vehicles (transportation)"], "description": "", "audience_size_lower_bound": 98080261, "audience_size_upper_bound": 115342387, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.51Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.51Z"}, {"_id": "67c56feccceafebe2cf7addb", "interest_id": "6003717914546", "raw_name": "Hybrids (vehicle)", "name": "Hybrids (vehicle)", "type": "interests", "path": ["Interests", "Hobbies and activities", "Vehicles (transportation)"], "description": "", "audience_size_lower_bound": 63554330, "audience_size_upper_bound": 74739893, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.51Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.51Z"}, {"_id": "67c56feccceafebe2cf7addd", "interest_id": "6003207605030", "raw_name": "Minivans (vehicle)", "name": "Minivans (vehicle)", "type": "interests", "path": ["Interests", "Hobbies and activities", "Vehicles (transportation)"], "description": "", "audience_size_lower_bound": 48699184, "audience_size_upper_bound": 57270241, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.511Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.511Z"}, {"_id": "67c56feccceafebe2cf7addf", "interest_id": "6003353550130", "raw_name": "Motorcycles (vehicles)", "name": "Motorcycles (vehicles)", "type": "interests", "path": ["Interests", "Hobbies and activities", "Vehicles (transportation)"], "description": "", "audience_size_lower_bound": 415968877, "audience_size_upper_bound": 489179400, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.511Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.511Z"}, {"_id": "67c56feccceafebe2cf7ade1", "interest_id": "6003394580331", "raw_name": "RVs (vehicle)", "name": "RVs (vehicle)", "type": "interests", "path": ["Interests", "Hobbies and activities", "Vehicles (transportation)"], "description": "", "audience_size_lower_bound": 53278409, "audience_size_upper_bound": 62655410, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.512Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.512Z"}, {"_id": "67c56feccceafebe2cf7ade3", "interest_id": "6003304473660", "raw_name": "SUVs (vehicles)", "name": "SUVs (vehicles)", "type": "interests", "path": ["Interests", "Hobbies and activities", "Vehicles (transportation)"], "description": "", "audience_size_lower_bound": 232294159, "audience_size_upper_bound": 273177931, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.512Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.512Z"}, {"_id": "67c56feccceafebe2cf7ade5", "interest_id": "6003446055283", "raw_name": "Scooters (vehicle)", "name": "Scooters (vehicle)", "type": "interests", "path": ["Interests", "Hobbies and activities", "Vehicles (transportation)"], "description": "", "audience_size_lower_bound": 116142112, "audience_size_upper_bound": 136583124, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.512Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.512Z"}, {"_id": "67c56feccceafebe2cf7ade7", "interest_id": "6003092882217", "raw_name": "Trucks (vehicles)", "name": "Trucks (vehicles)", "type": "interests", "path": ["Interests", "Hobbies and activities", "Vehicles (transportation)"], "description": "", "audience_size_lower_bound": 272359982, "audience_size_upper_bound": 320295340, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.513Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.513Z"}, {"_id": "67c56feccceafebe2cf7ade9", "interest_id": "6003088846792", "raw_name": "Beauty salons (cosmetics)", "name": "Beauty salons (cosmetics)", "type": "interests", "path": ["Interests", "Shopping and fashion", "Beauty (social concept)"], "description": "", "audience_size_lower_bound": 618195365, "audience_size_upper_bound": 726997750, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.514Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.514Z"}, {"_id": "67c56feccceafebe2cf7adeb", "interest_id": "6002839660079", "raw_name": "Cosmetics (personal care)", "name": "Cosmetics (personal care)", "type": "interests", "path": ["Interests", "Shopping and fashion", "Beauty (social concept)"], "description": "", "audience_size_lower_bound": 954268571, "audience_size_upper_bound": 1122219840, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.515Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.515Z"}, {"_id": "67c56feccceafebe2cf7aded", "interest_id": "6003443805331", "raw_name": "Fragrances (cosmetics)", "name": "Fragrances (cosmetics)", "type": "interests", "path": ["Interests", "Shopping and fashion", "Beauty (social concept)"], "description": "", "audience_size_lower_bound": 557077516, "audience_size_upper_bound": 655123159, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.515Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.515Z"}, {"_id": "67c56feccceafebe2cf7adef", "interest_id": "6003456330903", "raw_name": "Hair products (hair care)", "name": "Hair products (hair care)", "type": "interests", "path": ["Interests", "Shopping and fashion", "Beauty (social concept)"], "description": "", "audience_size_lower_bound": 725744795, "audience_size_upper_bound": 853475880, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.515Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.515Z"}, {"_id": "67c56feccceafebe2cf7adf1", "interest_id": "6003254590688", "raw_name": "Spas (personal care)", "name": "Spas (personal care)", "type": "interests", "path": ["Interests", "Shopping and fashion", "Beauty (social concept)"], "description": "", "audience_size_lower_bound": 588815610, "audience_size_upper_bound": 692447158, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.516Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.516Z"}, {"_id": "67c56feccceafebe2cf7adf3", "interest_id": "6003025268985", "raw_name": "Tattoos (body art)", "name": "Tattoos (body art)", "type": "interests", "path": ["Interests", "Shopping and fashion", "Beauty (social concept)"], "description": "", "audience_size_lower_bound": 495843528, "audience_size_upper_bound": 583111990, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.516Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.516Z"}, {"_id": "67c56feccceafebe2cf7adf5", "interest_id": "6003415393053", "raw_name": "Children's clothing (apparel)", "name": "Children's clothing (apparel)", "type": "interests", "path": ["Interests", "Shopping and fashion", "Clothing (apparel)"], "description": "", "audience_size_lower_bound": 261869502, "audience_size_upper_bound": 307958535, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.517Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.517Z"}, {"_id": "67c56feccceafebe2cf7adf7", "interest_id": "6011994253127", "raw_name": "Men's clothing (apparel)", "name": "Men's clothing (apparel)", "type": "interests", "path": ["Interests", "Shopping and fashion", "Clothing (apparel)"], "description": "", "audience_size_lower_bound": 455533027, "audience_size_upper_bound": 535706840, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.517Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.517Z"}, {"_id": "67c56feccceafebe2cf7adf9", "interest_id": "6003348453981", "raw_name": "Shoes (footwear)", "name": "Shoes (footwear)", "type": "interests", "path": ["Interests", "Shopping and fashion", "Clothing (apparel)"], "description": "", "audience_size_lower_bound": 841455391, "audience_size_upper_bound": 989551540, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.517Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.517Z"}, {"_id": "67c56feccceafebe2cf7adfb", "interest_id": "6011366104268", "raw_name": "Women's clothing (apparel)", "name": "Women's clothing (apparel)", "type": "interests", "path": ["Interests", "Shopping and fashion", "Clothing (apparel)"], "description": "", "audience_size_lower_bound": 598187925, "audience_size_upper_bound": 703469000, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.518Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.518Z"}, {"_id": "67c56feccceafebe2cf7adfd", "interest_id": "6003188355978", "raw_name": "Dresses (apparel)", "name": "Dresses (apparel)", "type": "interests", "path": ["Interests", "Shopping and fashion", "Fashion accessories (accessories)"], "description": "", "audience_size_lower_bound": 573380000, "audience_size_upper_bound": 674294881, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.518Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.518Z"}, {"_id": "67c56feccceafebe2cf7adff", "interest_id": "6003198476967", "raw_name": "Handbags (accessories)", "name": "Handbags (accessories)", "type": "interests", "path": ["Interests", "Shopping and fashion", "Fashion accessories (accessories)"], "description": "", "audience_size_lower_bound": 413353863, "audience_size_upper_bound": 486104143, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.519Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.519Z"}, {"_id": "67c56feccceafebe2cf7ae01", "interest_id": "6003266225248", "raw_name": "Jewelry (apparel)", "name": "Jewelry (apparel)", "type": "interests", "path": ["Interests", "Shopping and fashion", "Fashion accessories (accessories)"], "description": "", "audience_size_lower_bound": 721781530, "audience_size_upper_bound": 848815080, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.519Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.519Z"}, {"_id": "67c56feccceafebe2cf7ae03", "interest_id": "6003255640088", "raw_name": "Sunglasses (eyewear)", "name": "Sunglasses (eyewear)", "type": "interests", "path": ["Interests", "Shopping and fashion", "Fashion accessories (accessories)"], "description": "", "audience_size_lower_bound": 370515170, "audience_size_upper_bound": 435725841, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.519Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.519Z"}, {"_id": "67c56feccceafebe2cf7ae05", "interest_id": "6003103108917", "raw_name": "Boutiques (retailers)", "name": "Boutiques (retailers)", "type": "interests", "path": ["Interests", "Shopping and fashion", "Shopping (retail)"], "description": "", "audience_size_lower_bound": 532794960, "audience_size_upper_bound": 626566874, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.52Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.52Z"}, {"_id": "67c56feccceafebe2cf7ae07", "interest_id": "6003054884732", "raw_name": "Coupons (coupons & discounts)", "name": "Coupons (coupons & discounts)", "type": "interests", "path": ["Interests", "Shopping and fashion", "Shopping (retail)"], "description": "", "audience_size_lower_bound": 577801581, "audience_size_upper_bound": 679494660, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.52Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.52Z"}, {"_id": "67c56feccceafebe2cf7ae09", "interest_id": "6003220634758", "raw_name": "Discount stores (retail)", "name": "Discount stores (retail)", "type": "interests", "path": ["Interests", "Shopping and fashion", "Shopping (retail)"], "description": "", "audience_size_lower_bound": 393583676, "audience_size_upper_bound": 462854404, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.52Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.52Z"}, {"_id": "67c56feccceafebe2cf7ae0b", "interest_id": "6007828099136", "raw_name": "Luxury goods (retail)", "name": "Luxury goods (retail)", "type": "interests", "path": ["Interests", "Shopping and fashion", "Shopping (retail)"], "description": "", "audience_size_lower_bound": 673653435, "audience_size_upper_bound": 792216440, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.521Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.521Z"}, {"_id": "67c56feccceafebe2cf7ae0d", "interest_id": "6003346592981", "raw_name": "Online shopping (retail)", "name": "Online shopping (retail)", "type": "interests", "path": ["Interests", "Shopping and fashion", "Shopping (retail)"], "description": "", "audience_size_lower_bound": 1347410025, "audience_size_upper_bound": 1584554190, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.521Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.521Z"}, {"_id": "67c56feccceafebe2cf7ae0f", "interest_id": "6003390752144", "raw_name": "Shopping malls (retail)", "name": "Shopping malls (retail)", "type": "interests", "path": ["Interests", "Shopping and fashion", "Shopping (retail)"], "description": "", "audience_size_lower_bound": 588304336, "audience_size_upper_bound": 691845900, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.522Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.522Z"}, {"_id": "67c56feccceafebe2cf7ae11", "interest_id": "6003070122382", "raw_name": "Toys (toys)", "name": "Toys (toys)", "type": "interests", "path": ["Interests", "Shopping and fashion"], "description": "", "audience_size_lower_bound": 480501513, "audience_size_upper_bound": 565069780, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.522Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.522Z"}, {"_id": "67c56feccceafebe2cf7ae13", "interest_id": "6003122958658", "raw_name": "Boating (outdoors activities)", "name": "Boating (outdoors activities)", "type": "interests", "path": ["Interests", "Sports and outdoors", "Outdoor recreation (outdoors activities)"], "description": "", "audience_size_lower_bound": 76591163, "audience_size_upper_bound": 90071208, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.522Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.522Z"}, {"_id": "67c56feccceafebe2cf7ae15", "interest_id": "6003348662930", "raw_name": "Camping (outdoors activities)", "name": "Camping (outdoors activities)", "type": "interests", "path": ["Interests", "Sports and outdoors", "Outdoor recreation (outdoors activities)"], "description": "", "audience_size_lower_bound": 240108356, "audience_size_upper_bound": 282367427, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.523Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.523Z"}, {"_id": "67c56feccceafebe2cf7ae17", "interest_id": "6002979499920", "raw_name": "Fishing (outdoors activities)", "name": "Fishing (outdoors activities)", "type": "interests", "path": ["Interests", "Sports and outdoors", "Outdoor recreation (outdoors activities)"], "description": "", "audience_size_lower_bound": 278638503, "audience_size_upper_bound": 327678880, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.523Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.523Z"}, {"_id": "67c56feccceafebe2cf7ae19", "interest_id": "6003779859852", "raw_name": "Horseback riding (horse sport)", "name": "Horseback riding (horse sport)", "type": "interests", "path": ["Interests", "Sports and outdoors", "Outdoor recreation (outdoors activities)"], "description": "", "audience_size_lower_bound": 102889171, "audience_size_upper_bound": 120997666, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.524Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.524Z"}, {"_id": "67c56feccceafebe2cf7ae1b", "interest_id": "6003106813190", "raw_name": "Hunting (sport)", "name": "Hunting (sport)", "type": "interests", "path": ["Interests", "Sports and outdoors", "Outdoor recreation (outdoors activities)"], "description": "", "audience_size_lower_bound": 201917244, "audience_size_upper_bound": 237454680, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.524Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.524Z"}, {"_id": "67c56feccceafebe2cf7ae1d", "interest_id": "6003092330156", "raw_name": "Mountain biking (cycling)", "name": "Mountain biking (cycling)", "type": "interests", "path": ["Interests", "Sports and outdoors", "Outdoor recreation (outdoors activities)"], "description": "", "audience_size_lower_bound": 94696265, "audience_size_upper_bound": 111362808, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.524Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.524Z"}, {"_id": "67c56feccceafebe2cf7ae1f", "interest_id": "6002984573619", "raw_name": "Surfing (water sport)", "name": "Surfing (water sport)", "type": "interests", "path": ["Interests", "Sports and outdoors", "Outdoor recreation (outdoors activities)"], "description": "", "audience_size_lower_bound": 126273336, "audience_size_upper_bound": 148497444, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.525Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.525Z"}, {"_id": "67c56feccceafebe2cf7ae21", "interest_id": "6003376089674", "raw_name": "American football (sport)", "name": "American football (sport)", "type": "interests", "path": ["Interests", "Sports and outdoors", "Sports (sports)"], "description": "", "audience_size_lower_bound": 417768477, "audience_size_upper_bound": 491295730, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.525Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.525Z"}, {"_id": "67c56feccceafebe2cf7ae23", "interest_id": "6003107902433", "raw_name": "Association football (Soccer)", "name": "Association football (Soccer)", "type": "interests", "path": ["Interests", "Sports and outdoors", "Sports (sports)"], "description": "", "audience_size_lower_bound": 1239072202, "audience_size_upper_bound": 1457148910, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.526Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.526Z"}, {"_id": "67c56feccceafebe2cf7ae25", "interest_id": "6003146718552", "raw_name": "Auto racing (motor sports)", "name": "Auto racing (motor sports)", "type": "interests", "path": ["Interests", "Sports and outdoors", "Sports (sports)"], "description": "", "audience_size_lower_bound": 316189499, "audience_size_upper_bound": 371838851, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.526Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.526Z"}, {"_id": "67c56feccceafebe2cf7ae27", "interest_id": "6003087413192", "raw_name": "Baseball (sport)", "name": "Baseball (sport)", "type": "interests", "path": ["Interests", "Sports and outdoors", "Sports (sports)"], "description": "", "audience_size_lower_bound": 450915484, "audience_size_upper_bound": 530276610, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.527Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.527Z"}, {"_id": "67c56feccceafebe2cf7ae29", "interest_id": "6003369240775", "raw_name": "Basketball (sport)", "name": "Basketball (sport)", "type": "interests", "path": ["Interests", "Sports and outdoors", "Sports (sports)"], "description": "", "audience_size_lower_bound": 714404481, "audience_size_upper_bound": 840139670, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.527Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.527Z"}, {"_id": "67c56feccceafebe2cf7ae2b", "interest_id": "6003162931434", "raw_name": "College football (college sports)", "name": "College football (college sports)", "type": "interests", "path": ["Interests", "Sports and outdoors", "Sports (sports)"], "description": "", "audience_size_lower_bound": 106642491, "audience_size_upper_bound": 125411570, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.528Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.528Z"}, {"_id": "67c56feccceafebe2cf7ae2d", "interest_id": "6003510075864", "raw_name": "Golf (sport)", "name": "Golf (sport)", "type": "interests", "path": ["Interests", "Sports and outdoors", "Sports (sports)"], "description": "", "audience_size_lower_bound": 264069122, "audience_size_upper_bound": 310545288, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.529Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.529Z"}, {"_id": "67c56feccceafebe2cf7ae2f", "interest_id": "6003424404140", "raw_name": "Marathons (running event)", "name": "Marathons (running event)", "type": "interests", "path": ["Interests", "Sports and outdoors", "Sports (sports)"], "description": "", "audience_size_lower_bound": 189882514, "audience_size_upper_bound": 223301837, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.53Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.53Z"}, {"_id": "67c56feccceafebe2cf7ae31", "interest_id": "6003324287371", "raw_name": "Skiing (skiing & snowboarding)", "name": "Skiing (skiing & snowboarding)", "type": "interests", "path": ["Interests", "Sports and outdoors", "Sports (sports)"], "description": "", "audience_size_lower_bound": 141235696, "audience_size_upper_bound": 166093179, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.53Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.53Z"}, {"_id": "67c56feccceafebe2cf7ae33", "interest_id": "6003512053894", "raw_name": "Snowboarding (skiing & snowboarding)", "name": "Snowboarding (skiing & snowboarding)", "type": "interests", "path": ["Interests", "Sports and outdoors", "Sports (sports)"], "description": "", "audience_size_lower_bound": 109337720, "audience_size_upper_bound": 128581159, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.53Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.53Z"}, {"_id": "67c56feccceafebe2cf7ae35", "interest_id": "6003166397215", "raw_name": "Swimming (water sport)", "name": "Swimming (water sport)", "type": "interests", "path": ["Interests", "Sports and outdoors", "Sports (sports)"], "description": "", "audience_size_lower_bound": 228292881, "audience_size_upper_bound": 268472429, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.531Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.531Z"}, {"_id": "67c56feccceafebe2cf7ae37", "interest_id": "6003397425735", "raw_name": "Tennis (sport)", "name": "Tennis (sport)", "type": "interests", "path": ["Interests", "Sports and outdoors", "Sports (sports)"], "description": "", "audience_size_lower_bound": 329849081, "audience_size_upper_bound": 387902520, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.531Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.531Z"}, {"_id": "67c56feccceafebe2cf7ae39", "interest_id": "6003351764757", "raw_name": "Triathlons (athletics)", "name": "Triathlons (athletics)", "type": "interests", "path": ["Interests", "Sports and outdoors", "Sports (sports)"], "description": "", "audience_size_lower_bound": 95150246, "audience_size_upper_bound": 111896690, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.531Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.531Z"}, {"_id": "67c56feccceafebe2cf7ae3b", "interest_id": "6002929380259", "raw_name": "Volleyball (sport)", "name": "Volleyball (sport)", "type": "interests", "path": ["Interests", "Sports and outdoors", "Sports (sports)"], "description": "", "audience_size_lower_bound": 342179787, "audience_size_upper_bound": 402403430, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.532Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.532Z"}, {"_id": "67c56feccceafebe2cf7ae3d", "interest_id": "6003349175527", "raw_name": "Computer memory (computer hardware)", "name": "Computer memory (computer hardware)", "type": "interests", "path": ["Interests", "Technology (computers & electronics)", "Computers (computers & electronics)"], "description": "", "audience_size_lower_bound": 34725485, "audience_size_upper_bound": 40837171, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.532Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.532Z"}, {"_id": "67c56feccceafebe2cf7ae3f", "interest_id": "6003116038942", "raw_name": "Computer monitors (computer hardware)", "name": "Computer monitors (computer hardware)", "type": "interests", "path": ["Interests", "Technology (computers & electronics)", "Computers (computers & electronics)"], "description": "", "audience_size_lower_bound": 145069931, "audience_size_upper_bound": 170602240, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.533Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.533Z"}, {"_id": "67c56feccceafebe2cf7ae41", "interest_id": "6003142705949", "raw_name": "Computer processors (computer hardware)", "name": "Computer processors (computer hardware)", "type": "interests", "path": ["Interests", "Technology (computers & electronics)", "Computers (computers & electronics)"], "description": "", "audience_size_lower_bound": 186229379, "audience_size_upper_bound": 219005750, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.533Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.533Z"}, {"_id": "67c56feccceafebe2cf7ae43", "interest_id": "6003151951349", "raw_name": "Computer servers (computing)", "name": "Computer servers (computing)", "type": "interests", "path": ["Interests", "Technology (computers & electronics)", "Computers (computers & electronics)"], "description": "", "audience_size_lower_bound": 87445790, "audience_size_upper_bound": 102836250, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.534Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.534Z"}, {"_id": "67c56feccceafebe2cf7ae45", "interest_id": "6003115804542", "raw_name": "Desktop computers (consumer electronics)", "name": "Desktop computers (consumer electronics)", "type": "interests", "path": ["Interests", "Technology (computers & electronics)", "Computers (computers & electronics)"], "description": "", "audience_size_lower_bound": 155917219, "audience_size_upper_bound": 183358650, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.534Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.534Z"}, {"_id": "67c56feccceafebe2cf7ae47", "interest_id": "6003423416540", "raw_name": "Free software (software)", "name": "Free software (software)", "type": "interests", "path": ["Interests", "Technology (computers & electronics)", "Computers (computers & electronics)"], "description": "", "audience_size_lower_bound": 561457967, "audience_size_upper_bound": 660274570, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.534Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.534Z"}, {"_id": "67c56feccceafebe2cf7ae49", "interest_id": "6003629266583", "raw_name": "Hard drives (computer hardware)", "name": "Hard drives (computer hardware)", "type": "interests", "path": ["Interests", "Technology (computers & electronics)", "Computers (computers & electronics)"], "description": "", "audience_size_lower_bound": 122281267, "audience_size_upper_bound": 143802770, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.535Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.535Z"}, {"_id": "67c56feccceafebe2cf7ae4b", "interest_id": "6003656296104", "raw_name": "Network storage (computers & electronics)", "name": "Network storage (computers & electronics)", "type": "interests", "path": ["Interests", "Technology (computers & electronics)", "Computers (computers & electronics)"], "description": "", "audience_size_lower_bound": 18677605, "audience_size_upper_bound": 21964864, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.535Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.535Z"}, {"_id": "67c56feccceafebe2cf7ae4d", "interest_id": "6005609368513", "raw_name": "Software (computers & electronics)", "name": "Software (computers & electronics)", "type": "interests", "path": ["Interests", "Technology (computers & electronics)", "Computers (computers & electronics)"], "description": "", "audience_size_lower_bound": 925583622, "audience_size_upper_bound": 1088486340, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.536Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.536Z"}, {"_id": "67c56feccceafebe2cf7ae4f", "interest_id": "6002960574320", "raw_name": "Tablet computers (computers & electronics)", "name": "Tablet computers (computers & electronics)", "type": "interests", "path": ["Interests", "Technology (computers & electronics)", "Computers (computers & electronics)"], "description": "", "audience_size_lower_bound": 512619285, "audience_size_upper_bound": 602840280, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.536Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.536Z"}, {"_id": "67c56feccceafebe2cf7ae51", "interest_id": "6003729124262", "raw_name": "Audio equipment (electronics)", "name": "Audio equipment (electronics)", "type": "interests", "path": ["Interests", "Technology (computers & electronics)", "Consumer electronics (computers & electronics)"], "description": "", "audience_size_lower_bound": 43983308, "audience_size_upper_bound": 51724371, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.536Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.536Z"}, {"_id": "67c56feccceafebe2cf7ae53", "interest_id": "6002998517244", "raw_name": "Camcorders (consumer electronics)", "name": "Camcorders (consumer electronics)", "type": "interests", "path": ["Interests", "Technology (computers & electronics)", "Consumer electronics (computers & electronics)"], "description": "", "audience_size_lower_bound": 18698377, "audience_size_upper_bound": 21989292, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.537Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.537Z"}, {"_id": "67c56feccceafebe2cf7ae55", "interest_id": "6003325186571", "raw_name": "Cameras (photography)", "name": "Cameras (photography)", "type": "interests", "path": ["Interests", "Technology (computers & electronics)", "Consumer electronics (computers & electronics)"], "description": "", "audience_size_lower_bound": 435776598, "audience_size_upper_bound": 512473280, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.537Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.537Z"}, {"_id": "67c56feccceafebe2cf7ae57", "interest_id": "6008832464480", "raw_name": "E-book readers (consumer electronics)", "name": "E-book readers (consumer electronics)", "type": "interests", "path": ["Interests", "Technology (computers & electronics)", "Consumer electronics (computers & electronics)"], "description": "", "audience_size_lower_bound": 45626557, "audience_size_upper_bound": 53656832, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.538Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.538Z"}, {"_id": "67c56feccceafebe2cf7ae59", "interest_id": "6003280676501", "raw_name": "GPS devices (consumer electronics)", "name": "GPS devices (consumer electronics)", "type": "interests", "path": ["Interests", "Technology (computers & electronics)", "Consumer electronics (computers & electronics)"], "description": "", "audience_size_lower_bound": 24952948, "audience_size_upper_bound": 29344667, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.538Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.538Z"}, {"_id": "67c56feccceafebe2cf7ae5b", "interest_id": "6002971085794", "raw_name": "Mobile phones (smart phone)", "name": "Mobile phones (smart phone)", "type": "interests", "path": ["Interests", "Technology (computers & electronics)", "Consumer electronics (computers & electronics)"], "description": "", "audience_size_lower_bound": 1007046692, "audience_size_upper_bound": 1184286910, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.538Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.538Z"}, {"_id": "67c56feccceafebe2cf7ae5d", "interest_id": "6003381994165", "raw_name": "Portable media players (audio equipment)", "name": "Portable media players (audio equipment)", "type": "interests", "path": ["Interests", "Technology (computers & electronics)", "Consumer electronics (computers & electronics)"], "description": "", "audience_size_lower_bound": 6968758, "audience_size_upper_bound": 8195260, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.538Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.538Z"}, {"_id": "67c56feccceafebe2cf7ae5f", "interest_id": "6003288647527", "raw_name": "Projectors (consumer electronics)", "name": "Projectors (consumer electronics)", "type": "interests", "path": ["Interests", "Technology (computers & electronics)", "Consumer electronics (computers & electronics)"], "description": "", "audience_size_lower_bound": 31618071, "audience_size_upper_bound": 37182852, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.539Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.539Z"}, {"_id": "67c56feccceafebe2cf7ae61", "interest_id": "6003289911338", "raw_name": "Smartphones (consumer electronics)", "name": "Smartphones (consumer electronics)", "type": "interests", "path": ["Interests", "Technology (computers & electronics)", "Consumer electronics (computers & electronics)"], "description": "", "audience_size_lower_bound": 789714659, "audience_size_upper_bound": 928704440, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.54Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.54Z"}, {"_id": "67c56feccceafebe2cf7ae63", "interest_id": "6003224441249", "raw_name": "Televisions (consumer electronics)", "name": "Televisions (consumer electronics)", "type": "interests", "path": ["Interests", "Technology (computers & electronics)", "Consumer electronics (computers & electronics)"], "description": "", "audience_size_lower_bound": 1077339829, "audience_size_upper_bound": 1266951640, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.54Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.54Z"}, {"_id": "67c56feccceafebe2cf7ae65", "interest_id": "6018413514983", "raw_name": "Anniversary (within 61-90 days)", "name": "Anniversary (within 61-90 days)", "type": "behaviors", "path": ["Behaviours", "Anniversary"], "description": "People with an anniversary in 61-90 days", "audience_size_lower_bound": 7110308, "audience_size_upper_bound": 8361723, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.541Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.541Z"}, {"_id": "67c56feccceafebe2cf7ae67", "interest_id": "6320095608983", "raw_name": "Recently detected devices", "name": "Recently detected devices", "type": "behaviors", "path": ["Behaviours", "Behaviours", " Mobile Device User", "  All Mobile Devices by Operating System", "  Facebook access (mobile)"], "description": "Users who have recently connected to Facebook on a new smartphone device", "audience_size_lower_bound": 662592094, "audience_size_upper_bound": 779208303, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.541Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.541Z"}, {"_id": "67c56feccceafebe2cf7ae69", "interest_id": "6320095650783", "raw_name": "Recently detected iPhone 14 devices", "name": "Recently detected iPhone 14 devices", "type": "behaviors", "path": ["Behaviours", "Behaviours", " Mobile Device User", "  All Mobile Devices by Operating System", " Facebook access (mobile)"], "description": "Users who have recently connected to Facebook on an iPhone 14 device", "audience_size_lower_bound": 14955946, "audience_size_upper_bound": 17588193, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.541Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.541Z"}, {"_id": "67c56feccceafebe2cf7ae6b", "interest_id": "6086326072983", "raw_name": "People in Argentina who prefer high-value goods", "name": "People in Argentina who prefer high-value goods", "type": "behaviors", "path": ["Behaviours", "Consumer classification", "Argentina"], "description": "Aligned to (ABC1) group, people in Argentina who are predicted to prefer high-value goods", "audience_size_lower_bound": 930496, "audience_size_upper_bound": 1094264, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.542Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.542Z"}, {"_id": "67c56feccceafebe2cf7ae6d", "interest_id": "6086326078383", "raw_name": "People in Argentina who prefer mid- and high-value goods", "name": "People in Argentina who prefer mid- and high-value goods", "type": "behaviors", "path": ["Behaviours", "Consumer classification", "Argentina"], "description": "Aligned to (ABC1+C2) group, people in Argentina who are predicted to prefer mid to high-value goods", "audience_size_lower_bound": 3686816, "audience_size_upper_bound": 4335696, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.542Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.542Z"}, {"_id": "67c56feccceafebe2cf7ae6f", "interest_id": "6046096201583", "raw_name": "People in Brazil who prefer high-value goods", "name": "People in Brazil who prefer high-value goods", "type": "behaviors", "path": ["Behaviours", "Consumer classification", "Brazil"], "description": "Aligned to (A+B) group, people in Brazil who are predicted to prefer high-value goods", "audience_size_lower_bound": 12556798, "audience_size_upper_bound": 14766795, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.543Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.543Z"}, {"_id": "67c56feccceafebe2cf7ae71", "interest_id": "6110813675983", "raw_name": "People in Brazil who prefer mid-value and high-value goods", "name": "People in Brazil who prefer mid-value and high-value goods", "type": "behaviors", "path": ["Behaviours", "Consumer classification", "Brazil"], "description": "Aligned to (A+B+C) group, people in Brazil who are predicted to prefer mid-value and high-value goods", "audience_size_lower_bound": 30158023, "audience_size_upper_bound": 35465836, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.543Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.543Z"}, {"_id": "67c56feccceafebe2cf7ae73", "interest_id": "6086326043983", "raw_name": "People in Chile who prefer high-value goods", "name": "People in Chile who prefer high-value goods", "type": "behaviors", "path": ["Behaviours", "Consumer classification", "Chile"], "description": "Aligned to (ABC1) group, people in Chile who are predicted to prefer high-value goods", "audience_size_lower_bound": 746249, "audience_size_upper_bound": 877589, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.543Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.543Z"}, {"_id": "67c56feccceafebe2cf7ae75", "interest_id": "6086326068183", "raw_name": "People in Chile who prefer mid- and high-value goods", "name": "People in Chile who prefer mid- and high-value goods", "type": "behaviors", "path": ["Behaviours", "Consumer classification", "Chile"], "description": "Aligned to (A+B) group, people in Chile who are predicted to prefer mid to high-value goods", "audience_size_lower_bound": 2329600, "audience_size_upper_bound": 2739610, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.544Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.544Z"}, {"_id": "67c56feccceafebe2cf7ae77", "interest_id": "6028974370383", "raw_name": "People in India who prefer high-value goods", "name": "People in India who prefer high-value goods", "type": "behaviors", "path": ["Behaviours", "Consumer classification", "India"], "description": "Aligned to (A) group, people in India who are predicted to prefer high-value goods", "audience_size_lower_bound": 86492575, "audience_size_upper_bound": 101715269, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.544Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.544Z"}, {"_id": "67c56feccceafebe2cf7ae79", "interest_id": "6028974351183", "raw_name": "People in India who prefer mid- and high-value goods", "name": "People in India who prefer mid- and high-value goods", "type": "behaviors", "path": ["Behaviours", "Consumer classification", "India"], "description": "Aligned to (A+B) group, people in India who are predicted to prefer mid to high-value goods", "audience_size_lower_bound": 136611524, "audience_size_upper_bound": 160655153, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.545Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.545Z"}, {"_id": "67c56feccceafebe2cf7ae7b", "interest_id": "6110446593183", "raw_name": "People  in Indonesia who prefer mid-value and high-value goods", "name": "People  in Indonesia who prefer mid-value and high-value goods", "type": "behaviors", "path": ["Behaviours", "Consumer classification", "Indonesia"], "description": "Aligned to (upper I, upper II and middle I) group, people in Indonesia who are predicted to prefer high-value goods", "audience_size_lower_bound": 40560158, "audience_size_upper_bound": 47698746, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.545Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.545Z"}, {"_id": "67c56feccceafebe2cf7ae7d", "interest_id": "6092145447383", "raw_name": "People in Indonesia who prefer high-value goods", "name": "People in Indonesia who prefer high-value goods", "type": "behaviors", "path": ["Behaviours", "Consumer classification", "Indonesia"], "description": "Aligned to (Upper I) group, people in Indonesia who are predicted to prefer high-value goods", "audience_size_lower_bound": 16580813, "audience_size_upper_bound": 19499037, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.546Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.546Z"}, {"_id": "67c56feccceafebe2cf7ae7f", "interest_id": "6082317392983", "raw_name": "People who prefer high-value goods in the Kingdom of Saudi Arabia", "name": "People who prefer high-value goods in the Kingdom of Saudi Arabia", "type": "behaviors", "path": ["Behaviours", "Consumer classification", "Kingdom of Saudi Arabia"], "description": "Aligned to (A) group, people in the Kingdom of Saudi Arabia who are predicted to prefer high-value goods", "audience_size_lower_bound": 1067032, "audience_size_upper_bound": 1254830, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.546Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.546Z"}, {"_id": "67c56feccceafebe2cf7ae81", "interest_id": "6082317405583", "raw_name": "People who prefer mid to high-value goods in the Kingdom of Saudi Arabia", "name": "People who prefer mid to high-value goods in the Kingdom of Saudi Arabia", "type": "behaviors", "path": ["Behaviours", "Consumer classification", "Kingdom of Saudi Arabia"], "description": "Aligned to (A+B) group, people in the Kingdom of Saudi Arabia who are predicted to prefer mid to high-value goods", "audience_size_lower_bound": 2505170, "audience_size_upper_bound": 2946081, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.546Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.546Z"}, {"_id": "67c56feccceafebe2cf7ae83", "interest_id": "6100407132383", "raw_name": "People in Malaysia who prefer high-value goods", "name": "People in Malaysia who prefer high-value goods", "type": "behaviors", "path": ["Behaviours", "Consumer classification", "Malaysia"], "description": "Aligned to (AB) SEC group, people in Malaysia who are predicted to prefer high-value goods", "audience_size_lower_bound": 2331864, "audience_size_upper_bound": 2742273, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.547Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.547Z"}, {"_id": "67c56feccceafebe2cf7ae85", "interest_id": "6100407234583", "raw_name": "People in Malaysia who prefer mid- and high-value goods", "name": "People in Malaysia who prefer mid- and high-value goods", "type": "behaviors", "path": ["Behaviours", "Consumer classification", "Malaysia"], "description": "Aligned to (AB+C1) SEC group, people in Malaysia who are predicted to prefer mid- to high-value goods", "audience_size_lower_bound": 6231043, "audience_size_upper_bound": 7327707, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.547Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.547Z"}, {"_id": "67c56feccceafebe2cf7ae87", "interest_id": "6085888747383", "raw_name": "People in Mexico who prefer high-value goods", "name": "People in Mexico who prefer high-value goods", "type": "behaviors", "path": ["Behaviours", "Consumer classification", "Mexico"], "description": "Aligned to (AB) group, people in Mexico who are predicted to prefer high-value goods", "audience_size_lower_bound": 4970064, "audience_size_upper_bound": 5844796, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.548Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.548Z"}, {"_id": "67c56feccceafebe2cf7ae89", "interest_id": "6085888777383", "raw_name": "People in Mexico who prefer mid- and high-value goods", "name": "People in Mexico who prefer mid- and high-value goods", "type": "behaviors", "path": ["Behaviours", "Consumer classification", "Mexico"], "description": "Aligned to (ABC+) group, people in Mexico who are predicted to prefer mid to high-value goods", "audience_size_lower_bound": 16077843, "audience_size_upper_bound": 18907544, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.548Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.548Z"}, {"_id": "67c56feccceafebe2cf7ae8b", "interest_id": "6100406737783", "raw_name": "People in Pakistan who prefer high-value goods", "name": "People in Pakistan who prefer high-value goods", "type": "behaviors", "path": ["Behaviours", "Consumer classification", "Pakistan"], "description": "Aligned to (A) SEC group, people in Pakistan who are predicted to prefer high-value goods", "audience_size_lower_bound": 1466556, "audience_size_upper_bound": 1724671, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.548Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.548Z"}, {"_id": "67c56feccceafebe2cf7ae8d", "interest_id": "6100407062383", "raw_name": "People in Pakistan who prefer mid- and high-value goods", "name": "People in Pakistan who prefer mid- and high-value goods", "type": "behaviors", "path": ["Behaviours", "Consumer classification", "Pakistan"], "description": "Aligned to (A+B) SEC group, people in Pakistan who are predicted to prefer mid- to high-value goods", "audience_size_lower_bound": 7376604, "audience_size_upper_bound": 8674887, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.549Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.549Z"}, {"_id": "67c56feccceafebe2cf7ae8f", "interest_id": "6110633547383", "raw_name": "People who prefer high-value goods in the Philippines", "name": "People who prefer high-value goods in the Philippines", "type": "behaviors", "path": ["Behaviours", "Consumer classification", "Philippines"], "description": "Aligned to (A) group, people in the Philippines who are predicted to prefer high-value goods", "audience_size_lower_bound": 5983142, "audience_size_upper_bound": 7036176, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.549Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.549Z"}, {"_id": "67c56feccceafebe2cf7ae91", "interest_id": "6110636171983", "raw_name": "People who prefer mid-value and high-value goods in the Philippines", "name": "People who prefer mid-value and high-value goods in the Philippines", "type": "behaviors", "path": ["Behaviours", "Consumer classification", "Philippines"], "description": "Aligned to (A+B) group, people in the Philippines who are predicted to prefer mid to high-value goods", "audience_size_lower_bound": 27250680, "audience_size_upper_bound": 32046800, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.55Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.55Z"}, {"_id": "67c56feccceafebe2cf7ae93", "interest_id": "6046095968983", "raw_name": "People in South Africa who prefer high-value goods", "name": "People in South Africa who prefer high-value goods", "type": "behaviors", "path": ["Behaviours", "Consumer classification", "South Africa"], "description": "Aligned to (8,9,10) LSM group, people in South Africa who are predicted to prefer high-value goods", "audience_size_lower_bound": 2437240, "audience_size_upper_bound": 2866195, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.55Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.55Z"}, {"_id": "67c56feccceafebe2cf7ae95", "interest_id": "6046096047583", "raw_name": "People in South Africa who prefer mid- and high-value goods", "name": "People in South Africa who prefer mid- and high-value goods", "type": "behaviors", "path": ["Behaviours", "Consumer classification", "South Africa"], "description": "Aligned to (5,6,7) LSM group, people in South Africa who are predicted to prefer mid- to high-value goods", "audience_size_lower_bound": 3752709, "audience_size_upper_bound": 4413186, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.551Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.551Z"}, {"_id": "67c56feccceafebe2cf7ae97", "interest_id": "6089632523783", "raw_name": "People in Turkey who prefer high-value goods", "name": "People in Turkey who prefer high-value goods", "type": "behaviors", "path": ["Behaviours", "Consumer classification", "Turkey"], "description": "Aligned to (A) SES group, people in Turkey who are predicted to prefer high-value goods", "audience_size_lower_bound": 1747452, "audience_size_upper_bound": 2055004, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.551Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.551Z"}, {"_id": "67c56feccceafebe2cf7ae99", "interest_id": "6089632452783", "raw_name": "People in Turkey who prefer mid- and high-value goods", "name": "People in Turkey who prefer mid- and high-value goods", "type": "behaviors", "path": ["Behaviours", "Consumer classification", "Turkey"], "description": "Aligned to (A+B) SES group, people in Turkey who are predicted to prefer mid- to high-value goods", "audience_size_lower_bound": 7343177, "audience_size_upper_bound": 8635577, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.552Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.552Z"}, {"_id": "67c56feccceafebe2cf7ae9b", "interest_id": "6082317210583", "raw_name": "People who prefer high-value goods in UAE", "name": "People who prefer high-value goods in UAE", "type": "behaviors", "path": ["Behaviours", "Consumer classification", "UAE"], "description": "Aligned to (A) group, people in UAE who are predicted to prefer high-value goods", "audience_size_lower_bound": 56727, "audience_size_upper_bound": 66711, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.552Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.552Z"}, {"_id": "67c56feccceafebe2cf7ae9d", "interest_id": "6082317378383", "raw_name": "People who prefer mid and high-value goods in UAE", "name": "People who prefer mid and high-value goods in UAE", "type": "behaviors", "path": ["Behaviours", "Consumer classification", "UAE"], "description": "Aligned to (A+B) group, people in UAE who are predicted to prefer mid-to-high-value goods", "audience_size_lower_bound": 305747, "audience_size_upper_bound": 359559, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.552Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.552Z"}, {"_id": "67c56feccceafebe2cf7ae9f", "interest_id": "6378518542983", "raw_name": "All creators", "name": "All creators", "type": "behaviors", "path": ["Behaviours", "Digital activities"], "description": "People who are creators on Facebook and Instagram.", "audience_size_lower_bound": 10204496, "audience_size_upper_bound": 12000488, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.553Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.553Z"}, {"_id": "67c56feccceafebe2cf7aea1", "interest_id": "6019221046583", "raw_name": "Played Canvas games (last 14 days)", "name": "Played Canvas games (last 14 days)", "type": "behaviors", "path": ["Behaviours", "Digital activities", "Canvas gaming"], "description": "People who played a Canvas game in the last 14 days.", "audience_size_lower_bound": 2530792, "audience_size_upper_bound": 2976212, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.553Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.553Z"}, {"_id": "67c56feccceafebe2cf7aea3", "interest_id": "6019246164583", "raw_name": "Played Canvas games (last 3 days)", "name": "Played Canvas games (last 3 days)", "type": "behaviors", "path": ["Behaviours", "Digital activities", "Canvas gaming"], "description": "People who played a Canvas game in the last 3 days", "audience_size_lower_bound": 1935215, "audience_size_upper_bound": 2275813, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.553Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.553Z"}, {"_id": "67c56feccceafebe2cf7aea5", "interest_id": "6019221038183", "raw_name": "Played Canvas games (last 7 days)", "name": "Played Canvas games (last 7 days)", "type": "behaviors", "path": ["Behaviours", "Digital activities", "Canvas gaming"], "description": "People who played a Canvas game in the last 7 days.", "audience_size_lower_bound": 2225363, "audience_size_upper_bound": 2617028, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.554Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.554Z"}, {"_id": "67c56feccceafebe2cf7aea7", "interest_id": "6019221024783", "raw_name": "Played Canvas games (yesterday)", "name": "Played Canvas games (yesterday)", "type": "behaviors", "path": ["Behaviours", "Digital activities", "Canvas gaming"], "description": "People who played a Canvas game yesterday.", "audience_size_lower_bound": 1585718, "audience_size_upper_bound": 1864805, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.554Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.554Z"}, {"_id": "67c56feccceafebe2cf7aea9", "interest_id": "6007847947183", "raw_name": "Console gamers", "name": "Console gamers", "type": "behaviors", "path": ["Behaviours", "Digital activities"], "description": "People who have liked Pages related to console gaming or gaming systems", "audience_size_lower_bound": 58578406, "audience_size_upper_bound": 68888206, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.555Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.555Z"}, {"_id": "67c56feccceafebe2cf7aeab", "interest_id": "6003808923172", "raw_name": "Early technology adopters", "name": "Early technology adopters", "type": "behaviors", "path": ["Behaviours", "Digital activities"], "description": "People who are likely to adopt new technologies earlier than others", "audience_size_lower_bound": 68774147, "audience_size_upper_bound": 80878398, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.555Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.555Z"}, {"_id": "67c56feccceafebe2cf7aead", "interest_id": "6356471865383", "raw_name": "Facebook Lite app users", "name": "Facebook Lite app users", "type": "behaviors", "path": ["Behaviours", "Digital activities"], "description": "People who use the Facebook Lite app.", "audience_size_lower_bound": 295082220, "audience_size_upper_bound": 347016691, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.555Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.555Z"}, {"_id": "67c56feccceafebe2cf7aeaf", "interest_id": "6004948896972", "raw_name": "Facebook Payments users (30 days)", "name": "Facebook Payments users (30 days)", "type": "behaviors", "path": ["Behaviours", "Digital activities"], "description": "People who have used Facebook Payments in the past 30 days", "audience_size_lower_bound": 243169, "audience_size_upper_bound": 285967, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.556Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.556Z"}, {"_id": "67c56feccceafebe2cf7aeb1", "interest_id": "6002764392172", "raw_name": "Facebook Payments users (90 days)", "name": "Facebook Payments users (90 days)", "type": "behaviors", "path": ["Behaviours", "Digital activities"], "description": "People who have used Facebook payments in the past 90 days", "audience_size_lower_bound": 559659, "audience_size_upper_bound": 658159, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.556Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.556Z"}, {"_id": "67c56feccceafebe2cf7aeb3", "interest_id": "6004854404172", "raw_name": "Facebook access: older devices and OS", "name": "Facebook access: older devices and OS", "type": "behaviors", "path": ["Behaviours", "Digital activities"], "description": "People who primarily access Facebook on older devices or operating systems before Windows 7, Mac OS X or Windows NT 6.2.", "audience_size_lower_bound": 708032346, "audience_size_upper_bound": 832646040, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.556Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.556Z"}, {"_id": "67c56feccceafebe2cf7aeb5", "interest_id": "6020530281783", "raw_name": "Business Page admins", "name": "Business Page admins", "type": "behaviors", "path": ["Behaviours", "Digital activities", "Facebook page admins"], "description": "People who are an admin of a business Page on Facebook", "audience_size_lower_bound": 49764590, "audience_size_upper_bound": 58523159, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.557Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.557Z"}, {"_id": "67c56feccceafebe2cf7aeb7", "interest_id": "6020530280983", "raw_name": "Community and club Page admins", "name": "Community and club Page admins", "type": "behaviors", "path": ["Behaviours", "Digital activities", "Facebook page admins"], "description": "People who are an admin of a community and club Page on Facebook", "audience_size_lower_bound": 15382479, "audience_size_upper_bound": 18089796, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.557Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.557Z"}, {"_id": "67c56feccceafebe2cf7aeb9", "interest_id": "6015683810783", "raw_name": "Facebook Page admins", "name": "Facebook Page admins", "type": "behaviors", "path": ["Behaviours", "Digital activities", "Facebook page admins"], "description": "People who are an admin of at least one Page on Facebook.", "audience_size_lower_bound": 746808429, "audience_size_upper_bound": 878246713, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.558Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.558Z"}, {"_id": "67c56feccceafebe2cf7aebb", "interest_id": "6020530269183", "raw_name": "Food and restaurant Page admins", "name": "Food and restaurant Page admins", "type": "behaviors", "path": ["Behaviours", "Digital activities", "Facebook page admins"], "description": "People who are an admin of a food and restaurant Page on Facebook", "audience_size_lower_bound": 2723834, "audience_size_upper_bound": 3203229, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.558Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.558Z"}, {"_id": "67c56feccceafebe2cf7aebd", "interest_id": "6020568271383", "raw_name": "Health and beauty Page admins", "name": "Health and beauty Page admins", "type": "behaviors", "path": ["Behaviours", "Digital activities", "Facebook page admins"], "description": "People who are an admin of a health and beauty Page on Facebook", "audience_size_lower_bound": 23161286, "audience_size_upper_bound": 27237673, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.559Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.559Z"}, {"_id": "67c56feccceafebe2cf7aebf", "interest_id": "6041891177783", "raw_name": "New Page admins", "name": "New Page admins", "type": "behaviors", "path": ["Behaviours", "Digital activities", "Facebook page admins"], "description": "People who have become Facebook Page admins within the past two weeks.", "audience_size_lower_bound": 403386882, "audience_size_upper_bound": 474382974, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.559Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.559Z"}, {"_id": "67c56feccceafebe2cf7aec1", "interest_id": "6020530250383", "raw_name": "Retail Page admins", "name": "Retail Page admins", "type": "behaviors", "path": ["Behaviours", "Digital activities", "Facebook page admins"], "description": "People who are an admin of a retail Page on Facebook", "audience_size_lower_bound": 14309318, "audience_size_upper_bound": 16827759, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.56Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.56Z"}, {"_id": "67c56feccceafebe2cf7aec3", "interest_id": "6020530156983", "raw_name": "Sport Page admins", "name": "Sport Page admins", "type": "behaviors", "path": ["Behaviours", "Digital activities", "Facebook page admins"], "description": "People who are an admin of a sport Page on Facebook", "audience_size_lower_bound": 8334418, "audience_size_upper_bound": 9801276, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.56Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.56Z"}, {"_id": "67c56feccceafebe2cf7aec5", "interest_id": "6020530139383", "raw_name": "Travel and tourism Page admins", "name": "Travel and tourism Page admins", "type": "behaviors", "path": ["Behaviours", "Digital activities", "Facebook page admins"], "description": "People who are an admin of a travel and tourism Page on Facebook", "audience_size_lower_bound": 24967156, "audience_size_upper_bound": 29361376, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.56Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.56Z"}, {"_id": "67c56feccceafebe2cf7aec7", "interest_id": "6377407066783", "raw_name": "Food and drink creators", "name": "Food and drink creators", "type": "behaviors", "path": ["Behaviours", "Digital activities"], "description": "People who are food and drink creators on Facebook and Instagram.", "audience_size_lower_bound": 510667, "audience_size_upper_bound": 600545, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.561Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.561Z"}, {"_id": "67c56feccceafebe2cf7aec9", "interest_id": "6377407134383", "raw_name": "Health and wellness creators", "name": "Health and wellness creators", "type": "behaviors", "path": ["Behaviours", "Digital activities"], "description": "People who are health and wellness creators on Facebook and Instagram.", "audience_size_lower_bound": 125754, "audience_size_upper_bound": 147887, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.561Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.561Z"}, {"_id": "67c56feccceafebe2cf7aecb", "interest_id": "6297846662583", "raw_name": "Instagram business profile admins", "name": "Instagram business profile admins", "type": "behaviors", "path": ["Behaviours", "Digital activities"], "description": "People who are an administrator of an Instagram business profile", "audience_size_lower_bound": 70371724, "audience_size_upper_bound": 82757148, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.562Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.562Z"}, {"_id": "67c56feccceafebe2cf7aecd", "interest_id": "6015547900583", "raw_name": "Facebook access (browser): Chrome", "name": "Facebook access (browser): Chrome", "type": "behaviors", "path": ["Behaviours", "Digital activities", "Internet browser used"], "description": "People who primarily access Facebook using Google Chrome.", "audience_size_lower_bound": 217789782, "audience_size_upper_bound": 256120784, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.562Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.562Z"}, {"_id": "67c56feccceafebe2cf7aecf", "interest_id": "6015547847583", "raw_name": "Facebook access (browser): Firefox", "name": "Facebook access (browser): Firefox", "type": "behaviors", "path": ["Behaviours", "Digital activities", "Internet browser used"], "description": "People who primarily access Facebook using Firefox", "audience_size_lower_bound": 8521989, "audience_size_upper_bound": 10021860, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.562Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.562Z"}, {"_id": "67c56feccceafebe2cf7aed1", "interest_id": "6015593776783", "raw_name": "Facebook access (browser): Internet Explorer", "name": "Facebook access (browser): Internet Explorer", "type": "behaviors", "path": ["Behaviours", "Digital activities", "Internet browser used"], "description": "People who primarily access Facebook using Internet Explorer", "audience_size_lower_bound": 30162, "audience_size_upper_bound": 35471, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.563Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.563Z"}, {"_id": "67c56feccceafebe2cf7aed3", "interest_id": "6055133998183", "raw_name": "Facebook access (browser): Microsoft Edge", "name": "Facebook access (browser): Microsoft Edge", "type": "behaviors", "path": ["Behaviours", "Digital activities", "Internet browser used"], "description": "People who primarily access Facebook using Microsoft Edge", "audience_size_lower_bound": 21223795, "audience_size_upper_bound": 24959184, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.563Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.563Z"}, {"_id": "67c56feccceafebe2cf7aed5", "interest_id": "6015593652183", "raw_name": "Facebook access (browser): Opera", "name": "Facebook access (browser): Opera", "type": "behaviors", "path": ["Behaviours", "Digital activities", "Internet browser used"], "description": "People who primarily access Facebook using Opera", "audience_size_lower_bound": 1663649, "audience_size_upper_bound": 1956452, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.564Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.564Z"}, {"_id": "67c56feccceafebe2cf7aed7", "interest_id": "6015593608983", "raw_name": "Facebook access (browser): Safari", "name": "Facebook access (browser): Safari", "type": "behaviors", "path": ["Behaviours", "Digital activities", "Internet browser used"], "description": "People who primarily access Facebook using Safari", "audience_size_lower_bound": 34657429, "audience_size_upper_bound": 40757137, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.564Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.564Z"}, {"_id": "67c56feccceafebe2cf7aed9", "interest_id": "6378552460983", "raw_name": "Internet personality creators", "name": "Internet personality creators", "type": "behaviors", "path": ["Behaviours", "Digital activities"], "description": "People who are Internet personality creators on Facebook and Instagram.", "audience_size_lower_bound": 2524806, "audience_size_upper_bound": 2969172, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.564Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.564Z"}, {"_id": "67c56feccceafebe2cf7aedb", "interest_id": "6273196847983", "raw_name": "New active business (< 12 months)", "name": "New active business (< 12 months)", "type": "behaviors", "path": ["Behaviours", "Digital activities"], "description": "Admins of engaged businesses that were created in the last 12 months.", "audience_size_lower_bound": 21008847, "audience_size_upper_bound": 24706405, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.565Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.565Z"}, {"_id": "67c56feccceafebe2cf7aedd", "interest_id": "6273108107383", "raw_name": "New active business (< 24 months)", "name": "New active business (< 24 months)", "type": "behaviors", "path": ["Behaviours", "Digital activities"], "description": "Admins of engaged businesses that were created in the last 24 months.", "audience_size_lower_bound": 35425903, "audience_size_upper_bound": 41660862, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.565Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.565Z"}, {"_id": "67c56feccceafebe2cf7aedf", "interest_id": "6273108079183", "raw_name": "New active business (< 6 months)", "name": "New active business (< 6 months)", "type": "behaviors", "path": ["Behaviours", "Digital activities"], "description": "Admins of engaged businesses that were created in the last 6 months.", "audience_size_lower_bound": 12491415, "audience_size_upper_bound": 14689905, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.566Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.566Z"}, {"_id": "67c56feccceafebe2cf7aee1", "interest_id": "6003966451572", "raw_name": "Facebook access (OS): Mac OS X", "name": "Facebook access (OS): Mac OS X", "type": "behaviors", "path": ["Behaviours", "Digital activities", "Operating system used"], "description": "People who primarily access Facebook using Mac OS X", "audience_size_lower_bound": 531546, "audience_size_upper_bound": 625099, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.566Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.566Z"}, {"_id": "67c56feccceafebe2cf7aee3", "interest_id": "6063268655983", "raw_name": "Facebook access (OS): Mac Sierra", "name": "Facebook access (OS): Mac Sierra", "type": "behaviors", "path": ["Behaviours", "Digital activities", "Operating system used"], "description": "People who  primarily access Facebook using Mac Sierra", "audience_size_lower_bound": 878576, "audience_size_upper_bound": 1033206, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.567Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.567Z"}, {"_id": "67c56feccceafebe2cf7aee5", "interest_id": "6029587661983", "raw_name": "Facebook access (OS): Windows 10", "name": "Facebook access (OS): Windows 10", "type": "behaviors", "path": ["Behaviours", "Digital activities", "Operating system used"], "description": "People who primarily access Facebook using Windows 10", "audience_size_lower_bound": 1653213, "audience_size_upper_bound": 1944179, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.567Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.567Z"}, {"_id": "67c56feccceafebe2cf7aee7", "interest_id": "6003986707172", "raw_name": "Facebook access (OS): Windows 7", "name": "Facebook access (OS): Windows 7", "type": "behaviors", "path": ["Behaviours", "Digital activities", "Operating system used"], "description": "People who primarily access Facebook using Windows 7.", "audience_size_lower_bound": 2706, "audience_size_upper_bound": 3183, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.567Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.567Z"}, {"_id": "67c56feccceafebe2cf7aee9", "interest_id": "6006298077772", "raw_name": "Facebook access (OS): Windows 8", "name": "Facebook access (OS): Windows 8", "type": "behaviors", "path": ["Behaviours", "Digital activities", "Operating system used"], "description": "People who primarily access Facebook using Facebook using Windows 8", "audience_size_lower_bound": 11739, "audience_size_upper_bound": 13806, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.568Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.568Z"}, {"_id": "67c56feccceafebe2cf7aeeb", "interest_id": "6003966450972", "raw_name": "Facebook access (OS): Windows Vista", "name": "Facebook access (OS): Windows Vista", "type": "behaviors", "path": ["Behaviours", "Digital activities", "Operating system used"], "description": "People who primarily access Facebook using Windows Vista", "audience_size_lower_bound": 571, "audience_size_upper_bound": 672, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.568Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.568Z"}, {"_id": "67c56feccceafebe2cf7aeed", "interest_id": "6003966466972", "raw_name": "Facebook access (OS): Windows XP", "name": "Facebook access (OS): Windows XP", "type": "behaviors", "path": ["Behaviours", "Digital activities", "Operating system used"], "description": "People who primarily access Facebook using Windows XP", "audience_size_lower_bound": 184, "audience_size_upper_bound": 217, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.569Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.569Z"}, {"_id": "67c56feccceafebe2cf7aeef", "interest_id": "6202657388783", "raw_name": "People who have visited Facebook Gaming", "name": "People who have visited Facebook Gaming", "type": "behaviors", "path": ["Behaviours", "Digital activities"], "description": "People who have recently accessed Facebook Gaming to watch videos, view posts, access tournaments or play games.", "audience_size_lower_bound": 8658510, "audience_size_upper_bound": 10182408, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.569Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.569Z"}, {"_id": "67c56feccceafebe2cf7aef1", "interest_id": "6377178995383", "raw_name": "Shops admins", "name": "Shops admins", "type": "behaviors", "path": ["Behaviours", "Digital activities"], "description": "People who manage a shop on Facebook or Instagram.", "audience_size_lower_bound": 588255, "audience_size_upper_bound": 691788, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.57Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.57Z"}, {"_id": "67c56feccceafebe2cf7aef3", "interest_id": "6002714898572", "raw_name": "Small business owners", "name": "Small business owners", "type": "behaviors", "path": ["Behaviours", "Digital activities"], "description": "People who list themselves as small business owners or own small business Pages on Facebook", "audience_size_lower_bound": 47346806, "audience_size_upper_bound": 55679844, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.57Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.57Z"}, {"_id": "67c56feccceafebe2cf7aef5", "interest_id": "6377406843183", "raw_name": "Travel and outdoors creators", "name": "Travel and outdoors creators", "type": "behaviors", "path": ["Behaviours", "Digital activities"], "description": "People who are travel and outdoors creators on Facebook and Instagram.", "audience_size_lower_bound": 363570, "audience_size_upper_bound": 427559, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.57Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.57Z"}, {"_id": "67c56feccceafebe2cf7aef7", "interest_id": "6378532690183", "raw_name": "Music creators", "name": "Music creators", "type": "behaviors", "path": ["Behaviours", "Digital activitiesTeam"], "description": "People who are music creators on Facebook and Instagram.", "audience_size_lower_bound": 829311, "audience_size_upper_bound": 975270, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.571Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.571Z"}, {"_id": "67c56feccceafebe2cf7aef9", "interest_id": "6025753961783", "raw_name": "Family of those who live abroad", "name": "Family of those who live abroad", "type": "behaviors", "path": ["Behaviours", "Ex-pats"], "description": "Family of people who now live abroad", "audience_size_lower_bound": 32808218, "audience_size_upper_bound": 38582465, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.572Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.572Z"}, {"_id": "67c56feccceafebe2cf7aefb", "interest_id": "6203619967383", "raw_name": "Friends of those who live abroad", "name": "Friends of those who live abroad", "type": "behaviors", "path": ["Behaviours", "Ex-pats"], "description": "Friends of people who now live abroad", "audience_size_lower_bound": 1889569199, "audience_size_upper_bound": 2222133379, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.572Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.572Z"}, {"_id": "67c56feccceafebe2cf7aefd", "interest_id": "6071248932383", "raw_name": "Lived in Algeria (formerly Ex-pats – Algeria)", "name": "Lived in Algeria (formerly Ex-pats – Algeria)", "type": "behaviors", "path": ["Behaviours", "Ex-pats"], "description": "People who used to live in Algeria who now live abroad", "audience_size_lower_bound": 1130031, "audience_size_upper_bound": 1328917, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.573Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.573Z"}, {"_id": "67c56feccceafebe2cf7aeff", "interest_id": "6025000826583", "raw_name": "Lived in Argentina (formerly Expats – Argentina)", "name": "Lived in Argentina (formerly Expats – Argentina)", "type": "behaviors", "path": ["Behaviours", "Ex-pats"], "description": "People who used to live in Argentina who now live abroad", "audience_size_lower_bound": 1318720, "audience_size_upper_bound": 1550815, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.573Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.573Z"}, {"_id": "67c56feccceafebe2cf7af01", "interest_id": "6021354857783", "raw_name": "Lived in Australia (formerly Ex-pats – Australia)", "name": "Lived in Australia (formerly Ex-pats – Australia)", "type": "behaviors", "path": ["Behaviours", "Ex-pats"], "description": "People who used to live in Australia who now live abroad", "audience_size_lower_bound": 1437763, "audience_size_upper_bound": 1690810, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.574Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.574Z"}, {"_id": "67c56feccceafebe2cf7af03", "interest_id": "6023675997383", "raw_name": "Lived in Austria (formerly Ex-pats – Austria)", "name": "Lived in Austria (formerly Ex-pats – Austria)", "type": "behaviors", "path": ["Behaviours", "Ex-pats"], "description": "People who used to live in Austria who now live abroad", "audience_size_lower_bound": 143766, "audience_size_upper_bound": 169069, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.574Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.574Z"}, {"_id": "67c56feccceafebe2cf7af05", "interest_id": "6023356562783", "raw_name": "Lived in Bangladesh (formerly Ex-pats – Bangladesh)", "name": "Lived in Bangladesh (formerly Ex-pats – Bangladesh)", "type": "behaviors", "path": ["Behaviours", "Ex-pats"], "description": "People who used to live in Bangladesh who now live abroad", "audience_size_lower_bound": 7045602, "audience_size_upper_bound": 8285629, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.575Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.575Z"}, {"_id": "67c56feccceafebe2cf7af07", "interest_id": "6043702804583", "raw_name": "Lived in Belgium (formerly Expats – Belgium)", "name": "Lived in Belgium (formerly Expats – Belgium)", "type": "behaviors", "path": ["Behaviours", "Ex-pats"], "description": "People who used to live in Belgium who now live abroad", "audience_size_lower_bound": 352216, "audience_size_upper_bound": 414207, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.575Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.575Z"}, {"_id": "67c56feccceafebe2cf7af09", "interest_id": "6019564340583", "raw_name": "Lived in Brazil (formerly Ex-pats – Brazil)", "name": "Lived in Brazil (formerly Ex-pats – Brazil)", "type": "behaviors", "path": ["Behaviours", "Ex-pats"], "description": "People who used to live in Brazil who now live abroad", "audience_size_lower_bound": 4263359, "audience_size_upper_bound": 5013711, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.576Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.576Z"}, {"_id": "67c56feccceafebe2cf7af0b", "interest_id": "6018797036783", "raw_name": "Lived in Cameroon (formerly Expats – Cameroon)", "name": "Lived in Cameroon (formerly Expats – Cameroon)", "type": "behaviors", "path": ["Behaviours", "Ex-pats"], "description": "People who used to live in Cameroon who now live abroad", "audience_size_lower_bound": 543637, "audience_size_upper_bound": 639318, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.576Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.576Z"}, {"_id": "67c56feccceafebe2cf7af0d", "interest_id": "6019396764183", "raw_name": "Lived in Canada (formerly Expats – Canada)", "name": "Lived in Canada (formerly Expats – Canada)", "type": "behaviors", "path": ["Behaviours", "Ex-pats"], "description": "People who used to live in Canada who now live abroad", "audience_size_lower_bound": 1295829, "audience_size_upper_bound": 1523896, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.576Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.576Z"}, {"_id": "67c56feccceafebe2cf7af0f", "interest_id": "6025054896983", "raw_name": "Lived in Chile (formerly Expats – Chile)", "name": "Lived in Chile (formerly Expats – Chile)", "type": "behaviors", "path": ["Behaviours", "Ex-pats"], "description": "People who used to live in Chile who now live abroad", "audience_size_lower_bound": 565568, "audience_size_upper_bound": 665108, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.577Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.577Z"}, {"_id": "67c56feccceafebe2cf7af11", "interest_id": "6019452369983", "raw_name": "Lived in China (formerly Ex-pats – China)", "name": "Lived in China (formerly Ex-pats – China)", "type": "behaviors", "path": ["Behaviours", "Ex-pats"], "description": "People who used to live in China who now live abroad", "audience_size_lower_bound": 2165402, "audience_size_upper_bound": 2546513, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.577Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.577Z"}, {"_id": "67c56feccceafebe2cf7af13", "interest_id": "6019673525983", "raw_name": "Lived in Colombia (formerly Ex-pats – Colombia)", "name": "Lived in Colombia (formerly Ex-pats – Colombia)", "type": "behaviors", "path": ["Behaviours", "Ex-pats"], "description": "People who used to live in Colombia who now live abroad", "audience_size_lower_bound": 3140302, "audience_size_upper_bound": 3692996, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.578Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.578Z"}, {"_id": "67c56feccceafebe2cf7af15", "interest_id": "6018797127383", "raw_name": "Lived in Cuba (formerly Expats – Cuba)", "name": "Lived in Cuba (formerly Expats – Cuba)", "type": "behaviors", "path": ["Behaviours", "Ex-pats"], "description": "People who used to live in Cuba who now live abroad", "audience_size_lower_bound": 1944875, "audience_size_upper_bound": 2287173, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.578Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.578Z"}, {"_id": "67c56feccceafebe2cf7af17", "interest_id": "6023676002183", "raw_name": "Lived in Cyprus (formerly Ex-pats – Cyprus)", "name": "Lived in Cyprus (formerly Ex-pats – Cyprus)", "type": "behaviors", "path": ["Behaviours", "Ex-pats"], "description": "People who used to live in Cyprus who now live abroad", "audience_size_lower_bound": 89900, "audience_size_upper_bound": 105723, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.579Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.579Z"}, {"_id": "67c56feccceafebe2cf7af19", "interest_id": "6023287438783", "raw_name": "Lived in Czech Republic (formerly Ex-pats – Czech Republic)", "name": "Lived in Czech Republic (formerly Ex-pats – Czech Republic)", "type": "behaviors", "path": ["Behaviours", "Ex-pats"], "description": "People who used to live in the Czech Republic who now live abroad", "audience_size_lower_bound": 212534, "audience_size_upper_bound": 249940, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.579Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.579Z"}, {"_id": "67c56feccceafebe2cf7af1b", "interest_id": "6023422105983", "raw_name": "Lived in Côte d'Ivoire (formerly Ex-pats – Ivory Coast)", "name": "Lived in Côte d'Ivoire (formerly Ex-pats – Ivory Coast)", "type": "behaviors", "path": ["Behaviours", "Ex-pats"], "description": "People who used to live in Côte d'Ivoire who now live abroad", "audience_size_lower_bound": 643731, "audience_size_upper_bound": 757028, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.58Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.58Z"}, {"_id": "67c56feccceafebe2cf7af1d", "interest_id": "6023516373983", "raw_name": "Lived in DR Congo (formerly Ex-pats – DR Congo)", "name": "Lived in DR Congo (formerly Ex-pats – DR Congo)", "type": "behaviors", "path": ["Behaviours", "Ex-pats"], "description": "People who used to live in the Democratic Republic of the Congo who now live abroad", "audience_size_lower_bound": 2466858, "audience_size_upper_bound": 2901026, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.58Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.58Z"}, {"_id": "67c56feccceafebe2cf7af1f", "interest_id": "6023287455983", "raw_name": "Lived in Denmark (formerly Ex-pats – Denmark)", "name": "Lived in Denmark (formerly Ex-pats – Denmark)", "type": "behaviors", "path": ["Behaviours", "Ex-pats"], "description": "People who used to live in Denmark who now live abroad", "audience_size_lower_bound": 123925, "audience_size_upper_bound": 145736, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.581Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.581Z"}, {"_id": "67c56feccceafebe2cf7af21", "interest_id": "6019673762183", "raw_name": "Lived in Dominican Republic (formerly Ex-pats – Dominican Republic)", "name": "Lived in Dominican Republic (formerly Ex-pats – Dominican Republic)", "type": "behaviors", "path": ["Behaviours", "Ex-pats"], "description": "People who used to live in the Dominican Republic who now live abroad", "audience_size_lower_bound": 1501269, "audience_size_upper_bound": 1765493, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.581Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.581Z"}, {"_id": "67c56feccceafebe2cf7af23", "interest_id": "6019673777983", "raw_name": "Lived in El Salvador (formerly Expats – El Salvador)", "name": "Lived in El Salvador (formerly Expats – El Salvador)", "type": "behaviors", "path": ["Behaviours", "Ex-pats"], "description": "People who used to live in El Salvador who now live abroad", "audience_size_lower_bound": 1580198, "audience_size_upper_bound": 1858313, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.582Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.582Z"}, {"_id": "67c56feccceafebe2cf7af25", "interest_id": "6023287351383", "raw_name": "Lived in Estonia (formerly Expats – Estonia)", "name": "Lived in Estonia (formerly Expats – Estonia)", "type": "behaviors", "path": ["Behaviours", "Ex-pats"], "description": "People who used to live in Estonia who now live abroad", "audience_size_lower_bound": 81948, "audience_size_upper_bound": 96371, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.582Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.582Z"}, {"_id": "67c56feccceafebe2cf7af27", "interest_id": "6018797165983", "raw_name": "Lived in Ethiopia (formerly Expats – Ethiopia)", "name": "Lived in Ethiopia (formerly Expats – Ethiopia)", "type": "behaviors", "path": ["Behaviours", "Ex-pats"], "description": "People who used to live in Ethiopia who now live abroad", "audience_size_lower_bound": 1121798, "audience_size_upper_bound": 1319235, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.583Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.583Z"}, {"_id": "67c56feccceafebe2cf7af29", "interest_id": "6068209522983", "raw_name": "Lived in Finland (formerly Expats – Finland)", "name": "Lived in Finland (formerly Expats – Finland)", "type": "behaviors", "path": ["Behaviours", "Ex-pats"], "description": "People who used to live in Finland who now live abroad", "audience_size_lower_bound": 113775, "audience_size_upper_bound": 133800, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.584Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.584Z"}, {"_id": "67c56feccceafebe2cf7af2b", "interest_id": "6019367014383", "raw_name": "Lived in France (formerly Ex-pats – France)", "name": "Lived in France (formerly Ex-pats – France)", "type": "behaviors", "path": ["Behaviours", "Ex-pats"], "description": "People who used to live in France who now live abroad", "audience_size_lower_bound": 2792290, "audience_size_upper_bound": 3283734, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.584Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.584Z"}, {"_id": "67c56feccceafebe2cf7af2d", "interest_id": "6019367052983", "raw_name": "Lived in Germany (formerly Ex-pats – Germany)", "name": "Lived in Germany (formerly Ex-pats – Germany)", "type": "behaviors", "path": ["Behaviours", "Ex-pats"], "description": "People who used to live in Germany who now live abroad", "audience_size_lower_bound": 2031571, "audience_size_upper_bound": 2389128, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.584Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.584Z"}, {"_id": "67c56feccceafebe2cf7af2f", "interest_id": "6019673448383", "raw_name": "Lived in Ghana (formerly Ex-pats – Ghana)", "name": "Lived in Ghana (formerly Ex-pats – Ghana)", "type": "behaviors", "path": ["Behaviours", "Ex-pats"], "description": "People who used to live in Ghana who now live abroad", "audience_size_lower_bound": 794937, "audience_size_upper_bound": 934846, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.585Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.585Z"}, {"_id": "67c56feccceafebe2cf7af31", "interest_id": "6023676017583", "raw_name": "Lived in Greece (formerly Ex-pats – Greece)", "name": "Lived in Greece (formerly Ex-pats – Greece)", "type": "behaviors", "path": ["Behaviours", "Ex-pats"], "description": "People who used to live in Greece who now live abroad", "audience_size_lower_bound": 5865, "audience_size_upper_bound": 6898, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.585Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.585Z"}, {"_id": "67c56feccceafebe2cf7af33", "interest_id": "6019673808383", "raw_name": "Lived in Guatemala (formerly Ex-pats – Guatemala)", "name": "Lived in Guatemala (formerly Ex-pats – Guatemala)", "type": "behaviors", "path": ["Behaviours", "Ex-pats"], "description": "People who used to live in Guatemala who now live abroad", "audience_size_lower_bound": 2496793, "audience_size_upper_bound": 2936229, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.586Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.586Z"}, {"_id": "67c56feccceafebe2cf7af35", "interest_id": "6018797373783", "raw_name": "Lived in Haiti (formerly Expats – Haiti)", "name": "Lived in Haiti (formerly Expats – Haiti)", "type": "behaviors", "path": ["Behaviours", "Ex-pats"], "description": "People who used to live in Haiti who now live abroad", "audience_size_lower_bound": 1683424, "audience_size_upper_bound": 1979707, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.586Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.586Z"}, {"_id": "67c56feccceafebe2cf7af37", "interest_id": "6059793664583", "raw_name": "Lived in Honduras (formerly Ex-pats – Honduras)", "name": "Lived in Honduras (formerly Ex-pats – Honduras)", "type": "behaviors", "path": ["Behaviours", "Ex-pats"], "description": "People who used to live in Honduras who now live abroad", "audience_size_lower_bound": 1794332, "audience_size_upper_bound": 2110135, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.587Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.587Z"}, {"_id": "67c56feccceafebe2cf7af39", "interest_id": "6023676022783", "raw_name": "Lived in Hong Kong (formerly Ex-pats – Hong Kong)", "name": "Lived in Hong Kong (formerly Ex-pats – Hong Kong)", "type": "behaviors", "path": ["Behaviours", "Ex-pats"], "description": "People who used to live in Hong Kong who now live abroad", "audience_size_lower_bound": 753835, "audience_size_upper_bound": 886511, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.587Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.587Z"}, {"_id": "67c56feccceafebe2cf7af3b", "interest_id": "6019396638383", "raw_name": "Lived in Hungary (formerly Ex-pats – Hungary)", "name": "Lived in Hungary (formerly Ex-pats – Hungary)", "type": "behaviors", "path": ["Behaviours", "Ex-pats"], "description": "People who used to live in Hungary who now live abroad", "audience_size_lower_bound": 333772, "audience_size_upper_bound": 392517, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.588Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.588Z"}, {"_id": "67c56feccceafebe2cf7af3d", "interest_id": "6016916298983", "raw_name": "Lived in India (formerly Expats – India)", "name": "Lived in India (formerly Expats – India)", "type": "behaviors", "path": ["Behaviours", "Ex-pats"], "description": "People who used to live in India who now live abroad", "audience_size_lower_bound": 14179227, "audience_size_upper_bound": 16674772, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.588Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.588Z"}, {"_id": "67c56feccceafebe2cf7af3f", "interest_id": "6019564344583", "raw_name": "Lived in Indonesia (formerly Ex-pats – Indonesia)", "name": "Lived in Indonesia (formerly Ex-pats – Indonesia)", "type": "behaviors", "path": ["Behaviours", "Ex-pats"], "description": "People who used to live in Indonesia who now live abroad", "audience_size_lower_bound": 6340732, "audience_size_upper_bound": 7456702, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.589Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.589Z"}, {"_id": "67c56feccceafebe2cf7af41", "interest_id": "6019396650783", "raw_name": "Lived in Ireland (formerly Ex-pats – Ireland)", "name": "Lived in Ireland (formerly Ex-pats – Ireland)", "type": "behaviors", "path": ["Behaviours", "Ex-pats"], "description": "People who used to live in Ireland who now live abroad", "audience_size_lower_bound": 285335, "audience_size_upper_bound": 335555, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.589Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.589Z"}, {"_id": "67c56feccceafebe2cf7af43", "interest_id": "6025000823583", "raw_name": "Lived in Israel (formerly Ex-pats – Israel)", "name": "Lived in Israel (formerly Ex-pats – Israel)", "type": "behaviors", "path": ["Behaviours", "Ex-pats"], "description": "People who used to live in Israel who now live abroad", "audience_size_lower_bound": 583377, "audience_size_upper_bound": 686052, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.59Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.59Z"}, {"_id": "67c56feccceafebe2cf7af45", "interest_id": "6019396654583", "raw_name": "Lived in Italy (formerly Ex-pats – Italy)", "name": "Lived in Italy (formerly Ex-pats – Italy)", "type": "behaviors", "path": ["Behaviours", "Ex-pats"], "description": "People who used to live in Italy who now live abroad", "audience_size_lower_bound": 1745590, "audience_size_upper_bound": 2052815, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.59Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.59Z"}, {"_id": "67c56feccceafebe2cf7af47", "interest_id": "6023356956983", "raw_name": "Lived in Jamaica (formerly Ex-pats – Jamaica)", "name": "Lived in Jamaica (formerly Ex-pats – Jamaica)", "type": "behaviors", "path": ["Behaviours", "Ex-pats"], "description": "People who used to live in Jamaica who now live abroad", "audience_size_lower_bound": 730758, "audience_size_upper_bound": 859372, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.591Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.591Z"}, {"_id": "67c56feccceafebe2cf7af49", "interest_id": "6023676028783", "raw_name": "Lived in Japan (formerly Ex-pats – Japan)", "name": "Lived in Japan (formerly Ex-pats – Japan)", "type": "behaviors", "path": ["Behaviours", "Ex-pats"], "description": "People who used to live in Japan who now live abroad", "audience_size_lower_bound": 1643587, "audience_size_upper_bound": 1932859, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.591Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.591Z"}, {"_id": "67c56feccceafebe2cf7af4b", "interest_id": "6068843912183", "raw_name": "Lived in Jordan (formerly <PERSON><PERSON>pat<PERSON> – Jordan)", "name": "Lived in Jordan (formerly <PERSON><PERSON>pat<PERSON> – Jordan)", "type": "behaviors", "path": ["Behaviours", "Ex-pats"], "description": "People who used to live in Jordan who now live abroad", "audience_size_lower_bound": 806585, "audience_size_upper_bound": 948544, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.592Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.592Z"}, {"_id": "67c56feccceafebe2cf7af4d", "interest_id": "6018796980983", "raw_name": "Lived in Kenya (formerly Expats – Kenya)", "name": "Lived in Kenya (formerly Expats – Kenya)", "type": "behaviors", "path": ["Behaviours", "Ex-pats"], "description": "People who used to live in Kenya who now live abroad", "audience_size_lower_bound": 1962407, "audience_size_upper_bound": 2307791, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.592Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.592Z"}, {"_id": "67c56feccceafebe2cf7af4f", "interest_id": "6071248981583", "raw_name": "Lived in Kuwait (formerly Ex-pats – Kuwait)", "name": "Lived in Kuwait (formerly Ex-pats – Kuwait)", "type": "behaviors", "path": ["Behaviours", "Ex-pats"], "description": "People who used to live in Kuwait who now live abroad", "audience_size_lower_bound": 160632, "audience_size_upper_bound": 188904, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.593Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.593Z"}, {"_id": "67c56feccceafebe2cf7af51", "interest_id": "6068613839383", "raw_name": "Lived in Latvia (formerly Expats – Latvia)", "name": "Lived in Latvia (formerly Expats – Latvia)", "type": "behaviors", "path": ["Behaviours", "Ex-pats"], "description": "People who used to live in Latvia who now live abroad", "audience_size_lower_bound": 150796, "audience_size_upper_bound": 177337, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.593Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.593Z"}, {"_id": "67c56feccceafebe2cf7af53", "interest_id": "6068844014183", "raw_name": "Lived in Lebanon (formerly Ex-pats – Lebanon)", "name": "Lived in Lebanon (formerly Ex-pats – Lebanon)", "type": "behaviors", "path": ["Behaviours", "Ex-pats"], "description": "People who used to live in Lebanon who now live abroad", "audience_size_lower_bound": 804930, "audience_size_upper_bound": 946598, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.594Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.594Z"}, {"_id": "67c56feccceafebe2cf7af55", "interest_id": "6023676039183", "raw_name": "Lived in Lithuania (formerly Ex-pats – Lithuania)", "name": "Lived in Lithuania (formerly Ex-pats – Lithuania)", "type": "behaviors", "path": ["Behaviours", "Ex-pats"], "description": "People who used to live in Lithuania who now live abroad", "audience_size_lower_bound": 233113, "audience_size_upper_bound": 274142, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.594Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.594Z"}, {"_id": "67c56feccceafebe2cf7af57", "interest_id": "6023676044383", "raw_name": "Lived in Luxembourg (formerly Ex-pats – Luxembourg)", "name": "Lived in Luxembourg (formerly Ex-pats – Luxembourg)", "type": "behaviors", "path": ["Behaviours", "Ex-pats"], "description": "People who used to live in Luxembourg who now live abroad", "audience_size_lower_bound": 31459, "audience_size_upper_bound": 36996, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.595Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.595Z"}, {"_id": "67c56feccceafebe2cf7af59", "interest_id": "6027147160983", "raw_name": "Lived in Malaysia (formerly Ex-pats – Malaysia)", "name": "Lived in Malaysia (formerly Ex-pats – Malaysia)", "type": "behaviors", "path": ["Behaviours", "Ex-pats"], "description": "People who used to live in Malaysia who now live abroad", "audience_size_lower_bound": 2021990, "audience_size_upper_bound": 2377861, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.595Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.595Z"}, {"_id": "67c56feccceafebe2cf7af5b", "interest_id": "6023676045583", "raw_name": "Lived in Malta (formerly Expats – Malta)", "name": "Lived in Malta (formerly Expats – Malta)", "type": "behaviors", "path": ["Behaviours", "Ex-pats"], "description": "People who used to live in Malta who now live abroad", "audience_size_lower_bound": 35837, "audience_size_upper_bound": 42145, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.596Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.596Z"}, {"_id": "67c56feccceafebe2cf7af5d", "interest_id": "6023676072183", "raw_name": "Lived in Mexico (formerly Ex-pats – Mexico)", "name": "Lived in Mexico (formerly Ex-pats – Mexico)", "type": "behaviors", "path": ["Behaviours", "Ex-pats"], "description": "People who used to live in Mexico who now live abroad", "audience_size_lower_bound": 12705164, "audience_size_upper_bound": 14941273, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.596Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.596Z"}, {"_id": "67c56feccceafebe2cf7af5f", "interest_id": "6023676048183", "raw_name": "Lived in Monaco (formerly Ex-pats – Monaco)", "name": "Lived in Monaco (formerly Ex-pats – Monaco)", "type": "behaviors", "path": ["Behaviours", "Ex-pats"], "description": "People who used to live in Monaco who now live abroad", "audience_size_lower_bound": 26011, "audience_size_upper_bound": 30589, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.597Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.597Z"}, {"_id": "67c56feccceafebe2cf7af61", "interest_id": "6023516338783", "raw_name": "Lived in Morocco (formerly Ex-pats – Morocco)", "name": "Lived in Morocco (formerly Ex-pats – Morocco)", "type": "behaviors", "path": ["Behaviours", "Ex-pats"], "description": "People who used to live in Morocco who now live abroad", "audience_size_lower_bound": 2017498, "audience_size_upper_bound": 2372578, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.597Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.597Z"}, {"_id": "67c56feccceafebe2cf7af63", "interest_id": "6023356955383", "raw_name": "Lived in Nepal (formerly Ex-pats – Nepal)", "name": "Lived in Nepal (formerly Ex-pats – Nepal)", "type": "behaviors", "path": ["Behaviours", "Ex-pats"], "description": "People who used to live in Nepal who now live abroad", "audience_size_lower_bound": 4253042, "audience_size_upper_bound": 5001578, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.597Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.597Z"}, {"_id": "67c56feccceafebe2cf7af65", "interest_id": "6023516368383", "raw_name": "Lived in New Zealand (formerly Ex-pats – New Zealand)", "name": "Lived in New Zealand (formerly Ex-pats – New Zealand)", "type": "behaviors", "path": ["Behaviours", "Ex-pats"], "description": "People who used to live in New Zealand who now live abroad", "audience_size_lower_bound": 1233026, "audience_size_upper_bound": 1450039, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.598Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.598Z"}, {"_id": "67c56feccceafebe2cf7af67", "interest_id": "6071248894383", "raw_name": "Lived in Nicaragua (formerly Ex-pats – Nicaragua)", "name": "Lived in Nicaragua (formerly Ex-pats – Nicaragua)", "type": "behaviors", "path": ["Behaviours", "Ex-pats"], "description": "People who used to live in Nicaragua who now live abroad", "audience_size_lower_bound": 1010822, "audience_size_upper_bound": 1188727, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.598Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.598Z"}, {"_id": "67c56feccceafebe2cf7af69", "interest_id": "6018797004183", "raw_name": "Lived in Nigeria (formerly Expats – Nigeria)", "name": "Lived in Nigeria (formerly Expats – Nigeria)", "type": "behaviors", "path": ["Behaviours", "Ex-pats"], "description": "People who used to live in Nigeria who now live abroad", "audience_size_lower_bound": 2900832, "audience_size_upper_bound": 3411379, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.599Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.599Z"}, {"_id": "67c56feccceafebe2cf7af6b", "interest_id": "6023287459983", "raw_name": "Lived in Norway (formerly Ex-pats – Norway)", "name": "Lived in Norway (formerly Ex-pats – Norway)", "type": "behaviors", "path": ["Behaviours", "Ex-pats"], "description": "People who used to live in Norway who now live abroad", "audience_size_lower_bound": 183495, "audience_size_upper_bound": 215791, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.599Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.599Z"}, {"_id": "67c56feccceafebe2cf7af6d", "interest_id": "6027149008183", "raw_name": "Lived in Peru (formerly Ex-pats – Peru)", "name": "Lived in Peru (formerly Ex-pats – Peru)", "type": "behaviors", "path": ["Behaviours", "Ex-pats"], "description": "People who used to live in Peru who now live abroad", "audience_size_lower_bound": 1835395, "audience_size_upper_bound": 2158425, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.6Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.6Z"}, {"_id": "67c56feccceafebe2cf7af6f", "interest_id": "6018797091183", "raw_name": "Lived in Philippines (formerly Expats – Philippines)", "name": "Lived in Philippines (formerly Expats – Philippines)", "type": "behaviors", "path": ["Behaviours", "Ex-pats"], "description": "People who used to live in the Philippines who now live abroad", "audience_size_lower_bound": 10378706, "audience_size_upper_bound": 12205359, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.6Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.6Z"}, {"_id": "67c56feccceafebe2cf7af71", "interest_id": "6019396657183", "raw_name": "Lived in Poland (formerly Ex-pats – Poland)", "name": "Lived in Poland (formerly Ex-pats – Poland)", "type": "behaviors", "path": ["Behaviours", "Ex-pats"], "description": "People who used to live in Poland who now live abroad", "audience_size_lower_bound": 1362528, "audience_size_upper_bound": 1602334, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.601Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.601Z"}, {"_id": "67c56feccceafebe2cf7af73", "interest_id": "6021354882783", "raw_name": "Lived in Portugal (formerly Ex-pats – Portugal)", "name": "Lived in Portugal (formerly Ex-pats – Portugal)", "type": "behaviors", "path": ["Behaviours", "Ex-pats"], "description": "People who used to live in Portugal who now live abroad", "audience_size_lower_bound": 991910, "audience_size_upper_bound": 1166487, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.601Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.601Z"}, {"_id": "67c56feccceafebe2cf7af75", "interest_id": "6019520122583", "raw_name": "Lived in Puerto Rico (formerly Ex-pats – Puerto Rico)", "name": "Lived in Puerto Rico (formerly Ex-pats – Puerto Rico)", "type": "behaviors", "path": ["Behaviours", "Ex-pats"], "description": "People who used to live in Puerto Rico who now live abroad", "audience_size_lower_bound": 1149488, "audience_size_upper_bound": 1351798, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.602Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.602Z"}, {"_id": "67c56feccceafebe2cf7af77", "interest_id": "6071249058983", "raw_name": "Lived in Qatar (formerly Ex-pats – Qatar)", "name": "Lived in Qatar (formerly Ex-pats – Qatar)", "type": "behaviors", "path": ["Behaviours", "Ex-pats"], "description": "People who used to live in Qatar who now live abroad", "audience_size_lower_bound": 127126, "audience_size_upper_bound": 149501, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.602Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.602Z"}, {"_id": "67c56feccceafebe2cf7af79", "interest_id": "6027148962983", "raw_name": "Lived in Romania (formerly Ex-pats – Romania)", "name": "Lived in Romania (formerly Ex-pats – Romania)", "type": "behaviors", "path": ["Behaviours", "Ex-pats"], "description": "People who used to live in Romania who now live abroad", "audience_size_lower_bound": 1705450, "audience_size_upper_bound": 2005610, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.603Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.603Z"}, {"_id": "67c56feccceafebe2cf7af7b", "interest_id": "6025000815983", "raw_name": "Lived in Russia (formerly Ex-pats – Russia)", "name": "Lived in Russia (formerly Ex-pats – Russia)", "type": "behaviors", "path": ["Behaviours", "Ex-pats"], "description": "People who used to live in Russia who now live abroad", "audience_size_lower_bound": 1282361, "audience_size_upper_bound": 1508057, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.603Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.603Z"}, {"_id": "67c56feccceafebe2cf7af7d", "interest_id": "6025670492783", "raw_name": "Lived in Rwanda (formerly Ex-pats – Rwanda)", "name": "Lived in Rwanda (formerly Ex-pats – Rwanda)", "type": "behaviors", "path": ["Behaviours", "Ex-pats"], "description": "People who used to live in Rwanda who now live abroad", "audience_size_lower_bound": 145462, "audience_size_upper_bound": 171064, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.604Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.604Z"}, {"_id": "67c56feccceafebe2cf7af7f", "interest_id": "6025000813183", "raw_name": "Lived in Saudi Arabia (formerly Ex-pats – Saudi Arabia)", "name": "Lived in Saudi Arabia (formerly Ex-pats – Saudi Arabia)", "type": "behaviors", "path": ["Behaviours", "Ex-pats"], "description": "People who used to live in the Kingdom of Saudi Arabia who now live abroad", "audience_size_lower_bound": 1257357, "audience_size_upper_bound": 1478652, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.604Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.604Z"}, {"_id": "67c56feccceafebe2cf7af81", "interest_id": "6023357000583", "raw_name": "Lived in Senegal (formerly Ex-pats – Senegal)", "name": "Lived in Senegal (formerly Ex-pats – Senegal)", "type": "behaviors", "path": ["Behaviours", "Ex-pats"], "description": "People who used to live in Senegal who now live abroad", "audience_size_lower_bound": 490602, "audience_size_upper_bound": 576948, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.605Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.605Z"}, {"_id": "67c56feccceafebe2cf7af83", "interest_id": "6027149004983", "raw_name": "Lived in Serbia (formerly Expats – Serbia)", "name": "Lived in Serbia (formerly Expats – Serbia)", "type": "behaviors", "path": ["Behaviours", "Ex-pats"], "description": "People who used to live in Serbia who now live abroad", "audience_size_lower_bound": 572851, "audience_size_upper_bound": 673673, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.605Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.605Z"}, {"_id": "67c56feccceafebe2cf7af85", "interest_id": "6023356986383", "raw_name": "Lived in Sierra Leone (formerly Expats – Sierra Leone)", "name": "Lived in Sierra Leone (formerly Expats – Sierra Leone)", "type": "behaviors", "path": ["Behaviours", "Ex-pats"], "description": "People who used to live in Sierra Leone who now live abroad", "audience_size_lower_bound": 253663, "audience_size_upper_bound": 298308, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.606Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.606Z"}, {"_id": "67c56feccceafebe2cf7af87", "interest_id": "6023516403783", "raw_name": "Lived in Singapore (formerly Ex-pats – Singapore)", "name": "Lived in Singapore (formerly Ex-pats – Singapore)", "type": "behaviors", "path": ["Behaviours", "Ex-pats"], "description": "People who used to live in Singapore who now live abroad", "audience_size_lower_bound": 96059, "audience_size_upper_bound": 112966, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.606Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.606Z"}, {"_id": "67c56feccceafebe2cf7af89", "interest_id": "6023676055383", "raw_name": "Lived in Slovakia (formerly Ex-pats – Slovakia)", "name": "Lived in Slovakia (formerly Ex-pats – Slovakia)", "type": "behaviors", "path": ["Behaviours", "Ex-pats"], "description": "People who used to live in Slovakia who now live abroad", "audience_size_lower_bound": 244506, "audience_size_upper_bound": 287540, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.607Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.607Z"}, {"_id": "67c56feccceafebe2cf7af8b", "interest_id": "6023676060183", "raw_name": "Lived in Slovenia (formerly Ex-pats – Slovenia)", "name": "Lived in Slovenia (formerly Ex-pats – Slovenia)", "type": "behaviors", "path": ["Behaviours", "Ex-pats"], "description": "People who used to live in Slovenia who now live abroad", "audience_size_lower_bound": 57702, "audience_size_upper_bound": 67858, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.607Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.607Z"}, {"_id": "67c56feccceafebe2cf7af8d", "interest_id": "6019564383383", "raw_name": "Lived in South Africa (formerly Expats – South Africa)", "name": "Lived in South Africa (formerly Expats – South Africa)", "type": "behaviors", "path": ["Behaviours", "Ex-pats"], "description": "People who used to live in South Africa who now live abroad", "audience_size_lower_bound": 1355831, "audience_size_upper_bound": 1594458, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.608Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.608Z"}, {"_id": "67c56feccceafebe2cf7af8f", "interest_id": "6027148973583", "raw_name": "Lived in South Korea (formerly Ex-pats – South Korea)", "name": "Lived in South Korea (formerly Ex-pats – South Korea)", "type": "behaviors", "path": ["Behaviours", "Ex-pats"], "description": "People who used to live in South Korea who now live abroad", "audience_size_lower_bound": 3462636, "audience_size_upper_bound": 4072060, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.609Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.609Z"}, {"_id": "67c56feccceafebe2cf7af91", "interest_id": "6019366943583", "raw_name": "Lived in Spain (formerly Expats – Spain)", "name": "Lived in Spain (formerly Expats – Spain)", "type": "behaviors", "path": ["Behaviours", "Ex-pats"], "description": "People who used to live in Spain who now live abroad", "audience_size_lower_bound": 1759710, "audience_size_upper_bound": 2069420, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.609Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.609Z"}, {"_id": "67c56feccceafebe2cf7af93", "interest_id": "6023516315983", "raw_name": "Lived in Sri Lanka (formerly Ex-pats – Sri Lanka)", "name": "Lived in Sri Lanka (formerly Ex-pats – Sri Lanka)", "type": "behaviors", "path": ["Behaviours", "Ex-pats"], "description": "People who used to live in Sri Lanka who now live abroad", "audience_size_lower_bound": 1130702, "audience_size_upper_bound": 1329706, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.61Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.61Z"}, {"_id": "67c56feccceafebe2cf7af95", "interest_id": "6023287397383", "raw_name": "Lived in Sweden (formerly Expats – Sweden)", "name": "Lived in Sweden (formerly Expats – Sweden)", "type": "behaviors", "path": ["Behaviours", "Ex-pats"], "description": "People who used to live in Sweden who now live abroad", "audience_size_lower_bound": 272002, "audience_size_upper_bound": 319875, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.61Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.61Z"}, {"_id": "67c56feccceafebe2cf7af97", "interest_id": "6019377644783", "raw_name": "Lived in Switzerland (formerly Ex-pats – Switzerland)", "name": "Lived in Switzerland (formerly Ex-pats – Switzerland)", "type": "behaviors", "path": ["Behaviours", "Ex-pats"], "description": "People who used to live in Switzerland who now live abroad", "audience_size_lower_bound": 307558, "audience_size_upper_bound": 361689, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.611Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.611Z"}, {"_id": "67c56feccceafebe2cf7af99", "interest_id": "6023356926183", "raw_name": "Lived in Tanzania (formerly Expats – Tanzania)", "name": "Lived in Tanzania (formerly Expats – Tanzania)", "type": "behaviors", "path": ["Behaviours", "Ex-pats"], "description": "People who used to live in Tanzania who now live abroad", "audience_size_lower_bound": 577840, "audience_size_upper_bound": 679541, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.611Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.611Z"}, {"_id": "67c56feccceafebe2cf7af9b", "interest_id": "6023356966183", "raw_name": "Lived in Thailand (formerly Ex-pats – Thailand)", "name": "Lived in Thailand (formerly Ex-pats – Thailand)", "type": "behaviors", "path": ["Behaviours", "Ex-pats"], "description": "People who used to live in Thailand who now live abroad", "audience_size_lower_bound": 2078394, "audience_size_upper_bound": 2444192, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.612Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.612Z"}, {"_id": "67c56feccceafebe2cf7af9d", "interest_id": "6023516430783", "raw_name": "Lived in UAE (formerly Ex-pats – UAE)", "name": "Lived in UAE (formerly Ex-pats – UAE)", "type": "behaviors", "path": ["Behaviours", "Ex-pats"], "description": "People who used to live in the United Arab Emirates who now live abroad", "audience_size_lower_bound": 1868838, "audience_size_upper_bound": 2197754, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.612Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.612Z"}, {"_id": "67c56feccceafebe2cf7af9f", "interest_id": "6019673501783", "raw_name": "Lived in Uganda (formerly Ex-pats – Uganda)", "name": "Lived in Uganda (formerly Ex-pats – Uganda)", "type": "behaviors", "path": ["Behaviours", "Ex-pats"], "description": "People who used to live in Uganda who now live abroad", "audience_size_lower_bound": 589215, "audience_size_upper_bound": 692917, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.613Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.613Z"}, {"_id": "67c56feccceafebe2cf7afa1", "interest_id": "6026404871583", "raw_name": "Lived in Venezuela (formerly Expats – Venezuela)", "name": "Lived in Venezuela (formerly Expats – Venezuela)", "type": "behaviors", "path": ["Behaviours", "Ex-pats"], "description": "People who used to live in Venezuela who now live abroad", "audience_size_lower_bound": 5473758, "audience_size_upper_bound": 6437140, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.614Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.614Z"}, {"_id": "67c56feccceafebe2cf7afa3", "interest_id": "6027149006383", "raw_name": "Lived in Vietnam (formerly Ex-pats – Vietnam)", "name": "Lived in Vietnam (formerly Ex-pats – Vietnam)", "type": "behaviors", "path": ["Behaviours", "Ex-pats"], "description": "People who used to live in Vietnam who now live abroad", "audience_size_lower_bound": 4661766, "audience_size_upper_bound": 5482237, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.614Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.614Z"}, {"_id": "67c56feccceafebe2cf7afa5", "interest_id": "6047219032183", "raw_name": "Lived in Zambia (formerly Ex-pats – Zambia)", "name": "Lived in Zambia (formerly Ex-pats – Zambia)", "type": "behaviors", "path": ["Behaviours", "Ex-pats"], "description": "People who used to live in Zambia who now live abroad", "audience_size_lower_bound": 233250, "audience_size_upper_bound": 274302, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.614Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.614Z"}, {"_id": "67c56feccceafebe2cf7afa7", "interest_id": "6019673233983", "raw_name": "Lived in Zimbabwe (formerly Ex-pats – Zimbabwe)", "name": "Lived in Zimbabwe (formerly Ex-pats – Zimbabwe)", "type": "behaviors", "path": ["Behaviours", "Ex-pats"], "description": "People who used to live in Zimbabwe who now live abroad", "audience_size_lower_bound": 987031, "audience_size_upper_bound": 1160749, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.615Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.615Z"}, {"_id": "67c56feccceafebe2cf7afa9", "interest_id": "6023287393783", "raw_name": "Lived in the Netherlands (formerly Ex-pats – the Netherlands)", "name": "Lived in the Netherlands (formerly Ex-pats – the Netherlands)", "type": "behaviors", "path": ["Behaviours", "Ex-pats"], "description": "People who used to live in the Netherlands who now live abroad", "audience_size_lower_bound": 462463, "audience_size_upper_bound": 543857, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.615Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.615Z"}, {"_id": "67c56feccceafebe2cf7afab", "interest_id": "6021354152983", "raw_name": "Lived in the UK (formerly Ex-pats – UK)", "name": "Lived in the UK (formerly Ex-pats – UK)", "type": "behaviors", "path": ["Behaviours", "Ex-pats"], "description": "People who used to live in the United Kingdom who now live abroad", "audience_size_lower_bound": 6209401, "audience_size_upper_bound": 7302256, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.616Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.616Z"}, {"_id": "67c56feccceafebe2cf7afad", "interest_id": "6019396649183", "raw_name": "Lived in the United States (formerly Ex-pats – United States)", "name": "Lived in the United States (formerly Ex-pats – United States)", "type": "behaviors", "path": ["Behaviours", "Ex-pats"], "description": "People who used to live in the United States who now live abroad", "audience_size_lower_bound": 19198296, "audience_size_upper_bound": 22577197, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.616Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.616Z"}, {"_id": "67c56feccceafebe2cf7afaf", "interest_id": "6015559470583", "raw_name": "Lives abroad", "name": "Lives abroad", "type": "behaviors", "path": ["Behaviours", "Ex-pats"], "description": "People living outside their home country", "audience_size_lower_bound": 222786741, "audience_size_upper_bound": 261997208, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.617Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.617Z"}, {"_id": "67c56feccceafebe2cf7afb1", "interest_id": "6016670853583", "raw_name": "Facebook FTE [FB_only]", "name": "Facebook FTE [FB_only]", "type": "behaviors", "path": ["Behaviours", "Meta Internal Only"], "description": "This cluster contains all of the current full-time employees at Facebook.", "audience_size_lower_bound": 46957, "audience_size_upper_bound": 55222, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.618Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.618Z"}, {"_id": "67c56feccceafebe2cf7afb3", "interest_id": "6008868254383", "raw_name": "Owns: Kindle Fire", "name": "Owns: Kindle Fire", "type": "behaviors", "path": ["Behaviours", "Mobile Device User", "All Mobile Devices by Brand", "Amazon"], "description": "People who are likely to own a Kindle Fire", "audience_size_lower_bound": 1085, "audience_size_upper_bound": 1276, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.618Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.618Z"}, {"_id": "67c56feccceafebe2cf7afb5", "interest_id": "6004383767972", "raw_name": "Facebook access (mobile): iPad 1", "name": "Facebook access (mobile): iPad 1", "type": "behaviors", "path": ["Behaviours", "Mobile Device User", "All Mobile Devices by Brand", "Apple"], "description": "People who primarily access Facebook using an iPad 1 mobile device", "audience_size_lower_bound": 5130, "audience_size_upper_bound": 6033, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.619Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.619Z"}, {"_id": "67c56feccceafebe2cf7afb7", "interest_id": "6004383808772", "raw_name": "Facebook access (mobile): iPad 2", "name": "Facebook access (mobile): iPad 2", "type": "behaviors", "path": ["Behaviours", "Mobile Device User", "All Mobile Devices by Brand", "Apple"], "description": "People who primarily access Facebook using an iPad 2 mobile device", "audience_size_lower_bound": 1545, "audience_size_upper_bound": 1817, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.619Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.619Z"}, {"_id": "67c56feccceafebe2cf7afb9", "interest_id": "6004383806772", "raw_name": "Facebook access (mobile): iPad 3", "name": "Facebook access (mobile): iPad 3", "type": "behaviors", "path": ["Behaviours", "Mobile Device User", "All Mobile Devices by Brand", "Apple"], "description": "People who primarily access Facebook using an iPad 3 mobile device.", "audience_size_lower_bound": 40835, "audience_size_upper_bound": 48023, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.62Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.62Z"}, {"_id": "67c56feccceafebe2cf7afbb", "interest_id": "6004383941372", "raw_name": "Facebook access (mobile): iPhone 4", "name": "Facebook access (mobile): iPhone 4", "type": "behaviors", "path": ["Behaviours", "Mobile Device User", "All Mobile Devices by Brand", "Apple"], "description": "People who primarily access Facebook using an iPhone 4 mobile device.", "audience_size_lower_bound": 1207, "audience_size_upper_bound": 1420, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.62Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.62Z"}, {"_id": "67c56feccceafebe2cf7afbd", "interest_id": "6004386303972", "raw_name": "Facebook access (mobile): iPhone 4S", "name": "Facebook access (mobile): iPhone 4S", "type": "behaviors", "path": ["Behaviours", "Mobile Device User", "All Mobile Devices by Brand", "Apple"], "description": "People who primarily access Facebook using an iPhone 4S mobile device.", "audience_size_lower_bound": 3184, "audience_size_upper_bound": 3745, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.621Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.621Z"}, {"_id": "67c56feccceafebe2cf7afbf", "interest_id": "6004883585572", "raw_name": "Facebook access (mobile): iPhone 5", "name": "Facebook access (mobile): iPhone 5", "type": "behaviors", "path": ["Behaviours", "Mobile Device User", "All Mobile Devices by Brand", "Apple"], "description": "People who primarily access Facebook using an iPhone 5 mobile device", "audience_size_lower_bound": 19414, "audience_size_upper_bound": 22831, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.621Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.621Z"}, {"_id": "67c56feccceafebe2cf7afc1", "interest_id": "6010095794383", "raw_name": "Facebook access (mobile): iPhone 5C", "name": "Facebook access (mobile): iPhone 5C", "type": "behaviors", "path": ["Behaviours", "Mobile Device User", "All Mobile Devices by Brand", "Apple"], "description": "People who primarily access Facebook using an iPhone 5C mobile device", "audience_size_lower_bound": 8727, "audience_size_upper_bound": 10263, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.622Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.622Z"}, {"_id": "67c56feccceafebe2cf7afc3", "interest_id": "6010095777183", "raw_name": "Facebook access (mobile): iPhone 5S", "name": "Facebook access (mobile): iPhone 5S", "type": "behaviors", "path": ["Behaviours", "Mobile Device User", "All Mobile Devices by Brand", "Apple"], "description": "People who primarily access Facebook using an iPhone 5S mobile device.", "audience_size_lower_bound": 225716, "audience_size_upper_bound": 265443, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.622Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.622Z"}, {"_id": "67c56feccceafebe2cf7afc5", "interest_id": "6092512412783", "raw_name": "Facebook access (mobile): iPhone 8", "name": "Facebook access (mobile): iPhone 8", "type": "behaviors", "path": ["Behaviours", "Mobile Device User", "All Mobile Devices by Brand", "Apple"], "description": "People who are likely to own an iPhone 8 mobile device.", "audience_size_lower_bound": 5974016, "audience_size_upper_bound": 7025443, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.623Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.623Z"}, {"_id": "67c56feccceafebe2cf7afc7", "interest_id": "6092512424583", "raw_name": "Facebook access (mobile): iPhone 8 Plus", "name": "Facebook access (mobile): iPhone 8 Plus", "type": "behaviors", "path": ["Behaviours", "Mobile Device User", "All Mobile Devices by Brand", "Apple"], "description": "People who are likely to own an iPhone 8 Plus mobile device", "audience_size_lower_bound": 9097078, "audience_size_upper_bound": 10698164, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.623Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.623Z"}, {"_id": "67c56feccceafebe2cf7afc9", "interest_id": "6092512462983", "raw_name": "Facebook access (mobile): iPhone X", "name": "Facebook access (mobile): iPhone X", "type": "behaviors", "path": ["Behaviours", "Mobile Device User", "All Mobile Devices by Brand", "Apple"], "description": "People who are likely to own an iPhone X mobile device", "audience_size_lower_bound": 9174928, "audience_size_upper_bound": 10789716, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.624Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.624Z"}, {"_id": "67c56feccceafebe2cf7afcb", "interest_id": "6120699725783", "raw_name": "Facebook access (mobile): iPhone XR", "name": "Facebook access (mobile): iPhone XR", "type": "behaviors", "path": ["Behaviours", "Mobile Device User", "All Mobile Devices by Brand", "Apple"], "description": "People who are likely to own an iPhone XR mobile device", "audience_size_lower_bound": 19022358, "audience_size_upper_bound": 22370294, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.624Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.624Z"}, {"_id": "67c56feccceafebe2cf7afcd", "interest_id": "6120699687383", "raw_name": "Facebook access (mobile): iPhone XS", "name": "Facebook access (mobile): iPhone XS", "type": "behaviors", "path": ["Behaviours", "Mobile Device User", "All Mobile Devices by Brand", "Apple"], "description": "People who are likely to own an iPhone XS mobile device", "audience_size_lower_bound": 5843034, "audience_size_upper_bound": 6871408, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.625Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.625Z"}, {"_id": "67c56feccceafebe2cf7afcf", "interest_id": "6120699721983", "raw_name": "Facebook access (mobile): iPhone XS Max", "name": "Facebook access (mobile): iPhone XS Max", "type": "behaviors", "path": ["Behaviours", "Mobile Device User", "All Mobile Devices by Brand", "Apple"], "description": "People who are likely to own an iPhone XS Max mobile device", "audience_size_lower_bound": 8351876, "audience_size_upper_bound": 9821807, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.625Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.625Z"}, {"_id": "67c56feccceafebe2cf7afd1", "interest_id": "6004383890572", "raw_name": "Facebook access (mobile): iPod Touch", "name": "Facebook access (mobile): iPod Touch", "type": "behaviors", "path": ["Behaviours", "Mobile Device User", "All Mobile Devices by Brand", "Apple"], "description": "People who primarily access Facebook using an iPod Touch mobile device", "audience_size_lower_bound": 69066, "audience_size_upper_bound": 81222, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.626Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.626Z"}, {"_id": "67c56feccceafebe2cf7afd3", "interest_id": "6011191254383", "raw_name": "Owns: iPad 4", "name": "Owns: iPad 4", "type": "behaviors", "path": ["Behaviours", "Mobile Device User", "All Mobile Devices by Brand", "Apple"], "description": "People who are likely to own an iPad 4", "audience_size_lower_bound": 197424, "audience_size_upper_bound": 232171, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.627Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.627Z"}, {"_id": "67c56feccceafebe2cf7afd5", "interest_id": "6011244513583", "raw_name": "Owns: iPad Air", "name": "Owns: iPad Air", "type": "behaviors", "path": ["Behaviours", "Mobile Device User", "All Mobile Devices by Brand", "Apple"], "description": "People who are likely to own an iPad Air.", "audience_size_lower_bound": 643938, "audience_size_upper_bound": 757272, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.627Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.627Z"}, {"_id": "67c56feccceafebe2cf7afd7", "interest_id": "6018995113183", "raw_name": "Owns: iPad Air 2", "name": "Owns: iPad Air 2", "type": "behaviors", "path": ["Behaviours", "Mobile Device User", "All Mobile Devices by Brand", "Apple"], "description": "People who are likely to own an iPad Air 2 mobile device", "audience_size_lower_bound": 1379026, "audience_size_upper_bound": 1621735, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.628Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.628Z"}, {"_id": "67c56feccceafebe2cf7afd9", "interest_id": "6011191259183", "raw_name": "Owns: iPad Mini 1", "name": "Owns: iPad Mini 1", "type": "behaviors", "path": ["Behaviours", "Mobile Device User", "All Mobile Devices by Brand", "Apple"], "description": "People who are likely to own an iPad Mini 1", "audience_size_lower_bound": 96051, "audience_size_upper_bound": 112956, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.628Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.628Z"}, {"_id": "67c56feccceafebe2cf7afdb", "interest_id": "6011244510983", "raw_name": "Owns: iPad Mini 2", "name": "Owns: iPad Mini 2", "type": "behaviors", "path": ["Behaviours", "Mobile Device User", "All Mobile Devices by Brand", "Apple"], "description": "People who are likely to own an iPad Mini 2.", "audience_size_lower_bound": 357550, "audience_size_upper_bound": 420479, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.629Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.629Z"}, {"_id": "67c56feccceafebe2cf7afdd", "interest_id": "6019098117583", "raw_name": "Owns: iPad Mini 3", "name": "Owns: iPad Mini 3", "type": "behaviors", "path": ["Behaviours", "Mobile Device User", "All Mobile Devices by Brand", "Apple"], "description": "People who are likely to own an iPad Mini 3 mobile device.", "audience_size_lower_bound": 77639, "audience_size_upper_bound": 91304, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.63Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.63Z"}, {"_id": "67c56feccceafebe2cf7afdf", "interest_id": "6017831572183", "raw_name": "Owns: iPhone 6", "name": "Owns: iPhone 6", "type": "behaviors", "path": ["Behaviours", "Mobile Device User", "All Mobile Devices by Brand", "Apple"], "description": "People who are likely to own an iPhone 6 mobile device", "audience_size_lower_bound": 1572581, "audience_size_upper_bound": 1849356, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.63Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.63Z"}, {"_id": "67c56feccceafebe2cf7afe1", "interest_id": "6017831560783", "raw_name": "Owns: iPhone 6 Plus", "name": "Owns: iPhone 6 Plus", "type": "behaviors", "path": ["Behaviours", "Mobile Device User", "All Mobile Devices by Brand", "Apple"], "description": "People who are likely to own an Apple iPhone 6 Plus mobile device", "audience_size_lower_bound": 725672, "audience_size_upper_bound": 853391, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.631Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.631Z"}, {"_id": "67c56feccceafebe2cf7afe3", "interest_id": "6031259562783", "raw_name": "Owns: iPhone 6S", "name": "Owns: iPhone 6S", "type": "behaviors", "path": ["Behaviours", "Mobile Device User", "All Mobile Devices by Brand", "Apple"], "description": "People who are likely to own an iPhone 6S mobile device", "audience_size_lower_bound": 3512320, "audience_size_upper_bound": 4130489, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.631Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.631Z"}, {"_id": "67c56feccceafebe2cf7afe5", "interest_id": "6031259590183", "raw_name": "Owns: iPhone 6S Plus", "name": "Owns: iPhone 6S Plus", "type": "behaviors", "path": ["Behaviours", "Mobile Device User", "All Mobile Devices by Brand", "Apple"], "description": "People who are likely to own an iPhone 6S Plus mobile device.", "audience_size_lower_bound": 2880333, "audience_size_upper_bound": 3387272, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.632Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.632Z"}, {"_id": "67c56feccceafebe2cf7afe7", "interest_id": "6060616578383", "raw_name": "Owns: iPhone 7", "name": "Owns: iPhone 7", "type": "behaviors", "path": ["Behaviours", "Mobile Device User", "All Mobile Devices by Brand", "Apple"], "description": "People who are likely to own an iPhone 7 mobile device", "audience_size_lower_bound": 6803489, "audience_size_upper_bound": 8000904, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.632Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.632Z"}, {"_id": "67c56feccceafebe2cf7afe9", "interest_id": "6060616598183", "raw_name": "Owns: iPhone 7 Plus", "name": "Owns: iPhone 7 Plus", "type": "behaviors", "path": ["Behaviours", "Mobile Device User", "All Mobile Devices by Brand", "Apple"], "description": "People who are likely to own an iPhone 7 Plus mobile device.", "audience_size_lower_bound": 9014953, "audience_size_upper_bound": 10601585, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.633Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.633Z"}, {"_id": "67c56feccceafebe2cf7afeb", "interest_id": "6054947014783", "raw_name": "Owns: iPhone SE", "name": "Owns: iPhone SE", "type": "behaviors", "path": ["Behaviours", "Mobile Device User", "All Mobile Devices by Brand", "Apple"], "description": "People who likely own an iPhone SE mobile device", "audience_size_lower_bound": 13318225, "audience_size_upper_bound": 15662233, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.634Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.634Z"}, {"_id": "67c56feccceafebe2cf7afed", "interest_id": "6013017297383", "raw_name": "Curve 9220", "name": "Curve 9220", "type": "behaviors", "path": ["Behaviours", "Mobile Device User", "All Mobile Devices by Brand", "BlackBerry"], "description": "", "audience_size_lower_bound": 2, "audience_size_upper_bound": 3, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.634Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.634Z"}, {"_id": "67c56feccceafebe2cf7afef", "interest_id": "6004385886572", "raw_name": "Facebook access (mobile): HTC Android mobile devices", "name": "Facebook access (mobile): HTC Android mobile devices", "type": "behaviors", "path": ["Behaviours", "Mobile Device User", "All Mobile Devices by Brand"], "description": "People who primarily access Facebook using an HTC Android mobile device.", "audience_size_lower_bound": 527130, "audience_size_upper_bound": 619905, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.634Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.634Z"}, {"_id": "67c56feccceafebe2cf7aff1", "interest_id": "6004385879572", "raw_name": "Facebook access (mobile): Motorola Android mobile devices", "name": "Facebook access (mobile): Motorola Android mobile devices", "type": "behaviors", "path": ["Behaviours", "Mobile Device User", "All Mobile Devices by Brand"], "description": "People who primarily access Facebook using a Motorola Android mobile device", "audience_size_lower_bound": 87084949, "audience_size_upper_bound": 102411901, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.635Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.635Z"}, {"_id": "67c56feccceafebe2cf7aff3", "interest_id": "6004386010572", "raw_name": "Facebook access (mobile): Samsung Android mobile devices", "name": "Facebook access (mobile): Samsung Android mobile devices", "type": "behaviors", "path": ["Behaviours", "Mobile Device User", "All Mobile Devices by Brand"], "description": "People who primarily access Facebook using a Samsung Android mobile device", "audience_size_lower_bound": 625327623, "audience_size_upper_bound": 735385285, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.636Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.636Z"}, {"_id": "67c56feccceafebe2cf7aff5", "interest_id": "6004385865172", "raw_name": "Facebook access (mobile): Sony Android mobile devices", "name": "Facebook access (mobile): Sony Android mobile devices", "type": "behaviors", "path": ["Behaviours", "Mobile Device User", "All Mobile Devices by Brand"], "description": "People who primarily access Facebook using a Sony Android mobile device", "audience_size_lower_bound": 5034576, "audience_size_upper_bound": 5920662, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.636Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.636Z"}, {"_id": "67c56feccceafebe2cf7aff7", "interest_id": "6004385868372", "raw_name": "Facebook access(mobile): LG Android mobile devices", "name": "Facebook access(mobile): LG Android mobile devices", "type": "behaviors", "path": ["Behaviours", "Mobile Device User", "All Mobile Devices by Brand"], "description": "People who primarily access Facebook using an LG Android mobile device", "audience_size_lower_bound": 6557626, "audience_size_upper_bound": 7711769, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.637Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.637Z"}, {"_id": "67c56feccceafebe2cf7aff9", "interest_id": "6061668174383", "raw_name": "Owns: Google Pixel", "name": "Owns: Google Pixel", "type": "behaviors", "path": ["Behaviours", "Mobile Device User", "All Mobile Devices by Brand", "Google"], "description": "People who are likely to own a Google Pixel mobile device", "audience_size_lower_bound": 34017, "audience_size_upper_bound": 40004, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.637Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.637Z"}, {"_id": "67c56feccceafebe2cf7affb", "interest_id": "6014809400783", "raw_name": "Owns: Nexus 5", "name": "Owns: Nexus 5", "type": "behaviors", "path": ["Behaviours", "Mobile Device User", "All Mobile Devices by Brand", "Google"], "description": "People who are likely to own a Nexus 5 mobile device.", "audience_size_lower_bound": 157556, "audience_size_upper_bound": 185287, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.638Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.638Z"}, {"_id": "67c56feccceafebe2cf7affd", "interest_id": "6014809859183", "raw_name": "Owns: HTC One", "name": "Owns: HTC One", "type": "behaviors", "path": ["Behaviours", "Mobile Device User", "All Mobile Devices by Brand", "HTC"], "description": "People who are likely to own an HTC One mobile device", "audience_size_lower_bound": 65391, "audience_size_upper_bound": 76900, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.638Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.638Z"}, {"_id": "67c56feccceafebe2cf7afff", "interest_id": "6010231666183", "raw_name": "Owns: LG G2 devices", "name": "Owns: LG G2 devices", "type": "behaviors", "path": ["Behaviours", "Mobile Device User", "All Mobile Devices by Brand", "LG"], "description": "People who are likely to own LG G2 devices", "audience_size_lower_bound": 15186, "audience_size_upper_bound": 17859, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.639Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.639Z"}, {"_id": "67c56feccceafebe2cf7b001", "interest_id": "6017535283383", "raw_name": "Owns: LG G3", "name": "Owns: LG G3", "type": "behaviors", "path": ["Behaviours", "Mobile Device User", "All Mobile Devices by Brand", "LG"], "description": "People who are likely to own an LG G3 mobile device", "audience_size_lower_bound": 23642, "audience_size_upper_bound": 27804, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.639Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.639Z"}, {"_id": "67c56feccceafebe2cf7b003", "interest_id": "6043341245183", "raw_name": "Owns: LG V10", "name": "Owns: LG V10", "type": "behaviors", "path": ["Behaviours", "Mobile Device User", "All Mobile Devices by Brand", "LG"], "description": "People who are likely to own an LG V10 mobile device", "audience_size_lower_bound": 12142, "audience_size_upper_bound": 14280, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.64Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.64Z"}, {"_id": "67c56feccceafebe2cf7b005", "interest_id": "6023460563383", "raw_name": "Owns: Alcatel", "name": "Owns: Alcatel", "type": "behaviors", "path": ["Behaviours", "Mobile Device User", "All Mobile Devices by Brand"], "description": "People who are likely to own an Alcatel mobile device", "audience_size_lower_bound": 2062278, "audience_size_upper_bound": 2425240, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.64Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.64Z"}, {"_id": "67c56feccceafebe2cf7b007", "interest_id": "6023460590583", "raw_name": "Owns: Cherry Mobile", "name": "Owns: Cherry Mobile", "type": "behaviors", "path": ["Behaviours", "Mobile Device User", "All Mobile Devices by Brand"], "description": "People who are likely to own a Cherry mobile device.", "audience_size_lower_bound": 77173, "audience_size_upper_bound": 90756, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.641Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.641Z"}, {"_id": "67c56feccceafebe2cf7b009", "interest_id": "6071590219583", "raw_name": "Owns: <PERSON><PERSON><PERSON>", "name": "Owns: <PERSON><PERSON><PERSON>", "type": "behaviors", "path": ["Behaviours", "Mobile Device User", "All Mobile Devices by Brand"], "description": "People who likely own a Gionee mobile device", "audience_size_lower_bound": 786268, "audience_size_upper_bound": 924652, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.641Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.641Z"}, {"_id": "67c56feccceafebe2cf7b00b", "interest_id": "6011390261383", "raw_name": "Owns: <PERSON><PERSON><PERSON>", "name": "Owns: <PERSON><PERSON><PERSON>", "type": "behaviors", "path": ["Behaviours", "Mobile Device User", "All Mobile Devices by Brand"], "description": "People who are likely to own a Huawei mobile device.", "audience_size_lower_bound": 54057486, "audience_size_upper_bound": 63571604, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.642Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.642Z"}, {"_id": "67c56feccceafebe2cf7b00d", "interest_id": "6019164544783", "raw_name": "Owns: <PERSON><PERSON><PERSON><PERSON>", "name": "Owns: <PERSON><PERSON><PERSON><PERSON>", "type": "behaviors", "path": ["Behaviours", "Mobile Device User", "All Mobile Devices by Brand"], "description": "People who are likely to own a Karbonn mobile device.", "audience_size_lower_bound": 69460, "audience_size_upper_bound": 81685, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.643Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.643Z"}, {"_id": "67c56feccceafebe2cf7b00f", "interest_id": "6019164586183", "raw_name": "Owns: Micromax", "name": "Owns: Micromax", "type": "behaviors", "path": ["Behaviours", "Mobile Device User", "All Mobile Devices by Brand"], "description": "People who are likely to own a Micromax mobile device", "audience_size_lower_bound": 456165, "audience_size_upper_bound": 536451, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.643Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.643Z"}, {"_id": "67c56feccceafebe2cf7b011", "interest_id": "6056265200983", "raw_name": "Owns: <PERSON><PERSON>", "name": "Owns: <PERSON><PERSON>", "type": "behaviors", "path": ["Behaviours", "Mobile Device User", "All Mobile Devices by Brand"], "description": "People who are likely to own an Oppo mobile device", "audience_size_lower_bound": 229367231, "audience_size_upper_bound": 269735864, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.644Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.644Z"}, {"_id": "67c56feccceafebe2cf7b013", "interest_id": "6023460579583", "raw_name": "Owns: <PERSON><PERSON><PERSON>", "name": "Owns: <PERSON><PERSON><PERSON>", "type": "behaviors", "path": ["Behaviours", "Mobile Device User", "All Mobile Devices by Brand"], "description": "People who are likely to own a Tecno mobile device.", "audience_size_lower_bound": 107858428, "audience_size_upper_bound": 126841512, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.645Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.645Z"}, {"_id": "67c56feccceafebe2cf7b015", "interest_id": "6056265212183", "raw_name": "Owns: VIVO devices", "name": "Owns: VIVO devices", "type": "behaviors", "path": ["Behaviours", "Mobile Device User", "All Mobile Devices by Brand"], "description": "People who are likely to own VIVO mobile devices", "audience_size_lower_bound": 238946902, "audience_size_upper_bound": 281001557, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.646Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.646Z"}, {"_id": "67c56feccceafebe2cf7b017", "interest_id": "6019164630583", "raw_name": "Owns: <PERSON><PERSON>", "name": "Owns: <PERSON><PERSON>", "type": "behaviors", "path": ["Behaviours", "Mobile Device User", "All Mobile Devices by Brand"], "description": "People who are likely to own a Xiaomi mobile device.", "audience_size_lower_bound": 28994178, "audience_size_upper_bound": 34097154, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.646Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.646Z"}, {"_id": "67c56feccceafebe2cf7b019", "interest_id": "6023460572383", "raw_name": "Owns: ZTE", "name": "Owns: ZTE", "type": "behaviors", "path": ["Behaviours", "Mobile Device User", "All Mobile Devices by Brand"], "description": "People who are likely to own a ZTE mobile device", "audience_size_lower_bound": 11325718, "audience_size_upper_bound": 13319045, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.647Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.647Z"}, {"_id": "67c56feccceafebe2cf7b01b", "interest_id": "6013017235183", "raw_name": "Galaxy Note II", "name": "Galaxy Note II", "type": "behaviors", "path": ["Behaviours", "Mobile Device User", "All Mobile Devices by Brand", "Samsung"], "description": "", "audience_size_lower_bound": 2002, "audience_size_upper_bound": 2355, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.647Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.647Z"}, {"_id": "67c56feccceafebe2cf7b01d", "interest_id": "6016925667383", "raw_name": "Galaxy Tab", "name": "Galaxy Tab", "type": "behaviors", "path": ["Behaviours", "Mobile Device User", "All Mobile Devices by Brand", "Samsung"], "description": "People who are likely to own a Samsung Galaxy Tab", "audience_size_lower_bound": 454, "audience_size_upper_bound": 534, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.648Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.648Z"}, {"_id": "67c56feccceafebe2cf7b01f", "interest_id": "6013017236583", "raw_name": "Owns: Galaxy Grand", "name": "Owns: Galaxy Grand", "type": "behaviors", "path": ["Behaviours", "Mobile Device User", "All Mobile Devices by Brand", "Samsung"], "description": "People who are likely to own a Galaxy Grand mobile device", "audience_size_lower_bound": 34534, "audience_size_upper_bound": 40613, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.649Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.649Z"}, {"_id": "67c56feccceafebe2cf7b021", "interest_id": "6015441244983", "raw_name": "Owns: Galaxy Grand 2", "name": "Owns: Galaxy Grand 2", "type": "behaviors", "path": ["Behaviours", "Mobile Device User", "All Mobile Devices by Brand", "Samsung"], "description": "People who are likely to own a Samsung Galaxy Grand 2 mobile device", "audience_size_lower_bound": 32990, "audience_size_upper_bound": 38797, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.649Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.649Z"}, {"_id": "67c56feccceafebe2cf7b023", "interest_id": "6013279353983", "raw_name": "Owns: Galaxy Note 3", "name": "Owns: Galaxy Note 3", "type": "behaviors", "path": ["Behaviours", "Mobile Device User", "All Mobile Devices by Brand", "Samsung"], "description": "People who are likely to own a Galaxy Note 3 mobile device.", "audience_size_lower_bound": 140792, "audience_size_upper_bound": 165572, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.65Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.65Z"}, {"_id": "67c56feccceafebe2cf7b025", "interest_id": "6019098214783", "raw_name": "Owns: Galaxy Note 4", "name": "Owns: Galaxy Note 4", "type": "behaviors", "path": ["Behaviours", "Mobile Device User", "All Mobile Devices by Brand", "Samsung"], "description": "People who are likely to own a Galaxy Note 4 mobile device.", "audience_size_lower_bound": 138248, "audience_size_upper_bound": 162580, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.65Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.65Z"}, {"_id": "67c56feccceafebe2cf7b027", "interest_id": "6042330550783", "raw_name": "Owns: Galaxy Note 5", "name": "Owns: Galaxy Note 5", "type": "behaviors", "path": ["Behaviours", "Mobile Device User", "All Mobile Devices by Brand", "Samsung"], "description": "People who are likely to own a Samsung Galaxy Note 5 mobile device", "audience_size_lower_bound": 82711, "audience_size_upper_bound": 97269, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.65Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.65Z"}, {"_id": "67c56feccceafebe2cf7b029", "interest_id": "6058034528983", "raw_name": "Owns: Galaxy Note 7", "name": "Owns: Galaxy Note 7", "type": "behaviors", "path": ["Behaviours", "Mobile Device User", "All Mobile Devices by Brand", "Samsung"], "description": "People who are likely to own a Samsung Galaxy Note 7  mobile device", "audience_size_lower_bound": 1112, "audience_size_upper_bound": 1308, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.651Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.651Z"}, {"_id": "67c56feccceafebe2cf7b02b", "interest_id": "6083036245383", "raw_name": "Owns: Galaxy Note 8", "name": "Owns: Galaxy Note 8", "type": "behaviors", "path": ["Behaviours", "Mobile Device User", "All Mobile Devices by Brand", "Samsung"], "description": "People who are likely to own a Samsung Galaxy Note 8 mobile device", "audience_size_lower_bound": 1956778, "audience_size_upper_bound": 2301171, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.652Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.652Z"}, {"_id": "67c56feccceafebe2cf7b02d", "interest_id": "6013017308783", "raw_name": "Owns: Galaxy S 4 Mini", "name": "Owns: Galaxy S 4 Mini", "type": "behaviors", "path": ["Behaviours", "Mobile Device User", "All Mobile Devices by Brand", "Samsung"], "description": "People who are likely to own a Galaxy S 4 Mini mobile device.", "audience_size_lower_bound": 16360, "audience_size_upper_bound": 19240, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.652Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.652Z"}, {"_id": "67c56feccceafebe2cf7b02f", "interest_id": "6013017211983", "raw_name": "Owns: Galaxy S III Mini", "name": "Owns: Galaxy S III Mini", "type": "behaviors", "path": ["Behaviours", "Mobile Device User", "All Mobile Devices by Brand", "Samsung"], "description": "People who are likely to own a Galaxy S III Mini mobile device", "audience_size_lower_bound": 18951, "audience_size_upper_bound": 22287, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.653Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.653Z"}, {"_id": "67c56feccceafebe2cf7b031", "interest_id": "6007481031783", "raw_name": "Owns: Galaxy S III devices", "name": "Owns: Galaxy S III devices", "type": "behaviors", "path": ["Behaviours", "Mobile Device User", "All Mobile Devices by Brand", "Samsung"], "description": "People who likely own a Galaxy S III mobile device", "audience_size_lower_bound": 34981, "audience_size_upper_bound": 41138, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.653Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.653Z"}, {"_id": "67c56feccceafebe2cf7b033", "interest_id": "6013016790183", "raw_name": "Owns: Galaxy S4", "name": "Owns: Galaxy S4", "type": "behaviors", "path": ["Behaviours", "Mobile Device User", "All Mobile Devices by Brand", "Samsung"], "description": "People who are likely to own a Galaxy S4 mobile device", "audience_size_lower_bound": 118439, "audience_size_upper_bound": 139285, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.654Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.654Z"}, {"_id": "67c56feccceafebe2cf7b035", "interest_id": "6014808618583", "raw_name": "Owns: Galaxy S5", "name": "Owns: Galaxy S5", "type": "behaviors", "path": ["Behaviours", "Mobile Device User", "All Mobile Devices by Brand", "Samsung"], "description": "People who are likely to own a Samsung Galaxy S5 mobile device", "audience_size_lower_bound": 247540, "audience_size_upper_bound": 291108, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.654Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.654Z"}, {"_id": "67c56feccceafebe2cf7b037", "interest_id": "6026660740983", "raw_name": "Owns: Galaxy S6", "name": "Owns: Galaxy S6", "type": "behaviors", "path": ["Behaviours", "Mobile Device User", "All Mobile Devices by Brand", "Samsung"], "description": "People who are likely to own a Samsung Galaxy S6 mobile device", "audience_size_lower_bound": 616424, "audience_size_upper_bound": 724915, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.655Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.655Z"}, {"_id": "67c56feccceafebe2cf7b039", "interest_id": "6043523344783", "raw_name": "Owns: Galaxy S7", "name": "Owns: Galaxy S7", "type": "behaviors", "path": ["Behaviours", "Mobile Device User", "All Mobile Devices by Brand", "Samsung"], "description": "People who are likely to own a Samsung Galaxy S7  mobile device", "audience_size_lower_bound": 889994, "audience_size_upper_bound": 1046634, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.655Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.655Z"}, {"_id": "67c56feccceafebe2cf7b03b", "interest_id": "6043522870783", "raw_name": "Owns: Galaxy S7 Edge", "name": "Owns: Galaxy S7 Edge", "type": "behaviors", "path": ["Behaviours", "Mobile Device User", "All Mobile Devices by Brand", "Samsung"], "description": "People who are likely to own a Samsung Galaxy S7 Edge mobile device", "audience_size_lower_bound": 1071296, "audience_size_upper_bound": 1259845, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.656Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.656Z"}, {"_id": "67c56feccceafebe2cf7b03d", "interest_id": "6075237200983", "raw_name": "Owns: Galaxy S8", "name": "Owns: Galaxy S8", "type": "behaviors", "path": ["Behaviours", "Mobile Device User", "All Mobile Devices by Brand", "Samsung"], "description": "People who likely own a Galaxy S8 mobile device", "audience_size_lower_bound": 2602027, "audience_size_upper_bound": 3059984, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.656Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.656Z"}, {"_id": "67c56feccceafebe2cf7b03f", "interest_id": "6075237226583", "raw_name": "Owns: Galaxy S8+", "name": "Owns: Galaxy S8+", "type": "behaviors", "path": ["Behaviours", "Mobile Device User", "All Mobile Devices by Brand", "Samsung"], "description": "People who are likely to own a Galaxy S8+ mobile device", "audience_size_lower_bound": 1587298, "audience_size_upper_bound": 1866663, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.657Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.657Z"}, {"_id": "67c56feccceafebe2cf7b041", "interest_id": "6106223987983", "raw_name": "Owns: Galaxy S9", "name": "Owns: Galaxy S9", "type": "behaviors", "path": ["Behaviours", "Mobile Device User", "All Mobile Devices by Brand", "Samsung"], "description": "People who are likely to own a Galaxy S9 mobile device", "audience_size_lower_bound": 3409320, "audience_size_upper_bound": 4009361, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.657Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.657Z"}, {"_id": "67c56feccceafebe2cf7b043", "interest_id": "6106224431383", "raw_name": "Owns: Galaxy S9+", "name": "Owns: Galaxy S9+", "type": "behaviors", "path": ["Behaviours", "Mobile Device User", "All Mobile Devices by Brand", "Samsung"], "description": "People who are likely to own a Galaxy S9+ mobile device", "audience_size_lower_bound": 3028193, "audience_size_upper_bound": 3561156, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.658Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.658Z"}, {"_id": "67c56feccceafebe2cf7b045", "interest_id": "6016925657183", "raw_name": "Owns: Galaxy Tab 2", "name": "Owns: Galaxy Tab 2", "type": "behaviors", "path": ["Behaviours", "Mobile Device User", "All Mobile Devices by Brand", "Samsung"], "description": "People who are likely to own a Galaxy Tab 2 mobile device", "audience_size_lower_bound": 29471, "audience_size_upper_bound": 34659, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.658Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.658Z"}, {"_id": "67c56feccceafebe2cf7b047", "interest_id": "6016925643983", "raw_name": "Owns: Galaxy Tab 3", "name": "Owns: Galaxy Tab 3", "type": "behaviors", "path": ["Behaviours", "Mobile Device User", "All Mobile Devices by Brand", "Samsung"], "description": "People who are likely to own a Galaxy Tab 3 mobile device.", "audience_size_lower_bound": 221657, "audience_size_upper_bound": 260669, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.659Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.659Z"}, {"_id": "67c56feccceafebe2cf7b049", "interest_id": "6016925404783", "raw_name": "Owns: Galaxy Tab 4", "name": "Owns: Galaxy Tab 4", "type": "behaviors", "path": ["Behaviours", "Mobile Device User", "All Mobile Devices by Brand", "Samsung"], "description": "People who are likely to own a Samsung Galaxy Tab 4 mobile device", "audience_size_lower_bound": 174477, "audience_size_upper_bound": 205185, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.659Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.659Z"}, {"_id": "67c56feccceafebe2cf7b04b", "interest_id": "6016925394583", "raw_name": "Owns: Galaxy Tab Pro", "name": "Owns: Galaxy Tab Pro", "type": "behaviors", "path": ["Behaviours", "Mobile Device User", "All Mobile Devices by Brand", "Samsung"], "description": "People who are likely to own a Samsung Galaxy Tab Pro mobile device.", "audience_size_lower_bound": 8383, "audience_size_upper_bound": 9859, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.66Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.66Z"}, {"_id": "67c56feccceafebe2cf7b04d", "interest_id": "6016925328983", "raw_name": "Owns: Galaxy Tab S", "name": "Owns: Galaxy Tab S", "type": "behaviors", "path": ["Behaviours", "Mobile Device User", "All Mobile Devices by Brand", "Samsung"], "description": "People who are likely to own a Samsung Galaxy Tab S mobile device.", "audience_size_lower_bound": 61180, "audience_size_upper_bound": 71948, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.66Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.66Z"}, {"_id": "67c56feccceafebe2cf7b04f", "interest_id": "6015852294783", "raw_name": "Owns: Galaxy Y devices", "name": "Owns: Galaxy Y devices", "type": "behaviors", "path": ["Behaviours", "Mobile Device User", "All Mobile Devices by Brand", "Samsung"], "description": "People who are likely to own a Galaxy Y mobile device.", "audience_size_lower_bound": 2037, "audience_size_upper_bound": 2396, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.661Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.661Z"}, {"_id": "67c56feccceafebe2cf7b051", "interest_id": "6016926254583", "raw_name": "Owns: <PERSON><PERSON><PERSON>", "name": "Owns: <PERSON><PERSON><PERSON>", "type": "behaviors", "path": ["Behaviours", "Mobile Device User", "All Mobile Devices by Brand", "Sony"], "description": "People who are likely to own a Sony Xperia M mobile device", "audience_size_lower_bound": 2619, "audience_size_upper_bound": 3080, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.662Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.662Z"}, {"_id": "67c56feccceafebe2cf7b053", "interest_id": "6016926310383", "raw_name": "Owns: Xperia SL", "name": "Owns: Xperia SL", "type": "behaviors", "path": ["Behaviours", "Mobile Device User", "All Mobile Devices by Brand", "Sony"], "description": "People who are likely to own a Sony Xperia SLs", "audience_size_lower_bound": 297, "audience_size_upper_bound": 350, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.662Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.662Z"}, {"_id": "67c56feccceafebe2cf7b055", "interest_id": "6016926528983", "raw_name": "Owns: <PERSON>per<PERSON>", "name": "Owns: <PERSON>per<PERSON>", "type": "behaviors", "path": ["Behaviours", "Mobile Device User", "All Mobile Devices by Brand", "Sony"], "description": "People who are likely to own a Sony Xperia Z mobile device", "audience_size_lower_bound": 4767, "audience_size_upper_bound": 5606, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.663Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.663Z"}, {"_id": "67c56feccceafebe2cf7b057", "interest_id": "6016926651383", "raw_name": "Owns: Xperia <PERSON>", "name": "Owns: Xperia <PERSON>", "type": "behaviors", "path": ["Behaviours", "Mobile Device User", "All Mobile Devices by Brand", "Sony"], "description": "People who are likely to own a Sony Xperia Z Ultra mobile device", "audience_size_lower_bound": 1870, "audience_size_upper_bound": 2200, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.663Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.663Z"}, {"_id": "67c56feccceafebe2cf7b059", "interest_id": "6022430911783", "raw_name": "Owns: Xperia Z3", "name": "Owns: Xperia Z3", "type": "behaviors", "path": ["Behaviours", "Mobile Device User", "All Mobile Devices by Brand", "Sony"], "description": "People who are likely to own a Sony Xperia Z3", "audience_size_lower_bound": 12369, "audience_size_upper_bound": 14546, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.665Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.665Z"}, {"_id": "67c56feccceafebe2cf7b05b", "interest_id": "6016926471583", "raw_name": "Xperia T", "name": "Xperia T", "type": "behaviors", "path": ["Behaviours", "Mobile Device User", "All Mobile Devices by Brand", "Sony"], "description": "People who are likely to own a Sony Xperia T", "audience_size_lower_bound": 406, "audience_size_upper_bound": 478, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.666Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.666Z"}, {"_id": "67c56feccceafebe2cf7b05d", "interest_id": "6004386044572", "raw_name": "Facebook access (mobile): Android devices", "name": "Facebook access (mobile): Android devices", "type": "behaviors", "path": ["Behaviours", "Mobile Device User", "All Mobile Devices by Operating System"], "description": "People who primarily access Facebook using any Android mobile device", "audience_size_lower_bound": 2041191309, "audience_size_upper_bound": 2400440980, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.666Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.666Z"}, {"_id": "67c56feccceafebe2cf7b05f", "interest_id": "6004384041172", "raw_name": "Facebook access (mobile): Apple (iOS) devices", "name": "Facebook access (mobile): Apple (iOS) devices", "type": "behaviors", "path": ["Behaviours", "Mobile Device User", "All Mobile Devices by Operating System"], "description": "People who primarily access Facebook using an Apple (iOS) mobile device", "audience_size_lower_bound": 429559505, "audience_size_upper_bound": 505161979, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.667Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.667Z"}, {"_id": "67c56feccceafebe2cf7b061", "interest_id": "6004385895772", "raw_name": "Facebook access (mobile): Windows phones", "name": "Facebook access (mobile): Windows phones", "type": "behaviors", "path": ["Behaviours", "Mobile Device User", "All Mobile Devices by Operating System"], "description": "People who primarily access Facebook using a Windows mobile device", "audience_size_lower_bound": 9534, "audience_size_upper_bound": 11212, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.668Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.668Z"}, {"_id": "67c56feccceafebe2cf7b063", "interest_id": "6065048233383", "raw_name": "Android: 360 degree media not supported", "name": "Android: 360 degree media not supported", "type": "behaviors", "path": ["Behaviours", "Mobile Device User"], "description": "People whose primary mobile device is Android without support for 360 degree media (photos, videos).", "audience_size_lower_bound": 1167509, "audience_size_upper_bound": 1372991, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.668Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.668Z"}, {"_id": "67c56feccceafebe2cf7b065", "interest_id": "6063136545383", "raw_name": "Android: 360 degree media supported", "name": "Android: 360 degree media supported", "type": "behaviors", "path": ["Behaviours", "Mobile Device User"], "description": "People whose primary mobile device is Android with support for 360-degree media (photos, videos).", "audience_size_lower_bound": 1281239408, "audience_size_upper_bound": 1506737544, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.669Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.669Z"}, {"_id": "67c56feccceafebe2cf7b067", "interest_id": "6004382299972", "raw_name": "Facebook access (mobile): all mobile devices", "name": "Facebook access (mobile): all mobile devices", "type": "behaviors", "path": ["Behaviours", "Mobile Device User"], "description": "People who primarily access Facebook using mobile devices", "audience_size_lower_bound": 3121561253, "audience_size_upper_bound": 3670956034, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.67Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.67Z"}, {"_id": "67c56feccceafebe2cf7b069", "interest_id": "6004383149972", "raw_name": "Facebook access (mobile): feature phones", "name": "Facebook access (mobile): feature phones", "type": "behaviors", "path": ["Behaviours", "Mobile Device User"], "description": "People who primarily access Facebook using a feature phone", "audience_size_lower_bound": 890244, "audience_size_upper_bound": 1046927, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.67Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.67Z"}, {"_id": "67c56feccceafebe2cf7b06b", "interest_id": "6004383049972", "raw_name": "Facebook access (mobile): smartphones and tablets", "name": "Facebook access (mobile): smartphones and tablets", "type": "behaviors", "path": ["Behaviours", "Mobile Device User"], "description": "People who primarily access Facebook using a smartphone or tablet device", "audience_size_lower_bound": 2273411996, "audience_size_upper_bound": 2673532508, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.671Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.671Z"}, {"_id": "67c56feccceafebe2cf7b06d", "interest_id": "6016286626383", "raw_name": "Facebook access (mobile): tablets", "name": "Facebook access (mobile): tablets", "type": "behaviors", "path": ["Behaviours", "Mobile Device User"], "description": "People who primarily access Facebook using a tablet.", "audience_size_lower_bound": 1869728316, "audience_size_upper_bound": 2198800500, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.671Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.671Z"}, {"_id": "67c56feccceafebe2cf7b06f", "interest_id": "6017253486583", "raw_name": "Facebook access (network type): 2G", "name": "Facebook access (network type): 2G", "type": "behaviors", "path": ["Behaviours", "Mobile Device User", "Network Connection"], "description": "People who primarily access Facebook using a 2G network.", "audience_size_lower_bound": 6482540, "audience_size_upper_bound": 7623468, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.672Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.672Z"}, {"_id": "67c56feccceafebe2cf7b071", "interest_id": "6017253511583", "raw_name": "Facebook access (network type): 3G", "name": "Facebook access (network type): 3G", "type": "behaviors", "path": ["Behaviours", "Mobile Device User", "Network Connection"], "description": "People who primarily access Facebook using a 3G network.", "audience_size_lower_bound": 72371377, "audience_size_upper_bound": 85108740, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.673Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.673Z"}, {"_id": "67c56feccceafebe2cf7b073", "interest_id": "6017253531383", "raw_name": "Facebook access (network type): 4G", "name": "Facebook access (network type): 4G", "type": "behaviors", "path": ["Behaviours", "Mobile Device User", "Network Connection"], "description": "People who primarily access Facebook using a 4G network", "audience_size_lower_bound": 818769784, "audience_size_upper_bound": 962873267, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.673Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.673Z"}, {"_id": "67c56feccceafebe2cf7b075", "interest_id": "6015235495383", "raw_name": "Facebook access (network type): Wi-Fi", "name": "Facebook access (network type): Wi-Fi", "type": "behaviors", "path": ["Behaviours", "Mobile Device User", "Network Connection"], "description": "People who primarily access Facebook using a Wi-Fi network.", "audience_size_lower_bound": 1016095415, "audience_size_upper_bound": 1194928209, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.674Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.674Z"}, {"_id": "67c56feccceafebe2cf7b077", "interest_id": "6007078565383", "raw_name": "New smartphone and tablet users", "name": "New smartphone and tablet users", "type": "behaviors", "path": ["Behaviours", "Mobile Device User"], "description": "People who are new smartphone or tablet users.", "audience_size_lower_bound": 124417356, "audience_size_upper_bound": 146314811, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.674Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.674Z"}, {"_id": "67c56feccceafebe2cf7b079", "interest_id": "6106805412383", "raw_name": "Owns: OnePlus", "name": "Owns: OnePlus", "type": "behaviors", "path": ["Behaviours", "Mobile Device User"], "description": "People who are likely to own a OnePlus mobile device", "audience_size_lower_bound": 30429715, "audience_size_upper_bound": 35785346, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.675Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.675Z"}, {"_id": "67c56feccceafebe2cf7b07b", "interest_id": "6091658708183", "raw_name": "Uses a mobile device (1-3 months)", "name": "Uses a mobile device (1-3 months)", "type": "behaviors", "path": ["Behaviours", "Mobile device user/device use time"], "description": "People who are likely to have used a mobile device for 1-3 months", "audience_size_lower_bound": 175467738, "audience_size_upper_bound": 206350061, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.675Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.675Z"}, {"_id": "67c56feccceafebe2cf7b07d", "interest_id": "6091658540583", "raw_name": "Uses a mobile device (10-12 months)", "name": "Uses a mobile device (10-12 months)", "type": "behaviors", "path": ["Behaviours", "Mobile device user/device use time"], "description": "People who are likely to have used a mobile device for 10-12 months", "audience_size_lower_bound": 142329268, "audience_size_upper_bound": 167379220, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.676Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.676Z"}, {"_id": "67c56feccceafebe2cf7b07f", "interest_id": "6091658562383", "raw_name": "Uses a mobile device (13-18 months)", "name": "Uses a mobile device (13-18 months)", "type": "behaviors", "path": ["Behaviours", "Mobile device user/device use time"], "description": "People who are likely to have used a mobile device for 13-18 months", "audience_size_lower_bound": 235740068, "audience_size_upper_bound": 277230321, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.676Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.676Z"}, {"_id": "67c56feccceafebe2cf7b081", "interest_id": "6091658651583", "raw_name": "Uses a mobile device (19-24 months)", "name": "Uses a mobile device (19-24 months)", "type": "behaviors", "path": ["Behaviours", "Mobile device user/device use time"], "description": "People who are likely to have used a mobile device for 19-24 months", "audience_size_lower_bound": 177493676, "audience_size_upper_bound": 208732564, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.677Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.677Z"}, {"_id": "67c56feccceafebe2cf7b083", "interest_id": "6091658683183", "raw_name": "Uses a mobile device (25 months+)", "name": "Uses a mobile device (25 months+)", "type": "behaviors", "path": ["Behaviours", "Mobile device user/device use time"], "description": "People who are likely to have used a mobile device for 25+ months", "audience_size_lower_bound": 668398914, "audience_size_upper_bound": 786037124, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.678Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.678Z"}, {"_id": "67c56feccceafebe2cf7b085", "interest_id": "6091658512983", "raw_name": "Uses a mobile device (4-6 months)", "name": "Uses a mobile device (4-6 months)", "type": "behaviors", "path": ["Behaviours", "Mobile device user/device use time"], "description": "People who are likely to have used a mobile device for 4-6 months", "audience_size_lower_bound": 199300969, "audience_size_upper_bound": 234377940, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.678Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.678Z"}, {"_id": "67c56feccceafebe2cf7b087", "interest_id": "6091658512183", "raw_name": "Uses a mobile device (7-9 months)", "name": "Uses a mobile device (7-9 months)", "type": "behaviors", "path": ["Behaviours", "Mobile device user/device use time"], "description": "People who are likely to have used a mobile device for 7-9 months", "audience_size_lower_bound": 164479798, "audience_size_upper_bound": 193428243, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.679Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.679Z"}, {"_id": "67c56feccceafebe2cf7b089", "interest_id": "6091658707783", "raw_name": "Uses a mobile device (less than 1 month)", "name": "Uses a mobile device (less than 1 month)", "type": "behaviors", "path": ["Behaviours", "Mobile device user/device use time"], "description": "People who are likely to have used a mobile device for less than 1 month", "audience_size_lower_bound": 124418101, "audience_size_upper_bound": 146315687, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.68Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.68Z"}, {"_id": "67c56feccceafebe2cf7b08b", "interest_id": "6100052630783", "raw_name": "Interested in upcoming events", "name": "Interested in upcoming events", "type": "behaviors", "path": ["Behaviours", "More categories"], "description": "People who have expressed interest in attending an upcoming Facebook event.", "audience_size_lower_bound": 5934545, "audience_size_upper_bound": 6979026, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.68Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.68Z"}, {"_id": "67c56feccceafebe2cf7b08d", "interest_id": "6086568164383", "raw_name": "Marketing API developers (last 90 days)", "name": "Marketing API developers (last 90 days)", "type": "behaviors", "path": ["Behaviours", "More categories"], "description": "App developers that have used the Facebook marketing API in the last 90 days.", "audience_size_lower_bound": 11224, "audience_size_upper_bound": 13200, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.681Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.681Z"}, {"_id": "67c56feccceafebe2cf7b08f", "interest_id": "6071631541183", "raw_name": "Engaged shoppers", "name": "Engaged shoppers", "type": "behaviors", "path": ["Behaviours", "Purchase behaviour"], "description": "People who have clicked on the call-to-action button \"Shop Now\" in the past week.", "audience_size_lower_bound": 825229258, "audience_size_upper_bound": 970469608, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.681Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.681Z"}, {"_id": "67c56feccceafebe2cf7b091", "interest_id": "6080562616983", "raw_name": "Football fans (high content engagement)", "name": "Football fans (high content engagement)", "type": "behaviors", "path": ["Behaviours", "Soccer"], "description": "Interacted with content related to football five or more times over the past 90 days.", "audience_size_lower_bound": 12884177, "audience_size_upper_bound": 15151793, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.682Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.682Z"}, {"_id": "67c56feccceafebe2cf7b093", "interest_id": "6080562614783", "raw_name": "Football fans (moderate content engagement)", "name": "Football fans (moderate content engagement)", "type": "behaviors", "path": ["Behaviours", "Soccer"], "description": "Interacted with content related to football and sports fewer than five times over the past 90 days.", "audience_size_lower_bound": 94692568, "audience_size_upper_bound": 111358461, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.682Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.682Z"}, {"_id": "67c56feccceafebe2cf7b095", "interest_id": "6203619619383", "raw_name": "Friends of football fans", "name": "Friends of football fans", "type": "behaviors", "path": ["Behaviours", "Soccer"], "description": "Friends of anyone who is a moderately or highly engaged football fan. Excludes people who are already football fans.", "audience_size_lower_bound": 1788114082, "audience_size_upper_bound": 2102822161, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.683Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.683Z"}, {"_id": "67c56feccceafebe2cf7b097", "interest_id": "6013516370183", "raw_name": "Commuters", "name": "Commuters", "type": "behaviors", "path": ["Behaviours", "Travel"], "description": "People who likely commute from their home to their workplace on weekdays", "audience_size_lower_bound": *********, "audience_size_upper_bound": *********, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.683Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.683Z"}, {"_id": "67c56feccceafebe2cf7b099", "interest_id": "6022788483583", "raw_name": "Frequent international travellers", "name": "Frequent international travellers", "type": "behaviors", "path": ["Behaviours", "Travel"], "description": "People who have travelled abroad more than once in the past 6 months.", "audience_size_lower_bound": *********, "audience_size_upper_bound": 1045010941, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.684Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.684Z"}, {"_id": "67c56feccceafebe2cf7b09b", "interest_id": "6002714895372", "raw_name": "Frequent travellers", "name": "Frequent travellers", "type": "behaviors", "path": ["Behaviours", "Travel"], "description": "People whose activities on Facebook suggest that they are frequent travellers.", "audience_size_lower_bound": 2174797159, "audience_size_upper_bound": 2557561459, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.684Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.684Z"}, {"_id": "67c56feccceafebe2cf7b09d", "interest_id": "6008261969983", "raw_name": "Returned from travelling one week ago", "name": "Returned from travelling one week ago", "type": "behaviors", "path": ["Behaviours", "Travel"], "description": "People whose activities on Facebook suggest that they returned from travelling within the past week", "audience_size_lower_bound": *********, "audience_size_upper_bound": *********, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.685Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.685Z"}, {"_id": "67c56feccceafebe2cf7b09f", "interest_id": "6008297697383", "raw_name": "Returned from travelling two weeks ago", "name": "Returned from travelling two weeks ago", "type": "behaviors", "path": ["Behaviours", "Travel"], "description": "People whose activities on Facebook suggest that they returned from travelling within the past 2 weeks", "audience_size_lower_bound": *********, "audience_size_upper_bound": *********, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-03-03T09:01:32.686Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-03T09:01:32.686Z"}]