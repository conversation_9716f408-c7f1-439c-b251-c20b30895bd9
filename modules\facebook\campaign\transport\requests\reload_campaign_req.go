package requests

import (
	"godsp/modules/facebook/campaign/common/errs"

	"github.com/go-playground/validator/v10"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

type ReloadCampaignReq struct {
	AccountID  string `json:"account_id,omitempty"`
	CampaignID string `json:"campaign_id,omitempty"`

	UserId primitive.ObjectID `json:"-"`
}

func (req *ReloadCampaignReq) Validate() []string {
	validate := validator.New()
	var validationErrors []string

	err := validate.Struct(req)
	if err != nil {
		for _, err := range err.(validator.ValidationErrors) {
			switch err.Field() {
			case "AccountID":
				validationErrors = append(validationErrors, errs.ErrReloadCampaignAccountId.Error())
			case "CampaignID":
				validationErrors = append(validationErrors, errs.ErrReloadCampaignCampaignId.Error())
			}
		}
	}
	if req.AccountID == "" && req.CampaignID == "" {
		validationErrors = append(validationErrors, "Either AccountID or CampaignID must be provided")
	}
	return validationErrors
}
