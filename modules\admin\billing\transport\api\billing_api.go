package api

import (
	"context"
	"github.com/dev-networldasia/dspgos/sctx"
	"github.com/gofiber/fiber/v2"
	"godsp/modules/admin/billing/transport/requests"
	"godsp/modules/admin/billing/transport/response"
	"godsp/modules/admin/common/admconst"
	"godsp/pkg/gos/utils"
	"godsp/pkg/sctx/core"
	"net/http"
)

type BillingApiUsc interface {
	CreateApiBillingUsc(ctx context.Context, payload *requests.PayloadBillingCreation) (string, error)
	ListDatatableApiBillingUsc(ctx context.Context, payload *requests.ListBillingApiReq) (*[]response.BillingDataTable, error)
	ListDatatableApiBillingDetailUsc(ctx context.Context, payload *requests.ListBillingDetailApiReq) (*[]response.BillingDetailDataTable, error)
	EditApiBillingUsc(ctx context.Context, payload *requests.PayloadBillingEdition) error
	UpdateBillingDetailApiUsc(ctx context.Context, payload *requests.PayloadUpdateBillingDetail) error
	UpdateBillingApiUsc(ctx context.Context, payload *requests.PayloadUpdateBilling) error
}

type billingApi struct {
	usc    BillingApiUsc
	logger sctx.Logger
}

func NewBillingApi(usc BillingApiUsc, logger sctx.Logger) *billingApi {
	return &billingApi{
		usc:    usc,
		logger: logger,
	}
}

/**
 * Create billing
 */
func (a *billingApi) CreateBillingApi() fiber.Handler {
	return func(c *fiber.Ctx) error {
		var param requests.PayloadBillingCreation
		if err := c.BodyParser(&param); err != nil {
			return core.ReturnErrsForApi(c, err)
		}

		//validate
		validationErrors := param.Validate()
		if len(validationErrors) > 0 {
			return core.ReturnErrsForApi(c, validationErrors)
		}

		userId, err := utils.GetUserIdPrimitive(c.Context())
		if err != nil {
			return core.ReturnErrForApi(c, err.Error())
		}

		param.UserID = userId
		param.Status = admconst.STATUS_ACTIVE
		param.Type = admconst.STATUS_ACTIVE
		data, err := a.usc.CreateApiBillingUsc(c.Context(), &param)
		if err != nil {
			return core.ReturnErrForApi(c, err.Error())
		}

		return c.Status(http.StatusOK).JSON(core.ResponseData(map[string]interface{}{
			"msg":      "Create billing successfully!",
			"data":     data,
			"redirect": "/admins/billings/list",
		}))
	}
}

/**
 * list datatable
 */
func (a *billingApi) ListBillingApi() fiber.Handler {
	return func(ctx *fiber.Ctx) error {
		var payload requests.ListBillingApiReq
		if err := ctx.BodyParser(&payload); err != nil {
			return core.ReturnErrForApi(ctx, err.Error())
		}

		if err := payload.Validate(); err != nil {
			return core.ReturnErrForApi(ctx, err.Error())
		}

		data, err := a.usc.ListDatatableApiBillingUsc(ctx.Context(), &payload)
		if err != nil {
			return core.ReturnErrForApi(ctx, err.Error())
		}

		total := 0
		output := &[]response.BillingDataTable{}
		if data != nil {
			total = len(*data)
			output = data
		}

		return ctx.JSON(fiber.Map{
			"draw":            payload.Draw,
			"data":            output,
			"recordsTotal":    total,
			"recordsFiltered": total,
		})
	}
}

func (a *billingApi) ListBillingDetailApi() fiber.Handler {
	return func(ctx *fiber.Ctx) error {
		var payload requests.ListBillingDetailApiReq
		if err := ctx.BodyParser(&payload); err != nil {
			return core.ReturnErrForApi(ctx, err.Error())
		}

		if err := payload.Validate(); err != nil {
			return core.ReturnErrForApi(ctx, err.Error())
		}

		data, err := a.usc.ListDatatableApiBillingDetailUsc(ctx.Context(), &payload)
		if err != nil {
			return core.ReturnErrForApi(ctx, err.Error())
		}

		total := 0
		output := &[]response.BillingDetailDataTable{}
		if data != nil {
			total = len(*data)
			output = data
		}

		return ctx.JSON(fiber.Map{
			"draw":            payload.Draw,
			"data":            output,
			"recordsTotal":    total,
			"recordsFiltered": total,
		})
	}
}

/**
 * Edit billing
 */
func (a *billingApi) EditBillingApi() fiber.Handler {
	return func(c *fiber.Ctx) error {
		var param requests.PayloadBillingEdition
		err := c.BodyParser(&param)
		if err != nil {
			return core.ReturnErrForApi(c, err.Error())
		}

		//validate
		validationErrors := param.Validate()
		if len(validationErrors) > 0 {
			return core.ReturnErrsForApi(c, validationErrors)
		}

		userId, err := utils.GetUserIdPrimitive(c.Context())
		if err != nil {
			return core.ReturnErrForApi(c, err.Error())
		}
		param.UserID = userId

		err = a.usc.EditApiBillingUsc(c.Context(), &param)
		if err != nil {
			return core.ReturnErrForApi(c, err.Error())
		}

		return c.Status(http.StatusOK).JSON(core.ResponseData(map[string]interface{}{
			"msg":      "Edit billing successfully!",
			"redirect": "/admins/billings/list",
		}))
	}
}

func (a *billingApi) UpdateBillingApi() fiber.Handler {
	return func(c *fiber.Ctx) error {
		var param requests.PayloadUpdateBilling
		err := c.BodyParser(&param)
		if err != nil {
			return core.ReturnErrForApi(c, err.Error())
		}

		validationErrors := param.Validate()
		if len(validationErrors) > 0 {
			return core.ReturnErrsForApi(c, validationErrors)
		}

		auth, err := utils.GetInfoUserAuth(c.Context())
		if err != nil {
			return core.ReturnErrForApi(c, err.Error())
		}

		param.UserID = *auth.UserId
		err = a.usc.UpdateBillingApiUsc(c.Context(), &param)
		if err != nil {
			return core.ReturnErrForApi(c, err.Error())
		}

		return c.Status(http.StatusOK).JSON(core.ResponseData(map[string]interface{}{
			"msg":      "Update billing successfully!",
			"redirect": "/admins/billings/list",
		}))
	}
}

func (a *billingApi) UpdateBillingDetailApi() fiber.Handler {
	return func(c *fiber.Ctx) error {
		var param requests.PayloadUpdateBillingDetail
		err := c.BodyParser(&param)
		if err != nil {
			return core.ReturnErrForApi(c, err.Error())
		}

		validationErrors := param.Validate()
		if len(validationErrors) > 0 {
			return core.ReturnErrsForApi(c, validationErrors)
		}

		auth, err := utils.GetInfoUserAuth(c.Context())
		if err != nil {
			return core.ReturnErrForApi(c, err.Error())
		}

		param.UserID = *auth.UserId
		err = a.usc.UpdateBillingDetailApiUsc(c.Context(), &param)
		if err != nil {
			return core.ReturnErrForApi(c, err.Error())
		}

		return c.Status(http.StatusOK).JSON(core.ResponseData(map[string]interface{}{
			"msg":      "Update billing detail successfully!",
			"redirect": "/admins/billings/list",
		}))
	}
}
