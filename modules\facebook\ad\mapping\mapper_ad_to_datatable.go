package mapping

import (
	"fmt"
	"godsp/modules/facebook/ad/entity"
	"godsp/modules/facebook/ad/transport/responses"
	adserverReportE "godsp/modules/facebook/adserver_report/entity"
)

// ReportCampaignItem is a flat struct combining FacebookAdfbReportDetails and CampaignEntity fields

func MapperAdToDataTable(ads *[]entity.AdEntity, fbReportDetails *[]adserverReportE.FBReportDetailEntity) *[]responses.AdsDataTable {

	if ads == nil {
		return nil
	}

	fbReportDetailsData := make(map[string]adserverReportE.FBReportDetailEntity)
	if fbReportDetails != nil {
		for _, fbReportDetail := range *fbReportDetails {
			fbReportDetailsData[fbReportDetail.AdID] = fbReportDetail
		}
	}

	var datas []responses.AdsDataTable
	for _, ad := range *ads {
		record := responses.AdsDataTable{
			DTRowId:       fmt.Sprintf("row_%s", ad.AdID),
			AdID:          ad.AdID,
			CreativeID:    ad.CreativeID,
			Name:          ad.Name,
			Status:        ad.Status,
			TrackingSpecs: ad.TrackingSpecs,
			UpdatedAt:     ad.UpdatedAt,
			CreatedAt:     ad.CreatedAt,
			AdSet:         ad.AdSet,
			Campaign:      ad.Campaign,
		}

		// fmt.Printf("\n \n =------->ad.AdID: %v\n \n", ad.AdID)

		if adMetrics, exists := fbReportDetailsData[ad.AdID]; exists {
			// ========== Mentric Prority ==========
			record.Impressions = adMetrics.Impressions
			record.Reach = adMetrics.Reach
			record.VideoView = adMetrics.VideoView
			record.PostReaction = adMetrics.PostReaction
			record.PostComment = adMetrics.PostComment
			record.PostShare = adMetrics.PostShare
			record.Clicks = adMetrics.Clicks
			record.LinkClick = adMetrics.LinkClick
			record.PostEngagement = adMetrics.PostEngagement
			record.PostSave = adMetrics.PostSave
			record.PageLike = adMetrics.PageLike

			record.AccountID = adMetrics.AccountID
			record.AccountName = adMetrics.AccountName
			record.AccountCurrency = adMetrics.AccountCurrency
			record.CampaignID = adMetrics.CampaignID
			record.CampaignName = adMetrics.CampaignName
			record.AdSetID = adMetrics.AdSetID
			record.AdSetName = adMetrics.AdSetName
			record.AdID = adMetrics.AdID
			record.AdName = adMetrics.AdName
			record.Objective = adMetrics.Objective
			record.BuyingType = adMetrics.BuyingType
			record.OptimizationGoal = adMetrics.OptimizationGoal
			record.AttributionSetting = adMetrics.AttributionSetting
			record.Clicks = adMetrics.Clicks
			record.Impressions = adMetrics.Impressions
			record.Reach = adMetrics.Reach
			record.Frequency = adMetrics.Frequency
			record.Spend = adMetrics.Spend
			record.SocialSpend = adMetrics.SocialSpend
			record.CPM = adMetrics.CPM
			record.CTR = adMetrics.CTR
			record.CPC = adMetrics.CPC
			record.CPP = adMetrics.CPP
			record.WishBid = adMetrics.WishBid
			record.CostPerInlineLinkClick = adMetrics.CostPerInlineLinkClick
			record.CostPerInlinePostEngage = adMetrics.CostPerInlinePostEngage
			record.CostPerThruplay = adMetrics.CostPerThruplay
			record.CostPer15SecVideoView = adMetrics.CostPer15SecVideoView
			record.InlineLinkClicks = adMetrics.InlineLinkClicks
			record.InlineLinkClickCtr = adMetrics.InlineLinkClickCtr
			record.InlinePostEngagement = adMetrics.InlinePostEngagement
			record.UniqueClicks = adMetrics.UniqueClicks
			record.UniqueCTR = adMetrics.UniqueCTR
			record.WebsiteCTR = adMetrics.WebsiteCTR
			record.UniqueOutboundClicks = adMetrics.UniqueOutboundClicks
			record.VideoPlayActions = adMetrics.VideoPlayActions
			record.VideoAvgTimeWatchedActions = adMetrics.VideoAvgTimeWatchedActions
			// record.Actions = adMetrics.Actions
			record.ActionValues = adMetrics.ActionValues
			record.Conversions = adMetrics.Conversions
			record.ConversionValues = adMetrics.ConversionValues
			record.CostPerActionType = adMetrics.CostPerActionType
			record.CostPerConversion = adMetrics.CostPerConversion
			record.Video30SecWatched = adMetrics.Video30SecWatched
			record.VideoTimeWatched = adMetrics.VideoTimeWatched
			record.VideoRetention15s = adMetrics.VideoRetention15s
			record.VideoRetention60s = adMetrics.VideoRetention60s
			record.VideoRetentionGraph = adMetrics.VideoRetentionGraph
			record.VideoPlayCurveActions = adMetrics.VideoPlayCurveActions
			record.VideoP25Watched = adMetrics.VideoP25Watched
			record.VideoP50Watched = adMetrics.VideoP50Watched
			record.VideoP75Watched = adMetrics.VideoP75Watched
			record.VideoP95Watched = adMetrics.VideoP95Watched
			record.VideoP100Watched = adMetrics.VideoP100Watched
			record.DateStart = adMetrics.DateStart
			record.DateStop = adMetrics.DateStop
			record.Age = adMetrics.Age
			record.Gender = adMetrics.Gender
			record.Country = adMetrics.Country
			record.Placement = adMetrics.Placement
			record.DevicePlatform = adMetrics.DevicePlatform
			record.PublisherPlatform = adMetrics.PublisherPlatform
			record.ImpressionDevice = adMetrics.ImpressionDevice

			var actions []adserverReportE.ActionStats
			for _, action := range adMetrics.Actions {
				actions = append(actions, adserverReportE.ActionStats{
					ActionType: action.ActionType,
					Value:      action.Value,
				})
			}
			record.Actions = actions
		}

		datas = append(datas, record)
	}
	return &datas
}
