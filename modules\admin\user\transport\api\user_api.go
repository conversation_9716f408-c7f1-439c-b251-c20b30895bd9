package api

import (
	"context"
	"errors"
	"fmt"
	"godsp/modules/admin/common/admconst"
	"godsp/modules/admin/user/common/errs"
	"godsp/modules/admin/user/entity"
	"godsp/modules/admin/user/transport/requests"
	"godsp/modules/admin/user/transport/responses"
	"godsp/pkg/gos/utils"
	"godsp/pkg/sctx/core"
	"net/http"

	"github.com/gofiber/fiber/v2"
	"github.com/valyala/fasthttp"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

type UserApiUsc interface {
	CreateApiUserUsc(ctx context.Context, payload *requests.PayloadUserCreation) error
	ListDatatableApiUserUsc(ctx context.Context) (*[]responses.UserDataTable, error)
	UpdateUserUsc(ctx context.Context, data *requests.PayloadUserUpdate) (*entity.UserUpdate, error)
	GetListUserUsc(ctx context.Context) (*[]responses.ListUser, error)
	GetUserByIDUsc(ctx context.Context, userId primitive.ObjectID) (*responses.DetailsUser, error)
	UpdateBsonUserUsc(ctx context.Context, payload *requests.PayloadBsonUserUpdate) error
}

type userApi struct {
	usc UserApiUsc
}

func NewUserApi(usc UserApiUsc) *userApi {
	return &userApi{
		usc: usc,
	}
}

func (a *userApi) CreateUserApi() fiber.Handler {
	return func(c *fiber.Ctx) error {
		var param requests.PayloadUserCreation
		if err := c.BodyParser(&param); err != nil {
			return core.ReturnErrForApi(c, err.Error())
		}

		fmt.Printf("\n  Data Payload2 Request %+v : \n", param)

		//file
		file, err := c.FormFile("image")
		if err != nil && !errors.Is(err, fasthttp.ErrMissingFile) {
			return core.ReturnErrForApi(c, err.Error())
		} else {
			param.FileImg = file
		}

		//validate
		validationErrors := param.Validate()
		if len(validationErrors) > 0 {
			return core.ReturnErrsForApi(c, validationErrors)
		}
		userId, err := utils.GetUserIdPrimitive(c.Context())
		if err != nil {
			return core.ReturnErrForApi(c, err.Error())
		}

		param.UserID = userId

		if err := a.usc.CreateApiUserUsc(c.Context(), &param); err != nil {
			return core.ReturnErrForApi(c, err.Error())
		}

		return c.Status(http.StatusOK).JSON(core.ResponseData(map[string]interface{}{
			"message":  "Create user successfully!",
			"redirect": "/admins/users/list",
		}))
	}
}

/**
 * list datatable
 */

func (a *userApi) ListUserApi() fiber.Handler {
	return func(c *fiber.Ctx) error {
		data, err := a.usc.ListDatatableApiUserUsc(c.Context())

		if err != nil {
			return c.JSON(fiber.Map{
				"data": "",
			})
		}

		return c.JSON(fiber.Map{
			"data": data,
		})
	}
}

/**
 * list datatable
 */

func (a *userApi) ListUserAdminApi() fiber.Handler {
	return func(c *fiber.Ctx) error {
		data, err := a.usc.GetListUserUsc(c.Context())

		if err != nil {
			return core.ReturnErrForApi(c, err.Error())
		}
		if data == nil {
			return core.ErrForbidden
		}

		return c.JSON(fiber.Map{
			"data": data,
		})
	}
}

/**
 * Func update user
 * Neu roleID = inactive hoac role UNKNOWN =2> khong cho cap nhat status -> active
 */
func (a *userApi) UpdateUserApi() fiber.Handler {
	return func(c *fiber.Ctx) error {
		var param requests.PayloadUserUpdate
		ctx := c.Context()
		if err := c.BodyParser(&param); err != nil {

			return core.ReturnErrForApi(c, err.Error())
		}

		file, err := c.FormFile("image")
		if err != nil {
			if !errors.Is(err, fasthttp.ErrMissingFile) {
				return core.ReturnErrForApi(c, err.Error())
			}
		} else {
			param.FileImg = file
		}

		//validate
		validationErrors := param.Validate()
		if len(validationErrors) > 0 {
			return core.ReturnErrsForApi(c, validationErrors)
		}

		userId, err := utils.GetUserIdPrimitive(c.Context())
		if err != nil {
			return core.ReturnErrForApi(c, err.Error())
		}

		param.UserID = userId

		if param.UserID == param.ID && param.Status != admconst.STATUS_ACTIVE {
			return core.ReturnErrForApi(c, errs.ErrUpdateStatusUserLoginValidate.Error())
		}

		dataUser, err := a.usc.UpdateUserUsc(ctx, &param)
		if err != nil {
			return core.ReturnErrForApi(c, err.Error())
		}

		if param.UserID == param.ID && param.Email != param.OldEmail {
			return c.Status(http.StatusOK).JSON(core.ResponseData(map[string]interface{}{
				"msg":      "Updated user successfully.",
				"redirect": "/logout",
			}))
		}

		return c.Status(http.StatusOK).JSON(core.ResponseData(map[string]interface{}{
			"msg":      "Updated user successfully!",
			"redirect": "/admins/users/list",
			"data":     dataUser,
			"roleName": param.RoleName,
		}))
	}
}

/**
 * Func update user
 * Neu roleID = inactive hoac role UNKNOWN =2> khong cho cap nhat status -> active
 */
func (a *userApi) UpdateUserBsonApi() fiber.Handler {
	return func(c *fiber.Ctx) error {
		var param requests.PayloadBsonUserUpdate
		ctx := c.Context()
		if err := c.BodyParser(&param); err != nil {
			return core.ReturnErrForApi(c, err.Error())
		}

		//validate
		validationErrors := param.Validate()
		if len(validationErrors) > 0 {
			return core.ReturnErrsForApi(c, validationErrors)
		}

		userId, err := utils.GetUserIdPrimitive(c.Context())
		if err != nil {
			return core.ReturnErrForApi(c, err.Error())
		}

		param.UserID = userId

		err = a.usc.UpdateBsonUserUsc(ctx, &param)
		if err != nil {
			return core.ReturnErrForApi(c, err.Error())
		}

		return c.Status(http.StatusOK).JSON(core.ResponseData(map[string]interface{}{
			"msg": "Updated user successfully.",
		}))
	}
}

func (a *userApi) GetUserByIDApi() fiber.Handler {
	return func(c *fiber.Ctx) error {
		userId, err := utils.GetUserId(c.Context())
		if err != nil {
			return core.ReturnErrForApi(c, err.Error())
		}

		user, err := a.usc.GetUserByIDUsc(c.Context(), userId)
		if err != nil {
			return core.ReturnErrForApi(c, err.Error())
		}

		return c.Status(http.StatusOK).JSON(core.ResponseData(map[string]interface{}{
			"msg":  "Get user successfully!",
			"data": user,
		}))
	}
}
