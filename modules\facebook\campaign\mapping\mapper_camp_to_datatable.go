package mapping

import (
	"fmt"
	adserverReportE "godsp/modules/facebook/adserver_report/entity"
	"godsp/modules/facebook/campaign/entity"
	"godsp/modules/facebook/campaign/transport/responses"
)

// ReportCampaignItem is a flat struct combining FacebookAdfbReportDetails and CampaignEntity fields

func MapperCampaignToDatatable(campaigns *[]entity.CampaignEntity, fbReportDetails *[]adserverReportE.FBReportDetailEntity) *[]responses.ReportCampaignItem {

	if campaigns == nil {
		return nil
	}

	fbReportDetailsData := make(map[string]adserverReportE.FBReportDetailEntity)

	// jsonData, _ := json.MarshalIndent(campaigns, "", "  ")

	// fmt.Printf("campFB  %+v", string(jsonData))

	// jsonData2, _ := json.MarshalIndent(fbReportDetails, "", "  ")
	// fmt.Printf("jsonData2  %+v", string(jsonData2))

	// jsonData, _ := json.MarshalIndent(fbReportDetailsData, "", "  ")
	// fmt.Printf("fbReportDetails ------->: %v\n", fbReportDetails)

	if fbReportDetails != nil {
		for _, fbReportDetail := range *fbReportDetails {
			fbReportDetailsData[fbReportDetail.CampaignID] = fbReportDetail
		}
	}

	var datas []responses.ReportCampaignItem
	for _, campaign := range *campaigns {
		record := responses.ReportCampaignItem{
			DTRowId:                       fmt.Sprintf("row_%s", campaign.CampaignID),
			AccountID:                     campaign.AccountID,
			CampaignID:                    campaign.CampaignID,
			Name:                          campaign.Name,
			Objective:                     campaign.Objective,
			BidStrategy:                   campaign.BidStrategy,
			BidAmount:                     campaign.BidAmount,
			ConfiguredStatus:              campaign.ConfiguredStatus,
			DailyBudget:                   campaign.DailyBudget,
			EffectiveStatus:               campaign.EffectiveStatus,
			LifeTimeBudget:                campaign.LifeTimeBudget,
			CanUseSpendCap:                campaign.CanUseSpendCap,
			SpendCap:                      campaign.SpendCap,
			StartTime:                     campaign.StartTime,
			StopTime:                      campaign.StopTime,
			Status:                        campaign.Status,
			SpecialAdCategories:           campaign.SpecialAdCategories,
			BudgetRemaining:               campaign.BudgetRemaining,
			CampaignGroupActiveTime:       campaign.CampaignGroupActiveTime,
			HasSecondarySkadnetworkReport: campaign.HasSecondarySkadnetworkReporting,
			IsBudgetScheduleEnabled:       campaign.IsBudgetScheduleEnabled,
			IsSkadnetworkAttribution:      campaign.IsSkadnetworkAttribution,
			LastBudgetTogglingTime:        campaign.LastBudgetTogglingTime,
			PrimaryAttribution:            campaign.PrimaryAttribution,
			SmartPromotionType:            campaign.SmartPromotionType,
			SourceCampaignID:              campaign.SourceCampaignId,
			SpecialAdCategory:             campaign.SpecialAdCategory,
			UpdatedAt:                     campaign.UpdatedAt,
			CreatedAt:                     campaign.CreatedAt,
			Approve:                       campaign.Approve,
		}

		if campaignMetrics, exists := fbReportDetailsData[campaign.CampaignID]; exists {
			record.CampaignID = campaignMetrics.CampaignID
			record.CampaignName = campaignMetrics.CampaignName
			record.AccountID = campaignMetrics.AccountID
			record.AccountName = campaignMetrics.AccountName
			record.AccountCurrency = campaignMetrics.AccountCurrency
			// ID                 primitive.ObjectID `json:"_id,omitempty" bson:"_id,omitempty"`

			// ========== Mentric Prority ==========
			record.Impressions = campaignMetrics.Impressions
			record.Reach = campaignMetrics.Reach
			record.VideoView = campaignMetrics.VideoView
			record.PostReaction = campaignMetrics.PostReaction
			record.PostComment = campaignMetrics.PostComment
			record.PostShare = campaignMetrics.PostShare
			record.Clicks = campaignMetrics.Clicks
			record.LinkClick = campaignMetrics.LinkClick
			record.PostEngagement = campaignMetrics.PostEngagement
			record.PostSave = campaignMetrics.PostSave
			record.PageLike = campaignMetrics.PageLike

			// ========== End Mentric Prority ==========

			record.AdID = campaignMetrics.AdID
			record.AdName = campaignMetrics.AdName
			record.AdSetID = campaignMetrics.AdSetID
			record.AdSetName = campaignMetrics.AdSetName
			// record.Objective = campaignMetrics.Objective
			record.BuyingType = campaignMetrics.BuyingType
			record.AttributionSetting = campaignMetrics.AttributionSetting
			record.DateStart = campaignMetrics.DateStart
			record.DateStop = campaignMetrics.DateStop
			record.Date = campaignMetrics.Date
			// ========== Demographics ==========
			record.Age = campaignMetrics.Age
			record.Gender = campaignMetrics.Gender
			record.Country = campaignMetrics.Country
			record.Placement = campaignMetrics.Placement
			record.DevicePlatform = campaignMetrics.DevicePlatform
			record.PublisherPlatform = campaignMetrics.PublisherPlatform
			record.ImpressionDevice = campaignMetrics.ImpressionDevice

			// ========== Performance Metrics ==========
			record.Clicks = campaignMetrics.Clicks
			record.Impressions = campaignMetrics.Impressions
			record.Reach = campaignMetrics.Reach
			record.Frequency = campaignMetrics.Frequency
			record.Spend = campaignMetrics.Spend
			record.SocialSpend = campaignMetrics.SocialSpend
			record.CPM = campaignMetrics.CPM
			record.CTR = campaignMetrics.CTR
			record.CPC = campaignMetrics.CPC
			record.CPP = campaignMetrics.CPP
			record.WishBid = campaignMetrics.WishBid
			record.OptimizationGoal = campaignMetrics.OptimizationGoal
			// ========== Engagement ==========
			record.InlineLinkClicks = campaignMetrics.InlineLinkClicks
			record.InlineLinkClickCtr = campaignMetrics.InlineLinkClickCtr
			record.InlinePostEngagement = campaignMetrics.InlinePostEngagement
			record.UniqueClicks = campaignMetrics.UniqueClicks
			record.UniqueCTR = campaignMetrics.UniqueCTR
			record.UniqueOutboundClicks = campaignMetrics.UniqueOutboundClicks
			record.WebsiteCTR = campaignMetrics.WebsiteCTR
			record.ActionValues = campaignMetrics.ActionValues
			record.Conversions = campaignMetrics.Conversions
			record.ConversionValues = campaignMetrics.ConversionValues
			record.CostPerActionType = campaignMetrics.CostPerActionType
			record.CostPerConversion = campaignMetrics.CostPerConversion
			record.CostPerInlineLinkClick = campaignMetrics.CostPerInlineLinkClick
			record.CostPerInlinePostEngage = campaignMetrics.CostPerInlinePostEngage

			// ========== Video Metrics ==========
			record.CostPerThruplay = campaignMetrics.CostPerThruplay
			record.CostPer15SecVideoView = campaignMetrics.CostPer15SecVideoView
			record.VideoAvgTimeWatchedActions = campaignMetrics.VideoAvgTimeWatchedActions
			record.VideoPlayActions = campaignMetrics.VideoPlayActions
			record.Video30SecWatched = campaignMetrics.Video30SecWatched
			record.VideoTimeWatched = campaignMetrics.VideoTimeWatched
			record.VideoRetention15s = campaignMetrics.VideoRetention15s
			record.VideoRetention60s = campaignMetrics.VideoRetention60s
			record.VideoRetentionGraph = campaignMetrics.VideoRetentionGraph
			record.VideoPlayCurveActions = campaignMetrics.VideoPlayCurveActions
			record.VideoP25Watched = campaignMetrics.VideoP25Watched
			record.VideoP50Watched = campaignMetrics.VideoP50Watched
			record.VideoP75Watched = campaignMetrics.VideoP75Watched
			record.VideoP95Watched = campaignMetrics.VideoP95Watched
			record.VideoP100Watched = campaignMetrics.VideoP100Watched

			var actions []adserverReportE.ActionStats
			for _, action := range campaignMetrics.Actions {
				actions = append(actions, adserverReportE.ActionStats{
					ActionType: action.ActionType,
					Value:      action.Value,
				})
			}
			record.Actions = &actions
		}

		datas = append(datas, record)
	}
	return &datas
}
