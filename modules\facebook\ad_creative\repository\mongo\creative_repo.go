package mongo

import (
	"context"
	"errors"
	"godsp/modules/facebook/ad_creative/entity"
	"godsp/pkg/sctx/core"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
)

type adCreativeRepo struct {
	DB         *mongo.Database
	Collection *mongo.Collection
}

func NewAdCreativeRepo(DB *mongo.Database) *adCreativeRepo {
	return &adCreativeRepo{
		DB:         DB,
		Collection: DB.Collection(entity.AdCreativeEntity{}.CollectionName()),
	}
}

// upser creative
func (r *adCreativeRepo) UpsertAdCreativeRepo(ctx context.Context, filter bson.M, update bson.M) error {
	opts := options.Update().SetUpsert(true)
	_, err := r.Collection.UpdateOne(ctx, filter, update, opts)
	if err != nil {
		return err
	}

	return nil
}

/**
 * FindOne ad creative
 */
func (r *adCreativeRepo) FindOneAdCreativeRepo(ctx context.Context, filter interface{}, opts ...*options.FindOneOptions) (*entity.AdCreativeEntity, error) {
	var adAccount entity.AdCreativeEntity
	err := r.Collection.FindOne(ctx, filter, opts...).Decode(&adAccount)
	if errors.Is(err, mongo.ErrNoDocuments) {
		return nil, core.ErrNotFound
	}

	return &adAccount, nil
}

/**
 * Delete ad creative
 */
func (r *adCreativeRepo) DeleteAdCreativeRepo(ctx context.Context, filter bson.M) error {
	_, err := r.Collection.DeleteOne(ctx, filter)
	if err != nil {
		return err
	}

	return nil
}

func (r *adCreativeRepo) FindAdCreativesUseMessageTemplateRepo(
	ctx context.Context,
	filter interface{},
	opts ...*options.FindOneOptions) ([]*entity.AdCreativeEntity, error) {
	return nil, nil
}
