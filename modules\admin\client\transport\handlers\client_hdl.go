package handlers

import (
	"context"
	"godsp/conf"
	"godsp/modules/admin/client/entity"
	"godsp/modules/admin/client/mapping"
	"godsp/modules/admin/client/transport/responses"
	"godsp/modules/admin/common/admconst"
	userE "godsp/modules/admin/user/entity"
	adAccRes "godsp/modules/facebook/ad_account/transport/response"
	pixelE "godsp/modules/facebook/ad_pixel/entity"
	pageE "godsp/modules/facebook/pages/entity"
	tikAdverRes "godsp/modules/tiktok/advertiser/transport/responses"

	"godsp/pkg/gos/utils"

	"github.com/dev-networldasia/dspgos/sctx/core"
	"github.com/gofiber/fiber/v2"
	"github.com/gofiber/fiber/v2/middleware/session"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

type ClientUsc interface {
	FindOneDetailClientUsc(ctx context.Context, id string) (*entity.ClientEntity, error)
	GetUserOfClientUsc(ctx context.Context, id primitive.ObjectID) (*[]userE.UserEntity, error)
	ListAdAccountClientUsc(ctx context.Context) ([]*adAccRes.AdAccountEditClient, error)
	ListPageClientUsc(ctx context.Context) ([]*pageE.PageEntity, error)
	ListPixelClientUsc(ctx context.Context) ([]*pixelE.AdPixelEntity, error)
	ListCatalogueClientUsc(ctx context.Context) ([]*responses.CatalogueListEditClient, error)
	GetListAdAccountsTiktokUsc(ctx context.Context) (*[]tikAdverRes.ListAdvertiserRes, error)
	GetListPresetColumnTiktokUsc(context context.Context) ([]*mapping.TiktokPresetColumnTableRes, error)
}

type clientHdl struct {
	usc ClientUsc
}

func NewClientHdl(usc ClientUsc) *clientHdl {
	return &clientHdl{
		usc: usc,
	}
}

/**
 * List role hdl
 */
func (h *clientHdl) ListClientHdl() fiber.Handler {
	return func(c *fiber.Ctx) error {
		return c.Render("admins/clients/index", fiber.Map{
			"status": admconst.StatusFullName,
			"show": map[string]interface{}{
				"statusCreate": admconst.StatusCreateName,
				"defaultImg":   admconst.DEFAULT_IMG_ADMIN,
				"statusActive": admconst.STATUS_ACTIVE,
			},
		})
	}
}

/**
 * Edit user hdl
 */
func (h *clientHdl) EditClientHdl(store *session.Store) fiber.Handler {
	return func(c *fiber.Ctx) error {
		ctx := c.Context()
		id := c.Params("id")

		var clientId primitive.ObjectID
		if id != "" {
			oid, err := primitive.ObjectIDFromHex(id)
			if err != nil {
				utils.SetFlashMessage(c, store, "editClient", err.Error())
				return c.Redirect("/admins/clients/list")
			} else {
				clientId = oid
			}
		}

		detail, err := h.usc.FindOneDetailClientUsc(ctx, id)

		if err != nil {
			return c.Redirect("/admins/clients/list")
		}

		//System Users
		usersOfClient, err := h.usc.GetUserOfClientUsc(ctx, clientId)
		if err != nil {
			return c.Redirect("/admins/clients/list")
		}

		// Facebook Pages
		pages, err := h.usc.ListPageClientUsc(ctx)
		if err != nil {
			return c.Redirect("/admins/clients/list")
		}

		// Facebook AdAccounts
		adAccounts, err := h.usc.ListAdAccountClientUsc(ctx)
		if err != nil {
			return c.Redirect("/admins/clients/list")
		}

		// Facebook Pixels
		pixels, err := h.usc.ListPixelClientUsc(ctx)
		if err != nil {
			return c.Redirect("/admins/clients/list")
		}

		// jsonData2, _ := json.MarshalIndent(pixels, "", "  ")
		// fmt.Printf("pixels  ------->: %v\n", *client_id)
		// client_id := utils.GetClientIdPrimitive(ctx)

		// Facebook Catalogues
		catalogues, err := h.usc.ListCatalogueClientUsc(ctx)
		if err != nil {
			return c.Redirect("/admins/clients/list")
		}

		// Tiktok Advertisers
		tikAdvertisers, err := h.usc.GetListAdAccountsTiktokUsc(ctx)
		if err != nil {
			return c.Redirect("/admins/clients/list")
		}

		// Tiktok Preset Column
		presetColumns, err := h.usc.GetListPresetColumnTiktokUsc(ctx)
		if err != nil {
			return c.Redirect("/admins/clients/list")
		}

		_, roleName, clientId, _, _ := utils.GetInfoUser(c.Context())

		isAdmin := roleName == conf.SysConf.RoleAdmin
		return c.Render("admins/clients/edit", fiber.Map{
			"authPermission": core.GetPermission(c.Context()).GetPermissions(),
			"userInfo": map[string]interface{}{
				"roleName": roleName,
				"clientId": clientId,
			},
			"isAdmin":           isAdmin,
			"detail":            detail,
			"status":            admconst.StatusFullName,
			"statusCreate":      admconst.StatusCreateName,
			"defaultImg":        admconst.DEFAULT_IMG_ADMIN,
			"productDefaultImg": conf.PathImgProductDefault,
			"pages":             pages,
			"pixels":            pixels,
			"adAccounts":        adAccounts,
			"catalogues":        catalogues,
			"tiktokAdvertisers": tikAdvertisers,
			"presetColumns":     presetColumns,
			"users":             usersOfClient,
		})
	}
}
