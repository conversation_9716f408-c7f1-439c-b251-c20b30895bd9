package consts

type BillingColumn struct {
	Prefix string
	Title  string
}

type BillingStatus struct {
	Prefix string
	Title  string
}

const (
	RETRY_MAX_COUNT       = 3
	DEFAULT_IMG_ADMIN     = "/static/images/user-dummy-img.jpg"
	SERVICE_NAME_BILLINGS = "billings"

	PLATFORM_FB    = "FB"
	PLATFORM_DV360 = "DV360"

	BILLING_STATUS_CREATE_PAYMENT_ID = 0
	BILLING_STATUS_CHECKING_ID       = 1
	BILLING_STATUS_COMPLETED_ID      = 2
	BILLING_STATUS_FAILED_ID         = 3
	BILLING_STATUS_CREATE_PAYMENT    = "CREATE"
	BILLING_STATUS_CHECKING          = "CHECKING"
	BILLING_STATUS_COMPLETED         = "COMPLETED"
	BILLING_STATUS_FAILED            = "FAILED"

	BILLING_DETAIL_STATUS_CREATE_PAYMENT      = "CREATE"
	BILLING_DETAIL_STATUS_ACCOUNTANT_APPROVED = "ACCOUNTANT_APPROVED"
	BILLING_DETAIL_STATUS_ACCOUNTANT_REJECTED = "ACCOUNTANT_REJECTED"
	BILLING_DETAIL_STATUS_ADMIN_APPROVED      = "ADMIN_APPROVED"
	BILLING_DETAIL_STATUS_ADMIN_REJECTED      = "ADMIN_REJECTED"
	BILLING_DETAIL_STATUS_FAILED              = "FAILED"

	IS_IPN_NONE   = 0
	IS_IPN_ACTIVE = 1
	IS_IPN_ERROR  = 2

	DV360_PAYMENT_API_PREFIX                = "/api/payments"
	DV360_BILLING_PAYMENT_API_PREFIX        = DV360_PAYMENT_API_PREFIX + "/billing"
	DV360_BILLING_DETAIL_PAYMENT_API_PREFIX = DV360_PAYMENT_API_PREFIX + "/billing-detail"
)

var (
	BILLING_STATUS_TYPES = map[string]BillingStatus{
		BILLING_STATUS_CREATE_PAYMENT: {Prefix: BILLING_STATUS_CREATE_PAYMENT, Title: "Create"},
		BILLING_STATUS_CHECKING:       {Prefix: BILLING_STATUS_CHECKING, Title: "Checking"},
		BILLING_STATUS_COMPLETED:      {Prefix: BILLING_STATUS_COMPLETED, Title: "Completed"},
		BILLING_STATUS_FAILED:         {Prefix: BILLING_STATUS_FAILED, Title: "Failed"},
	}
	BILLING_COLUMNS_BY_ROLE = map[string][]BillingColumn{
		"ADMIN": {
			{Prefix: "client", Title: "Client"},
			{Prefix: "billNumber", Title: "Bill number"},
			{Prefix: "title", Title: "Title"},
			{Prefix: "amount", Title: "Amount"},
			{Prefix: "balance", Title: "Balance"},
			{Prefix: "currency", Title: "Currency"},
			{Prefix: "billingType", Title: "Billing type"},
			{Prefix: "description", Title: "Description"},
			{Prefix: "billingDate", Title: "Billing date"},
			{Prefix: "isIpn", Title: "Is Ipn"},
			{Prefix: "isActive", Title: "Is Active"},
			{Prefix: "status", Title: "Status"},
			{Prefix: "platform", Title: "Platform"},
			{Prefix: "approvedUser", Title: "Approved User"},
			{Prefix: "approvedAt", Title: "Approved At"},
			{Prefix: "bankCodeId", Title: "Bank code id"},
			{Prefix: "bankCode", Title: "Bank code"},
			{Prefix: "merchantName", Title: "Merchant name"},
		},
		"ACCOUNTANT": {
			{Prefix: "billNumber", Title: "Bill number"},
			{Prefix: "title", Title: "Title"},
			{Prefix: "amount", Title: "Amount"},
			{Prefix: "balance", Title: "Balance"},
			{Prefix: "currency", Title: "Currency"},
			{Prefix: "billingType", Title: "Billing type"},
			{Prefix: "description", Title: "Description"},
			{Prefix: "billingDate", Title: "Billing date"},
			{Prefix: "isActive", Title: "Is Active"},
			{Prefix: "status", Title: "Status"},
			{Prefix: "approvedUser", Title: "Approved User"},
			{Prefix: "approvedAt", Title: "Approved At"},
		},
		"CLIENT": {
			{Prefix: "billNumber", Title: "Bill number"},
			{Prefix: "title", Title: "Title"},
			{Prefix: "amount", Title: "Amount"},
			{Prefix: "balance", Title: "Balance"},
			{Prefix: "currency", Title: "Currency"},
			{Prefix: "billingType", Title: "Billing type"},
			{Prefix: "description", Title: "Description"},
			{Prefix: "billingDate", Title: "Billing date"},
			{Prefix: "status", Title: "Status"},
		},
	}

	DV360_BILLING_API = map[string]string{
		"LIST":          DV360_BILLING_PAYMENT_API_PREFIX + "/list",
		"STORE":         DV360_BILLING_PAYMENT_API_PREFIX + "/create-bank-transfer",
		"UPDATE":        DV360_BILLING_PAYMENT_API_PREFIX + "/update",
		"UPDATE_STATUS": DV360_BILLING_PAYMENT_API_PREFIX + "/update-status",
		"EDIT":          DV360_BILLING_PAYMENT_API_PREFIX + "/edit",
		"CREATE_VTB":    DV360_BILLING_PAYMENT_API_PREFIX + "/create-vtb-payment",
	}

	DV360_BILLING_DETAIL_API = map[string]string{
		"LIST":   DV360_BILLING_DETAIL_PAYMENT_API_PREFIX + "/list",
		"UPDATE": DV360_BILLING_DETAIL_PAYMENT_API_PREFIX + "/update",
	}
)
