package pipelines

import "go.mongodb.org/mongo-driver/bson"

func PipelineListTableUsers() []bson.M {
	pipeline := []bson.M{
		{
			"$match": bson.M{
				"path": bson.M{
					"$nin": []string{
						"/login",
						"/api/auths/login",
						"/register",
						"/page-forbidden",
						"/logout",
					},
				},
			},
		},
		{
			"$lookup": bson.M{
				"from":         "admin_users",
				"localField":   "created_by",
				"foreignField": "_id",
				"as":           "user_created",
			},
		},
		{
			"$lookup": bson.M{
				"from":         "admin_users",
				"localField":   "updated_by",
				"foreignField": "_id",
				"as":           "user_updated",
			},
		},
		{
			"$lookup": bson.M{
				"from":         "admins_permissions",
				"localField":   "parent_id",
				"foreignField": "_id",
				"as":           "parent",
			},
		},
		{
			"$unwind": bson.M{
				"path":                       "$user_created",
				"preserveNullAndEmptyArrays": true,
			},
		},
		{
			"$unwind": bson.M{
				"path":                       "$user_updated",
				"preserveNullAndEmptyArrays": true,
			},
		},
		{
			"$unwind": bson.M{
				"path":                       "$parent",
				"preserveNullAndEmptyArrays": true,
			},
		},
		{
			"$sort": bson.M{
				"module":   1,
				"ordering": 1,
			},
		},
	}

	return pipeline
}
