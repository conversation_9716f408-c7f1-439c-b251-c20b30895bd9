package responses

import "go.mongodb.org/mongo-driver/bson/primitive"

type DetailsUser struct {
	ID       primitive.ObjectID `json:"_id" bson:"_id"`
	Email    string             `json:"email" bson:"email"`
	RoleName string             `json:"role_name" bson:"role_name"`
	Image    string             `json:"image"  bson:"image"`
	FirtName string             `json:"first_name" bson:"first_name"`
	LastName string             `json:"last_name" bson:"last_name"`
	Username *string            `json:"username,omitempty" bson:"username"`
	Gender   int                `json:"gender" bson:"gender"`
}
