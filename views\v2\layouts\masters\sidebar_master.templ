package masters

import "github.com/dev-networldasia/dspgos/gos/templates"

templ Sidebar(data LayoutMasterData) {
	<div class="app-menu navbar-menu">
		@nabarLogo()
		@menus(data)
		<div class="sidebar-background"></div>
	</div>
}

templ nabarLogo() {
	<div class="navbar-brand-box">
		<a href={ templates.SafeURL("/admins") } class="logo logo-dark">
			<span class="logo-sm">
				<img src={ templates.AssetURL("/static/images/symbol-logo-dark.svg") } alt="" style="height: 2.2em"/>
			</span>
			<span class="logo-lg">
				<img src={ templates.AssetURL("/static/images/logo-dark.png") } alt="" style="height: 5.5em"/>
			</span>
		</a>
		<button type="button" class="btn btn-sm p-0 fs-20 header-item float-end btn-vertical-sm-hover" id="vertical-hover">
			<i class="ri-record-circle-line"></i>
		</button>
	</div>
}

templ menus(data LayoutMasterData) {
	@styleSibarMenu()
	<div id="scrollbar" class="h-100">
		<div class="container-fluid">
			<div id="two-column-menu"></div>
			<ul class="navbar-nav navbar-nav-custom" id="navbar-nav">
				if data.AuthPermission == nil {
					@menuItemAdmin(data)
				} else {
					@menuFeaturesUser(data)
				}
				if data.AuthPermission != nil {
					if val, ok := data.AuthPermission["/dsp/facebook/api/campaigns/list-datatable-POST"]; ok && val == 1 {
						@menuItemFacebook(data)
					}
				} else {
					@menuItemFacebook(data)
				}
				if data.AuthPermission != nil {
					if val, ok := data.AuthPermission["/dsp/tiktok/api/campaign/list-table-POST"]; ok && val == 1 {
						@menuItemTiktok(data)
					}
				} else {
					@menuItemTiktok(data)
				}
				@menuDV360()
			</ul>
		</div>
	</div>
}

templ menuItemAdmin(data LayoutMasterData) {
	// <li class="menu-title"><i class="ri-more-fill"></i> <span data-key="t-admin-manager">Admin Manager</span></li>
	<li class="nav-item">
		<a
			class="nav-link menu-link"
			href="#sidebarAdmins"
			data-bs-toggle="collapse"
			role="button"
			aria-expanded="true"
			aria-controls="sidebarAdmins"
			style="height: 48px;"
		>
			<img
				src={ templates.AssetURL("/static/img/admin_icon.svg") }
				width="22px"
				style="margin-right: 3px;-: 2px;min-width: 1.5em;margin-left: -2px;"
			/>
			<span class="item-text">Admins</span>
		</a>
		<div class="menu-dropdown collapse show" id="sidebarAdmins">
			<ul class="nav nav-sm flex-column">
				<li class="nav-item">
					<a class="nav-link" href={ templates.SafeURL("/admins/clients/list") }>
						<i class=" ri-building-4-line"></i>
						<span data-key="t-admins-clients">Clients</span>
					</a>
				</li>
				<li class="nav-item">
					<a class="nav-link" href={ templates.SafeURL("/admins/users/list") }>
						<i class="ri-user-settings-line"></i>
						<span data-key="t-admins-users">Users</span>
					</a>
				</li>
				<li class="nav-item">
					<a class="nav-link" href={ templates.SafeURL("/admins/billings/list") }>
						<i class="ri-bank-card-line"></i>
						<span data-key="t-admins-billings">Billings</span>
					</a>
				</li>
				<li class="nav-item">
					<a class="nav-link" href={ templates.SafeURL("/admins/roles/list") }>
						<i class="ri-shield-user-line"></i>
						<span data-key="t-admins-roles">Roles</span>
					</a>
				</li>
				<li class="nav-item">
					<a class="nav-link" href={ templates.SafeURL("/admins/permissions/list") }>
						<i class="ri-lock-2-line"></i>
						<span data-key="t-admins-permissions">Permissions</span>
					</a>
				</li>
			</ul>
		</div>
	</li>
}

templ menuFeaturesUser(data LayoutMasterData) {
	// <li class="menu-title"><i class="ri-more-fill"></i> <span data-key="t-system-feature">Menu</span></li>
	<li class="nav-item">
		<a
			class="nav-link menu-link"
			href="#sidebarsystemFeature"
			data-bs-toggle="collapse"
			role="button"
			aria-expanded="true"
			aria-controls="sidebarsystemFeature"
			style="height: 48px;"
		>
			<img
				src={ templates.AssetURL("/static/img/admin_icon.svg") }
				width="28px"
				style="margin-right: 3px;-: 2px;min-width: 1.5em;margin-left: -2px;"
			/>
			<span class="item-text">Features</span>
		</a>
		<div class="menu-dropdown collapse show" id="sidebarsystemFeature">
			<ul class="nav nav-sm flex-column">
				if val, ok := data.AuthPermission["/admins/clients/profile-GET"]; ok && val == 1 {
					<li class="nav-item">
						<a class="nav-link" href={ templates.SafeURL("/admins/clients/profile/" + data.UserInfo.ClientId.Hex()) }>
							<i class=" ri-building-4-line"></i>
							<span data-key="t-system-feature-client-profile">Client Profile</span>
						</a>
					</li>
				}
				if val, ok := data.AuthPermission["/admins/billings/list-GET"]; ok && val == 1 {
					<li class="nav-item">
						<a class="nav-link" href={ templates.SafeURL("/admins/billings/list") }>
							<i class="ri-article-line"></i>
							<span data-key="t-system-feature-billing">Billings</span>
						</a>
					</li>
				}
				<li class="nav-item">
					<a class="nav-link" href={ templates.SafeURL("/admins/users/profile/" + data.UserInfo.UserId.Hex()) }>
						<i class="ri-account-box-line"></i>
						<span data-key="t-system-feature-user-profile">My Profile</span>
					</a>
				</li>
			</ul>
		</div>
	</li>
}

templ menuItemFacebook(data LayoutMasterData) {
	// <li class="menu-title"><i class="ri-more-fill"></i> <span data-key="t-facebook-ads">Facebook Ads</span></li>
	<li class="nav-item">
		<a
			class="nav-link menu-link"
			href="#sidebarFacebook"
			data-bs-toggle="collapse"
			role="button"
			aria-expanded="true"
			aria-controls="sidebarFacebook"
			style="height: 48px;"
		>
			<img
				src={ templates.AssetURL("/static/img/meta_icon.svg") }
				width="22px"
				style="margin-right: 3px;-: 2px;min-width: 1.5em;margin-left: -2px;"
			/>
			<span class="item-text">Facebook</span>
		</a>
		<div class="menu-dropdown collapse show" id="sidebarFacebook">
			<ul class="nav nav-sm flex-column">
				<li class="nav-item">
					<a href={ templates.SafeURL("/dsp/facebook/campaigns/list") } class="nav-link" data-key="t-campaigns">
						<i class=" ri-folder-chart-2-line"></i> Campaigns
					</a>
				</li>
				<li class="nav-item">
					<a href={ templates.SafeURL("/dsp/facebook/adsets/list") } class="nav-link" data-key="t-assets">
						<i class=" ri-folders-line"></i> Ad sets
					</a>
				</li>
				<li class="nav-item">
					<a href={ templates.SafeURL("/dsp/facebook/ads/list") } class="nav-link" data-key="t-ads">
						<i class="ri-file-chart-2-line"></i> Ads
					</a>
				</li>
				<li class="nav-item">
					<a href={ templates.SafeURL("/dsp/facebook/audiences/list") } class="nav-link" data-key="t-adiences">
						<i class=" ri-file-search-line"></i> Audiences
					</a>
				</li>
			</ul>
		</div>
	</li>
}

templ menuItemTiktok(data LayoutMasterData) {
	// <li class="menu-title"><i class="ri-more-fill"></i> <span data-key="t-tiktok-ads">Tiktok Ads</span></li>
	<li class="nav-item">
		<a
			class="nav-link menu-link"
			href="#sidebar-tiktok"
			data-bs-toggle="collapse"
			role="button"
			aria-expanded="true"
			aria-controls="sidebar-tiktok"
			style="height: 48px;"
		>
			<img
				src={ templates.AssetURL("/static/img/tiktok-icon.svg") }
				width="22px"
				style="margin-right: 3px;-: 2px;min-width: 1.5em;margin-left: -2px;"
			/>
			<span class="item-text">Tiktok</span>
		</a>
		<div class="menu-dropdown collapse show" id="sidebar-tiktok">
			<ul class="nav nav-sm flex-column">
				<li class="nav-item">
					<a href={ templates.SafeURL("/dsp/tiktok/gmv-max-campaign/list") } class="nav-link" data-key="t-gmv-max-campaign">
						<i class=" ri-folder-chart-2-line"></i>
						GMV Max
					</a>
				</li>
				<li class="nav-item">
					<a href={ templates.SafeURL("/dsp/tiktok/campaign/list") } class="nav-link" data-key="t-campaigns">
						<i class=" ri-folder-chart-2-line"></i>
						Campaigns
					</a>
				</li>
				<li class="nav-item">
					<a href={ templates.SafeURL("/dsp/tiktok/adgroup/list") } class="nav-link" data-key="t-adgroup">
						<i class=" ri-folders-line"></i>
						Ad Groups
					</a>
				</li>
				<li class="nav-item">
					<a href={ templates.SafeURL("/dsp/tiktok/ad/list") } class="nav-link" data-key="t-ads">
						<i class="ri-file-chart-2-line"></i>
						Ads
					</a>
				</li>
				<li class="nav-item">
					<a href={ templates.SafeURL("/dsp/tiktok/gmv-max-campaign/list") } class="nav-link" data-key="t-gmv-max-campaigns">
						<i class=""></i> GMV Max
					</a>
				</li>
				if val, ok := data.AuthPermission["//dsp/tiktok/ad-accounts/list-GET"]; ok && val == 1 {
					<li class="nav-item">
						<a href={ templates.SafeURL("/dsp/tiktok/advertiser/list") } class="nav-link" data-key="t-ads">
							<i class=" ri-account-box-line"></i>
							Advertisers
						</a>
					</li>
				}
			</ul>
		</div>
	</li>
}

templ menuDV360() {
	// <li class="menu-title"><i class="ri-more-fill"></i> <span data-key="t-tiktok-ads">Display & Video 360</span></li>
	<li class="nav-item">
		<a
			class="nav-link menu-link"
			href="#sidebar-dv360"
			data-bs-toggle="collapse"
			role="button"
			aria-expanded="true"
			aria-controls="sidebar-dv360"
			style="height: 48px;"
		>
			<img
				src={ templates.AssetURL("/static/img/dv360_icon.svg") }
				width="22px"
				style="margin-right: 3px;-: 2px;min-width: 1.5em;margin-left: -2px;"
			/>
			<span class="item-text">Display & Video 360 </span>
		</a>
		<div class="menu-dropdown collapse show" id="sidebar-dv360">
			<ul class="nav nav-sm flex-column">
				<li class="nav-item">
					<a href="https://dv360.networldsolutions.net//dsp/dv360/campaigns" class="nav-link" data-key="t-ad-accounts">
						<i class=" ri-folder-chart-2-line"></i>
						Campaigns
					</a>
				</li>
				<li class="nav-item">
					<a href="https://dv360.networldsolutions.net//dsp/dv360/creatives" class="nav-link" data-key="t-dv360-creatives">
						<i class="ri-pencil-ruler-2-line"></i>
						Creatives
					</a>
				</li>
				<li class="nav-item">
					<a href="https://dv360.networldsolutions.net//dsp/dv360/reports" class="nav-link" data-key="t-dv360-creatives">
						<i class="ri-newspaper-line"></i>
						Reports
					</a>
				</li>
			</ul>
		</div>
	</li>
}

templ styleSibarMenu() {
	<style>
	html[data-sidebar-size="sm"] .navbar-nav .simplebar-mask .nav-item:hover img {
		filter: brightness(0) invert(1);
	}
</style>
}
