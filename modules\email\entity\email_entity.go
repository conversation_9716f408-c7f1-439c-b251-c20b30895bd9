package entity

import (
	"time"

	"go.mongodb.org/mongo-driver/bson/primitive"
)

type EmailEntity struct {
	To      string `json:"to" bson:"to"`
	Subject string `json:"subject" bson:"subject"`
	Body    string `json:"body" bson:"body"` // nội dung email đã được mã hoá -> có vẫn đề khi find cần phải decode lại

	Type  string `json:"type" bson:"type"` // phân biệt email topup hay full balance,...
	Retry int    `json:"retry" bson:"retry"`

	Status int `json:"status" bson:"status"`

	CreatedBy primitive.ObjectID `json:"created_by" bson:"created_by"`
	UpdatedBy primitive.ObjectID `json:"updated_by,omitempty" bson:"updated_by,omitempty"`
	CreatedAt time.Time          `json:"created_at" bson:"created_at"`
	UpdatedAt time.Time          `json:"updated_at" bson:"updated_at"`
}
