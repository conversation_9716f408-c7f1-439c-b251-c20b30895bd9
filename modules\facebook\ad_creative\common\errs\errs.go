package errs

import "errors"

var (
	ErrStatus       = errors.New("validate status ad creatives is not in the correct format")
	ErrEnrollStatus = errors.New("validate enroll status ad creatives is not in the correct format")
	//reload
	ErrReloadAdCreativeAdAccountIdCreativeId = errors.New("validate either account id or creative id must be provided")
	ErrReloadAdCreativeAdAccountId           = errors.New("validate account id is required")
	ErrReloadAdCreativeCreativeId            = errors.New("validate creative id is required")

	//reload
	ErrGetCreativeDetailFB             = errors.New("error get creative detail facebook")
	ErrGetCreativeDetailFBNotFound     = errors.New("error get creative detail facebook not found")
	ErrListOfAdAccountCreativeDetailFB = errors.New("error list of ad account creative detail facebook")

	ErrReloadListCreativeFB         = errors.New("error list of ad account creatives facebook")
	ErrReloadListCreativeFBNotFound = errors.New("error list of ad account creatives facebook not found")

	//create
	ErrReloadAdCreativeActorID = errors.New("validate actor_id (page_id) is required")

	//preview
	ErrGetCreativeObj = errors.New("creative object is required")
	ErrGetAdFormat    = errors.New("validate ad_format is required")
	ErrPreviewBodyFB  = errors.New("preview facebook body not found")
	ErrApiPreviewFB   = errors.New("facebook api preview error")

	ErrPageIdOfPost  = errors.New("page id of post not exist")
	ErrPostIdsOfPost = errors.New("post id is validate")
)
