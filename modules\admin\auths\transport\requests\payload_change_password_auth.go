package requests

import (
	"godsp/modules/admin/common/admerrs"
	"godsp/modules/admin/user/transport/rules"

	"github.com/go-playground/validator/v10"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

type PayloadAuthChangePassword struct {
	ID          primitive.ObjectID `form:"id" validate:"required"`
	OldPassword string             `form:"old_password" validate:"required,checkPassword"`
	NewPassword string             `form:"new_password" validate:"required,checkPassword"`
}

func (pl *PayloadAuthChangePassword) Validate() []*string {
	validate := validator.New()
	validate.RegisterValidation("checkPassword", rules.RulePasswordReq)
	err := validate.Struct(pl)
	var validationErrors []*string
	if err != nil {
		for _, err := range err.(validator.ValidationErrors) {
			switch err.Field() {
			case "ID":
				errMsg := admerrs.ErrIsUnvalid.Error()
				validationErrors = append(validationErrors, &errMsg)
			case "Password":
				errMsg := admerrs.ErrValidateLoginPassword.Error()
				validationErrors = append(validationErrors, &errMsg)
			}
		}
		return validationErrors
	}

	if pl.NewPassword == pl.OldPassword {
		errMsg := admerrs.ErrPasswordIsTheSame.Error()
		validationErrors = append(validationErrors, &errMsg)
	}

	if len(validationErrors) > 0 {
		return validationErrors
	}

	return nil
}
