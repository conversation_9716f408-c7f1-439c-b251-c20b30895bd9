package pipelines

import (
	"godsp/conf"
	"godsp/modules/facebook/common/fbenums"
	"godsp/modules/facebook/iface"

	"go.mongodb.org/mongo-driver/bson"
)

var (
	GetDetailsAdsetPipeline = []bson.M{
		{
			"$lookup": bson.M{
				"from":         "fb_audiences",
				"localField":   "targeting.customaudiences.id",
				"foreignField": "custom_audience_id",
				"as":           "customaudiences_details",
			},
		},
		{
			"$lookup": bson.M{
				"from":         "fb_audiences",
				"localField":   "targeting.excludedcustomaudiences.id",
				"foreignField": "custom_audience_id",
				"as":           "excludedcustomaudiences_details",
			},
		},
		{
			"$addFields": bson.M{
				"targeting.customaudiences": bson.M{
					"$map": bson.M{
						"input": "$targeting.customaudiences",
						"as":    "customaudience",
						"in": bson.M{
							"$mergeObjects": bson.A{
								"$$customaudience",
								bson.M{
									"subtype": bson.M{
										"$let": bson.M{
											"vars": bson.M{
												"matched": bson.M{
													"$arrayElemAt": bson.A{
														bson.M{
															"$filter": bson.M{
																"input": "$customaudiences_details",
																"as":    "detail",
																"cond": bson.M{
																	"$eq": bson.A{
																		"$$detail.custom_audience_id", "$$customaudience.id",
																	},
																},
															},
														},
														0,
													},
												},
											},
											"in": "$$matched.subtype",
										},
									},
								},
							},
						},
					},
				},
				"targeting.excludedcustomaudiences": bson.M{
					"$map": bson.M{
						"input": "$targeting.excludedcustomaudiences",
						"as":    "excluded",
						"in": bson.M{
							"$mergeObjects": bson.A{
								"$$excluded",
								bson.M{
									"subtype": bson.M{
										"$let": bson.M{
											"vars": bson.M{
												"matched": bson.M{
													"$arrayElemAt": bson.A{
														bson.M{
															"$filter": bson.M{
																"input": "$excludedcustomaudiences_details",
																"as":    "detail",
																"cond": bson.M{
																	"$eq": bson.A{
																		"$$detail.custom_audience_id", "$$excluded.id",
																	},
																},
															},
														},
														0,
													},
												},
											},
											"in": "$$matched.subtype",
										},
									},
								},
							},
						},
					},
				},
			},
		},
		{
			"$project": bson.M{
				"customaudiences_details":         0,
				"excludedcustomaudiences_details": 0,
			},
		},
	}
)

func GetDetailAds(adsetId string, user iface.UserInfoFb) []bson.M {
	// bson.A{
	// 	bson.M{"adset_id": adsetId},
	// }
	filter := []bson.M{
		{
			"adset_id": adsetId,
		},
		{
			"status": bson.M{
				"$nin": []string{fbenums.FB_STATUS_ARCHIVED, fbenums.FB_STATUS_DELETED},
			},
		},
	}
	if user.RoleName != conf.SysConf.RoleAdmin {
		filter = append(filter, bson.M{
			"account_id": user.AdAccountId,
		})
		if user.RoleName == conf.SysConf.RoleClientAdmin || user.RoleName == conf.SysConf.RoleClientAdminViewer {
			filter = append(filter, bson.M{
				"client_id": user.ClientId,
			})
		} else {
			filter = append(filter, bson.M{
				"list_user_ids": user.UserId,
				"client_id":     user.ClientId,
			})
		}
	}
	// if role != "ADMIN" {
	// 	filter = append(filter, bson.M{
	// 		"list_user_ids": userId,
	// 	})
	// }
	return []bson.M{
		{
			"$match": bson.M{
				"$and": filter,
			},
		},
		{
			"$lookup": bson.M{
				"from":         "fb_campaigns",
				"localField":   "campaign_id",
				"foreignField": "campaign_id",
				"as":           "campaign_info",
			},
		},
		{
			"$addFields": bson.M{
				"campaign": bson.M{
					"$arrayElemAt": bson.A{
						"$campaign_info",
						0,
					},
				},
			},
		},
		{
			"$lookup": bson.M{
				"from":         "fb_audiences",
				"localField":   "targeting.customaudiences.id",
				"foreignField": "custom_audience_id",
				"as":           "customaudiences_details",
			},
		},
		{
			"$lookup": bson.M{
				"from":         "fb_audiences",
				"localField":   "targeting.excludedcustomaudiences.id",
				"foreignField": "custom_audience_id",
				"as":           "excludedcustomaudiences_details",
			},
		},
		{
			"$addFields": bson.M{
				"targeting.customaudiences": bson.M{
					"$map": bson.M{
						"input": "$targeting.customaudiences",
						"as":    "customaudience",
						"in": bson.M{
							"$mergeObjects": bson.A{
								"$$customaudience",
								bson.M{
									"subtype": bson.M{
										"$let": bson.M{
											"vars": bson.M{
												"matched": bson.M{
													"$arrayElemAt": bson.A{
														bson.M{
															"$filter": bson.M{
																"input": "$customaudiences_details",
																"as":    "detail",
																"cond": bson.M{
																	"$eq": bson.A{
																		"$$detail.custom_audience_id", "$$customaudience.id",
																	},
																},
															},
														},
														0,
													},
												},
											},
											"in": "$$matched.subtype",
										},
									},
								},
							},
						},
					},
				},
				"targeting.excludedcustomaudiences": bson.M{
					"$map": bson.M{
						"input": "$targeting.excludedcustomaudiences",
						"as":    "excluded",
						"in": bson.M{
							"$mergeObjects": bson.A{
								"$$excluded",
								bson.M{
									"subtype": bson.M{
										"$let": bson.M{
											"vars": bson.M{
												"matched": bson.M{
													"$arrayElemAt": bson.A{
														bson.M{
															"$filter": bson.M{
																"input": "$excludedcustomaudiences_details",
																"as":    "detail",
																"cond": bson.M{
																	"$eq": bson.A{
																		"$$detail.custom_audience_id", "$$excluded.id",
																	},
																},
															},
														},
														0,
													},
												},
											},
											"in": "$$matched.subtype",
										},
									},
								},
							},
						},
					},
				},
			},
		},
		{
			"$project": bson.M{
				"customaudiences_details":         0,
				"excludedcustomaudiences_details": 0,
			},
		},
	}
}
