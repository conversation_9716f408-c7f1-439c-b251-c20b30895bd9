package requests

import (
	"godsp/modules/facebook/ad_pixel/common/errs"

	"github.com/go-playground/validator/v10"
)

type ReloadAdPixelReq struct {
	AccountID string `json:"account_id" validate:"required,numeric,gt=0"`
	PixelID   string `json:"pixel_id,omitempty"`
}

func (req *ReloadAdPixelReq) Validate() []string {
	validate := validator.New()
	var validationErrors []string

	err := validate.Struct(req)
	if err != nil {
		for _, err := range err.(validator.ValidationErrors) {
			switch err.Field() {
			case "AccountID":
				validationErrors = append(validationErrors, errs.ErrAdPixelAdAccount.Error())
			case "PixelID":
				validationErrors = append(validationErrors, errs.ErrAdPixelId.Error())
			}
		}
	}

	return validationErrors
}
