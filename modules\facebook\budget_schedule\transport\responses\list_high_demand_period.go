package responses

import "time"

type HighDemandPeriodEntity struct {
	CampaignID         string    `json:"campaign_id,omitempty" bson:"campaign_id"`
	AdsetID            string    `json:"adset_id,omitempty" bson:"adset_id"`
	HighDemandPeriodID string    `json:"high_demand_period_id" bson:"high_demand_period_id"`
	BudgetValue        uint64    `json:"budget_value,omitempty" bson:"budget_value"`
	BudgetValueType    string    `json:"budget_value_type,omitempty" bson:"budget_value_type"`
	RecurrenceType     string    `json:"recurrence_type,omitempty" bson:"recurrence_type"`
	TimeStart          time.Time `json:"time_start,omitempty" bson:"time_start"`
	TimeEnd            time.Time `json:"time_end,omitempty" bson:"time_end"`
}
