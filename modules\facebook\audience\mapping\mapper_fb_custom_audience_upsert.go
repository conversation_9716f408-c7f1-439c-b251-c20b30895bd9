package mapping

import (
	v20 "godsp/pkg/fb-marketing/marketing/v20"
	"godsp/pkg/gos/utils"
	"time"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

func MapperFBCustomAudienceToUpsert(audience *v20.CustomAudience, userId primitive.ObjectID) bson.M {
	now := time.Now()
	updateData := bson.M{
		"custom_audience_id":            audience.ID,
		"account_id":                    audience.AccountID,
		"name":                          audience.Name,
		"description":                   audience.Description,
		"subtype":                       audience.Subtype,
		"approximate_count_upper_bound": audience.ApproximateCountUpperBound,
		"approximate_count_lower_bound": audience.ApproximateCountLowerBound,
		"rule":                          audience.Rule,
		"customer_file_source":          audience.CustomerFileSource,
		"lookalike_audience_ids":        audience.Lookalikes,
		"adaccounts":                    audience.Adaccounts,
		"lookalike_spec":                audience.LookalikeSpec,
		"origin_audience_id":            audience.OriginAudienceID,
		// "type":                          enums.TYPE_CUSTOM_AUDIENCES,
		"pixel_id":         audience.PixelId,
		"data_source":      audience.DataSource,
		"delivery_status":  audience.DeliveryStatus,
		"retention_days":   audience.RetentionDays,
		"time_created":     utils.ConvertIntToTime(int64(audience.TimeCreated)),
		"time_updated":     utils.ConvertIntToTime(int64(audience.TimeUpdated)),
		"operation_status": audience.OperationStatus,
		"is_value_based":   audience.IsValueBased,
		"updated_at":       now,
		"updated_by":       userId,
	}

	if audience.DeleteTime > 0 {
		updateData["delete_time"] = audience.DeleteTime
	}

	createData := bson.M{
		"create_at":  now,
		"created_by": userId,
	}

	return bson.M{
		"$set":         updateData,
		"$setOnInsert": createData,
	}
}
