// https://github.com/krasimir/navigo/blob/master/DOCUMENTATION.md#navigating-between-routes
// import { initializeModalHandlers } from "../pages/ad/components/modalHandler.js";
// import CampAdsetAd from "/static/js/pages/camp-adset-ad/index.js";
import RouterHelper from "/static/js/router/helper.js";
import { PATHS } from "/static/js/tiktok/constants/routes.js";

const initTab = {
    camp: true,
    adgroup: true,
    ad: true,
};
let isReloadPage = true;

const router = new Navigo("/");
const routerHelper = new RouterHelper(router);

function actionDispatchEvent(keyEvent, params) {
    const detail = {
        params,
        init: keyEvent === "campaignTabActive" ? initTab.camp : keyEvent === "adgroupTabActive" ? initTab.adgroup : keyEvent === "adTabActive" ? initTab.ad : initTab.gmvMax,
        isReloadPage,
    };

    const event = new CustomEvent(keyEvent, { detail });

    if (isReloadPage) {
        setTimeout(() => document.dispatchEvent(event), 100);
    } else {
        document.dispatchEvent(event);
    }
}

function handleAction(type, params, tabKey, editPath, listPath, paramKey) {
    actionDispatchEvent(`${type}TabActive`, params);

    if (initTab[tabKey]) {
        document.querySelector(`ul#navTabTableAds li a[data-navigo-href][href="#${tabKey}"]`)?.click();
        initTab[tabKey] = false;
        if (isReloadPage) isReloadPage = false;
    }

    if (params.url === editPath && !getParamURL(paramKey)) {
        routerHelper.navigateWithQuery(listPath);
    }
}

function handlerCamp(params) {
    handleAction("campaign", params, "camp", PATHS.camp.edit, PATHS.camp.list, "edit_campaign_id");
}

function handlerAdGroup(params) {
    handleAction("adgroup", params, "adgroup", PATHS.adgroup.edit, PATHS.adgroup.list, "edit_campaign_id");
}

function handlerAd(params) {
    handleAction("ad", params, "ad", PATHS.ad.edit, PATHS.ad.list, "edit_ad_id");
}

function handlerGmvMax(params) {
    handleAction("gmv-max", params, "gmv-max", PATHS.gmvMax.edit, PATHS.gmvMax.list, "edit_gmv_max")
}

const routes = [
    {
        path: PATHS.camp.list,
        handler: handlerCamp,
    },
    {
        path: PATHS.camp.edit,
        handler: handlerCamp,
    },
    {
        path: PATHS.adgroup.list,
        handler: handlerAdGroup,
    },
    {
        path: PATHS.adgroup.edit,
        handler: handlerAdGroup,
    },
    {
        path: PATHS.ad.list,
        handler: handlerAd,
    },
    {
        path: PATHS.ad.edit,
        handler: handlerAd,
    },
    {
        path: PATHS.gmvMax.list,
        handler: handlerGmvMax,
    },
    {
        path: PATHS.gmvMax.edit,
        handler: handlerGmvMax,
    }
];

routes.forEach(({ path, handler }) => router.on(path, handler));
router.resolve();

(() => {
    $(document).ready(function () {
        // isReloadPage = true;
        routerHelper.initNavigoLink();
    });
})();

export { router, routerHelper };
