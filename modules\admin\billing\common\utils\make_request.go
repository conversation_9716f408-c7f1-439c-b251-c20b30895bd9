package utils

import (
	"bytes"
	"context"
	"fmt"
	"godsp/conf"
	"io"
	"net/http"
	"time"
)

const (
	REQUEST_GET    = "GET"
	REQUEST_POST   = "POST"
	REQUEST_PUT    = "PUT"
	REQUEST_DELETE = "DELETE"
)

func MakeRequest(ctx context.Context, method, path string, data []byte) ([]byte, error) {
	httpClient := &http.Client{
		Timeout: 30 * time.Second, // Set a reasonable timeout
	}

	// Create a new request with context to allow for cancellation/timeout.
	req, err := http.NewRequestWithContext(ctx, method, conf.DV360ApiUrl+path, bytes.NewBuffer(data))
	if err != nil {
		// usc.logger.Error(fmt.Errorf("CreateApiBillingUsc: failed to create VTB API request: %w", err))
		return nil, fmt.Errorf("failed to create VTB API request: %w", err)
	}

	// Set necessary headers
	req.Header.Set("Content-Type", "application/json")
	// Add any other required headers, e.g., Authorization token
	// req.Header.Set("Authorization", "Bearer YOUR_VTB_API_TOKEN") // Token should be from config

	// usc.logger.Info(fmt.Sprintf("CreateApiBillingUsc: sending request to VTB API at %s", url))
	resp, err := httpClient.Do(req)
	if err != nil {
		// usc.logger.Error(fmt.Errorf("CreateApiBillingUsc: failed to send request to VTB API: %w", err))
		return nil, fmt.Errorf("failed to send request to VTB API: %w", err)
	}
	defer resp.Body.Close()

	// 3. Handle the VTB API response
	responseBody, err := io.ReadAll(resp.Body)
	if err != nil {
		// usc.logger.Error(fmt.Errorf("CreateApiBillingUsc: failed to read VTB API response body: %w", err))
		return nil, fmt.Errorf("failed to read VTB API response body: %w", err)
	}

	if !(resp.StatusCode >= 200 && resp.StatusCode < 300) {
		errMessage := fmt.Sprintf("CreateApiBillingUsc: VTB API request failed. Status: %s, Body: %s", resp.Status, string(responseBody))
		// usc.logger.Error(errMessage)
		return nil, fmt.Errorf("VTB API request failed with status %s, %s, %s", errMessage, resp.Status, string(responseBody))
	}

	return responseBody, nil
}

func MakeClientRequest(ctx context.Context, method, path string, data []byte) ([]byte, error) {
	httpClient := &http.Client{
		Timeout: 30 * time.Second, // Set a reasonable timeout
	}

	// Create a new request with context to allow for cancellation/timeout.
	req, err := http.NewRequestWithContext(ctx, method, conf.DV360ApiUrl+path, bytes.NewBuffer(data))
	if err != nil {
		fmt.Errorf("%w", err)

		return nil, fmt.Errorf("%w", err)
	}

	// Set necessary headers
	req.Header.Set("Content-Type", "application/json")
	// Add any other required headers, e.g., Authorization token
	// req.Header.Set("Authorization", "Bearer YOUR_VTB_API_TOKEN") // Token should be from config

	resp, err := httpClient.Do(req)
	if err != nil {
		return nil, fmt.Errorf("%w", err)
	}

	defer resp.Body.Close()

	responseBody, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, err
	}

	if !(resp.StatusCode >= 200 && resp.StatusCode < 300) {
		return nil, fmt.Errorf("%s", string(responseBody))
	}

	return responseBody, nil
}
