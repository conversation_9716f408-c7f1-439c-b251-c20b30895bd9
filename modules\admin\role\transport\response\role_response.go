package response

import (
	"godsp/modules/admin/common/admconst"
	"godsp/modules/admin/role/entity"
	"godsp/pkg/gos/goconst"
	"godsp/pkg/gos/utils"
)

type RoleDataTable struct {
	ID          string `json:"id"`
	Name        string `json:"name"`
	Status      string `json:"status"`
	RoleName    string `json:"role_name"`
	Ordering    int64  `json:"ordering"`
	Permission  string `json:"permission,omitempty"`
	CreatedName string `json:"created_name" bson:"created_name"`
	UpdatedName string `json:"updated_name" bson:"updated_name"`
	CreatedAt   string `json:"created_at"`
	UpdatedAt   string `json:"updated_at,omitempty"`
}

func MapperDataTable(r entity.RoleEntity) RoleDataTable {
	createdAt := utils.FormatTimeToString(r.CreatedAt, goconst.YYYY_MM_DD_HH_MM_SS)
	updatedAt := utils.FormatTimeToString(r.UpdatedAt, goconst.YYYY_MM_DD_HH_MM_SS)

	updatedName := ""
	createdName := ""
	if r.UserCreated != nil {
		createdName = r.UserCreated.FullName
	}

	if r.UserUpdated != nil {
		updatedName = r.UserUpdated.FullName
	}

	data := RoleDataTable{
		ID:          r.ID.Hex(),
		Name:        r.Name,
		Status:      admconst.StatusFullName[r.Status],
		RoleName:    r.RoleName,
		Ordering:    r.Ordering,
		CreatedAt:   createdAt,
		UpdatedAt:   updatedAt,
		UpdatedName: updatedName,
		CreatedName: createdName,
	}

	return data
}
