package mongo

import (
	"context"
	"godsp/modules/facebook/audience/entity"
	"godsp/modules/facebook/common/fberrs"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
)

type audienceRepo struct {
	DB         *mongo.Database
	Collection *mongo.Collection
}

func NewAudienceRepo(DB *mongo.Database) *audienceRepo {
	return &audienceRepo{
		DB:         DB,
		Collection: DB.Collection(entity.CustomAudienceEntity{}.CollectionName()),
	}
}

/**
 * Upsert Audience
 */
func (r *audienceRepo) UpsertAudienceRepo(ctx context.Context, filter bson.M, update bson.M) error {
	opts := options.Update().SetUpsert(true)
	_, err := r.Collection.UpdateOne(ctx, filter, update, opts)
	if err != nil {
		return err
	}
	return nil
}

/**
 * Find audience
 */
func (r *audienceRepo) FindAudienceRepo(ctx context.Context, filter interface{}, opts ...*options.FindOptions) (*[]entity.CustomAudienceEntity, error) {

	var audiences []entity.CustomAudienceEntity
	cursor, err := r.Collection.Find(ctx, filter, opts...)

	if err != nil {
		return nil, err
	}

	defer cursor.Close(ctx)

	if cursor == nil {
		return nil, fberrs.ErrNilCursorValue
	}

	err = cursor.All(ctx, &audiences)

	if err != nil {
		return nil, err
	}

	return &audiences, nil
}

/**
 * Count audiences
 */
func (r *audienceRepo) CountAudiencesRepo(ctx context.Context, filter interface{}) (int64, error) {
	return r.Collection.CountDocuments(ctx, filter)
}

func (r *audienceRepo) InsertAudienceRepo(ctx context.Context, customAudience *entity.CustomAudienceEntity) error {
	_, err := r.Collection.InsertOne(ctx, customAudience)
	if err != nil {
		return err
	}

	return nil
}

func (r *audienceRepo) FindOneAudienceRepo(ctx context.Context, filter interface{}) (*entity.CustomAudienceEntity, error) {
	var audience entity.CustomAudienceEntity
	err := r.Collection.FindOne(ctx, filter).Decode(&audience)
	if err != nil {
		return nil, err
	}

	return &audience, nil
}

func (r *audienceRepo) DeleteAudienceRepo(ctx context.Context, filter interface{}, update interface{}) error {
	_, err := r.Collection.UpdateMany(ctx, filter, update)
	if err != nil {
		return err
	}

	return nil
}

// func (r *audienceRepo) DeleteAudienceRepo(ctx context.Context, filter interface{}) error {
// 	_, err := r.Collection.DeleteMany(ctx, filter)
// 	if err != nil {
// 		return err
// 	}

// 	return nil
// }
