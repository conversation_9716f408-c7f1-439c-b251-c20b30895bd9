package api

import (
	"context"
	"godsp/modules/facebook/ad/entity"
	"godsp/modules/facebook/ad/transport/requests"
	"godsp/modules/facebook/ad/transport/responses"

	"go.mongodb.org/mongo-driver/bson/primitive"
)

type ApiAdUsc interface {
	ReloadWithAccountAdsUsc(ctx context.Context, accountId string) []string
	ReloadWithAdsetUsc(ctx context.Context, adsetID string) []string
	ReloadAdDetailUsc(ctx context.Context, AdID string, userId primitive.ObjectID) error

	CreateAdUsc(ctx context.Context, payload *requests.CreateAdReq) (*entity.AdEntity, error)

	FindOneAdDetailUsc(ctx context.Context, adId string) (*entity.AdDetailReviewEntity, error)

	FindOneAdUsc(ctx context.Context, adId string) (*entity.AdEntity, error)
	UpdateAdUsc(ctx context.Context, payload *requests.UpdateAdReq) (*entity.AdEntity, error)
	ListAdsUsc(ctx context.Context, payload *requests.ListAdTableReq) (*responses.DataTableAdsRes, error)
	UpdateNameStatusAdUsc(ctx context.Context, payload *requests.UpdateNameStatusAdReq) error
	DeleteAdUsc(ctx context.Context, payload *requests.DeleteAdReq) ([]string, error)
	GetRoleByUserId(ctx context.Context, userId primitive.ObjectID) (*string, error)
	ListDatatableAdsUsc(ctx context.Context, payload *requests.ListAdTableReq) (*responses.DataTableAdsRes, error)
}

type adApi struct {
	usc ApiAdUsc
}

// DeleteAdApi implements routes.ComposerAdApi.

func NewAdApi(usc ApiAdUsc) *adApi {
	return &adApi{
		usc: usc,
	}
}
