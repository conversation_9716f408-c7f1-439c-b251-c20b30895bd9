package usecase

import (
	"context"
	"errors"

	"godsp/conf"
	"godsp/modules/facebook/ad_account/common/errs"
	"godsp/modules/facebook/ad_account/entity"
	"godsp/modules/facebook/ad_account/mapping"
	"godsp/modules/facebook/ad_account/transport/response"
	v20 "godsp/pkg/fb-marketing/marketing/v20"
	"godsp/pkg/sctx/core"

	"github.com/dev-networldasia/dspgos/sctx"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo/options"
)

type ApiAdAccountRepo interface {
	FindOneAdAccountRepo(ctx context.Context, filter interface{}, opts ...*options.FindOneOptions) (*entity.AdAccountEntity, error)
	InsertAdAccountRepo(ctx context.Context, adAccountEntity *entity.AdAccountEntity) error
	UpdateOneAdAccountRepo(ctx context.Context, filter interface{}, adAccountEntity *entity.AdAccountEntity, opts ...*options.UpdateOptions) error

	FindAdAccountAggregateRepo(ctx context.Context, filter interface{}, opts ...*options.FindOptions) ([]*response.AdAccountWithFullname, error)
	CountAdAccountRepo(ctx context.Context, filter interface{}) (int64, error)
}

type apiFBAdAccountUsc struct {
	fbService *v20.Service
	repo      ApiAdAccountRepo
	logger    sctx.Logger
}

func NewFBMarketingUsc(fbService *v20.Service, repo ApiAdAccountRepo, logger sctx.Logger) *apiFBAdAccountUsc {
	return &apiFBAdAccountUsc{
		fbService: fbService,
		repo:      repo,
		logger:    logger,
	}
}

/**
 * Reload Ad Account Facebook V20
 * - Nếu ko có tạo mới
 * - Nếu có rồi thì update
 */
func (usc *apiFBAdAccountUsc) ReloadAdAccountUsc(ctx context.Context, userId primitive.ObjectID) []string {
	var errs []string
	adaccounts, err := usc.fbService.AdAccounts.List(ctx, conf.FBConf.BusinessID)
	if err != nil {
		errs = append(errs, err.Error())
		return errs
	}

	for _, adc := range adaccounts {
		filter := bson.M{"account_id": adc.AccountID}
		adAccount, err := usc.repo.FindOneAdAccountRepo(ctx, filter)
		if err != nil && !errors.Is(err, core.ErrNotFound) {
			usc.logger.Error(err)
			errs = append(errs, err.Error())
			break
		}

		if errors.Is(err, core.ErrNotFound) {
			adAccountCreate := mapping.MapperAdAccountFBToEntity(&adc, userId)
			err := usc.repo.InsertAdAccountRepo(ctx, adAccountCreate)
			if err != nil {
				errs = append(errs, err.Error())
			}
		} else {
			mapping.MapperAdAccountFBUpdate(&adc, adAccount, userId)
			err := usc.repo.UpdateOneAdAccountRepo(ctx, filter, adAccount)
			if err != nil {
				errs = append(errs, err.Error())
			}
		}
	}

	return errs
}

/**
 * list data table ad account
 *
 * @return response data table
 */
func (usc *apiFBAdAccountUsc) ListDatatableAdAccountUsc(ctx context.Context) (*response.AdAccountListResponse, error) {
	opts := options.FindOptions{}
	opts.SetProjection(bson.M{
		"_id":               1,
		"name":              1,
		"currency":          1,
		"timezone_name":     1,
		"created_by":        1,
		"creator_full_name": 1,
		"created_at":        1,
		"updated_by":        1,
		"updater_full_name": 1,
		"updated_at":        1,
	})

	adAccounts, err := usc.repo.FindAdAccountAggregateRepo(ctx, bson.M{}, &opts)
	if err != nil {
		usc.logger.Error(err)
		return nil, errs.ErrFindAdAccount
	}

	if len(adAccounts) == 0 {
		return &response.AdAccountListResponse{
			AdAccountDataTables: nil,
			Count:               0,
			CountFilter:         0,
		}, nil
	}

	total, err := usc.repo.CountAdAccountRepo(ctx, bson.M{})
	if err != nil {
		usc.logger.Error(err)
		return nil, errs.ErrCountAdAccount
	}

	adAccountDatatables := response.AdAccountListResponse{
		Count:       total,
		CountFilter: total,
	}

	var datas []*response.AdAccountDataTable
	for _, adc := range adAccounts {
		datas = append(datas, response.MapperDatatable(*adc))
	}
	adAccountDatatables.AdAccountDataTables = datas

	return &adAccountDatatables, nil
}
