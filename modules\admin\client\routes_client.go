package client

import (
	"godsp/modules/admin/client/routes"

	"github.com/dev-networldasia/dspgos/sctx"

	"github.com/gofiber/fiber/v2"
	"github.com/gofiber/fiber/v2/middleware/session"
)

func SetupRoutesClients(app *fiber.App, serviceCtx sctx.ServiceContext, store *session.Store, midds ...fiber.Handler) {
	group := app.Group("admins/clients")
	{
		if len(midds) > 0 {
			for _, midd := range midds {
				group.Use(midd)
			}
		}

		comp := routes.ComposerClientHdlService(serviceCtx)
		group.Get("/list", comp.ListClientHdl()).Name("admins.client.list")
		group.Get("/edit/:id", comp.EditClientHdl(store)).Name("admins.client.edit")
		group.Get("/profile/:id", comp.EditClientHdl(store)).Name("admins.client.profile")

	}

	apiGroup := app.Group("api/admins/clients")
	{
		if len(midds) > 0 {
			for _, midd := range midds {
				apiGroup.Use(midd)
			}
		}
		compApi := routes.ComposerClientApiService(serviceCtx)

		apiGroup.Post("/create", compApi.CreateClientApi()).Name("admins.client.api.create")
		apiGroup.Delete("/delete/:id", compApi.DeleteOneClientApi()).Name("admins.client.api.delete")
		apiGroup.Put("/update", compApi.UpdateUserApi()).Name("admins.client.api.update")
		apiGroup.Post("/update-facebook-resource", compApi.UpdateUserIdClientIdFacebookResourceApi()).Name("admins.client.api.update_facebook_resource")
		apiGroup.Post("/update-tiktok-resource", compApi.UpdateUserIdClientIdTiktokResourceApi()).Name("admins.client.api.update_tiktok_resource")
		apiGroup.Post("/list-datatable", compApi.ListClientsApi()).Name("admins.client.api.list_datatable")

	}
}
