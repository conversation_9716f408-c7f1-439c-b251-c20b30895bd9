package conf

import (
	"github.com/namsral/flag"
)

const (
	KeyCompReportMongoDB = "report_mongodb"
)

type FbConf struct {
	AppID       string
	BusinessID  string
	Act         string
	RoleAdminID string
}

type SystemConf struct {
	RoleAdmin             string
	RoleAccountant        string
	RoleClientAdmin       string
	RoleClientAdminViewer string
	RoleClientUser        string
}
type Email struct {
	From       string
	Host       string
	Port       int
	Username   string
	Password   string
	AdminEmail string
}

type TikTokConf struct {
	AppID       string
	AccessToken string
	AppSecret   string
}

var (
	// Domain                = "#"
	FBConf                FbConf
	SysConf               SystemConf
	EmailConf             Email
	Location              = "Asia/Ho_Chi_Minh"
	UploadPathPublic      = "public"
	PathImgUserDefault    = "images/users/user-dummy-img.jpg"
	PathImgProductDefault = "images/product.png"
	PathImgUser           = "/images/users"
	PathImgClient         = "/images/clients"
	// TikTok
	TiktokConf TikTokConf
	// config for payment info
	PaymentMethod         = ""
	PaymentBankCode       = ""
	PaymentBankCodeId     = ""
	PaymentPayload        = "01"
	PaymentCCY            = "704"
	PaymentPointMethod    = "12"
	PaymentMasterMerchant = "970489"
	PaymentCoutryCode     = "VN"
	PaymentPinCode        = "0000"
	PaymentLocaleVN       = ""
	PaymentLocaleEN       = ""
	VTBMerchantName       = ""
	VTBMerchantCode       = ""
	VTBMerchantCC         = ""
	VTBTerminalID         = ""
	VTBMerchantCity       = ""
	VTBMasterMerchant     = ""
	VTBStoreID            = ""
	APIPaymentCreate      = "https://dev-dv360.brancherx.net/api/payments/billing/create-vtb-payment"
	APIPaymentList        = "https://dev-dv360.brancherx.net/api/payments/billing/list"
	DV360ApiUrl           = "https://dev-dv360.brancherx.net"
)

func init() {
	flag.StringVar(&FBConf.AppID, "app-id", "", "Facebook app id")
	flag.StringVar(&FBConf.BusinessID, "business-id", "", "Facebook marketing business id")
	flag.StringVar(&FBConf.Act, "act", "", "Facebook marketing act")

	// Tiktok config
	flag.StringVar(&TiktokConf.AppID, "tiktok-app-id", "", "tiktok app id")
	flag.StringVar(&TiktokConf.AccessToken, "tiktok-access-token", "", "tiktok access token")
	flag.StringVar(&TiktokConf.AppSecret, "tiktok-app-secret", "", "tiktok app secret")
	// flag.StringVar(&Domain, "domain", "/", "Domain website ex : https://example.com")
	flag.StringVar(&Location, "location", "Asia/Ho_Chi_Minh", "local time, should be local time default: Asia/Ho_Chi_Minh")

	flag.StringVar(&UploadPathPublic, "upload-path-public", "public", "upload public public default: public")
	flag.StringVar(&PathImgUserDefault, "path-img-user-default", "images/users/user-dummy-img.jpg", "path img user default: images/users/user-dummy-img.jpg")
	flag.StringVar(&PathImgProductDefault, "product", "images/product.png", "path img user default: images/product.png")
	flag.StringVar(&PathImgUser, "path-img-user", "editors/images/users", "path img user ex: image/users")
	flag.StringVar(&PathImgClient, "path-img-client", "editors/images/clients", "path img client ex: image/clients")

	flag.StringVar(&PaymentMethod, "payment-method", "", "payment method")
	flag.StringVar(&PaymentBankCode, "payment-bank-code", "", "payment bank code")
	flag.StringVar(&PaymentBankCodeId, "payment-bankcode-id", "", "payment bank code id")
	flag.StringVar(&PaymentLocaleVN, "payment-locale-vn", "", "payment locale vn")
	flag.StringVar(&PaymentLocaleEN, "payment-locale-en", "", "payment locale en")
	flag.StringVar(&VTBMerchantName, "vtb-merchant-name", "", "vtb merchant name")
	flag.StringVar(&VTBMerchantCode, "vtb-merchant-code", "", "vtb merchant code")
	flag.StringVar(&VTBMerchantCC, "vtb-merchant-cc", "", "vtb merchant cc")
	flag.StringVar(&VTBTerminalID, "vtb-terminal-id", "", "vtb terminal id")
	flag.StringVar(&VTBMerchantCity, "vtb-merchant-city", "", "vtb merchant city")
	flag.StringVar(&VTBMasterMerchant, "vtb-master-merchant", "", "vtb master merchant")
	flag.StringVar(&VTBStoreID, "vtb-store-id", "", "vtb store id")
	// flag.StringVar(&PaymentMasterMerchant, "vtb-master-merchant", "", "vtb master merchant")
	flag.StringVar(&APIPaymentCreate, "api-payment-create", "", "api to create payment")
	flag.StringVar(&APIPaymentList, "api-payment-list", "", "api to get list payment")
	flag.StringVar(&DV360ApiUrl, "dv360-api-url", "https://dev-dv360.brancherx.net", "dv360 api url")
	flag.StringVar(&SysConf.RoleAdmin, "role-admin", "ADMIN", "role admin default: ADMIN")
	flag.StringVar(&SysConf.RoleAccountant, "role-accountant", "ACCOUNTANT", "role accountant default: ACCOUNTANT")
	flag.StringVar(&SysConf.RoleClientAdmin, "role-client-admin", "CLIENT-ADMIN", "role client admin default: CLIENT-ADMIN")
	flag.StringVar(&SysConf.RoleClientAdminViewer, "role-client-admin-viewer", "CLIENT-ADMIN-VIEWER", "role client admin viewer default: CLIENT-ADMIN-VIEWER")
	flag.StringVar(&SysConf.RoleClientUser, "role-client-user", "CLIENT-USER", "role client user default: CLIENT-USER")

	// Email
	flag.StringVar(&EmailConf.From, "mail-from-address", "", "mail from address")
	flag.StringVar(&EmailConf.Host, "mail-host", "", "mail host")
	flag.IntVar(&EmailConf.Port, "mail-port", 0, "mail port")
	flag.StringVar(&EmailConf.Username, "mail-username", "", "mail username")
	flag.StringVar(&EmailConf.Password, "mail-password", "", "mail password")
	flag.StringVar(&EmailConf.AdminEmail, "mail-admin", "<EMAIL>", "admin email address")

	flag.Parse()
}
