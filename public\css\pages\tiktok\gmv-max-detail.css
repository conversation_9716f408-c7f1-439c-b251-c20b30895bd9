/* Chart container styling */
#gmv_max_line_chart {
    min-height: 350px;
}

/* Metric cards styling for detail page */
.metric-card {
    transition: all 0.3s ease;
    border: 1px solid #e9ecef;
    border-radius: 8px;
}

.metric-card:hover {
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    transform: translateY(-2px);
}

.metric-card.selected {
    border-color: #405189;
    background-color: #f8f9ff;
}

/* Status indicator styling */
.status-indicator {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    padding: 4px 12px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: 500;
}

.status-indicator.not-delivering {
    background-color: #fff3cd;
    color: #856404;
}

/* Campaign header styling */
.campaign-header {
    background: linear-gradient(135deg, #405189 0%, #5a6acf 100%);
    color: white;
    border-radius: 12px;
    padding: 24px;
    margin-bottom: 24px;
}

.campaign-header h4 {
    margin-bottom: 12px;
    font-weight: 600;
}

.campaign-info {
    display: flex;
    flex-wrap: wrap;
    gap: 24px;
    font-size: 14px;
}

.campaign-info span {
    opacity: 0.9;
}

.campaign-info strong {
    opacity: 1;
    font-weight: 500;
}