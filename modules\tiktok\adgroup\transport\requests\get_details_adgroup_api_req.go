package requests

import (
	clientErrs "godsp/modules/admin/client/common/errs"

	"godsp/pkg/gos/utils"

	"github.com/go-playground/validator/v10"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

type GetDetailsAdgroupsReq struct {
	AdvertiserID *string             `json:"advertiser_id,omitempty" form:"advertiser_id,omitempty" validate:"omitempty,numeric,min=5,max=25"`
	ClientIDStr  string              `json:"clientId,omitempty" form:"clientId"`
	ClientID     *primitive.ObjectID `json:"-" form:"-"`
	AdgroupID    string              `json:"adgroup_id,omitempty" validate:"required,numeric,min=5,max=25"`
	UserID       *primitive.ObjectID `json:"-" form:"-"`
}

func (req *GetDetailsAdgroupsReq) Validate() []*string {
	validate := validator.New()
	var validationErrors []*string

	err := validate.Struct(req)
	if err != nil {
		for _, err := range err.(validator.ValidationErrors) {
			switch err.Field() {

			case "AdgroupID":
				errMsg := "Adgroup ID is required"
				validationErrors = append(validationErrors, &errMsg)
			}

		}
	}

	if req.ClientIDStr != "" && !utils.ValidateObjectID(req.ClientIDStr) {
		errMsg := clientErrs.ErrIDClientValidate.Error()
		validationErrors = append(validationErrors, &errMsg)
	}

	if req.ClientIDStr != "" {
		clientID, _ := primitive.ObjectIDFromHex(req.ClientIDStr)
		req.ClientID = &clientID
	}

	return validationErrors
}
