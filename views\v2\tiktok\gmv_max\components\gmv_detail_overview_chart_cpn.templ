package components

templ GmvDetailOverviewChartCpn() {
	<div class="row mt-4 fade-in">
		<div class="col-12">
			<div class="card">
				<div class="card-header">
					<div class="d-flex justify-content-between align-items-center">
						<h5 class="card-title mb-0">Performance Overview</h5>
						<div class="d-flex gap-2">
							<select class="form-select" style="width: auto;">
								<option>Last 7 days</option>
								<option>Last 14 days</option>
								<option>Last 30 days</option>
							</select>
						</div>
					</div>
				</div>
				<div class="card-body">
					<!-- Metrics Cards -->
					<div class="row mb-4">
						<div class="col-lg-2 col-md-4 col-sm-6">
							<div class="metric-card" data-metric="cost">
								<div class="metric-value">$82,308</div>
								<div class="metric-label">Cost</div>
								<div class="metric-change positive">+5.2%</div>
							</div>
						</div>
						<div class="col-lg-2 col-md-4 col-sm-6">
							<div class="metric-card" data-metric="gross_revenue">
								<div class="metric-value">$385,099</div>
								<div class="metric-label">Gross Revenue</div>
								<div class="metric-change positive">+8.1%</div>
							</div>
						</div>
						<div class="col-lg-2 col-md-4 col-sm-6">
							<div class="metric-card" data-metric="orders">
								<div class="metric-value">168</div>
								<div class="metric-label">Orders</div>
								<div class="metric-change positive">+12.3%</div>
							</div>
						</div>
						<div class="col-lg-2 col-md-4 col-sm-6">
							<div class="metric-card" data-metric="cost_per_order">
								<div class="metric-value">$489</div>
								<div class="metric-label">Cost per Order</div>
								<div class="metric-change negative">-2.1%</div>
							</div>
						</div>
						<div class="col-lg-2 col-md-4 col-sm-6">
							<div class="metric-card" data-metric="roi">
								<div class="metric-value">4.68</div>
								<div class="metric-label">ROI</div>
								<div class="metric-change positive">+3.5%</div>
							</div>
						</div>
						<div class="col-lg-2 col-md-4 col-sm-6">
							<div class="metric-card">
								<div class="metric-value">2.8%</div>
								<div class="metric-label">CVR</div>
								<div class="metric-change positive">+0.3%</div>
							</div>
						</div>
					</div>
					
					<!-- Chart Container -->
					<div class="chart-container">
						<div id="detail-performance-chart" style="height: 350px;"></div>
					</div>
					
					<!-- Chart Instructions -->
					<div class="mt-3">
						<p class="text-muted small mb-0">
							<i class="ri-information-line"></i>
							Click on metric cards to add/remove them from the chart. Maximum 2 metrics can be displayed simultaneously.
						</p>
					</div>
				</div>
			</div>
		</div>
	</div>
}
