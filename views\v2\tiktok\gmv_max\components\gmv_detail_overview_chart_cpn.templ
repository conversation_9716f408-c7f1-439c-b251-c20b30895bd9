package components

templ GmvDetailOverviewChartCpn() {
<div class="row mt-4 fade-in">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <div class="d-flex justify-content-between align-items-center">
                    <h5 class="card-title mb-0">Overview</h5>
                    <div class="d-flex align-items-center gap-2">
                        <small class="text-muted">(UTC+07:00) Indochina time</small>
                        @components.DateRangeFilterTable()
                        <button class="btn btn-outline-secondary btn-sm">
                            <i class="ri-bar-chart-line"></i> Data views
                        </button>
                    </div>
                </div>
            </div>
            <div class="card-body">
                <!-- Metrics Cards -->
                <div class="row g-3 mb-4">
                    <div class="col-lg-2 col-md-4 col-sm-6">
                        <div class="metric-card p-3 border rounded position-relative" data-metric="cost">
                            <div class="form-check position-absolute top-0 end-0 mt-2 me-2">
                                <input class="form-check-input metric-checkbox" type="checkbox" id="detailCostCheck"
                                    checked />
                            </div>
                            <div class="d-flex align-items-center mb-2">
                                <h6 class="mb-0 me-2">Cost</h6>
                                <i class="ri-information-line text-muted"></i>
                            </div>
                            <h4 class="mb-1 fw-bold">5,397,173 VND</h4>
                            <small class="text-muted">vs last 7 days</small>
                            <span class="text-danger ms-1">-43.34%</span>
                        </div>
                    </div>
                    <div class="col-lg-2 col-md-4 col-sm-6">
                        <div class="metric-card p-3 border rounded position-relative" data-metric="orders">
                            <div class="form-check position-absolute top-0 end-0 mt-2 me-2">
                                <input class="form-check-input metric-checkbox" type="checkbox" id="detailOrdersCheck"
                                    checked />
                            </div>
                            <div class="d-flex align-items-center mb-2">
                                <h6 class="mb-0 me-2">Orders (SKU)</h6>
                                <i class="ri-information-line text-muted"></i>
                            </div>
                            <h4 class="mb-1 fw-bold">106</h4>
                            <small class="text-muted">vs last 7 days</small>
                            <span class="text-danger ms-1">-52.25%</span>
                        </div>
                    </div>
                    <div class="col-lg-2 col-md-4 col-sm-6">
                        <div class="metric-card p-3 border rounded position-relative" data-metric="cost_per_order">
                            <div class="form-check position-absolute top-0 end-0 mt-2 me-2">
                                <input class="form-check-input metric-checkbox" type="checkbox"
                                    id="detailCostPerOrderCheck" />
                            </div>
                            <div class="d-flex align-items-center mb-2">
                                <h6 class="mb-0 me-2">Cost per order</h6>
                                <i class="ri-information-line text-muted"></i>
                            </div>
                            <h4 class="mb-1 fw-bold">50,917 VND</h4>
                            <small class="text-muted">vs last 7 days</small>
                            <span class="text-success ms-1">+18.67%</span>
                        </div>
                    </div>
                    <div class="col-lg-2 col-md-4 col-sm-6">
                        <div class="metric-card p-3 border rounded position-relative" data-metric="gross_revenue">
                            <div class="form-check position-absolute top-0 end-0 mt-2 me-2">
                                <input class="form-check-input metric-checkbox" type="checkbox"
                                    id="detailGrossRevenueCheck" />
                            </div>
                            <div class="d-flex align-items-center mb-2">
                                <h6 class="mb-0 me-2">Gross revenue</h6>
                                <i class="ri-information-line text-muted"></i>
                            </div>
                            <h4 class="mb-1 fw-bold">22,494,198 VND</h4>
                            <small class="text-muted">vs last 7 days</small>
                            <span class="text-danger ms-1">-52.54%</span>
                        </div>
                    </div>
                    <div class="col-lg-2 col-md-4 col-sm-6">
                        <div class="metric-card p-3 border rounded position-relative" data-metric="roi">
                            <div class="form-check position-absolute top-0 end-0 mt-2 me-2">
                                <input class="form-check-input metric-checkbox" type="checkbox" id="detailRoiCheck" />
                            </div>
                            <div class="d-flex align-items-center mb-2">
                                <h6 class="mb-0 me-2">ROI</h6>
                                <i class="ri-information-line text-muted"></i>
                            </div>
                            <h4 class="mb-1 fw-bold">4.17</h4>
                            <small class="text-muted">vs last 7 days</small>
                            <span class="text-danger ms-1">-16.27%</span>
                        </div>
                    </div>
                </div>
                <!-- Chart Container -->
                <div class="chart-container">
                    <div id="gmv_max_detail_chart" class="apex-charts" dir="ltr"></div>
                </div>
            </div>
        </div>
    </div>
</div>
}