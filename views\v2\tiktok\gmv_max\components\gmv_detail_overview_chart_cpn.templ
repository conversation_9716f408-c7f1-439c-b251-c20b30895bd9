package components

import "godsp/views/v2/tiktok/tableAds/components"

templ GmvDetailOverviewChartCpn() {
	<div class="row mt-4 fade-in">
		<div class="col-12">
			<div class="card">
				<div class="card-header">
					<div class="d-flex justify-content-between align-items-center">
						<h5 class="card-title mb-0">Overview</h5>
						<div class="d-flex align-items-center gap-2">
							<small class="text-muted">(UTC+07:00) Indochina time</small>
							@components.DateRangeFilterTable()
                            @groupDataViews()
						</div>
					</div>
				</div>
				<div class="card-body">
					<!-- Metrics Cards -->
					<div class="row g-3 mb-4">
						<div class="col-lg-2 col-md-4 col-sm-6">
							<div class="metric-card p-3 border rounded position-relative" data-metric="cost">
								<div class="form-check position-absolute top-0 end-0 mt-2 me-2">
									<input
										class="form-check-input metric-checkbox"
										type="checkbox"
										id="detailCostCheck"
										checked
									/>
								</div>
								<div class="d-flex align-items-center mb-2">
									<h6 class="mb-0 me-2">Cost</h6>
									<i class="ri-information-line text-muted"></i>
								</div>
								<h4 class="mb-1 fw-bold">5,397,173 VND</h4>
								<small class="text-muted">vs last 7 days</small>
								<span class="text-danger ms-1">-43.34%</span>
							</div>
						</div>
						<div class="col-lg-2 col-md-4 col-sm-6">
							<div class="metric-card p-3 border rounded position-relative" data-metric="orders">
								<div class="form-check position-absolute top-0 end-0 mt-2 me-2">
									<input
										class="form-check-input metric-checkbox"
										type="checkbox"
										id="detailOrdersCheck"
										checked
									/>
								</div>
								<div class="d-flex align-items-center mb-2">
									<h6 class="mb-0 me-2">Orders (SKU)</h6>
									<i class="ri-information-line text-muted"></i>
								</div>
								<h4 class="mb-1 fw-bold">106</h4>
								<small class="text-muted">vs last 7 days</small>
								<span class="text-danger ms-1">-52.25%</span>
							</div>
						</div>
						<div class="col-lg-2 col-md-4 col-sm-6">
							<div class="metric-card p-3 border rounded position-relative" data-metric="cost_per_order">
								<div class="form-check position-absolute top-0 end-0 mt-2 me-2">
									<input
										class="form-check-input metric-checkbox"
										type="checkbox"
										id="detailCostPerOrderCheck"
									/>
								</div>
								<div class="d-flex align-items-center mb-2">
									<h6 class="mb-0 me-2">Cost per order</h6>
									<i class="ri-information-line text-muted"></i>
								</div>
								<h4 class="mb-1 fw-bold">50,917 VND</h4>
								<small class="text-muted">vs last 7 days</small>
								<span class="text-success ms-1">+18.67%</span>
							</div>
						</div>
						<div class="col-lg-2 col-md-4 col-sm-6">
							<div class="metric-card p-3 border rounded position-relative" data-metric="gross_revenue">
								<div class="form-check position-absolute top-0 end-0 mt-2 me-2">
									<input
										class="form-check-input metric-checkbox"
										type="checkbox"
										id="detailGrossRevenueCheck"
									/>
								</div>
								<div class="d-flex align-items-center mb-2">
									<h6 class="mb-0 me-2">Gross revenue</h6>
									<i class="ri-information-line text-muted"></i>
								</div>
								<h4 class="mb-1 fw-bold">22,494,198 VND</h4>
								<small class="text-muted">vs last 7 days</small>
								<span class="text-danger ms-1">-52.54%</span>
							</div>
						</div>
						<div class="col-lg-2 col-md-4 col-sm-6">
							<div class="metric-card p-3 border rounded position-relative" data-metric="roi">
								<div class="form-check position-absolute top-0 end-0 mt-2 me-2">
									<input class="form-check-input metric-checkbox" type="checkbox" id="detailRoiCheck"/>
								</div>
								<div class="d-flex align-items-center mb-2">
									<h6 class="mb-0 me-2">ROI</h6>
									<i class="ri-information-line text-muted"></i>
								</div>
								<h4 class="mb-1 fw-bold">4.17</h4>
								<small class="text-muted">vs last 7 days</small>
								<span class="text-danger ms-1">-16.27%</span>
							</div>
						</div>
					</div>
					<!-- Chart Container -->
					<div class="chart-container">
						<div id="gmv_max_detail_chart" class="apex-charts" dir="ltr"></div>
					</div>
				</div>
			</div>
		</div>
	</div>
}

templ groupDataViews() {
	<div class="btn-group">
		<button
			type="button"
			class="btn btn-outline-secondary btn-sm dropdown-toggle"
			data-bs-toggle="dropdown"
			aria-haspopup="true"
			aria-expanded="false"
		>
			<i class="ri-bar-chart-line"></i> Data views
		</button>
		<div class="dropdown-menu dropdown-menu-md p-4" style="min-width: 350px;">
			<div class="mb-3">
				<h6 class="mb-3">30-day performance comparison</h6>
				<p class="text-muted small mb-3">
					Compare ads data with 30-day windows before and after this campaign's run dates,
					for all campaigns active on or after November 3, 2024. Campaigns created before
					December 3, 2024 will have limited pre-campaign data.
				</p>
				<div class="form-check mb-2 d-flex align-items-center gap-2">
					<input
						class="form-check-input"
						type="radio"
						name="dataComparison"
						id="salesDataBefore"
						value="sales_data_before"
					/>
					<label class="form-check-label" for="salesDataBefore">
						Sales data before campaign
					</label>
				</div>
				<div class="form-check mb-3 d-flex align-items-center gap-2">
					<input
						class="form-check-input"
						type="radio"
						name="dataComparison"
						id="promotionDays"
						value="promotion_days"
						checked
					/>
					<label class="form-check-label" for="promotionDays">
						Promotion days
					</label>
				</div>
			</div>
			<div class="mb-3">
				<h6 class="mb-2">Campaign settings</h6>
				<div class="form-check d-flex align-items-center gap-2 mb-3">
					<input class="form-check-input" type="checkbox" id="editedSettings" checked/>
					<label class="form-check-label" for="editedSettings">
						Edited settings
					</label>
				</div>
			</div>
			<div class="d-flex justify-content-end gap-2">
				<button type="button" class="btn btn-light btn-sm">Cancel</button>
				<button type="button" class="btn btn-primary btn-sm">Apply</button>
			</div>
		</div>
	</div>
}
