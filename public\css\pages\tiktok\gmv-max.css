:root {
    /* Velzon Custom Variables */
    --vz-border-radius-sm: 0.6rem;
    --vz-border-radius-lg: 0.8rem;
    --neutral-100: #000000;
    --neutral-200: #222222;
    --neutral-300: #464749;
    --neutral-400: #666666;
    --neutral-500: #adadad;
    --neutral-600: #d6d6d6;
    --neutral-700: #ebebeb;
    --neutral-800: #f5f5f5;
    --neutral-900: #ffffff;
    --primary-50: #e8ebfa;
    --primary-100: #c4ccf2;
    --primary-200: #9bacea;
    --primary-300: #708be1;
    --primary-400: #4c70db;
    --primary-500: #1858d4;
    --primary-600: #0e4ec9;
    --primary-700: #0044bd;
    --primary-800: #003ab1;
    --primary-900: #00269e;
    --tiktok-primary-400: #00a89d;
    --tiktok-primary-500: #00988b;
    --tiktok-primary-600: #008b7e;
}

body {
    font-family: 'Public Sans', sans-serif;
    background-color: var(--neutral-800);
    color: var(--neutral-200);
}

.page-content {
    padding: 1.5rem;
}

.card {
    border: none;
    border-radius: var(--vz-border-radius-lg);
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
}

.card-header {
    background-color: transparent;
    border-bottom: 1px solid var(--neutral-700);
    padding: 1.5rem;
}

.card-body {
    padding: 1.5rem;
}

.btn-primary {
    background-color: var(--primary-500);
    border-color: var(--primary-500);
    border-radius: var(--vz-border-radius-sm);
}

.btn-primary:hover {
    background-color: var(--primary-600);
    border-color: var(--primary-600);
}

.btn-success {
    background-color: var(--tiktok-primary-500);
    border-color: var(--tiktok-primary-500);
    border-radius: var(--vz-border-radius-sm);
}

.btn-success:hover {
    background-color: var(--tiktok-primary-600);
    border-color: var(--tiktok-primary-600);
}

.text-muted {
    color: var(--neutral-400) !important;
}

.text-primary {
    color: var(--primary-500) !important;
}

.bg-light {
    background-color: var(--neutral-800) !important;
}

.border-light {
    border-color: var(--neutral-700) !important;
}

.recommendation-card {
    border: 1px solid var(--neutral-700);
    border-radius: var(--vz-border-radius-sm);
    transition: all 0.2s ease;
}

.recommendation-card:hover {
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.1);
}

.product-thumbnail {
    width: 48px;
    height: 48px;
    border-radius: var(--vz-border-radius-sm);
    background: linear-gradient(45deg, #ff6b6b, #ffa500);
    border: 1px solid var(--neutral-700);
}

.product-thumbnail:nth-child(2) {
    background: linear-gradient(45deg, #4ecdc4, #44a08d);
}

.product-thumbnail:nth-child(3) {
    background: linear-gradient(45deg, #a8e6cf, #7fcdcd);
}

.shop-logo {
    width: 40px;
    height: 40px;
    background-color: var(--neutral-700);
    border-radius: var(--vz-border-radius-sm);
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
    color: var(--neutral-400);
}

.info-badge {
    width: 20px;
    height: 20px;
    background-color: var(--primary-50);
    color: var(--primary-500);
    border-radius: 50%;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
    font-weight: 600;
}

.switch-shop-link {
    color: var(--neutral-400);
    text-decoration: none;
    font-size: 0.875rem;
    transition: color 0.2s ease;
}

.switch-shop-link:hover {
    color: var(--neutral-300);
}

.fade-in {
    animation: fadeInUp 0.5s ease forwards;
    opacity: 0;
    transform: translateY(20px);
}

.fade-in:nth-child(1) {
    animation-delay: 0.1s;
}

.fade-in:nth-child(2) {
    animation-delay: 0.2s;
}

.fade-in:nth-child(3) {
    animation-delay: 0.3s;
}

@keyframes fadeInUp {
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.close-btn {
    background: none;
    border: none;
    color: var(--neutral-400);
    font-size: 1.25rem;
    cursor: pointer;
    padding: 0.25rem;
    border-radius: var(--vz-border-radius-sm);
    transition: all 0.2s ease;
}

.close-btn:hover {
    color: var(--neutral-300);
    background-color: var(--neutral-700);
}

/* Metric Cards Row - Make all cards equal width and height */
.row.g-3.mb-4 {
    display: flex;
    flex-wrap: wrap;
    gap: 1rem;
}

.row.g-3.mb-4>[class*="col-"] {
    flex: 1;
    min-width: 0;
    /* Allow flex items to shrink below content size */
    display: flex;
}

/* Metric Cards Styles */
.metric-card {
    background-color: var(--neutral-900);
    border: 1px solid var(--neutral-700);
    border-radius: var(--vz-border-radius-sm);
    transition: all 0.2s ease;
    cursor: pointer;
    position: relative;
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    min-height: 140px;
    /* Ensure consistent minimum height */
    width: 100%;
}

.metric-card:hover {
    border-color: var(--primary-300);
    box-shadow: 0 0.25rem 0.5rem rgba(0, 0, 0, 0.1);
}

.metric-card.active {
    border-color: var(--primary-500);
    background-color: var(--primary-50);
}

.metric-card .form-check-input {
    border-color: var(--neutral-500);
}

.metric-card .form-check-input:checked {
    background-color: var(--primary-500);
    border-color: var(--primary-500);
}

.metric-card h4 {
    color: var(--neutral-100);
    font-size: 1.5rem;
    font-weight: 700;
}

.metric-card h6 {
    color: var(--neutral-200);
    font-size: 0.875rem;
    font-weight: 600;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 1;
    line-clamp: 1;
    overflow: hidden;
    text-overflow: ellipsis;
}

.metric-card .text-success {
    color: var(--tiktok-primary-500) !important;
}

.metric-card .text-danger {
    color: #dc3545 !important;
}

/* Chart Container */
.chart-container {
    background-color: var(--neutral-900);
    border-radius: var(--vz-border-radius-sm);
    padding: 1rem;
    margin-top: 1rem;
}

/* ApexCharts Custom Styles */
.apexcharts-canvas {
    background: transparent !important;
}

.apexcharts-tooltip {
    background: var(--neutral-900) !important;
    border: 1px solid var(--neutral-700) !important;
    color: var(--neutral-200) !important;
}

.apexcharts-tooltip-title {
    background: var(--neutral-800) !important;
    border-bottom: 1px solid var(--neutral-700) !important;
    color: var(--neutral-200) !important;
}

.apexcharts-legend {
    color: var(--neutral-200) !important;
}

.apexcharts-legend-text {
    color: var(--neutral-200) !important;
}

.apexcharts-xaxis-label,
.apexcharts-yaxis-label {
    fill: var(--neutral-400) !important;
}

.apexcharts-gridline {
    stroke: var(--neutral-700) !important;
}

/* Campaign Tabs Styles */
.nav-tabs-custom {
    border-bottom: 2px solid var(--neutral-700);
    width: max-content;
}

.nav-tabs-custom .nav-link {
    border: none;
    border-radius: 0;
    color: var(--neutral-400);
    font-weight: 600;
    padding: 1rem 1.5rem;
    background: transparent;
    border-bottom: 2px solid transparent;
    transition: all 0.3s ease;
    width: max-content;
    white-space: nowrap;
}

.nav-tabs-custom .nav-link:hover {
    color: var(--primary-500);
    border-bottom-color: var(--primary-300);
    background: transparent;
}

.nav-tabs-custom .nav-link.active {
    color: var(--primary-500);
    background: transparent;
    border-bottom-color: var(--primary-500);
}

.tab-content {
    padding-top: 1.5rem;
}

/* Table Container Styles */
.table-container {
    background-color: var(--neutral-900);
    border-radius: var(--vz-border-radius-sm);
    border: 1px solid var(--neutral-700);
}

/* Search and Filter Styles */
.input-group .form-control {
    border-color: var(--neutral-700);
}

.input-group .form-control:focus {
    border-color: var(--primary-500);
    box-shadow: 0 0 0 0.2rem rgba(24, 88, 212, 0.25);
}

.form-select {
    border-color: var(--neutral-700);
    background-color: var(--neutral-900);
    color: var(--neutral-200);
}

.form-select:focus {
    border-color: var(--primary-500);
    box-shadow: 0 0 0 0.2rem rgba(24, 88, 212, 0.25);
}

.btn-outline-secondary {
    border-color: var(--neutral-700);
    color: var(--neutral-400);
}

.btn-outline-secondary:hover {
    background-color: var(--neutral-700);
    border-color: var(--neutral-600);
    color: var(--neutral-200);
}

/* Loading States */
.spinner-border {
    width: 2rem;
    height: 2rem;
}

.text-center.py-4 {
    min-height: 200px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
}

.text-center.py-5 {
    min-height: 300px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
}

/* Responsive adjustments */
@media (max-width: 768px) {

    /* Metric cards stack vertically on mobile */
    .row.g-3.mb-4 {
        flex-direction: column;
        gap: 0.75rem;
    }

    .row.g-3.mb-4>[class*="col-"] {
        flex: none;
        width: 100%;
    }

    .metric-card {
        min-height: 120px;
    }

    .metric-card h4 {
        font-size: 1.25rem;
    }

    .metric-card h6 {
        font-size: 0.8rem;
    }

    .nav-tabs-custom .nav-link {
        padding: 0.75rem 1rem;
        font-size: 0.875rem;
    }

    .d-flex.justify-content-between.align-items-center.mb-3 {
        flex-direction: column;
        gap: 1rem;
        align-items: stretch !important;
    }

    .d-flex.align-items-center.gap-3 {
        flex-direction: column;
        gap: 0.5rem !important;
    }

    .input-group {
        width: 100% !important;
    }

    .form-select {
        width: 100% !important;
    }
}

/* Tablet adjustments */
@media (min-width: 769px) and (max-width: 1024px) {
    .row.g-3.mb-4>[class*="col-"] {
        min-width: calc(50% - 0.5rem);
        max-width: calc(50% - 0.5rem);
    }
}

/* Custom Status Dropdown Styling */
.custom-select {
    position: relative;
    width: 150px;
    font-weight: 500;
    cursor: pointer;
    user-select: none;
}

.custom-select .selected {
    background-color: #fff;
    border: 1px solid #ced4da;
    border-radius: 0.375rem;
    padding: 0.375rem 0.75rem;
    display: flex;
    align-items: center;
    justify-content: space-between;
    font-size: 0.875rem;
    color: #495057;
    transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}

.custom-select .selected:after {
    content: "▼";
    font-size: 0.75rem;
    color: #6c757d;
    transition: transform 0.2s ease;
}

.custom-select.open .selected:after {
    transform: rotate(180deg);
}

.custom-select .selected:hover {
    border-color: #86b7fe;
}

.custom-select.open .selected {
    border-color: #86b7fe;
    box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
}

.custom-select .options {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background-color: #fff;
    border: 1px solid #ced4da;
    border-top: none;
    border-radius: 0 0 0.375rem 0.375rem;
    max-height: 200px;
    overflow-y: auto;
    z-index: 1000;
    display: none;
    list-style: none;
    padding: 0;
    margin: 0;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
}

.custom-select.open .options {
    display: block;
}

.custom-select .options li {
    padding: 0.375rem 0.75rem;
    cursor: pointer;
    display: flex;
    align-items: center;
    font-size: 0.875rem;
    color: #495057;
    transition: background-color 0.15s ease;
}

.custom-select .options li:hover {
    background-color: #f8f9fa;
}

.custom-select .options li:active {
    background-color: #e9ecef;
}

/* Status circle colors */
.status-item-circle.active {
    background-color: #28a745;
    /* Green */
}

.status-item-circle.inactive {
    background-color: #6c757d;
    /* Gray */
}

.status-item-circle.deleted {
    background-color: #dc3545;
    /* Red */
}

.status-item-circle.not-delivering {
    background-color: #fd7e14;
    /* Orange */
}

/* Status Circle Indicator */
.status-item-circle {
    border-radius: 50%;
    display: inline-block;
    height: 8px;
    margin-right: 8px;
    width: 8px;
}

/* Status circle colors */
.status-item-circle.active {
    background-color: #28a745;
    /* Green */
}

.status-item-circle.inactive {
    background-color: #6c757d;
    /* Gray */
}

.status-item-circle.deleted {
    background-color: #dc3545;
    /* Red */
}

.status-item-circle.not-delivering {
    background-color: #fd7e14;
    /* Orange */
}

/* Status badges for table display */
.status-badge {
    display: inline-flex;
    align-items: center;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 500;
    gap: 4px;
}

.status-badge .status-item-circle {
    margin-right: 4px;
}