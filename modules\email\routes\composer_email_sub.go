package routes

import (
	userMongo "godsp/modules/admin/user/repository/mongo"
	"godsp/modules/email/common"
	"godsp/modules/email/service"
	"godsp/modules/email/transport/subredis"
	"godsp/modules/email/usecase"
	"godsp/pkg/gos/auths"

	"github.com/dev-networldasia/dspgos/sctx"
	"github.com/dev-networldasia/dspgos/sctx/component/discord"
	"github.com/dev-networldasia/dspgos/sctx/component/mongodb"
	"github.com/dev-networldasia/dspgos/sctx/component/watermillapp/redisstream"
	"github.com/dev-networldasia/dspgos/sctx/configs"
)

// ComposerEmailApi defines the interface for email API handlers.
type ComposerEmailSub interface {
	SendEmailResetPasswordSub() redisstream.NoPublishHandlerFunc
	SendEmailNotifyApprovalTopupSub() redisstream.NoPublishHandlerFunc
	SendEmailNotifyFullBalanceAdAccountSub() redisstream.NoPublishHandlerFunc
	SendEmailBillingDetailSub() redisstream.NoPublishHandlerFunc
	SendEmailBillingSub() redisstream.NoPublishHandlerFunc
}

func ComposerEmailSubService(serviceCtx sctx.ServiceContext) ComposerEmailSub {
	logger := serviceCtx.MustGet(configs.KeyLoggerDaily).(sctx.AppLoggerDaily).GetLogger("email_sub")
	discordSvc := serviceCtx.MustGet(configs.KeyDiscordSMS).(discord.SendMessageDiscordSVC)

	mongoDB := serviceCtx.MustGet(configs.KeyCompMongoDB).(mongodb.MongoComponent).GetDatabase()
	redisStreamPub := serviceCtx.MustGet(configs.KeyRedisStreamPub).(redisstream.RedisStreamPublisher)
	// _ = serviceCtx.MustGet(configs.KeyRedisStreamSub).(redisstream.RedisStreamSubcriber)

	userRepo := userMongo.NewUserRepo(mongoDB)
	config := common.LoadEmailConfig()
	srv := service.NewEmailService(config)
	hasher := new(auths.Hasher)

	emailUsc := usecase.NewEmailSubUsecase(srv, logger, userRepo, redisStreamPub, hasher, discordSvc)

	subFunc := subredis.NewEmailSub(emailUsc, logger)
	return subFunc
}
