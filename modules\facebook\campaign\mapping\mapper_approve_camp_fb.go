package mapping

import (
	campEnum "godsp/modules/facebook/campaign/common/enum"
	"godsp/modules/facebook/campaign/entity"
	"godsp/modules/facebook/campaign/transport/requests"
	"godsp/modules/facebook/common/fbenums"
	v20 "godsp/pkg/fb-marketing/marketing/v20"
	"time"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

func MapperApproveCampaign(payload *requests.ApproveCampaignReq, campPre *entity.CampaignEntity, userId primitive.ObjectID) (bson.M, *v20.Campaign) {
	campFB := v20.Campaign{
		ID:        payload.CampaignID,
		AccountID: payload.AccountID,
		// Name:      payload.Name,
	}
	campUpdate := bson.M{
		"updated_at": time.Now(),
		"updated_by": userId,
	}

	campUpdate["approve"] = campEnum.Approve
	campUpdate["status"] = fbenums.FB_STATUS_ACTIVE
	campFB.Status = fbenums.FB_STATUS_ACTIVE
	return bson.M{
		"$set": campUpdate,
	}, &campFB

}

// func MapperApproveCampaign(payload *requests.ApproveCampaignReq, campPre *entity.CampaignEntity, userId primitive.ObjectID) (bson.M, *v20.Campaign) {
// 	now := time.Now()
// 	campFB := v20.Campaign{
// 		ID:        payload.CampaignID,
// 		AccountID: payload.AccountID,
// 		Name:      payload.Name,
// 	}
// 	campUpdate := bson.M{
// 		"updated_at": now,
// 		"updated_by": userId,
// 	}

// 	if payload.Status == "" {
// 		return campUpdate, &campFB
// 	}

// 	if payload.Status == fbenums.FB_STATUS_PAUSED {
// 		if payload.Approve == campEnum.Review {
// 			campUpdate["approve"] = campEnum.Approve
// 			campUpdate["status"] = fbenums.FB_STATUS_ACTIVE
// 			campFB.Status = fbenums.FB_STATUS_ACTIVE
// 			return bson.M{
// 				"$set": campUpdate,
// 			}, &campFB
// 		}
// 	}

// 	return nil, nil
// }
