package billing

import (
	"context"
	"godsp/modules/admin/billing/repository/mongo"
	"godsp/modules/admin/billing/transport/api"
	"godsp/modules/admin/billing/transport/handlers"
	"godsp/modules/admin/billing/usecase"
	clientR "godsp/modules/admin/client/repository/mongo"
	userR "godsp/modules/admin/user/repository/mongo"
	adAccR "godsp/modules/facebook/ad_account/repository/mongo"

	"github.com/dev-networldasia/dspgos/sctx"
	"github.com/dev-networldasia/dspgos/sctx/component/mongodb"
	"github.com/dev-networldasia/dspgos/sctx/component/watermillapp/redisstream"
	"github.com/dev-networldasia/dspgos/sctx/configs"
	"github.com/gofiber/fiber/v2"
)

// index mongo for billing
type composerIndexMongoBilling interface {
	CreateBillingIndex(ctx context.Context) error
}

func SetupIndexMongoBilling(serviceCtx sctx.ServiceContext) composerIndexMongoBilling {
	mongoDB := serviceCtx.MustGet(configs.KeyCompMongoDB).(mongodb.MongoComponent).GetDatabase()
	repo := mongo.NewBillingRepo(mongoDB)
	return repo
}

type composerBilling interface {
	ListBillingHdl() fiber.Handler
	CreateBillingHdl() fiber.Handler
	EditBillingHdl() fiber.Handler
}

type composerBillingApi interface {
	CreateBillingApi() fiber.Handler
	ListBillingApi() fiber.Handler
	ListBillingDetailApi() fiber.Handler
	EditBillingApi() fiber.Handler
	UpdateBillingDetailApi() fiber.Handler
	UpdateBillingApi() fiber.Handler
}

func composerBillingServive(serviceCtx sctx.ServiceContext) composerBilling {
	logger := serviceCtx.MustGet(configs.KeyLoggerDaily).(sctx.AppLoggerDaily).GetLogger("admins")
	mongoDB := serviceCtx.MustGet(configs.KeyCompMongoDB).(mongodb.MongoComponent).GetDatabase()
	adAccRepo := adAccR.NewAdAccountRepo(mongoDB)
	repo := mongo.NewBillingRepo(mongoDB)
	clientRepo := clientR.NewClientRepo(mongoDB)
	userRepo := userR.NewUserRepo(mongoDB)
	usc := usecase.NewBillingUsc(repo, logger, adAccRepo, clientRepo, userRepo)
	hdl := handlers.NewBillingHdl(usc)

	return hdl
}

func composerBillingApiServive(serviceCtx sctx.ServiceContext) composerBillingApi {
	logger := serviceCtx.MustGet(configs.KeyLoggerDaily).(sctx.AppLoggerDaily).GetLogger("admins")
	mongoDB := serviceCtx.MustGet(configs.KeyCompMongoDB).(mongodb.MongoComponent).GetDatabase()
	redisStreamPub := serviceCtx.MustGet(configs.KeyRedisStreamPub).(redisstream.RedisStreamPublisher)
	repo := mongo.NewBillingRepo(mongoDB)
	adAccRepo := adAccR.NewAdAccountRepo(mongoDB)
	clientRepo := clientR.NewClientRepo(mongoDB)
	userRepo := userR.NewUserRepo(mongoDB)
	usc := usecase.NewApiBillingUsc(repo, adAccRepo, clientRepo, userRepo, redisStreamPub, logger)
	api := api.NewBillingApi(usc, logger)

	return api
}

func SetupRoutesBilling(app *fiber.App, serviceCtx sctx.ServiceContext, midds ...fiber.Handler) {
	group := app.Group("admins/billings")
	{
		if len(midds) > 0 {
			for _, midd := range midds {
				group.Use(midd)
			}
		}

		comp := composerBillingServive(serviceCtx)
		group.Get("/list", comp.ListBillingHdl()).Name("admins.billing.list")
		group.Get("/create", comp.CreateBillingHdl()).Name("admins.billing.create")
		group.Get("/edit/:billingId", comp.EditBillingHdl()).Name("admins.billing.edit")
	}

	apiGroup := app.Group("api/admins/billings")
	{
		if len(midds) > 0 {
			for _, midd := range midds {
				apiGroup.Use(midd)
			}
		}

		compApi := composerBillingApiServive(serviceCtx)
		apiGroup.Post("/create", compApi.CreateBillingApi()).Name("admins.api.billing.create_api")
		apiGroup.Post("/list-datatable", compApi.ListBillingApi()).Name("admins.api.billing.list_datatable")
		apiGroup.Post("/detail/list-datatable", compApi.ListBillingDetailApi()).Name("admins.api.billing.detail.list_datatable")
		apiGroup.Put("/detail/:billingDetailId", compApi.UpdateBillingDetailApi()).Name("admins.api.billing.detail.update")
		//apiGroup.Put("/edit", compApi.EditBillingApi()).Name("admins.api.billing.edit")
		apiGroup.Put("/:billingId", compApi.UpdateBillingApi()).Name("admins.api.billing.update")
	}
}
