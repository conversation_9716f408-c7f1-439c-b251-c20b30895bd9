package usecase

import (
	"context"

	clientE "godsp/modules/admin/client/entity"
	"godsp/modules/admin/user/common/pipelines"
	"godsp/modules/admin/user/entity"
	userRes "godsp/modules/admin/user/transport/responses"
	adAccRes "godsp/modules/facebook/ad_account/transport/response"

	"godsp/modules/facebook/common/fbenums"
	"godsp/modules/facebook/iface_repo"

	v20 "godsp/pkg/fb-marketing/marketing/v20"

	"github.com/dev-networldasia/dspgos/sctx"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo/options"
)

type CampaignRepo interface {
}

type UserRepo interface {
	FindOneUserRepo(ctx context.Context, filter interface{}, opts ...*options.FindOneOptions) (*entity.UserEntity, error)
	FindOneDetailUserWithPipelineRepo(ctx context.Context, pipeline []bson.M, opts ...*options.AggregateOptions) (*userRes.DetailsUser, error)
	// FindOneDetailUserWithPipelineRepo(ctx context.Context, pipeline []bson.M, opts ...*options.AggregateOptions) (*responses.DetailsUser, error)
}

type ClientRepo interface {
	FindClientRepo(ctx context.Context, filter interface{}, opts ...*options.FindOptions) ([]*clientE.ClientEntity, error)
}

type campaignUsc struct {
	fbService  *v20.Service
	repo       CampaignRepo
	userRepo   UserRepo
	logger     sctx.Logger
	adAcc      iface_repo.AdAccountRepo
	clientRepo ClientRepo
}

func NewCampaignUsc(fbService *v20.Service, repo CampaignRepo, userRepo UserRepo, logger sctx.Logger, adAcc iface_repo.AdAccountRepo, clientRepo ClientRepo) *campaignUsc {
	return &campaignUsc{
		fbService:  fbService,
		repo:       repo,
		userRepo:   userRepo,
		logger:     logger,
		adAcc:      adAcc,
		clientRepo: clientRepo,
	}
}

/**
 * Get User by ID
 */
func (usc *campaignUsc) GetUserInfo(ctx context.Context, userId primitive.ObjectID) (*userRes.DetailsUser, error) {

	pipeline := pipelines.PipelineGetDetatilInfoUser(userId)

	user, err := usc.userRepo.FindOneDetailUserWithPipelineRepo(ctx, pipeline)
	if err != nil {
		usc.logger.Error(err)
		return nil, err
	}

	return user, nil
}

/**
 * list ad account
 */
func (usc *campaignUsc) ListAdAccountCampaignUsc(ctx context.Context) ([]*adAccRes.AdAccountEditClient, error) {
	allowDiskUse := fbenums.ALLOW_DISK_USE_TRUE
	opts := &options.FindOptions{
		AllowDiskUse: &allowDiskUse,
		Sort:         bson.D{{Key: "name", Value: -1}},
		Projection:   bson.M{"id": 1, "name": 1, "account_id": 1},
	}
	return usc.adAcc.FindAdAccountEditClientRepo(ctx, bson.M{}, opts)
}

/**
 * List client
 */
func (usc *campaignUsc) ListClientCampaignUsc(ctx context.Context) ([]*clientE.ClientEntity, error) {
	return usc.clientRepo.FindClientRepo(ctx, bson.M{})
}
