package entity

import (
	"time"

	"go.mongodb.org/mongo-driver/bson/primitive"
)

type PermissionMapInt map[string]int

type RoleEntity struct {
	ID          primitive.ObjectID `json:"_id,omitempty" bson:"_id,omitempty"`
	Name        string             `json:"name" bson:"name"`
	Status      int                `json:"status" bson:"status"`
	RoleName    string             `json:"role_name" bson:"role_name"`
	Ordering    int64              `json:"ordering" bson:"ordering"`
	Permissions PermissionMapInt   `json:"permissions,omitempty" bson:"permissions,omitempty"`
	CreatedBy   primitive.ObjectID `json:"created_by" bson:"created_by"`
	UpdatedBy   primitive.ObjectID `json:"updated_by,omitempty" bson:"updated_by,omitempty"`
	CreatedAt   time.Time          `json:"created_at" bson:"created_at"`
	UpdatedAt   time.Time          `json:"updated_at" bson:"updated_at"`
	UserCreated *UserRole          `json:"user_created,omitempty" bson:"user_created,omitempty"`
	UserUpdated *UserRole          `json:"user_updated,omitempty" bson:"user_updated,omitempty"`
}

func (RoleEntity) CollectionName() string {
	return "admin_roles"
}

type UserRole struct {
	ID       primitive.ObjectID `json:"_id,omitempty" bson:"_id,omitempty"`
	FullName string             `json:"full_name" bson:"full_name"`
}
