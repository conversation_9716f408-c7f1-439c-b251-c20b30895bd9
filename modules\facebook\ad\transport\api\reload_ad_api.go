package api

import (
	"fmt"
	"godsp/modules/facebook/ad/transport/requests"
	"godsp/pkg/gos/utils"
	"godsp/pkg/sctx/core"
	"net/http"

	"github.com/gofiber/fiber/v2"
)

/***
 * Api reload Ad
 */
func (a *adApi) ReloadAdApi() fiber.Handler {
	return func(c *fiber.Ctx) error {
		var payload requests.ReloadAdReq
		if c.Body() != nil && len(c.Body()) > 0 {
			if err := c.BodyParser(&payload); err != nil {
				return core.ReturnErrsForApi(c, err)
			}
		}

		//validate
		validationErrors := payload.Validate()
		if len(validationErrors) > 0 {
			return core.ReturnErrsForApi(c, validationErrors)
		}

		userId, err := utils.GetUserId(c.Context())
		if err != nil {
			fmt.Println("Error:", err)
			return core.ReturnErrsForApi(c, err)
		}

		roleName, err := a.usc.GetRoleByUserId(c.Context(), userId)
		if *roleName != "ADMIN" || err != nil {
			return core.ErrForbidden
		}

		if payload.AdID != "" {
			if err := a.usc.ReloadAdDetailUsc(c.Context(), payload.AdID, userId); err != nil {
				return core.ReturnErrForApi(c, err.Error())
			}
		} else if payload.AdsetID != "" {
			if err := a.usc.ReloadWithAdsetUsc(c.Context(), payload.AdsetID); err != nil {
				return core.ReturnErrsForApi(c, err)
			}
		} else {
			fmt.Println("\n-------- AdsFromFB -------> \n", payload.AccountID)

			if err := a.usc.ReloadWithAccountAdsUsc(c.Context(), payload.AccountID); err != nil {
				return core.ReturnErrsForApi(c, err)
			}
			// if act := utils.GetAdAccount(c.Context()); act != nil {
			// 	payload.AccountID = *act
			// 	if err := a.usc.ReloadWithAccountAdsUsc(c.Context(), payload.AccountID); err != nil {
			// 		return core.ReturnErrsForApi(c, err)
			// 	}
			// }
		}

		return c.Status(http.StatusOK).JSON(core.ResponseData(map[string]interface{}{
			"msg": "Reload Ad successfully",
		}))
	}
}
