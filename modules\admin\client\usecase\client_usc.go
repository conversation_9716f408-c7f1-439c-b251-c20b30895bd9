package usecase

import (
	"context"
	"errors"
	"fmt"
	"godsp/modules/admin/client/common/errs"
	"godsp/modules/admin/client/common/pipelines"
	"godsp/modules/admin/client/entity"
	"godsp/modules/admin/client/mapping"
	"godsp/modules/admin/client/transport/responses"
	"godsp/modules/admin/common/admconst"
	userE "godsp/modules/admin/user/entity"
	adAccRes "godsp/modules/facebook/ad_account/transport/response"
	pixelE "godsp/modules/facebook/ad_pixel/entity"
	pageE "godsp/modules/facebook/pages/entity"
	tikadverErrs "godsp/modules/tiktok/advertiser/common/errs"
	tikAdverMapping "godsp/modules/tiktok/advertiser/mapping"
	tikAdverRes "godsp/modules/tiktok/advertiser/transport/responses"

	"godsp/modules/facebook/common/fbenums"

	"github.com/dev-networldasia/dspgos/sctx"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo/options"
)

type ClientRepo interface {
	FindClientsWithPipelineRepo(ctx context.Context, pipeline []bson.M, opts ...*options.AggregateOptions) (*[]entity.ClientEntity, error)
	InsertUserRepo(ctx context.Context, user *entity.ClientCreationEntity) (*primitive.ObjectID, error)
	FindOneClientRepo(ctx context.Context, filter interface{}, opts ...*options.FindOneOptions) (*entity.ClientEntity, error)
	FindOneClientWithPipelineRepo(ctx context.Context, pipeline []bson.M, opts ...*options.AggregateOptions) (*entity.ClientEntity, error)
}

type clientUsc struct {
	repo              ClientRepo
	userRepo          UserRepo
	adAcc             AdAccountRepo
	pageRepo          PageRepo
	pixelRepo         PixelRepo
	catalogueRepo     CatalogueRepo
	tikAdvertiserRepo TiktokAdvertiserRepo
	tikPresetColRepo  TiktokPresetColumnRepo
	logger            sctx.Logger
}

func NewClientUsc(logger sctx.Logger, repo ClientRepo, userRepo UserRepo, adAcc AdAccountRepo, pageRepo PageRepo, pixelRepo PixelRepo, catalogueRepo CatalogueRepo, tikAdvertiserRepo TiktokAdvertiserRepo, tikPresetColRepo TiktokPresetColumnRepo) *clientUsc {
	return &clientUsc{
		repo:              repo,
		userRepo:          userRepo,
		adAcc:             adAcc,
		pageRepo:          pageRepo,
		pixelRepo:         pixelRepo,
		catalogueRepo:     catalogueRepo,
		tikAdvertiserRepo: tikAdvertiserRepo,
		tikPresetColRepo:  tikPresetColRepo,
		logger:            logger,
	}
}

/**
 * Get detail user by id
 */
func (usc *clientUsc) FindOneDetailClientUsc(ctx context.Context, id string) (*entity.ClientEntity, error) {
	objectID, err := primitive.ObjectIDFromHex(id)
	if err != nil {
		usc.logger.Error(err)
		return nil, err
	}

	allowDiskUse := fbenums.ALLOW_DISK_USE_TRUE
	opts := &options.AggregateOptions{
		AllowDiskUse: &allowDiskUse,
	}

	pipeline := pipelines.PipelineGetOneClient(objectID)

	data, err := usc.repo.FindOneClientWithPipelineRepo(ctx, pipeline, opts)
	if err != nil {
		usc.logger.Error(err)
		return nil, errs.ErrIDClientValidate
	}

	if data.Status == admconst.STATUS_DELETE {
		return nil, errs.ErrEditStatusDeleteClient
	}
	data.PathImg()

	return data, nil
}

/**
 * list ad user
 */
func (usc *clientUsc) GetUserOfClientUsc(ctx context.Context, id primitive.ObjectID) (*[]userE.UserEntity, error) {

	allowDiskUse := fbenums.ALLOW_DISK_USE_TRUE
	opts := &options.AggregateOptions{
		AllowDiskUse: &allowDiskUse,
	}

	pipeline := pipelines.PipelineUserOfClient(id)

	data, err := usc.userRepo.FindUsersWithPipelineRepo(ctx, pipeline, opts)
	if err != nil {
		fmt.Printf("FindUsersWithPipelineRepo - Client USC  ------->: %v\n", err)
		usc.logger.Error(err)
		return nil, errs.ErrGetUser
	}
	// jsonData, _ := json.MarshalIndent(data, "", "  ")
	// fmt.Printf("usersData - Client USC  ------->: %v\n", string(jsonData))

	return data, nil
}

/**
 * list ad account
 */
func (usc *clientUsc) ListAdAccountClientUsc(ctx context.Context) ([]*adAccRes.AdAccountEditClient, error) {
	allowDiskUse := fbenums.ALLOW_DISK_USE_TRUE
	opts := &options.FindOptions{
		AllowDiskUse: &allowDiskUse,
		Sort:         bson.D{{Key: "name", Value: -1}},
		Projection:   bson.M{"id": 1, "name": 1, "account_id": 1},
	}
	return usc.adAcc.FindAdAccountEditClientRepo(ctx, bson.M{}, opts)
}

/**
 * Get List Page of User
 */

func (usc *clientUsc) ListPageClientUsc(ctx context.Context) ([]*pageE.PageEntity, error) {

	filter := bson.M{}
	page, err := usc.pageRepo.FindPageRepo(ctx, filter)
	if err != nil {
		usc.logger.Error(err)
		return nil, err
	}

	return page, nil
}

/**
 * Get List Page of User
 */

func (usc *clientUsc) ListPixelClientUsc(ctx context.Context) ([]*pixelE.AdPixelEntity, error) {

	filter := bson.M{}
	pixels, err := usc.pixelRepo.FindAdPixelRepo(ctx, filter)
	if err != nil {
		usc.logger.Error(err)
		return nil, err
	}

	return pixels, nil
}

/**
 * Get List Catalogue
 */

func (usc *clientUsc) ListCatalogueClientUsc(ctx context.Context) ([]*responses.CatalogueListEditClient, error) {

	filter := bson.M{}

	catalogues, err := usc.catalogueRepo.FindCataloguesRepo(ctx, filter)
	if err != nil {
		usc.logger.Error(err)
		return nil, err
	}

	dataReturn := mapping.MappingCatalogueList(catalogues)

	return dataReturn, nil
}

/**
 * Get List Advertiser
 */
func (usc *clientUsc) GetListAdAccountsTiktokUsc(ctx context.Context) (*[]tikAdverRes.ListAdvertiserRes, error) {

	advertiserEs, err := usc.tikAdvertiserRepo.FindListAdaccountsRepo(ctx, bson.M{})
	if err != nil {
		usc.logger.Error(err)
		return nil, err
	}

	if advertiserEs == nil {
		return nil, tikadverErrs.ErrAdvertiserNotFound
	}

	// jsonData2, _ := json.MarshalIndent(advertiserEs, "", "  ")
	for _, adver := range *advertiserEs {
		fmt.Printf("advertiserEs  ------->: %v\n", adver.ClientIDs)
	}
	// fmt.Printf("tikAdvertisers  ------->: %v\n", string(jsonData2))

	results := tikAdverMapping.MapListAdvertiserToResponse(advertiserEs)

	return results, nil
}

/**
 * GetListPresetColumnUsc
 */
func (usc *clientUsc) GetListPresetColumnTiktokUsc(context context.Context) ([]*mapping.TiktokPresetColumnTableRes, error) {
	filter := bson.M{
		"type": 2,
	}

	presetsColumn, err := usc.tikPresetColRepo.FindListPresetColumnRepo(context, filter)

	if err != nil {
		usc.logger.Error("Error finding preset column", "error", err)
		return nil, err
	}

	if presetsColumn == nil {
		usc.logger.Warn("Preset column not found", "filter", filter)
		return nil, errors.New("preset column not found")
	}

	presets := mapping.TikMapperListPresetColSelectClient(presetsColumn)
	return presets, nil
}
