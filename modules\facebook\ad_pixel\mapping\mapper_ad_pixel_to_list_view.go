package mapping

import (
	"godsp/modules/facebook/ad_pixel/entity"
	"godsp/modules/facebook/ad_pixel/transport/response"
)

func MapperAdPixelEntityToListView(pixels []*entity.AdPixelEntity) *[]response.AdPixelView {

	var pixelList []response.AdPixelView

	for _, v := range pixels {

		pixelList = append(pixelList, response.AdPixelView{
			AccountID: v.AccountID,
			ID:        v.PixelID,
			Name:      v.Name,
		})
	}

	return &pixelList
}
