https://developers.facebook.com/docs/marketing-api/reference/ad-campaign

https://developers.facebook.com/docs/marketing-apis/get-started#ad-set-targeting

## targeting

get country_group: search?type=adgeolocation&location_types=['country_group']&q=&limit=1000

get countries all: search?type=adgeolocation&location_types=["country"]&q=&limit=1000

https://developers.facebook.com/docs/marketing-api/audiences/reference/placement-targeting


## frequency control
https://developers.facebook.com/docs/marketing-api/reference/ad-campaign-frequency-control-specs/


## Adset 
https://github.com/facebook/facebook-nodejs-business-sdk/blob/4a73348474f969e4fc4d55b65b70217896486747/src/objects/ad-set.js

## Adset Performance goals:
https://developers.facebook.com/docs/marketing-api/reference/ad-campaign/


# Destination type
https://developers.facebook.com/docs/marketing-api/adset/destination_type


# Promoted Object
https://developers.facebook.com/docs/marketing-api/reference/ad-promoted-object/