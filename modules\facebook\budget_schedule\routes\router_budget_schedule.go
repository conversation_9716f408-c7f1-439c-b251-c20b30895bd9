package routes

import (
	"github.com/dev-networldasia/dspgos/sctx"

	"github.com/gofiber/fiber/v2"
)

func SetupRouterBudgetSchedule(app *fiber.App, serviceCtx sctx.ServiceContext, midds ...fiber.Handler) {
	apiGroup := app.Group("/dsp/facebook/api/budget-schedules")
	{
		if len(midds) > 0 {
			for _, midd := range midds {
				apiGroup.Use(midd)
			}
		}
		compApi := ComposerBudgetScheduleApiService(serviceCtx)
		// apiGroup.Patch("/reload", compApi.ReloadBudgetScheduleByCampaignAdsetIdApi()).Name("fb.budget-schedul.api.campagin_reload")
		apiGroup.Patch("/reload", compApi.ReloadBudgetScheduleByCampaignAdsetIdApi()).Name("fb.budget-schedule.api.reload")
		apiGroup.Post("", compApi.GetByCampaignAdsetIdApiBudgetScheduleApi()).Name("fb.budget-schedule.api.campagin_post")
		apiGroup.Post("/create", compApi.CreateBudgetScheduleApi()).Name("fb.budget-schedule.api.campagin_create")
		apiGroup.Delete("", compApi.DeleteByIdBudgetScheduleApi()).Name("fb.budget-schedule.api.campagin_delete")
		apiGroup.Patch("", compApi.UpdateListBudgetScheduleApi()).Name("fb.budget-schedule.api.campagin_update")
	}
}
