package gmv_max

import (
	"github.com/dev-networldasia/dspgos/gos/templates"
	"godsp/views/v2/layouts"
	layoutCops "godsp/views/v2/layouts/components"
	"godsp/views/v2/layouts/masters"
	cop "godsp/views/v2/tiktok/components"
	"godsp/views/v2/tiktok/tableAds/components"
)

func getDataLayoutMaster(data *ListTableGmvMaxLayoutData) masters.LayoutMasterData {
	return masters.LayoutMasterData{
		AuthPermission: data.AuthPermission,
		UserInfo:       data.UserInfo,
	}
}
func getPathBreadcrumb() []layoutCops.PathBreadcrumb {
	return []layoutCops.PathBreadcrumb{
		{Title: "GMV Max", DataKey: "t-gmv-max-campaigns", Url: "/dsp/tiktok/gmv-max-campaign/list"},
		{Title: "List", DataKey: "t-gmv-max-campaigns", Url: "/dsp/tiktok/gmv-max-campaign/list#"},
	}
}

func getDataLayoutTable(data *ListTableGmvMaxLayoutData) components.LayoutTableData {
	return components.LayoutTableData{
		AuthPermission: data.AuthPermission,
		UserInfo:       data.UserInfo,
		Clients:        data.Clients,
	}
}

templ ListDatatableGmvMaxCamp(data *ListTableGmvMaxLayoutData) {
	{{
		dataLayoutMaster := getDataLayoutMaster(data)
		pathBreadcrumb := getPathBreadcrumb()
		// dataLayoutTable := getDataLayoutTable(data) 
	}}
	@layouts.Master(dataLayoutMaster, []templ.Component{cssHeader()}) {
		@layoutCops.ListBreadcrumdCpn("GMV Max", pathBreadcrumb)
		if data != nil && data.FlashMsg != "" {
			@cop.FlashMsgCop(data.FlashMsg)
		}

		<!-- Page Header -->
		<div class="row mb-4">
			<div class="col-12">
				<div class="d-flex justify-content-between align-items-center">
					<h1 class="h3 mb-0 fw-bold">GMV Max</h1>
					<a href="#" class="switch-shop-link">
						<i class="bi bi-arrow-left-right me-2"></i>
						Switch to another shop
					</a>
				</div>
			</div>
		</div>

		<!-- Recommendations Section -->
		<div class="row mb-4 fade-in">
			<div class="col-12">
				<div class="card">
					<div class="card-header">
						<div class="d-flex justify-content-between align-items-start">
							<div class="d-flex align-items-center">
								<h5 class="card-title mb-0 me-2">Quick actions and recommendations to drive more
									sales</h5>
								<span class="info-badge">i</span>
							</div>
							<button type="button" class="close-btn" onclick="closeRecommendations()">
								<i class="bi bi-x"></i>
							</button>
						</div>
					</div>
					<div class="card-body">
						<div class="recommendation-card p-3 mb-3">
							<div class="row align-items-start">
								<div class="col-lg-8 col-md-7">
									<h6 class="fw-semibold mb-2">Increase sales on 3 recommended products with
										GMV Max ads</h6>
									<p class="text-muted mb-3 small">
										Recommended products are items from your catalog that shoppers on TikTok
										may be interested in purchasing,
										based on TikTok's top-selling product categories, trending products and
										most searched keywords.
									</p>
									<div class="d-flex gap-2">
										<div class="product-thumbnail"></div>
										<div class="product-thumbnail"></div>
										<div class="product-thumbnail"></div>
									</div>
								</div>
								<div class="col-lg-4 col-md-5 text-md-end mt-3 mt-md-0">
									<button type="button" class="btn btn-primary btn-sm" onclick="createAds()">
										Create ads
									</button>
								</div>
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>
		
		<!-- Shop Section -->
		<div class="row fade-in">
			<div class="col-12">
				<div class="card">
					<div class="card-body">
						@components.DateRangeFilterTable()
						<div class="d-flex justify-content-between align-items-center">
							<div class="d-flex align-items-center">
								<div class="shop-logo me-3">M</div>
								<div>
									<h6 class="mb-1 fw-semibold">Maybi</h6>
									<small class="text-muted">Shop code: VNLCRYWLDC</small>
								</div>
							</div>
							<button type="button" class="btn btn-success" onclick="createGMVAds()">
								<i class="bi bi-plus-lg me-1"></i>
								Create GMV Max ads
							</button>
						</div>
					</div>
				</div>
			</div>
		</div>
	}
}

templ cssHeader() {
	<link href={ templates.AssetURL("/static/css/tiktok.css") } rel="stylesheet" type="text/css"/>
	<link href={ templates.AssetURL("/static/css/pages/tiktok/gmv-max.css") } rel="stylesheet" type="text/css"/>
}
