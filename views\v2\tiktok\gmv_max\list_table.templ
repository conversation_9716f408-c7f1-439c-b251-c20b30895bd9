package gmv_max

import (
"github.com/dev-networldasia/dspgos/gos/templates"
"godsp/views/v2/layouts"
layoutCops "godsp/views/v2/layouts/components"
"godsp/views/v2/layouts/masters"
cop "godsp/views/v2/tiktok/components"
cpn "godsp/views/v2/tiktok/gmv_max/components"
"godsp/views/v2/tiktok/tableAds/components"
)

func getDataLayoutMaster(data *ListTableGmvMaxLayoutData) masters.LayoutMasterData {
return masters.LayoutMasterData{
AuthPermission: data.AuthPermission,
UserInfo: data.UserInfo,
}
}
func getPathBreadcrumb() []layoutCops.PathBreadcrumb {
return []layoutCops.PathBreadcrumb{
{Title: "GMV Max", DataKey: "t-gmv-max-campaigns", Url: "/dsp/tiktok/gmv-max-campaign/list"},
{Title: "List", DataKey: "t-gmv-max-campaigns", Url: "/dsp/tiktok/gmv-max-campaign/list#"},
}
}

func getDataLayoutTable(data *ListTableGmvMaxLayoutData) components.LayoutTableData {
return components.LayoutTableData{
AuthPermission: data.AuthPermission,
UserInfo: data.UserInfo,
Clients: data.Clients,
}
}

templ ListDatatableGmvMaxCamp(data *ListTableGmvMaxLayoutData) {
{{
dataLayoutMaster := getDataLayoutMaster(data)
pathBreadcrumb := getPathBreadcrumb()
// dataLayoutTable := getDataLayoutTable(data)
}}
@layouts.Master(dataLayoutMaster, []templ.Component{cssHeader()}, scriptGmvMaxListTable()) {
@layoutCops.ListBreadcrumdCpn("GMV Max", pathBreadcrumb)
if data != nil && data.FlashMsg != "" {
@cop.FlashMsgCop(data.FlashMsg)
}
<!-- Page Header -->
@cpn.GmvPageHeaderCpn()
<!-- Recommendations Section -->
@cpn.RecommendationsCpn()
<!-- Shop Section -->
@cpn.GmvShopCpn()
<!-- Overview Section with Chart -->
@cpn.GmvOverviewChartCpn()
<!-- Campaign List Section -->
@cpn.GmvCampaignListCpn()
}
}

templ cssHeader() {
<link href={ templates.AssetURL("/static/css/tiktok.css") } rel="stylesheet" type="text/css" />
<link href={ templates.AssetURL("/static/css/pages/tiktok/gmv-max.css") } rel="stylesheet" type="text/css" />
}

templ scriptGmvMaxListTable() {
<script src="https://cdn.jsdelivr.net/npm/apexcharts@latest"></script>
<script type="module" src={ templates.AssetURL("/static/js/pages/tiktok/gmv_max/list-table.js") }></script>

<script>
	document.addEventListener("DOMContentLoaded", function () {
		// Chart data
		const chartData = {
			cost: [10230801, 9500000, 11200000, 8900000, 12100000, 10800000, 9700000, 10230801],
			gross_revenue: [48509851, 45000000, 52000000, 41000000, 55000000, 49000000, 46000000, 48509851],
			orders: [218, 200, 240, 180, 260, 220, 190, 218],
			cost_per_order: [46930, 47500, 46667, 49444, 46538, 49091, 51053, 46930],
			roi: [4.74, 4.73, 4.64, 4.61, 4.55, 4.54, 4.74, 4.74]
		};

		const dates = ['Jun 25', 'Jun 26', 'Jun 27', 'Jun 28', 'Jun 29', 'Jun 30', 'Jul 01', 'Jul 02'];

		let chart;
		let activeMetrics = ['cost', 'orders']; // Default active metrics

		// Initialize chart
		function initChart() {
			const series = activeMetrics.map(metric => ({
				name: getMetricDisplayName(metric),
				data: chartData[metric],
				type: 'line'
			}));

			const options = {
				series: series,
				chart: {
					height: 350,
					type: 'line',
					toolbar: {
						show: true
					},
					zoom: {
						enabled: false
					}
				},
				dataLabels: {
					enabled: true,
					formatter: function (val, opts) {
						return formatValue(val, activeMetrics[opts.seriesIndex]);
					}
				},
				stroke: {
					curve: 'smooth',
					width: 3
				},
				colors: ['#405189', '#0ab39c', '#f7b84b', '#f06548', '#6c757d'],
				xaxis: {
					categories: dates,
					labels: {
						style: {
							colors: '#8c9097',
							fontSize: '13px'
						}
					}
				},
				yaxis: activeMetrics.map((metric, index) => ({
					seriesName: getMetricDisplayName(metric),
					opposite: index > 0,
					axisTicks: {
						show: true,
					},
					axisBorder: {
						show: true,
						color: series[index]?.color || '#405189'
					},
					labels: {
						style: {
							colors: series[index]?.color || '#405189',
						},
						formatter: function (val) {
							return formatValue(val, metric);
						}
					},
					title: {
						text: getMetricDisplayName(metric),
						style: {
							color: series[index]?.color || '#405189',
						}
					}
				})),
				grid: {
					borderColor: '#f1f1f1',
					strokeDashArray: 3
				},
				legend: {
					position: 'top',
					horizontalAlign: 'right',
					floating: true,
					offsetY: -25,
					offsetX: -5
				},
				tooltip: {
					shared: true,
					intersect: false,
					y: {
						formatter: function (val, opts) {
							const metric = activeMetrics[opts.seriesIndex];
							return formatValue(val, metric);
						}
					}
				}
			};

			if (chart) {
				chart.destroy();
			}

			chart = new ApexCharts(document.querySelector("#gmv_max_line_chart"), options);
			chart.render();
		}

		// Helper functions
		function getMetricDisplayName(metric) {
			const names = {
				cost: 'Cost',
				gross_revenue: 'Gross Revenue',
				orders: 'Orders',
				cost_per_order: 'Cost per Order',
				roi: 'ROI'
			};
			return names[metric] || metric;
		}

		function formatValue(val, metric) {
			if (metric === 'cost' || metric === 'gross_revenue' || metric === 'cost_per_order') {
				return new Intl.NumberFormat('vi-VN').format(val) + ' VND';
			} else if (metric === 'roi') {
				return val.toFixed(2);
			} else {
				return new Intl.NumberFormat('vi-VN').format(val);
			}
		}

		// Handle metric checkbox changes
		document.querySelectorAll('.metric-checkbox').forEach(checkbox => {
			checkbox.addEventListener('change', function () {
				const metricCard = this.closest('.metric-card');
				const metric = metricCard.getAttribute('data-metric');

				if (this.checked) {
					// If we already have 2 active metrics, remove the oldest one
					if (activeMetrics.length >= 2) {
						const oldestMetric = activeMetrics.shift(); // Remove first (oldest) metric
						// Uncheck the oldest metric's checkbox
						const oldestCheckbox = document.querySelector(`[data-metric="${oldestMetric}"] .metric-checkbox`);
						if (oldestCheckbox) {
							oldestCheckbox.checked = false;
						}
					}
					// Add new metric to active list
					activeMetrics.push(metric);
				} else {
					// Remove metric from active list
					activeMetrics = activeMetrics.filter(m => m !== metric);
				}

				// Update chart
				initChart();
			});
		});

		// Initialize chart on page load
		initChart();

		// Tab switching functionality
		const campaignTabs = document.querySelectorAll('#campaignTabs button[data-bs-toggle="tab"]');

		campaignTabs.forEach(tab => {
			tab.addEventListener('shown.bs.tab', function (event) {
				const targetTab = event.target.getAttribute('data-bs-target');
				const tabType = targetTab === '#product-gmv' ? 'product' : 'live';

				console.log(`Switching to ${tabType} GMV Max tab`);

				// Load table based on tab type
				loadCampaignTable(tabType);
			});
		});

		// Function to load campaign table
		function loadCampaignTable(type) {
			const containerId = type === 'product' ? 'product-gmv-table-container' : 'live-gmv-table-container';
			const container = document.getElementById(containerId);

			if (!container) return;

			// Show loading state
			container.innerHTML = `
				<div class="text-center py-4">
					<div class="spinner-border text-primary" role="status">
						<span class="visually-hidden">Loading...</span>
					</div>
					<p class="mt-2 text-muted">Loading ${type === 'product' ? 'Product' : 'LIVE'} GMV Max campaigns...</p>
				</div>
			`;

			// Simulate loading delay (replace with actual API call)
			setTimeout(() => {
				// This is where you would make your API call to load the table
				// For now, just show a placeholder message
				container.innerHTML = `
					<div class="text-center py-5">
						<i class="ri-table-line text-muted" style="font-size: 3rem;"></i>
						<h6 class="mt-3 text-muted">${type === 'product' ? 'Product' : 'LIVE'} GMV Max Table</h6>
						<p class="text-muted">Table content will be loaded here</p>
						<small class="text-muted">You can add your table implementation here</small>
					</div>
				`;

				// Trigger custom event for table loaded
				const event = new CustomEvent('gmvTableLoaded', {
					detail: {
						type: type,
						containerId: containerId
					}
				});
				document.dispatchEvent(event);
			}, 1000);
		}

		// Load initial table (Product GMV Max)
		loadCampaignTable('product');

		// Search functionality
		document.getElementById('productSearchInput').addEventListener('input', function (e) {
			console.log('Product search:', e.target.value);
			// Add your search logic here
		});

		document.getElementById('liveSearchInput').addEventListener('input', function (e) {
			console.log('Live search:', e.target.value);
			// Add your search logic here
		});

		// Status filter functionality
		document.getElementById('productStatusFilter').addEventListener('change', function (e) {
			console.log('Product status filter:', e.target.value);
			// Status values: STATUS_DELIVERY_OK, STATUS_DISABLE, STATUS_DELETE
			// Add your filter logic here
		});

		document.getElementById('liveStatusFilter').addEventListener('change', function (e) {
			console.log('Live status filter:', e.target.value);
			// Status values: STATUS_DELIVERY_OK, STATUS_DISABLE, STATUS_DELETE
			// Add your filter logic here
		});
	});
</script>
}