package gmv_max

import (
	"github.com/dev-networldasia/dspgos/gos/templates"
	"godsp/views/v2/layouts"
	layoutCops "godsp/views/v2/layouts/components"
	"godsp/views/v2/layouts/masters"
	cop "godsp/views/v2/tiktok/components"
	"godsp/views/v2/tiktok/tableAds/components"
)

func getDataLayoutMaster(data *ListTableGmvMaxLayoutData) masters.LayoutMasterData {
	return masters.LayoutMasterData{
		AuthPermission: data.AuthPermission,
		UserInfo:       data.UserInfo,
	}
}
func getPathBreadcrumb() []layoutCops.PathBreadcrumb {
	return []layoutCops.PathBreadcrumb{
		{Title: "GMV Max", DataKey: "t-gmv-max-campaigns", Url: "/dsp/tiktok/gmv-max-campaign/list"},
		{Title: "List", DataKey: "t-gmv-max-campaigns", Url: "/dsp/tiktok/gmv-max-campaign/list#"},
	}
}

func getDataLayoutTable(data *ListTableGmvMaxLayoutData) components.LayoutTableData {
	return components.LayoutTableData{
		AuthPermission: data.AuthPermission,
		UserInfo:       data.UserInfo,
		Clients:        data.Clients,
	}
}

templ ListDatatableGmvMaxCamp(data *ListTableGmvMaxLayoutData) {
	{{
		dataLayoutMaster := getDataLayoutMaster(data)
		pathBreadcrumb := getPathBreadcrumb()
		// dataLayoutTable := getDataLayoutTable(data)
	}}
	@layouts.Master(dataLayoutMaster, []templ.Component{cssHeader()}, scriptGmvMaxListTable()) {
		@layoutCops.ListBreadcrumdCpn("GMV Max", pathBreadcrumb)
		if data != nil && data.FlashMsg != "" {
			@cop.FlashMsgCop(data.FlashMsg)
		}
		<!-- Page Header -->
		<div class="row mb-4">
			<div class="col-12">
				<div class="d-flex justify-content-between align-items-center">
					<h1 class="h3 mb-0 fw-bold">GMV Max</h1>
					<a href="#" class="switch-shop-link">
						<i class="bi bi-arrow-left-right me-2"></i>
						Switch to another shop
					</a>
				</div>
			</div>
		</div>
		<!-- Recommendations Section -->
		<div class="row mb-4 fade-in">
			<div class="col-12">
				<div class="card">
					<div class="card-header">
						<div class="d-flex justify-content-between align-items-start">
							<div class="d-flex align-items-center">
								<h5 class="card-title mb-0 me-2">
									Quick actions and recommendations to drive more
									sales
								</h5>
								<span class="info-badge">i</span>
							</div>
							<button type="button" class="close-btn" onclick="closeRecommendations()">
								<i class="bi bi-x"></i>
							</button>
						</div>
					</div>
					<div class="card-body">
						<div class="recommendation-card p-3 mb-3">
							<div class="row align-items-start">
								<div class="col-lg-8 col-md-7">
									<h6 class="fw-semibold mb-2">
										Increase sales on 3 recommended products with
										GMV Max ads
									</h6>
									<p class="text-muted mb-3 small">
										Recommended products are items from your catalog that shoppers on TikTok
										may be interested in purchasing,
										based on TikTok's top-selling product categories, trending products and
										most searched keywords.
									</p>
									<div class="d-flex gap-2">
										<div class="product-thumbnail"></div>
										<div class="product-thumbnail"></div>
										<div class="product-thumbnail"></div>
									</div>
								</div>
								<div class="col-lg-4 col-md-5 text-md-end mt-3 mt-md-0">
									<button type="button" class="btn btn-primary btn-sm" onclick="createAds()">
										Create ads
									</button>
								</div>
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>
		<!-- Shop Section -->
		<div class="row fade-in">
			<div class="col-12">
				<div class="card">
					<div class="card-body">
						<div class="d-flex justify-content-between align-items-center">
							<div class="d-flex align-items-center">
								<div class="shop-logo me-3">M</div>
								<div>
									<h6 class="mb-1 fw-semibold">Maybi</h6>
									<small class="text-muted">Shop code: VNLCRYWLDC</small>
								</div>
							</div>
							<button type="button" class="btn btn-success" onclick="createGMVAds()">
								<i class="bi bi-plus-lg me-1"></i>
								Create GMV Max ads
							</button>
						</div>
					</div>
				</div>
			</div>
		</div>
		<!-- Overview Section with Chart -->
		<div class="row mt-4 fade-in">
			<div class="col-12">
				<div class="card">
					<div class="card-header">
						<div class="d-flex justify-content-between align-items-center">
							<h5 class="card-title mb-0">Overview</h5>
							<div class="d-flex align-items-center gap-2">
								<small class="text-muted">(UTC+07:00) Indochina time</small>
								@components.DateRangeFilterTable()
							</div>
						</div>
						<p class="text-muted mb-0 mt-2">
							Reporting includes GMV Max ads created on TikTok Ads Manager and TikTok
							Seller Center.
						</p>
					</div>
					<div class="card-body">
						<!-- Metrics Cards -->
						<div class="row g-3 mb-4">
							<div class="col-lg-2 col-md-4 col-sm-6">
								<div class="metric-card p-3 border rounded position-relative" data-metric="cost">
									<div class="form-check position-absolute top-0 end-0 mt-2 me-2">
										<input class="form-check-input metric-checkbox" type="checkbox" id="costCheck" checked/>
									</div>
									<div class="d-flex align-items-center mb-2">
										<h6 class="mb-0 me-2">Cost</h6>
										<i class="ri-information-line text-muted"></i>
									</div>
									<h4 class="mb-1 fw-bold">10,230,801 VND</h4>
									<small class="text-muted">vs last 7 days</small>
									<span class="text-danger ms-1">-31.35%</span>
								</div>
							</div>
							<div class="col-lg-2 col-md-4 col-sm-6">
								<div class="metric-card p-3 border rounded position-relative" data-metric="orders">
									<div class="form-check position-absolute top-0 end-0 mt-2 me-2">
										<input class="form-check-input metric-checkbox" type="checkbox" id="ordersCheck" checked/>
									</div>
									<div class="d-flex align-items-center mb-2">
										<h6 class="mb-0 me-2">Orders (Current shop)</h6>
										<i class="ri-information-line text-muted"></i>
									</div>
									<h4 class="mb-1 fw-bold">218</h4>
									<small class="text-muted">vs last 7 days</small>
									<span class="text-danger ms-1">-39.84%</span>
								</div>
							</div>
							<div class="col-lg-2 col-md-4 col-sm-6">
								<div class="metric-card p-3 border rounded position-relative" data-metric="cost_per_order">
									<div class="form-check position-absolute top-0 end-0 mt-2 me-2">
										<input class="form-check-input metric-checkbox" type="checkbox" id="costPerOrderCheck"/>
									</div>
									<div class="d-flex align-items-center mb-2">
										<h6 class="mb-0 me-2">Cost per order</h6>
										<i class="ri-information-line text-muted"></i>
									</div>
									<h4 class="mb-1 fw-bold">46,930 VND</h4>
									<small class="text-muted">vs last 7 days</small>
									<span class="text-success ms-1">+14.31%</span>
								</div>
							</div>
							<div class="col-lg-2 col-md-4 col-sm-6">
								<div class="metric-card p-3 border rounded position-relative" data-metric="gross_revenue">
									<div class="form-check position-absolute top-0 end-0 mt-2 me-2">
										<input
											class="form-check-input metric-checkbox"
											type="checkbox"
											id="grossRevenueCheck"
										/>
									</div>
									<div class="d-flex align-items-center mb-2">
										<h6 class="mb-0 me-2">Gross revenue (Current shop)</h6>
										<i class="ri-information-line text-muted"></i>
									</div>
									<h4 class="mb-1 fw-bold">48,509,851 VND</h4>
									<small class="text-muted">vs last 7 days</small>
									<span class="text-danger ms-1">-39.96%</span>
								</div>
							</div>
							<div class="col-lg-2 col-md-4 col-sm-6">
								<div class="metric-card p-3 border rounded position-relative" data-metric="roi">
									<div class="form-check position-absolute top-0 end-0 mt-2 me-2">
										<input class="form-check-input metric-checkbox" type="checkbox" id="roiCheck"/>
									</div>
									<div class="d-flex align-items-center mb-2">
										<h6 class="mb-0 me-2">ROI (Current shop)</h6>
										<i class="ri-information-line text-muted"></i>
									</div>
									<h4 class="mb-1 fw-bold">4.74</h4>
									<small class="text-muted">vs last 7 days</small>
									<span class="text-danger ms-1">-12.55%</span>
								</div>
							</div>
						</div>
						<!-- Chart Container -->
						<div class="chart-container">
							<div id="gmv_max_line_chart" class="apex-charts" dir="ltr"></div>
						</div>
					</div>
				</div>
			</div>
		</div>
		<!-- Campaign List Section -->
		<div class="row mt-4 fade-in">
			<div class="col-12">
				<div class="card">
					<div class="card-header">
						<div class="d-flex justify-content-between align-items-center">
							<h5 class="card-title mb-0">Campaign list</h5>
						</div>
						<p class="text-muted mb-0 mt-2">Reporting includes GMV Max ads created on TikTok Ads Manager and TikTok Seller Center.</p>
					</div>
					<div class="card-body">
						<!-- Tab Navigation -->
						<ul class="nav nav-tabs nav-tabs-custom mb-3" id="campaignTabs" role="tablist" style="width: max-content;">
							<li class="nav-item" role="presentation">
								<button class="nav-link active" id="product-gmv-tab" data-bs-toggle="tab" data-bs-target="#product-gmv" type="button" role="tab" aria-controls="product-gmv" aria-selected="true">
									Product GMV Max
								</button>
							</li>
							<li class="nav-item" role="presentation">
								<button class="nav-link" id="live-gmv-tab" data-bs-toggle="tab" data-bs-target="#live-gmv" type="button" role="tab" aria-controls="live-gmv" aria-selected="false">
									LIVE GMV Max
								</button>
							</li>
						</ul>
						<!-- Tab Content -->
						<div class="tab-content" id="campaignTabsContent">
							<!-- Product GMV Max Tab -->
							<div class="tab-pane fade show active" id="product-gmv" role="tabpanel" aria-labelledby="product-gmv-tab">
								<div class="d-flex justify-content-between align-items-center mb-3">
									<div class="d-flex align-items-center gap-3">
										<div class="input-group" style="width: 300px;">
											<input type="text" class="form-control" placeholder="Search campaign or TikTok account ID" id="productSearchInput"/>
											<button class="btn btn-outline-secondary" type="button">
												<i class="ri-search-line"></i>
											</button>
										</div>
										<select class="form-select" style="width: 150px;" id="productStatusFilter">
											<option value="">Status</option>
											<option value="active">Active</option>
											<option value="inactive">Inactive</option>
											<option value="not_delivering">Not delivering</option>
										</select>
									</div>
									<div class="d-flex gap-2">
										<button class="btn btn-outline-secondary">
											<i class="ri-grid-line"></i>
										</button>
										<button class="btn btn-outline-secondary">
											<i class="ri-external-link-line"></i>
										</button>
										<button class="btn btn-outline-secondary">
											<i class="ri-file-copy-line"></i>
										</button>
									</div>
								</div>
								<!-- Product GMV Max Table Container -->
								<div id="product-gmv-table-container">
									<!-- Table will be loaded here -->
									<div class="text-center py-4">
										<div class="spinner-border text-primary" role="status">
											<span class="visually-hidden">Loading...</span>
										</div>
										<p class="mt-2 text-muted">Loading Product GMV Max campaigns...</p>
									</div>
								</div>
							</div>
							<!-- LIVE GMV Max Tab -->
							<div class="tab-pane fade" id="live-gmv" role="tabpanel" aria-labelledby="live-gmv-tab">
								<div class="d-flex justify-content-between align-items-center mb-3">
									<div class="d-flex align-items-center gap-3">
										<div class="input-group" style="width: 300px;">
											<input type="text" class="form-control" placeholder="Search campaign or TikTok account ID" id="liveSearchInput"/>
											<button class="btn btn-outline-secondary" type="button">
												<i class="ri-search-line"></i>
											</button>
										</div>
										<select class="form-select" style="width: 150px;" id="liveStatusFilter">
											<option value="">Status</option>
											<option value="active">Active</option>
											<option value="inactive">Inactive</option>
											<option value="not_delivering">Not delivering</option>
										</select>
									</div>
									<div class="d-flex gap-2">
										<button class="btn btn-outline-secondary">
											<i class="ri-grid-line"></i>
										</button>
										<button class="btn btn-outline-secondary">
											<i class="ri-external-link-line"></i>
										</button>
										<button class="btn btn-outline-secondary">
											<i class="ri-file-copy-line"></i>
										</button>
									</div>
								</div>
								<!-- LIVE GMV Max Table Container -->
								<div id="live-gmv-table-container">
									<!-- Table will be loaded here -->
									<div class="text-center py-4">
										<div class="spinner-border text-primary" role="status">
											<span class="visually-hidden">Loading...</span>
										</div>
										<p class="mt-2 text-muted">Loading LIVE GMV Max campaigns...</p>
									</div>
								</div>
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>
	}
}

templ cssHeader() {
	<link href={ templates.AssetURL("/static/css/tiktok.css") } rel="stylesheet" type="text/css"/>
	<link href={ templates.AssetURL("/static/css/pages/tiktok/gmv-max.css") } rel="stylesheet" type="text/css"/>
}

templ scriptGmvMaxListTable() {
	<script src="https://cdn.jsdelivr.net/npm/apexcharts@latest"></script>
	<script type="module" src={ templates.AssetURL("/static/js/pages/tiktok/gmv_max/list-table.js") }></script>
	<script>
	document.addEventListener("DOMContentLoaded", function () {
		// Chart data
		const chartData = {
			cost: [10230801, 9500000, 11200000, 8900000, 12100000, 10800000, 9700000, 10230801],
			gross_revenue: [48509851, 45000000, 52000000, 41000000, 55000000, 49000000, 46000000, 48509851],
			orders: [218, 200, 240, 180, 260, 220, 190, 218],
			cost_per_order: [46930, 47500, 46667, 49444, 46538, 49091, 51053, 46930],
			roi: [4.74, 4.73, 4.64, 4.61, 4.55, 4.54, 4.74, 4.74]
		};

		const dates = ['Jun 25', 'Jun 26', 'Jun 27', 'Jun 28', 'Jun 29', 'Jun 30', 'Jul 01', 'Jul 02'];

		let chart;
		let activeMetrics = ['cost', 'orders']; // Default active metrics

		// Initialize chart
		function initChart() {
			const series = activeMetrics.map(metric => ({
				name: getMetricDisplayName(metric),
				data: chartData[metric],
				type: 'line'
			}));

			const options = {
				series: series,
				chart: {
					height: 350,
					type: 'line',
					toolbar: {
						show: true
					},
					zoom: {
						enabled: false
					}
				},
				dataLabels: {
					enabled: true,
					formatter: function (val, opts) {
						return formatValue(val, activeMetrics[opts.seriesIndex]);
					}
				},
				stroke: {
					curve: 'smooth',
					width: 3
				},
				colors: ['#405189', '#0ab39c', '#f7b84b', '#f06548', '#6c757d'],
				xaxis: {
					categories: dates,
					labels: {
						style: {
							colors: '#8c9097',
							fontSize: '13px'
						}
					}
				},
				yaxis: activeMetrics.map((metric, index) => ({
					seriesName: getMetricDisplayName(metric),
					opposite: index > 0,
					axisTicks: {
						show: true,
					},
					axisBorder: {
						show: true,
						color: series[index]?.color || '#405189'
					},
					labels: {
						style: {
							colors: series[index]?.color || '#405189',
						},
						formatter: function (val) {
							return formatValue(val, metric);
						}
					},
					title: {
						text: getMetricDisplayName(metric),
						style: {
							color: series[index]?.color || '#405189',
						}
					}
				})),
				grid: {
					borderColor: '#f1f1f1',
					strokeDashArray: 3
				},
				legend: {
					position: 'top',
					horizontalAlign: 'right',
					floating: true,
					offsetY: -25,
					offsetX: -5
				},
				tooltip: {
					shared: true,
					intersect: false,
					y: {
						formatter: function (val, opts) {
							const metric = activeMetrics[opts.seriesIndex];
							return formatValue(val, metric);
						}
					}
				}
			};

			if (chart) {
				chart.destroy();
			}

			chart = new ApexCharts(document.querySelector("#gmv_max_line_chart"), options);
			chart.render();
		}

		// Helper functions
		function getMetricDisplayName(metric) {
			const names = {
				cost: 'Cost',
				gross_revenue: 'Gross Revenue',
				orders: 'Orders',
				cost_per_order: 'Cost per Order',
				roi: 'ROI'
			};
			return names[metric] || metric;
		}

		function formatValue(val, metric) {
			if (metric === 'cost' || metric === 'gross_revenue' || metric === 'cost_per_order') {
				return new Intl.NumberFormat('vi-VN').format(val) + ' VND';
			} else if (metric === 'roi') {
				return val.toFixed(2);
			} else {
				return new Intl.NumberFormat('vi-VN').format(val);
			}
		}

		// Handle metric checkbox changes
		document.querySelectorAll('.metric-checkbox').forEach(checkbox => {
			checkbox.addEventListener('change', function () {
				const metricCard = this.closest('.metric-card');
				const metric = metricCard.getAttribute('data-metric');

				if (this.checked) {
					// If we already have 2 active metrics, remove the oldest one
					if (activeMetrics.length >= 2) {
						const oldestMetric = activeMetrics.shift(); // Remove first (oldest) metric
						// Uncheck the oldest metric's checkbox
						const oldestCheckbox = document.querySelector(`[data-metric="${oldestMetric}"] .metric-checkbox`);
						if (oldestCheckbox) {
							oldestCheckbox.checked = false;
						}
					}
					// Add new metric to active list
					activeMetrics.push(metric);
				} else {
					// Remove metric from active list
					activeMetrics = activeMetrics.filter(m => m !== metric);
				}

				// Update chart
				initChart();
			});
		});

		// Initialize chart on page load
		initChart();

		// Tab switching functionality
		const campaignTabs = document.querySelectorAll('#campaignTabs button[data-bs-toggle="tab"]');

		campaignTabs.forEach(tab => {
			tab.addEventListener('shown.bs.tab', function (event) {
				const targetTab = event.target.getAttribute('data-bs-target');
				const tabType = targetTab === '#product-gmv' ? 'product' : 'live';

				console.log(`Switching to ${tabType} GMV Max tab`);

				// Load table based on tab type
				loadCampaignTable(tabType);
			});
		});

		// Function to load campaign table
		function loadCampaignTable(type) {
			const containerId = type === 'product' ? 'product-gmv-table-container' : 'live-gmv-table-container';
			const container = document.getElementById(containerId);

			if (!container) return;

			// Show loading state
			container.innerHTML = `
				<div class="text-center py-4">
					<div class="spinner-border text-primary" role="status">
						<span class="visually-hidden">Loading...</span>
					</div>
					<p class="mt-2 text-muted">Loading ${type === 'product' ? 'Product' : 'LIVE'} GMV Max campaigns...</p>
				</div>
			`;

			// Simulate loading delay (replace with actual API call)
			setTimeout(() => {
				// This is where you would make your API call to load the table
				// For now, just show a placeholder message
				container.innerHTML = `
					<div class="text-center py-5">
						<i class="ri-table-line text-muted" style="font-size: 3rem;"></i>
						<h6 class="mt-3 text-muted">${type === 'product' ? 'Product' : 'LIVE'} GMV Max Table</h6>
						<p class="text-muted">Table content will be loaded here</p>
						<small class="text-muted">You can add your table implementation here</small>
					</div>
				`;

				// Trigger custom event for table loaded
				const event = new CustomEvent('gmvTableLoaded', {
					detail: {
						type: type,
						containerId: containerId
					}
				});
				document.dispatchEvent(event);
			}, 1000);
		}

		// Load initial table (Product GMV Max)
		loadCampaignTable('product');

		// Search functionality
		document.getElementById('productSearchInput').addEventListener('input', function(e) {
			console.log('Product search:', e.target.value);
			// Add your search logic here
		});

		document.getElementById('liveSearchInput').addEventListener('input', function(e) {
			console.log('Live search:', e.target.value);
			// Add your search logic here
		});

		// Status filter functionality
		document.getElementById('productStatusFilter').addEventListener('change', function(e) {
			console.log('Product status filter:', e.target.value);
			// Add your filter logic here
		});

		document.getElementById('liveStatusFilter').addEventListener('change', function(e) {
			console.log('Live status filter:', e.target.value);
			// Add your filter logic here
		});
	});
</script>
}
