package requests

import (
	"godsp/modules/admin/client/common/errs"
	"godsp/pkg/gos/utils"

	"github.com/go-playground/validator/v10"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

type GetListPixelReq struct {
	ClientIDStr string             `json:"client_id,omitempty" validate:"required"`
	ClientID    primitive.ObjectID `json:"-"`
	UserId      primitive.ObjectID `json:"-"`
}

func (req *GetListPixelReq) Validate() []string {
	validate := validator.New()
	var validationErrors []string
	err := validate.Struct(req)

	if err != nil {
		for _, err := range err.(validator.ValidationErrors) {
			switch err.Field() {
			case "ClientIDStr":
				validationErrors = append(validationErrors, errs.ErrClientIdEmpty.Error())
			}
		}
	}

	if !utils.ValidateObjectID(req.ClientIDStr) {
		validationErrors = append(validationErrors, errs.ErrIDClientValidate.Error())
		return validationErrors
	}
	req.ClientID, _ = primitive.ObjectIDFromHex(req.ClientIDStr)

	return nil
}
