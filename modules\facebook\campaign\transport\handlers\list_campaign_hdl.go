package handlers

import (
	"godsp/conf"
	"godsp/pkg/gos/utils"
	"godsp/pkg/sctx/core"

	"github.com/gofiber/fiber/v2"
)

func (h *campaignHdl) ListCampaignHdl() fiber.Handler {
	return func(c *fiber.Ctx) error {

		adAccounts, _ := h.usc.ListAdAccountCampaignUsc(c.Context())
		clients, _ := h.usc.ListClientCampaignUsc(c.Context())

		userInfo, err := utils.GetInfoUserBasic(c.Context())

		if err != nil && userInfo.RoleName != conf.SysConf.RoleAdmin {
			return c.Redirect("/page-forbidden")
		}

		return c.Render("facebook/camp-adset-ad/index", fiber.Map{
			"authPermission": core.GetPermission(c.Context()).GetPermissions(),
			"userInfo":       userInfo,
			"adAccounts":     adAccounts,
			"clients":        clients,
		})
	}
}
