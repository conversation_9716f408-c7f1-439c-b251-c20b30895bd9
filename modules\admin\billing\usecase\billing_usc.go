package usecase

import (
	"context"
	"encoding/json"
	"fmt"
	"godsp/modules/admin/billing/common/consts"
	client "godsp/modules/admin/billing/common/utils"
	"godsp/modules/admin/billing/entity"
	"godsp/modules/admin/billing/transport/response"
	clientE "godsp/modules/admin/client/entity"
	"godsp/modules/admin/common/admfunc"
	userE "godsp/modules/admin/user/entity"
	adAccE "godsp/modules/facebook/ad_account/entity"
	"godsp/modules/facebook/iface_repo"
	"godsp/pkg/gos/utils"

	"github.com/dev-networldasia/dspgos/sctx"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo/options"
)

type BillingRepo interface {
	MaxOrderingRepo(ctx context.Context, filter interface{}) (int64, error)
	FindOneBillingRepo(ctx context.Context, filter interface{}, opts ...*options.FindOneOptions) (*entity.BillingEntity, error)
}

type ClientRepo interface {
	FindOneClientRepo(ctx context.Context, filter interface{}, opts ...*options.FindOneOptions) (*clientE.ClientEntity, error)
	FindClientRepo(ctx context.Context, filter interface{}, opts ...*options.FindOptions) ([]*clientE.ClientEntity, error)
}

type UserRepo interface {
	FindUserRepo(ctx context.Context, filter interface{}, opts ...*options.FindOptions) (*[]userE.UserEntity, error)
}

type billingUsc struct {
	repo       BillingRepo
	adAccRepo  iface_repo.AdAccountRepo
	logger     sctx.Logger
	clientRepo ClientRepo
	userRepo   UserRepo
}

func NewBillingUsc(
	repo BillingRepo,
	logger sctx.Logger,
	adAccRepo iface_repo.AdAccountRepo,
	clientRepo ClientRepo,
	userRepo UserRepo,
) *billingUsc {
	return &billingUsc{
		repo:       repo,
		adAccRepo:  adAccRepo,
		logger:     logger,
		clientRepo: clientRepo,
		userRepo:   userRepo,
	}
}

/***
 * max ordering
 */
func (usc *billingUsc) MaxOrderingBillingUsc(ctx context.Context) (int64, error) {
	return usc.repo.MaxOrderingRepo(ctx, nil)
}

func (usc *billingUsc) FindOneBillingUsc(ctx context.Context, id string) (*response.BillingDataTable, error) {
	billing, err := usc.getBilling(ctx, id)
	if err != nil {
		usc.logger.Error(err)

		return nil, err
	}

	auth, err := utils.GetInfoUserAuth(ctx)
	if err != nil {
		usc.logger.Error(err)

		return nil, err
	}

	clientId, err := primitive.ObjectIDFromHex(billing.MClientID)
	if err != nil {
		usc.logger.Error(err)

		return nil, err
	}

	userIDSet := make(map[string]struct{})
	if billing.ApprovedBy != "" {
		userIDSet[billing.ApprovedBy] = struct{}{}
	}

	if billing.MCreatedBy != "" {
		userIDSet[billing.MCreatedBy] = struct{}{}
	}

	if billing.UpdatedBy != "" {
		userIDSet[billing.UpdatedBy] = struct{}{}
	}

	var userIDs []primitive.ObjectID
	for id := range userIDSet {
		objID, err := primitive.ObjectIDFromHex(id)
		if err == nil {
			userIDs = append(userIDs, objID)
		}
	}

	users := &[]userE.UserEntity{}
	if len(userIDs) != 0 {
		users, err = usc.userRepo.FindUserRepo(ctx, bson.M{"_id": bson.M{"$in": userIDs}})
		if err != nil {
			usc.logger.Error(err)

			return nil, err
		}
	}

	client, err := usc.clientRepo.FindOneClientRepo(ctx, bson.M{"_id": clientId})
	if err != nil {
		usc.logger.Error(err)

		return nil, err
	}

	roleGroup, err := admfunc.GetRoleGroup(ctx, &auth.RoleName)
	if err != nil {
		usc.logger.Error(err)

		return nil, err
	}

	billingTable := response.GetDataBillingResponse(*billing, users, client, roleGroup)

	return billingTable, nil
}

func (usc *billingUsc) getBilling(ctx context.Context, id string) (*response.BillingDataResponse, error) {
	jsonParams, er := json.Marshal(struct{}{})
	if er != nil {
		usc.logger.Error(er)

		return nil, er
	}

	path := fmt.Sprintf("%s/%s", consts.DV360_BILLING_API["EDIT"], id)
	billing, err := client.MakeRequest(ctx, client.REQUEST_GET, path, jsonParams)
	if err != nil {
		usc.logger.Error(err)

		return nil, err
	}

	var resp response.DataBillingResponse
	err = json.Unmarshal(billing, &resp)
	if err != nil {
		usc.logger.Error(err)

		return nil, err
	}

	return &resp.DataResponse.Data, nil
}

/**
 * list ad account
 */
func (usc *billingUsc) ListAdAccountOfClientUsc(ctx context.Context, clientId primitive.ObjectID) ([]*adAccE.AdAccountEntity, error) {

	// allowDiskUse := fbenums.ALLOW_DISK_USE_TRUE
	opts := &options.FindOptions{
		// AllowDiskUse: &allowDiskUse,
		Sort:       bson.D{{Key: "name", Value: -1}},
		Projection: bson.M{"id": 1, "name": 1, "account_id": 1},
	}
	filter := bson.M{
		"client_ids": clientId,
	}

	return usc.adAccRepo.FindAdAccountRepo(ctx, filter, opts)
}

func (usc *billingUsc) ListClientUsc(ctx context.Context) ([]*clientE.ClientEntity, error) {
	return usc.clientRepo.FindClientRepo(ctx, bson.M{})
}
