package api

import (
	"godsp/modules/email/transport/requests"

	"github.com/gofiber/fiber/v2"
)

type EmailApiUsc interface {
	SendEmail(to string, subject string, body string) error
	SendExampleEmail(toEmail string, recipientName string) error
	SendLoginNotificationEmail(userEmail string, userName string) error
}

type EmailApi struct {
	usc EmailApiUsc
}

func NewEmailApi(usc EmailApiUsc) *EmailApi {
	return &EmailApi{usc: usc}
}

func (h *EmailApi) SendEmailLoginExampleApi() fiber.Handler {
	return func(c *fiber.Ctx) error {
		var req requests.SendEmailExampleRequest
		if err := c.BodyParser(&req); err != nil {
			return c.Status(fiber.StatusBadRequest).JSON(fiber.Map{
				"error": "Invalid request payload: " + err.Error(),
			})
		}

		if req.To == "" {
			return c.Status(fiber.StatusBadRequest).JSON(fiber.Map{
				"error": "Missing required fields: to",
			})
		}

		if err := h.usc.SendLoginNotificationEmail(req.To, "User Test"); err != nil {
			return c.Status(fiber.StatusInternalServerError).JSON(fiber.Map{
				"error": "Failed to send email: " + err.Error(),
			})
		}

		return c.Status(fiber.StatusOK).JSON(fiber.Map{
			"message": "Email sent successfully",
		})
	}
}

func (h *EmailApi) SendEmailExampleApi() fiber.Handler {
	return func(c *fiber.Ctx) error {
		var req requests.SendEmailExampleRequest
		if err := c.BodyParser(&req); err != nil {
			return c.Status(fiber.StatusBadRequest).JSON(fiber.Map{
				"error": "Invalid request payload: " + err.Error(),
			})
		}

		if req.To == "" {
			return c.Status(fiber.StatusBadRequest).JSON(fiber.Map{
				"error": "Missing required fields: to",
			})
		}

		if err := h.usc.SendExampleEmail(req.To, "User Test"); err != nil {
			return c.Status(fiber.StatusInternalServerError).JSON(fiber.Map{
				"error": "Failed to initiate sending example email: " + err.Error(),
			})
		}

		return c.Status(fiber.StatusOK).JSON(fiber.Map{
			"message": "Example email sending initiated successfully",
		})
	}
}
