package admfunc

import (
	"context"
	"godsp/modules/admin/common/admconst"
	"godsp/pkg/gos/utils"
	"strings"
)

func GetRoleGroup(ctx context.Context, roleName *string) (string, error) {
	if roleName == nil {
		auth, err := utils.GetInfoUserAuth(ctx)
		if err != nil {
			return "", err
		}

		roleName = &auth.RoleName
	}

	if strings.Contains(*roleName, admconst.ROLE_GROUP_CLIENT) {
		return admconst.ROLE_GROUP_CLIENT, nil
	}

	if strings.Contains(*roleName, admconst.ROLE_GROUP_ACCOUNTANT) {
		return admconst.ROLE_GROUP_ACCOUNTANT, nil
	}

	return admconst.ROLE_GROUP_ADMIN, nil
}
