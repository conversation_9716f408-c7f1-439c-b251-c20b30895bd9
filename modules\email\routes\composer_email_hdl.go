package routes

import (
	userMongo "godsp/modules/admin/user/repository/mongo"
	"godsp/modules/email/common"
	"godsp/modules/email/service"
	"godsp/modules/email/transport/handler"
	"godsp/modules/email/usecase"
	"godsp/pkg/gos/auths"

	"github.com/dev-networldasia/dspgos/sctx"
	"github.com/dev-networldasia/dspgos/sctx/component/mongodb"
	"github.com/dev-networldasia/dspgos/sctx/component/watermillapp/redisstream"
	"github.com/dev-networldasia/dspgos/sctx/configs"

	"github.com/gofiber/fiber/v2"
)

// ComposerEmailApi defines the interface for email API handlers.
type ComposerEmailHdl interface {
	SendEmailResetPasswordHdl() fiber.Handler
}

func ComposerEmailService(serviceCtx sctx.ServiceContext) ComposerEmailHdl {
	logger := serviceCtx.MustGet(configs.KeyLoggerDaily).(sctx.AppLoggerDaily).GetLogger("email_hdl")
	mongoDB := serviceCtx.MustGet(configs.KeyCompMongoDB).(mongodb.MongoComponent).GetDatabase()
	redisStreamPub := serviceCtx.MustGet(configs.KeyRedisStreamPub).(redisstream.RedisStreamPublisher)
	userRepo := userMongo.NewUserRepo(mongoDB)
	config := common.LoadEmailConfig()
	srv := service.NewEmailService(config)
	hasher := new(auths.Hasher)
	emailUsc := usecase.NewEmailUsecase(srv, logger, userRepo, redisStreamPub, hasher)

	hdl := handler.NewEmailHdl(emailUsc)
	return hdl
}
