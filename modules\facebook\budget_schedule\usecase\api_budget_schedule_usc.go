package usecase

import (
	"context"
	"errors"
	"fmt"
	userEt "godsp/modules/admin/user/entity"
	userRp "godsp/modules/admin/user/repository/mongo"
	"godsp/modules/facebook/budget_schedule/common/errs"
	"godsp/modules/facebook/budget_schedule/entity"
	"godsp/modules/facebook/budget_schedule/mapping"
	"godsp/modules/facebook/budget_schedule/transport/requests"
	"godsp/modules/facebook/budget_schedule/transport/responses"
	campEt "godsp/modules/facebook/campaign/entity"
	v20 "godsp/pkg/fb-marketing/marketing/v20"
	"godsp/pkg/gos/utils"
	"godsp/pkg/sctx/core"

	"regexp"

	"github.com/dev-networldasia/dspgos/sctx"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo/options"
)

type ApiBudgetScheduleRepo interface {
	UpsertHighDemandPeriodRepo(ctx context.Context, filter bson.M, update bson.M) error
	FindHighDemandPeriodRepo(ctx context.Context, filter interface{}, opts ...*options.FindOptions) (*[]entity.HighDemandPeriodEntity, error)
	DeleteHighDemandPeriodRepo(ctx context.Context, filter interface{}, opts ...*options.DeleteOptions) error
	DeleteByListIdHighDemandPeriodRepo(ctx context.Context, filter interface{}, opts ...*options.DeleteOptions) error
	FindOneHighDemandPeriodRepo(ctx context.Context, filter interface{}, opts ...*options.FindOptions) (*entity.HighDemandPeriodEntity, error)
}

type ApiUserRepo interface {
	FindOneUserRepo(ctx context.Context, filter interface{}, opts ...*options.FindOneOptions) (*userEt.UserEntity, error)
	GetRoleOfUser(ctx context.Context, userId primitive.ObjectID, opts ...*options.AggregateOptions) (*userRp.RoleInfo, error)
}

type ApiCampRepo interface {
	FindOneCampaignRepo(ctx context.Context, filter interface{}, opts ...*options.FindOneOptions) (*campEt.CampaignEntity, error)
}

type apiBudgetScheduleUsc struct {
	fbService *v20.Service
	repo      ApiBudgetScheduleRepo
	userRepo  ApiUserRepo
	logger    sctx.Logger
	camRepo   ApiCampRepo
}

func NewApiBudgetScheduleUsc(fbService *v20.Service, repo ApiBudgetScheduleRepo, userRepo ApiUserRepo, campRepo ApiCampRepo, logger sctx.Logger) *apiBudgetScheduleUsc {
	return &apiBudgetScheduleUsc{
		fbService: fbService,
		repo:      repo,
		userRepo:  userRepo,
		logger:    logger,
		camRepo:   campRepo,
	}
}

/**
 * Get Role By UserId
 * @return string
 */
func (usc *apiBudgetScheduleUsc) getRoleByUserId(ctx context.Context, userId primitive.ObjectID) (*string, error) {
	role, err := usc.userRepo.GetRoleOfUser(ctx, userId)
	if err != nil {
		return nil, err
	}
	return &role.RoleName, nil
}

// func

/**
 * Reload Budget schedule by campaign id
 */
func (usc *apiBudgetScheduleUsc) ReloadByCampaignAdsetIdApiBudgetScheduleForAdminUsc(ctx context.Context, payload *requests.ReloadBudgetScheduleByCampaignAdsetIdReq, userId primitive.ObjectID) error {
	// var errStr []string

	name, err := usc.getRoleByUserId(ctx, userId)
	if *name != "ADMIN" || err != nil {
		return core.ErrBadRequest
	}
	var campAdsetId string
	var typeAction = "campaign"
	filter := bson.M{}
	if payload.AdsetId != "" {
		typeAction = "adset"
		campAdsetId = payload.AdsetId
		filter["adset_id"] = campAdsetId
	} else {
		campAdsetId = payload.CampaignId
		filter["campaign_id"] = campAdsetId
	}

	budgetPre, err := usc.repo.FindOneHighDemandPeriodRepo(ctx, filter)
	fmt.Printf("\n -----------  FindOneHighDemandPeriodRepo ----------- %+v \n", filter)
	fmt.Printf("\n -----------  budgetPre ----------- %+v \n", budgetPre)

	if err != nil {
		return err
	}

	listUserIDs := []primitive.ObjectID{userId}
	if budgetPre != nil {
		listUserIDs = budgetPre.ListUserIDs
		if err := usc.DeleteListByCampaignAdsetIdBudgetScheduleUsc(ctx, filter); err != nil {
			usc.logger.Error(err)
		}
	}

	buds, err := usc.fbService.BudgetSchedule.GetByCampaignId(ctx, campAdsetId)
	if err != nil {
		usc.logger.Error(err)
		return err
	}

	if buds == nil || buds.Data == nil {
		usc.logger.Error(errs.ErrFBBudgetScheduleNotFound)
		return errs.ErrFBBudgetScheduleNotFound
	}

	for _, period := range buds.Data {
		updateBson := mapping.MapperHighDemandPeriodFBUpsertReload(&period, userId, listUserIDs, campAdsetId, typeAction)
		filter["high_demand_period_id"] = period.ID
		fmt.Printf("\n -----------  budgetPre ----------- %+v \n", filter)
		if err := usc.repo.UpsertHighDemandPeriodRepo(ctx, filter, updateBson); err != nil {
			usc.logger.Error(err)
			return err
		}
	}

	return nil
}

/**
 * Reload Budget schedule by campaign id
 */
func (usc *apiBudgetScheduleUsc) ReloadByCampaignAdsetIdApiBudgetScheduleUsc(ctx context.Context, campAdsetId string, typeAction string, userId primitive.ObjectID) error {
	// var errStr []string

	// name, err := usc.getRoleByUserId(ctx, userId)
	// if err != nil {
	// 	return core.ErrBadRequest
	// }
	// fmt.Printf("\n ----------- User ---------- %v \n", userId)
	// fmt.Printf("\n ----------- Role name ---------- %v \n", *name)

	filter := bson.M{}
	if typeAction == "campaign" {
		filter["campaign_id"] = campAdsetId
	} else {
		filter["adset_id"] = campAdsetId
	}
	// if *name != "ADMIN" {
	// 	filter["list_user_ids"] = userId
	// }
	fmt.Printf("\n -----------filter ---------- %v \n", filter)

	if err := usc.DeleteListByCampaignAdsetIdBudgetScheduleUsc(ctx, filter); err != nil {
		usc.logger.Error(err)
	}
	buds, err := usc.fbService.BudgetSchedule.GetByCampaignId(ctx, campAdsetId)
	// fmt.Printf("----------------->, %+v \n", buds)ç
	if err != nil {
		usc.logger.Error(err)
		// errStr = append(errStr, errs.ErrFBBudgetSchedule.Error())
		return err
	}

	if buds == nil || buds.Data == nil {
		usc.logger.Error(errs.ErrFBBudgetScheduleNotFound)
		// errStr = append(errStr, errs.ErrFBBudgetScheduleNotFound.Error())
		return errs.ErrFBBudgetScheduleNotFound
	}

	for _, period := range buds.Data {
		// fmt.Printf("---------- period ------->, %+v \n", period)
		updateBson := mapping.MapperHighDemandPeriodFBUpsert(&period, userId, campAdsetId)
		// fmt.Printf("---------- updateBson ------->, %+v \n", updateBson)
		// filter := bson.M{"campaign_id": campaignId, "high_demand_period_id": period.ID}
		filter["high_demand_period_id"] = period.ID

		if err := usc.repo.UpsertHighDemandPeriodRepo(ctx, filter, updateBson); err != nil {
			usc.logger.Error(err)
			// errStr = append(errStr, err.Error())
			return err
		}
	}

	return nil
}

/**
 * Get Budget schedule by campaign id
 */

func (usc *apiBudgetScheduleUsc) GetByCampaignAdsetIdApiBudgetScheduleUsc(ctx context.Context, campAdsetId string, typeAction string, userId primitive.ObjectID) (*[]responses.HighDemandPeriodEntity, error) {

	name, err := usc.getRoleByUserId(ctx, userId)
	if err != nil {
		return nil, core.ErrBadRequest
	}

	filter := bson.M{}
	if typeAction == "campaign" {
		filter["campaign_id"] = campAdsetId
	} else {
		filter["adset_id"] = campAdsetId
	}

	if *name != "ADMIN" {
		filter["list_user_ids"] = userId
	}
	opts := &options.FindOptions{}
	opts.SetSort(bson.M{"time_end": 1})

	// fmt.Printf("\n -----------  Role Name ----------- %+v \n", *name)
	// fmt.Printf("\n -----------  Filter Get Budget ----------- %+v \n", filter)

	// Hàm tìm kiếm dữ liệu
	findBudgets := func() (*[]entity.HighDemandPeriodEntity, error) {
		budsEntity, err := usc.repo.FindHighDemandPeriodRepo(ctx, filter, opts)
		// fmt.Printf("\n -----------  err Get Budget ----------- %+v \n", budsEntity)
		if err != nil {
			return nil, err
		}
		if budsEntity == nil {
			return nil, nil
		}
		return budsEntity, nil
	}

	// Thử tìm kiếm lần đầu tiên
	budsEntity, err := findBudgets()
	if budsEntity == nil {
		return nil, nil
	}
	if err == nil {
		return mapping.MapperListEntityToView(budsEntity), nil
	}
	return nil, err
}

// func (usc *apiBudgetScheduleUsc) GetByCampaignAdsetIdApiBudgetScheduleUsc(ctx context.Context, campAdsetId string, typeAction string, userId primitive.ObjectID) (*[]entity.HighDemandPeriodEntity, error) {

// 	filter := bson.M{}
// 	if typeAction == "campaign" {
// 		filter = bson.M{"$match": bson.M{"campaign_id": campAdsetId}}
// 	} else {
// 		filter = bson.M{"$match": bson.M{"adset_id": campAdsetId}}
// 	}
// 	camp, err := usc.camRepo.FindOneCampaignRepo(ctx, filter)
// 	if err != nil {
// 		return nil, err
// 	}

// 	if camp.IsBudgetScheduleEnabled {
// 		filter = bson.M{
// 			"campaign_id": campAdsetId,
// 		}

// 		name, err := usc.getRoleByUserId(ctx, userId)
// 		if err != nil {
// 			return nil, core.ErrBadRequest
// 		}
// 		// if *name != "ADMIN" {
// 		// 	filter["list_user_ids"] = userId
// 		// }

// 		opts := &options.FindOptions{}
// 		opts.SetSort(bson.M{"time_end": 1})

// 		fmt.Printf("\n -----------  Role Name ----------- %+v \n", *name)
// 		fmt.Printf("\n -----------  Filter Get Budget ----------- %+v \n", filter)

// 		// Hàm tìm kiếm dữ liệu
// 		findBudgets := func() (*[]entity.HighDemandPeriodEntity, error) {
// 			budsEntity, err := usc.repo.FindHighDemandPeriodRepo(ctx, filter, opts)
// 			if err != nil {
// 				return nil, err
// 			}
// 			if budsEntity == nil || len(*budsEntity) == 0 {
// 				return nil, errs.ErrFBBudgetScheduleNotFound
// 			}
// 			return budsEntity, nil
// 		}

// 		// Thử tìm kiếm lần đầu tiên
// 		budsEntity, err := findBudgets()
// 		if err == nil {
// 			return budsEntity, nil
// 		}
// 		return nil, err
// 	} else {
// 		return nil, err
// 	}
// }

/**
 * create budget schedule
 * 1 - create
 * 2 - success reload budget schedule camp (job)
 *
 */
func (usc *apiBudgetScheduleUsc) CreateBudgetScheduleUsc(ctx context.Context, payload *requests.CreateBudgetScheduleReq) (*[]responses.HighDemandPeriodEntity, error) {

	userId, err := utils.GetUserIdPrimitive(ctx)
	if err != nil {
		return nil, core.ErrBadRequest
	}

	fmt.Printf("\n ----------- User  CreateBudgetScheduleUsc ---------- %v \n", userId)

	createBudgetScheduleFB := mapping.MapperBudgetScheduleByCampaignToFB(payload)

	var campAdsetId string
	var typeAction = "campaign"
	if payload.AdsetId != "" {
		typeAction = "adset"
		campAdsetId = payload.AdsetId
	} else {
		campAdsetId = payload.CampaignId
	}
	_, err = usc.fbService.BudgetSchedule.CreateBudgetScheduleByCampaignId(ctx, campAdsetId, createBudgetScheduleFB)

	if err != nil {
		return nil, err
	}

	if err := usc.ReloadByCampaignAdsetIdApiBudgetScheduleUsc(ctx, campAdsetId, typeAction, userId); err != nil {
		return nil, nil
	}

	buds, err := usc.GetByCampaignAdsetIdApiBudgetScheduleUsc(ctx, campAdsetId, typeAction, userId)
	if err != nil {
		return nil, nil
	}

	return buds, nil
}

// Create List Schedules budget
func (usc *apiBudgetScheduleUsc) CreateListBudgetScheduleUsc(ctx context.Context, payload *requests.CreateListBudgetScheduleReq, userId primitive.ObjectID) (*[]responses.HighDemandPeriodEntity, []string) {

	schedules := *payload
	var errors []string

	var campAdsetId string
	var typeAction = "campaign"

	fmt.Printf("\n ----------- User  CreateBudgetScheduleUsc ---------- %v \n", userId)

	for _, schedule := range schedules {
		if schedule.AdsetId != "" {
			typeAction = "adset"
			campAdsetId = schedule.AdsetId
		} else {
			campAdsetId = schedule.CampaignId
			typeAction = "campaign"
		}
		scheduleFB := mapping.MapperBudgetScheduleByCampaignToFB(schedule)

		fmt.Printf("schedule: %v \n", schedule)
		fmt.Printf("scheduleFB: %v \n", scheduleFB)
		_, err := usc.fbService.BudgetSchedule.CreateBudgetScheduleByCampaignId(ctx, campAdsetId, scheduleFB)

		if err != nil {
			regex := regexp.MustCompile(`.*\"https://graph.*?\":\s*`)
			errorResult := regex.ReplaceAllString(err.Error(), "")
			fmt.Printf("error-------> %v\n", errorResult)
			errors = append(errors, errorResult)
		}

		if err := usc.ReloadByCampaignAdsetIdApiBudgetScheduleUsc(ctx, campAdsetId, typeAction, userId); err != nil {
			fmt.Printf("\n error----ReloadByCampaignIdApiBudgetScheduleUsc---> %v\n", err)
			errors = append(errors, err.Error())
		}
	}

	buds, _ := usc.GetByCampaignAdsetIdApiBudgetScheduleUsc(ctx, campAdsetId, typeAction, userId)

	if len(errors) > 0 {
		return nil, errors
	}

	return buds, nil
}

/**
 * Update budget schedule
 * 1 - Update
 * 2 - success reload budget schedule camp (job)
 *
 */
func (usc *apiBudgetScheduleUsc) UpdateListBudgetScheduleUsc(ctx context.Context, payload *requests.UpdateListScheduleReq, userId primitive.ObjectID) (*[]responses.HighDemandPeriodEntity, error) {

	var campAdsetId string
	var typeAction = "campaign"
	if len(*payload) == 0 {
		return nil, errs.ErrBudgetScheduleUpdateUnknown
	}

	idCreated := &requests.DeleteBudgetScheduleReq{
		HighDemandPeriodIds: []string{},
	}
	budgetItem := (*payload)[0]
	if budgetItem.AdsetId != "" {
		typeAction = "adset"
		campAdsetId = budgetItem.AdsetId
		idCreated.AdsetId = budgetItem.AdsetId
	} else {
		campAdsetId = budgetItem.CampaignId
		idCreated.CampaignId = budgetItem.CampaignId
		typeAction = "campaign"
	}
	schedulesUpdate, scheduleUpdateRemove, _ := mapping.MapperUpdateBudgetScheduleByCampaignAdsetIdToFB(payload, campAdsetId, typeAction)

	if schedulesUpdate == nil {
		return nil, errs.ErrFBBudgetSchedule
	}

	// Get Schedule Backup
	schedulesBackup, _errs := usc.GetByCampaignAdsetIdApiBudgetScheduleUsc(ctx, campAdsetId, typeAction, userId)
	if _errs != nil {
		return nil, errs.ErrFBBudgetSchedule
	}

	// Remove Schedule Update
	idsDeleted, errRemove := usc.DeleteListByIdBudgetScheduleUsc(ctx, scheduleUpdateRemove)
	if errRemove != nil {
		fmt.Printf("\n -----------  errRemove ----------- %+v \n", errRemove)

		return nil, errs.ErrBudgetScheduleUpdateUnknown
	}

	// Create Schedule Update

	for _, schedule := range schedulesUpdate {
		scheduleFB := mapping.MapperBudgetScheduleByCampaignToFB(schedule)

		id, err := usc.fbService.BudgetSchedule.CreateBudgetScheduleByCampaignId(ctx, campAdsetId, scheduleFB)
		fmt.Printf("\n -----------  err err ----------- %+v \n", err)
		fmt.Printf("\n -----------  err xcampAdsetIdte ----------- %+v \n", campAdsetId)
		fmt.Printf("\n -----------  err xxxx Create ----------- %+v \n", scheduleFB)
		if err != nil {
			if _, err := usc.DeleteListByIdBudgetScheduleUsc(ctx, idCreated); err != nil {
				scheduleBKCreateFB := mapping.MapperBudgetScheduleBKToFB(schedulesBackup, *idsDeleted)
				_, createErr := usc.CreateListBudgetScheduleUsc(ctx, scheduleBKCreateFB, userId)
				usc.logger.Error(createErr)
				return nil, errors.New(createErr[0])
			}
			return nil, errs.ErrBudgetScheduleUpdateUnknown
		} else {
			idCreated.HighDemandPeriodIds = append(idCreated.HighDemandPeriodIds, *id)
		}
	}

	// Reload Schedule Backup
	_ = usc.ReloadByCampaignAdsetIdApiBudgetScheduleUsc(ctx, campAdsetId, typeAction, userId)

	budsReturn, _ := usc.GetByCampaignAdsetIdApiBudgetScheduleUsc(ctx, campAdsetId, typeAction, userId)

	return budsReturn, nil
}

/**
 * Delete budget schedule by campaign
 * 1 - Delete
 * 2 - success reload budget schedule camp (job)
 *
 */
func (usc *apiBudgetScheduleUsc) DeleteListByCampaignAdsetIdBudgetScheduleUsc(ctx context.Context, filter interface{}) error {

	err := usc.repo.DeleteHighDemandPeriodRepo(ctx, filter)
	if err != nil {
		return err
	}

	return nil
}

/**
 * Delete budget schedule by high_demand_period_id
 * 1 - Delete
 * 2 - success reload budget schedule camp (job)
 *
 */
func (usc *apiBudgetScheduleUsc) DeleteListByIdBudgetScheduleUsc(ctx context.Context, payload *requests.DeleteBudgetScheduleReq) (*[]string, []error) {

	ids := payload.HighDemandPeriodIds
	var errs []string
	var deleted []string
	var campAdsetId string
	var typeAction = "campaign"

	if payload.AdsetId != "" {
		typeAction = "adset"
		campAdsetId = payload.AdsetId
	} else {
		campAdsetId = payload.CampaignId
		typeAction = "campaign"
	}

	for _, id := range ids {
		if err := usc.fbService.HighDemandPeriod.Delete(ctx, id); err != nil {
			errs = append(errs, err.Error())
		} else {
			deleted = append(deleted, id)
		}
	}

	fmt.Printf("Results: %v\n", errs)

	go func() {
		if err := usc.ReloadByCampaignAdsetIdApiBudgetScheduleUsc(ctx, campAdsetId, typeAction, payload.UserId); err != nil {
			usc.logger.Error(err)
		}
	}()

	return &deleted, nil
}
