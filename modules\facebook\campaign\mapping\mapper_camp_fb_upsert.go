package mapping

import (
	campEnum "godsp/modules/facebook/campaign/common/enum"
	"godsp/modules/facebook/common/fbenums"
	v20 "godsp/pkg/fb-marketing/marketing/v20"

	"time"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

func MapperCampaignsToUpsert(camp *v20.Campaign, userId primitive.ObjectID, clientID primitive.ObjectID, approveCreate *campEnum.ApproveStatus) bson.M {

	now := time.Now()
	updateData := bson.M{
		"account_id":                 camp.AccountID,
		"bid_amount":                 camp.BidAmount,
		"bid_strategy":               camp.BidStrategy,
		"buying_type":                camp.BuyingType,
		"campaign_group_active_time": camp.CampaignGroupActiveTime,
		"can_use_spend_cap":          camp.CanUseSpendCap,
		"configured_status":          camp.ConfiguredStatus,

		"daily_budget":                        camp.DailyBudget,
		"effective_status":                    camp.EffectiveStatus,
		"has_secondary_skadnetwork_reporting": camp.HasSecondarySkadnetworkReporting,
		"is_budget_schedule_enabled":          camp.IsBudgetScheduleEnabled,
		"is_skadnetwork_attribution":          camp.IsSkadnetworkAttribution,
		"last_budget_toggling_time":           time.Time(camp.LastBudgetTogglingTime),
		"lifetime_budget":                     camp.LifeTimeBudget,
		"name":                                camp.Name,
		"objective":                           camp.Objective,
		"primary_attribution":                 camp.PrimaryAttribution,
		"smart_promotion_type":                camp.SmartPromotionType,
		"source_campaign_id":                  camp.SourceCampaignId,
		"special_ad_categories":               camp.SpecialAdCategories,
		"special_ad_category":                 camp.SpecialAdCategory,
		"spend_cap":                           camp.SpendCap,
		"start_time":                          time.Time(camp.StartTime),
		"status":                              camp.Status,
		"stop_time":                           time.Time(camp.StopTime),
		"updated_time":                        time.Time(camp.UpdatedTime),
		"create_time":                         time.Time(camp.CreatedTime),
		"budget_remaining":                    camp.BudgetRemaining,
		"promoted_object": bson.M{
			"application_id":         camp.PromotedObject.ApplicationID,
			"boosted_product_set_id": camp.PromotedObject.BoostedProductSetID,
			"conversion_goal_id":     camp.PromotedObject.ConversionGoalID,
			"product_catalog_id":     camp.PromotedObject.ProductCatalogID,
			"product_set_id":         camp.PromotedObject.ProductSetID,
			"product_id":             camp.PromotedObject.ProductItemID},

		"created_at": time.Time(camp.CreatedTime),
		// "created_at": utils.ConvertTimeUTCStringToTime(camp.CreatedTime),
		"updated_at": now,
		"updated_by": userId,
	}

	createData := bson.M{
		"created_by": userId,
		"client_id":  clientID,
	}

	if approveCreate != nil {
		if *approveCreate == campEnum.Review {
			updateData["status"] = fbenums.FB_STATUS_PAUSED
			updateData["approve"] = campEnum.Review
		} else {
			createData["approve"] = campEnum.None
		}
	} else {
		createData["approve"] = campEnum.None
	}

	return bson.M{
		"$set":         updateData,
		"$setOnInsert": createData,
		"$addToSet":    bson.M{"list_user_ids": userId},
	}
}
