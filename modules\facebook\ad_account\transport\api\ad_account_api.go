package api

import (
	"context"
	"fmt"
	"godsp/modules/facebook/ad_account/transport/response"
	"godsp/pkg/gos/utils"
	"godsp/pkg/sctx/core"
	"net/http"
	"time"

	"github.com/gofiber/fiber/v2"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

type AdAccountApiUsc interface {
	ReloadAdAccountUsc(ctx context.Context, userId primitive.ObjectID) []string
	ListDatatableAdAccountUsc(ctx context.Context) (*response.AdAccountListResponse, error)
}

type adAccountApi struct {
	usc AdAccountApiUsc
}

func NewAdAccadAccountApi(usc AdAccountApiUsc) *adAccountApi {
	return &adAccountApi{
		usc: usc,
	}
}

/**
 * Api reload ad account
 */
func (a *adAccountApi) ReloadAdAccountApi() fiber.Handler {
	return func(c *fiber.Ctx) error {
		var ctx, cancel = context.WithTimeout(context.Background(), 200*time.Second)
		defer cancel()

		userId, err := utils.GetUserId(c.Context())
		if err != nil {
			fmt.Println("Error:", err)
			return core.ReturnErrsForApi(c, err)
		}

		errs := a.usc.ReloadAdAccountUsc(ctx, userId)
		if errs != nil {
			return core.ReturnErrsForApi(c, errs)
		}

		return c.Status(http.StatusOK).JSON(core.ResponseData(map[string]interface{}{
			"msg": "Reload data successfully",
		}))
	}
}

/***
 *	Api list data table ad account
 *
 */
func (a *adAccountApi) ListDatatableAdAccountApi() fiber.Handler {
	return func(c *fiber.Ctx) error {
		datas, err := a.usc.ListDatatableAdAccountUsc(c.Context())
		if err != nil {
			return c.Status(http.StatusOK).JSON(fiber.Map{
				"data":            "",
				"draw":            1,
				"recordsTotal":    0,
				"recordsFiltered": 0,
				"err":             err.Error(),
			})
		}

		return c.Status(http.StatusOK).JSON(fiber.Map{
			"data":            datas.AdAccountDataTables,
			"draw":            2,
			"recordsTotal":    datas.Count,
			"recordsFiltered": datas.CountFilter,
		})
	}
}
