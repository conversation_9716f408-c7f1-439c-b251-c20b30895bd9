package admfunc

import "godsp/modules/admin/common/admconst"

func GetPageNumberFromDatatableRequest(startInput, lengthInput int) int {
	length := lengthInput
	if length == 0 {
		length = admconst.DEFAULT_LIMIT
	}

	page := 1
	if startInput > 0 {
		page = (startInput / length) + 1
	}

	return page
}

func PrepareSortOrderFromDatatableRequest(sortField *string, sortOrder *int, order []admconst.OrderDatatable) {
	*sortOrder = 1
	if order == nil || len(order) == 0 {
		*sortField = admconst.DEFAULT_SORT_COLUMN
	} else {
		orderColumn := order[0].Column
		if sortFieldVal, ok := admconst.DefaultAvailableSortFields[orderColumn]; !ok {
			*sortField = admconst.DEFAULT_SORT_COLUMN
		} else {
			*sortField = sortFieldVal
		}

		if order[0].Dir == admconst.DEFAULT_ORDER_DESCENDING {
			*sortOrder = -1
		}

		if *sortField == admconst.DefaultAvailableSortFields[1] {
			*sortOrder = -1
		}
	}
}
