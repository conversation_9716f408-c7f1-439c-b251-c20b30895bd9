package entity

import (
	campE "godsp/modules/facebook/campaign/common/enum"
	"time"

	"go.mongodb.org/mongo-driver/bson/primitive"
)

type CampaignEntity struct {
	ID primitive.ObjectID `json:"_id,omitempty" bson:"_id,omitempty"`

	CampaignID string `json:"campaign_id,omitempty" bson:"campaign_id"`
	Name       string `json:"name,omitempty" bson:"name"`
	AccountID  string `json:"account_id,omitempty"  bson:"account_id"`
	BuyingType string `json:"buying_type,omitempty" bson:"buying_type"`

	BidStrategy      string `json:"bid_strategy,omitempty" bson:"bid_strategy"`
	BidAmount        uint64 `json:"bid_amount,omitempty" bson:"bid_amount"`
	ConfiguredStatus string `json:"configured_status,omitempty" bson:"configured_status"`
	CreatedTime      string `json:"created_time,omitempty" bson:"created_time"`
	DailyBudget      uint64 `json:"daily_budget,omitempty,string" bson:"daily_budget"`
	EffectiveStatus  string `json:"effective_status,omitempty" bson:"effective_status"`

	LifeTimeBudget uint64 `json:"lifetime_budget,omitempty,string" bson:"lifetime_budget"`

	Objective           string    `json:"objective,omitempty" bson:"objective"`
	CanUseSpendCap      bool      `json:"can_use_spend_cap,omitempty" bson:"can_use_spend_cap"`
	SpendCap            uint64    `json:"spend_cap,omitempty,string" bson:"spend_cap"`
	StartTime           time.Time `json:"start_time,omitempty" bson:"start_time"`
	Status              string    `json:"status,omitempty" bson:"status"`
	StopTime            time.Time `json:"stop_time,omitempty" bson:"stop_time"`
	UpdatedTime         time.Time `json:"updated_time,omitempty" bson:"updated_time"`
	SpecialAdCategories []string  `json:"special_ad_categories,omitempty" bson:"special_ad_categories"`
	BudgetRemaining     string    `json:"budget_remaining,omitempty"`

	CampaignGroupActiveTime          string    `json:"campaign_group_active_time,omitempty" bson:"campaign_group_active_time"`
	HasSecondarySkadnetworkReporting bool      `json:"has_secondary_skadnetwork_reporting,omitempty" bson:"has_secondary_skadnetwork_reporting"`
	IsBudgetScheduleEnabled          bool      `json:"is_budget_schedule_enabled" bson:"is_budget_schedule_enabled"`
	IsSkadnetworkAttribution         bool      `json:"is_skadnetwork_attribution,omitempty" bson:"is_skadnetwork_attribution"`
	LastBudgetTogglingTime           time.Time `json:"last_budget_toggling_time,omitempty" bson:"last_budget_toggling_time"`
	PrimaryAttribution               string    `json:"primary_attribution,omitempty" bson:"primary_attribution"`
	SmartPromotionType               string    `json:"smart_promotion_type,omitempty" bson:"smart_promotion_type"`
	SourceCampaignId                 string    `json:"source_campaign_id,omitempty" bson:"source_campaign_id"`
	SpecialAdCategory                string    `json:"special_ad_category,omitempty" bson:"special_ad_category"`

	PromotedObject AdPromotedObject `json:"promoted_object,omitempty"`

	CreateddBy primitive.ObjectID `json:"created_by" bson:"created_by"`
	CreatedAt  time.Time          `json:"created_at" bson:"created_at"`
	UpdatedBy  primitive.ObjectID `json:"updated_by" bson:"updated_by"`
	UpdatedAt  time.Time          `json:"updated_at" bson:"updated_at"`

	ListUserIDs []primitive.ObjectID `json:"list_user_ids,omitempty" bson:"list_user_ids"`
	ClientID    primitive.ObjectID   `bson:"client_id,omitempty" json:"client_id,omitempty"`
	Approve     campE.ApproveStatus  `json:"approve,omitempty" bson:"approve,omitempty"`
}

type Ad struct {
	AdID string `json:"ad_id" bson:"ad_id"`
	Name string `json:"name"`
}
type Adset struct {
	AdsetID string `json:"adset_id" bson:"adset_id"`
	Name    string `json:"name"`
	Ads     *[]Ad  `json:"ads"`
}

type CampaignDetailResponseEntity struct {
	// ID primitive.ObjectID `json:"" bson:"_id,omitempty"`

	CampaignID string `json:"campaign_id" bson:"campaign_id"`
	Name       string `json:"name" bson:"name"`
	AccountID  string `json:"account_id"  bson:"account_id"`
	BuyingType string `json:"buying_type" bson:"buying_type"`

	BidStrategy      string `json:"bid_strategy" bson:"bid_strategy"`
	BidAmount        uint64 `json:"bid_amount" bson:"bid_amount"`
	ConfiguredStatus string `json:"configured_status" bson:"configured_status"`
	CreatedTime      string `json:"created_time,omitempty" bson:"created_time"`
	DailyBudget      uint64 `json:"daily_budget,string" bson:"daily_budget"`
	EffectiveStatus  string `json:"effective_status" bson:"effective_status"`

	LifeTimeBudget uint64 `json:"lifetime_budget,string" bson:"lifetime_budget"`

	Objective           string    `json:"objective" bson:"objective"`
	CanUseSpendCap      bool      `json:"can_use_spend_cap" bson:"can_use_spend_cap"`
	SpendCap            uint64    `json:"spend_cap" bson:"spend_cap"`
	StartTime           time.Time `json:"start_time" bson:"start_time"`
	Status              string    `json:"status" bson:"status"`
	StopTime            time.Time `json:"stop_time" bson:"stop_time"`
	UpdatedTime         time.Time `json:"updated_time,omitempty" bson:"updated_time"`
	SpecialAdCategories []string  `json:"special_ad_categories" bson:"special_ad_categories"`
	BudgetRemaining     string    `json:"budget_remaining"`

	CampaignGroupActiveTime          string    `json:"campaign_group_active_time" bson:"campaign_group_active_time"`
	HasSecondarySkadnetworkReporting bool      `json:"has_secondary_skadnetwork_reporting" bson:"has_secondary_skadnetwork_reporting"`
	IsSkadnetworkAttribution         bool      `json:"is_skadnetwork_attribution" bson:"is_skadnetwork_attribution"`
	LastBudgetTogglingTime           time.Time `json:"last_budget_toggling_time" bson:"last_budget_toggling_time"`
	IsBudgetScheduleEnabled          bool      `json:"is_budget_schedule_enabled" bson:"is_budget_schedule_enabled"`
	PrimaryAttribution               string    `json:"primary_attribution" bson:"primary_attribution"`
	SmartPromotionType               string    `json:"smart_promotion_type" bson:"smart_promotion_type"`
	SourceCampaignId                 string    `json:"source_campaign_id" bson:"source_campaign_id"`
	SpecialAdCategory                string    `json:"special_ad_category" bson:"special_ad_category"`
	ClientID                         string    `json:"client_id" bson:"client_id"`

	CreateddBy primitive.ObjectID `json:"created_by" bson:"created_by"`
	CreatedAt  time.Time          `json:"created_at" bson:"created_at"`
	UpdatedBy  primitive.ObjectID `json:"updated_by" bson:"updated_by"`
	UpdatedAt  time.Time          `json:"updated_at" bson:"updated_at"`

	Adsets         []*Adset          `json:"adsets" bson:"adsets"`
	PromotedObject *AdPromotedObject `json:"promoted_object" bson:"promoted_object"`

	Approve campE.ApproveStatus `json:"approve" bson:"approve"`
}

type AdPromotedObject struct {
	ApplicationID       *string `json:"application_id,omitempty" bson:"application_id,omitempty"`
	BoostedProductSetID *string `json:"boosted_product_set_id,omitempty" bson:"boosted_product_set_id,omitempty"`
	ConversionGoalID    *string `json:"conversion_goal_id,omitempty" bson:"conversion_goal_id,omitempty"`
	ProductCatalogID    *string `json:"product_catalog_id,omitempty" bson:"product_catalog_id,omitempty"`
	ProductItemID       *string `json:"product_item_id,omitempty" bson:"product_item_id,omitempty"`
	ProductSetID        *string `json:"product_set_id,omitempty" bson:"product_set_id,omitempty"`
}

type CustomEventType string

func (CampaignEntity) CollectionName() string {
	return "fb_campaigns"
}

// Table campaing có các filed CampaignId, Name, Description
// Table adset có các filed AdsetId, Name, CampaignId(là khóa phụ)
// Table adset có các filed AdId, Name, AdsetId(là khóa phụ), CampaignId(là khóa phụ)

// Tôi muốn viết câu truy vấn cho mongo với điều kiện dữ liệu vào là CampainId, dự liệu lấy ra sẽ
// {
// 	campaignId: "12389273",
// 	Name: "Name campaign",
// 	Description: "Description campaign",
//    Adsets: [
//       {
// 			AdsetId: "12389273_1234567890",
//          Name: "Name Adset 1",
// 			ads: [
// 				{
// 					AdId: "12389273_1234567890_1234567890",
//                Name: "Name Ad 1",
// 				},
// 				{
// 					AdId: "12389273_1234567890_1234567890",
//                Name: "Name Ad 2",
// 				}
// 			]
// 		},
// 		{
// 			AdsetId: "12389273_12234567890",
//          Name: "Name Adset 2",
// 			ads: [
// 				{
// 					AdId: "12389273_1234567890_1234567890",
//                Name: "Name Ad 3",
// 				},
// 				{
// 					AdId: "12389273_1234567890_1234567890",
//                Name: "Name Ad 4",
// 				}
// 			]
// 		}
// 		]
// }
