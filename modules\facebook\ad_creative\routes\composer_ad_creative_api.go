package routes

import (
	"godsp/modules/facebook/ad_creative/repository/mongo"
	"godsp/modules/facebook/ad_creative/transport/api"
	"godsp/modules/facebook/ad_creative/usecase"
	"godsp/pkg/sctx/component/facebook/fbmarketing"

	"github.com/dev-networldasia/dspgos/sctx/component/mongodb"

	"github.com/dev-networldasia/dspgos/sctx/configs"

	"github.com/dev-networldasia/dspgos/sctx"

	"github.com/gofiber/fiber/v2"
)

type ComposerAdCreativeApi interface {
	ReloadAdCreativeApi() fiber.Handler
	DetailAdCreativeApi() fiber.Handler

	CreateAdCreativeApi() fiber.Handler
	DeleteAdCreativeApi() fiber.Handler
	GenerateAdCreativePreviewApi() fiber.Handler
	GenerateInstagramAdCreativePreviewApi() fiber.Handler
}

func ComposerAdCreativeApiService(serviceCtx sctx.ServiceContext) ComposerAdCreativeApi {
	mongoDB := serviceCtx.MustGet(configs.KeyCompMongoDB).(mongodb.MongoComponent).GetDatabase()
	logger := serviceCtx.MustGet(configs.KeyLoggerDaily).(sctx.AppLoggerDaily).GetLogger("webfb")
	fbService := serviceCtx.MustGet(configs.KeyFBMarketingV20).(fbmarketing.FBMarketingServices).GetFBMarketingService()

	repo := mongo.NewAdCreativeRepo(mongoDB)
	usc := usecase.NewApiAdCreativeUsc(fbService, repo, logger)
	api := api.NewAdCreativeApi(usc)

	return api
}
