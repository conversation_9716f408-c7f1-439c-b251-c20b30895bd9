package requests

import (
	"github.com/go-playground/validator/v10"
	"godsp/modules/admin/common/admconst"
	"godsp/modules/admin/common/admfunc"
)

type PayloadListBillingDetailApi struct {
	BillNumber string `json:"bill_number"`
	Page       int    `json:"page,omitempty"`
	PageSize   int    `json:"page_size,omitempty"`
}

type ListBillingDetailApiReq struct {
	Draw      int                       `json:"draw" form:"draw"`
	Length    int                       `json:"length" form:"length"`
	Start     int                       `json:"start" form:"start"`
	Order     []admconst.OrderDatatable `json:"order"`
	Filter    BillingDetailFilter       `json:"filter" form:"filter"`
	Page      int                       `json:"-" form:"-"`
	SortField string                    `json:"-" form:"-"`
	SortOrder int                       `json:"-" form:"-"`
}

type BillingDetailFilter struct {
	Search     string `json:"search" form:"search"`
	BillNumber string `json:"bill_number" form:"bill_number"`
}

func (req *ListBillingDetailApiReq) Validate() error {
	validate := validator.New()
	err := validate.Struct(req)
	if err != nil {
		return err
	}
	//
	//req.SortOrder = 1
	//if req.Order == nil && len(req.Order) == 0 {
	//	req.SortField = admconst.DEFAULT_SORT_COLUMN
	//} else {
	//	orderColumn := req.Order[0].Column
	//	if sortField, ok := admconst.DefaultAvailableSortFields[orderColumn]; !ok {
	//		req.SortField = admconst.DEFAULT_SORT_COLUMN
	//	} else {
	//		req.SortField = sortField
	//	}
	//
	//	if sortOrder := req.Order[0].Dir; sortOrder == admconst.DEFAULT_ORDER_DESCENDING {
	//		req.SortOrder = -1
	//	}
	//
	//	if req.SortField == admconst.DefaultAvailableSortFields[1] {
	//		req.SortOrder = -1
	//	}
	//}

	admfunc.PrepareSortOrderFromDatatableRequest(&req.SortField, &req.SortOrder, req.Order)
	req.Page = admfunc.GetPageNumberFromDatatableRequest(req.Start, req.Length)

	return nil
}
