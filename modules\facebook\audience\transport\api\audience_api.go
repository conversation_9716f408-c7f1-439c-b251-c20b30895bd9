package api

import (
	"context"
	"fmt"
	"godsp/conf"
	clientE "godsp/modules/admin/client/entity"
	"godsp/modules/facebook/ad/common/errs"
	"godsp/modules/facebook/audience/entity"
	"godsp/modules/facebook/audience/transport/requests"
	"godsp/modules/facebook/audience/transport/responses"
	"godsp/pkg/gos/utils"
	"godsp/pkg/sctx/core"
	"net/http"
	"time"

	"github.com/gofiber/fiber/v2"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

type ApiAudienceUsc interface {
	ReloadAudienceUsc(ctx context.Context, adAccountId string, userId primitive.ObjectID) []string
	ListAudiencesUsc(ctx context.Context, payload *requests.ListAudiencesReq) (*responses.ListAudiencesResp, error)
	ListDatatableAudienceUsc(ctx context.Context, payload *requests.ListTableAudienceReq) (*responses.DatatableAudienceResq, error)
	GetRoleByUserId(ctx context.Context, userId primitive.ObjectID) (*string, error)
	CreateCustomAudienceUsc(ctx context.Context, payload *requests.CreateCustomAudienceReq) (*entity.CustomAudienceEntity, error)
	UpdateCustomAudienceUcs(ctx context.Context, req *requests.UpdateCustomAudienceReq) error
	DeleteCustomAudienceUsc(ctx context.Context, payload *requests.DeleteCustomAudienceReq) error
	GetDetailCustomAudienceUsc(ctx context.Context, payload *requests.GetDetailCustomAudienceReq) (*entity.CustomAudienceEntity, error)
	ListClientAudienceUsc(ctx context.Context) ([]*clientE.ClientEntity, error)
}

type audienceApi struct {
	usc ApiAudienceUsc
}

func NewAudienceApi(usc ApiAudienceUsc) *audienceApi {
	return &audienceApi{
		usc: usc,
	}
}

/**
 * Reload Audience by act
 */
func (a *audienceApi) ReloadAudienceApi() fiber.Handler {
	return func(c *fiber.Ctx) error {
		var payload requests.ReloadAudienceReq
		payload.AccountID = conf.FBConf.Act

		if c.Body() != nil && len(c.Body()) > 0 {
			if err := c.BodyParser(&payload); err != nil {
				return core.ReturnErrsForApi(c, err)
			}
		}

		//validate
		validationErrors := payload.Validate()
		if len(validationErrors) > 0 {
			return core.ReturnErrsForApi(c, validationErrors)
		}

		userId, err := utils.GetUserId(c.Context())
		if err != nil {
			fmt.Println("Error:", err)
			return core.ReturnErrsForApi(c, err)
		}

		if errs := a.usc.ReloadAudienceUsc(c.Context(), payload.AccountID, userId); errs != nil {
			return core.ReturnErrsForApi(c, errs)
		}

		return c.Status(http.StatusOK).JSON(core.ResponseData(map[string]interface{}{
			"msg": "Reload audiences successfully",
		}))
	}
}

/**
 * List audiences
 */
func (a *audienceApi) ListAudiencesApi() fiber.Handler {
	return func(c *fiber.Ctx) error {
		resData := responses.ListAudiencesResp{}
		var payload requests.ListAudiencesReq
		if err := c.BodyParser(&payload); err != nil {
			resData.Msg = err.Error()

			return c.JSON(resData)
		}

		resData.Draw = payload.Draw
		if err := payload.Validate(); err != nil {
			resData.Msg = err.Error()

			return c.JSON(resData)
		}

		audienecs, err := a.usc.ListAudiencesUsc(c.Context(), &payload)
		fmt.Println("audienecs err", err)
		if err != nil {
			resData.Msg = err.Error()
			return c.JSON(fiber.Map{
				"msg":  err.Error(),
				"data": []string{},
			})
		}

		return c.JSON(audienecs)
	}
}

/**
 * List data table audience api
 */
func (a *audienceApi) ListTableAudienceApi() fiber.Handler {
	return func(c *fiber.Ctx) error {
		resData := responses.DatatableAudienceResq{}
		var payload requests.ListTableAudienceReq

		if c.Body() != nil && len(c.Body()) > 0 {
			if err := c.BodyParser(&payload); err != nil {
				resData.Msg = err.Error()
				return c.JSON(fiber.Map{
					"msg":  err.Error(),
					"data": []string{},
				})
			}
		}

		resData.Draw = payload.Draw
		if err := payload.Validate(); err != nil {
			resData.Msg = err.Error()
			return c.JSON(fiber.Map{
				"msg":  err.Error(),
				"data": []string{},
			})
		}

		audienceDatatable, err := a.usc.ListDatatableAudienceUsc(c.Context(), &payload)
		if err != nil {
			resData.Msg = err.Error()
			return c.JSON(fiber.Map{
				"msg":  err.Error(),
				"data": []string{},
			})
		}

		return c.JSON(fiber.Map{
			"msg":  "Get list audience successfully",
			"data": audienceDatatable,
		})
	}
}

func (a *audienceApi) CreateCustomAudienceApi() fiber.Handler {
	return func(c *fiber.Ctx) error {
		var payload requests.CreateCustomAudienceReq

		if err := c.BodyParser(&payload); err != nil {
			return c.Status(http.StatusBadRequest).JSON(map[string]interface{}{
				"error":   "Invalid request format",
				"details": err.Error(),
			})
		}

		//validate
		validationErrors := payload.Validate()
		if len(validationErrors) > 0 {
			return core.ReturnErrsForApi(c, validationErrors)
		}

		userId, _, clientId, adccountId, err := utils.GetInfoUser(c.Context())

		if err != nil {
			return core.ReturnErrsForApi(c, err)
		} else {
			payload.ClientID = clientId
			payload.AccountID = adccountId
			payload.CreatedBy = userId
		}

		_, err = a.usc.CreateCustomAudienceUsc(c.Context(), &payload)
		if err != nil {
			return core.ReturnErrsForApi(c, err)
		}

		return c.Status(http.StatusOK).JSON(map[string]interface{}{
			"msg": "Create custom audience successfully",
		})
	}
}

func (a *audienceApi) UpdateCustomAudienceApi() fiber.Handler {
	return func(c *fiber.Ctx) error {
		var payload requests.UpdateCustomAudienceReq

		if err := c.BodyParser(&payload); err != nil {
			return c.Status(http.StatusBadRequest).JSON(map[string]interface{}{
				"error":   "Invalid request format",
				"details": err.Error(),
			})
		}

		//validate
		validateErrors := payload.Validate()
		if len(validateErrors) > 0 {
			return core.ReturnErrsForApi(c, validateErrors)
		}

		userId, _, clientId, adccountId, err := utils.GetInfoUser(c.Context())

		if err != nil {
			return core.ReturnErrsForApi(c, err)
		} else {
			payload.ClientID = clientId
			payload.AccountID = adccountId
			payload.UpdatedBy = userId
			payload.UpdatedAt = time.Now()
		}

		err = a.usc.UpdateCustomAudienceUcs(c.Context(), &payload)
		if err != nil {
			return core.ReturnErrsForApi(c, err)
		}

		return c.Status(http.StatusOK).JSON(map[string]interface{}{
			"msg":  "Update custom audience successfully",
			"data": userId,
		})
	}
}

func (a *audienceApi) DeleteCustomAudienceApi() fiber.Handler {
	return func(c *fiber.Ctx) error {

		var payload requests.DeleteCustomAudienceReq

		if err := c.BodyParser(&payload); err != nil {
			return c.Status(http.StatusBadRequest).JSON(map[string]interface{}{
				"error":   "Invalid request format",
				"details": err.Error(),
			})
		}

		//validate
		validateErrors := payload.Validate()
		if len(validateErrors) > 0 {
			return core.ReturnErrsForApi(c, validateErrors)
		}

		// userId, _, clientId, adccountId, err := utils.GetInfoUser(c.Context())

		// if err != nil {
		// 	return core.ReturnErrsForApi(c, err)
		// } else {
		// 	payload.ClientID = clientId
		// 	payload.AccountID = adccountId
		// 	payload.UpdatedBy = userId
		// 	payload.UserId = userId
		// 	payload.UpdatedAt = time.Now()
		// }

		err := a.usc.DeleteCustomAudienceUsc(c.Context(), &payload)
		if err != nil {
			return core.ReturnErrsForApi(c, err)
		}

		return c.Status(http.StatusOK).JSON(map[string]interface{}{
			"msg": "Delete custom audience successfully",
		})
	}
}

func (a *audienceApi) GetDetailAudienceApi() fiber.Handler {
	return func(c *fiber.Ctx) error {
		var payload requests.GetDetailCustomAudienceReq

		if err := c.BodyParser(&payload); err != nil {
			return c.Status(http.StatusBadRequest).JSON(map[string]interface{}{
				"error":   "Invalid request format",
				"details": err.Error(),
			})
		}

		if payload.AudienceID == "" {
			return core.ReturnErrsForApi(c, errs.ErrAudienceID)
		}

		customAudience, err := a.usc.GetDetailCustomAudienceUsc(c.Context(), &payload)
		if err != nil {
			return core.ReturnErrsForApi(c, err)
		}

		return c.Status(http.StatusOK).JSON(map[string]interface{}{
			"msg":  "Get detail audience successfully",
			"data": customAudience,
		})
	}
}
