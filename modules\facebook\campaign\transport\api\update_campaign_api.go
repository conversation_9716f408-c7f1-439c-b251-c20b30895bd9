package api

import (
	"fmt"
	"godsp/modules/facebook/campaign/transport/requests"
	"godsp/pkg/gos/utils"
	"godsp/pkg/sctx/core"
	"net/http"

	"github.com/gofiber/fiber/v2"
)

/***
 * Update camp api
 */

func (a *campaignApi) UpdateCampaignApi() fiber.Handler {
	return func(c *fiber.Ctx) error {
		var payload requests.UpdateCampaignReq
		if err := c.BodyParser(&payload); err != nil {
			return core.ReturnErrsForApi(c, err)
		}

		//validate
		validationErrors := payload.Validate()
		if len(validationErrors) > 0 {
			return core.ReturnErrsForApi(c, validationErrors)
		}

		userId, err := utils.GetUserId(c.Context())
		if err != nil {
			return core.ReturnErrsForApi(c, err)
		}

		adaccountId := utils.GetAdAccount(c.Context())

		payload.UserId = userId
		payload.AccountID = *adaccountId

		err = a.usc.UpdateCampaignUsc(c.Context(), &payload)
		if err != nil {
			return core.ReturnErrsForApi(c, map[string]interface{}{
				"msg": err,
			})
		}

		return c.Status(http.StatusOK).JSON(core.ResponseData(map[string]interface{}{
			"msg": "Update Campaign successfully",
			// "data": camp,
		}))
	}
}

func (a *campaignApi) UpdateNameStatusCampaignApi() fiber.Handler {
	return func(c *fiber.Ctx) error {
		var payload requests.UpdateNameStatusCampaignReq
		fmt.Println("validationErrors", payload)
		if err := c.BodyParser(&payload); err != nil {
			return core.ReturnErrsForApi(c, err)
		}
		user, _ := utils.GetInfoUserBasic(c.Context())

		validationErrors := payload.Validate()
		if len(validationErrors) > 0 {
			return core.ReturnErrsForApi(c, validationErrors)
		}

		payload.UserId = *user.UserId

		err := a.usc.UpdateNameOrStatusCampaignUsc(c.Context(), &payload)
		if err != nil {
			return core.ReturnErrsForApi(c, []string{err.Error()})
		}

		return c.Status(http.StatusOK).JSON(core.ResponseData(map[string]interface{}{
			"msg": "Update Campaign successfully",
		}))
	}
}
