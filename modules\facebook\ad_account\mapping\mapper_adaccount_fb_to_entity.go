package mapping

import (
	"godsp/modules/facebook/ad_account/entity"
	v20 "godsp/pkg/fb-marketing/marketing/v20"
	"time"

	"go.mongodb.org/mongo-driver/bson/primitive"
)

func MapperAdAccountFBToEntity(adc *v20.AdAccount, userId primitive.ObjectID) *entity.AdAccountEntity {
	now := time.Now()

	return &entity.AdAccountEntity{
		ID:           primitive.NewObjectID(),
		AccountID:    adc.AccountID,
		Name:         adc.Name,
		Currency:     adc.Currency,
		TimeZoneName: adc.TimeZoneName,
		CreatedBy:   userId,
		CreatedAt:    now,
		UpdatedAt:    now,
		UpdatedBy:    userId,
	}
}
