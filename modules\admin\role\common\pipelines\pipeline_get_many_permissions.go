package pipelines

import (
	"go.mongodb.org/mongo-driver/bson"
)

/**
 * Get list modules asc by group of permission
 * 1. Groups documents by the "group" field and collects unique "module" values into an array.
 * 2. $unwind and $sort order to ordering list modules asc
 * Use: FindGroupModulePermissionRoleUsc
 */
func PipelineGetModulesByGroup() []bson.M {
	pipeline := []bson.M{
		{
			"$match": bson.M{
				"path": bson.M{
					"$nin": []string{
						"/login",
						"/api/auths/login",
						"/register",
						"/page-forbidden",
						"/logout",
					},
				},
			},
		},
		{
			"$group": bson.M{
				"_id":              "$group",
				"modules_by_group": bson.M{"$addToSet": "$module"},
			},
		},
		{
			"$unwind": "$modules_by_group",
		},
		{
			"$sort": bson.M{"modules_by_group": 1},
		},
		{
			"$group": bson.M{
				"_id":              "$_id",
				"modules_by_group": bson.M{"$push": "$modules_by_group"},
			},
		},
		{
			"$project": bson.M{
				"_id":              0,
				"group":            "$_id",
				"modules_by_group": 1,
			},
		},
		{
			"$sort": bson.M{"group": 1},
		},
	}

	return pipeline
}

func PipelineGetPermissionsByModule(module string, group string) []bson.M {
	if group == "" {
		group = "fb"
	}

	pipeline := []bson.M{
		{
			"$match": bson.M{
				"module": module,
				"group":  group,
				"$or": []bson.M{
					{"parent_id": nil},
				},
			},
		},
		{
			"$lookup": bson.M{
				"from":         "admins_permissions",
				"localField":   "parent_id",
				"foreignField": "_id",
				"as":           "parent",
			},
		},
		{
			"$lookup": bson.M{
				"from":         "admins_permissions",
				"localField":   "_id",
				"foreignField": "parent_id",
				"as":           "childrens",
			},
		},
		{
			"$unwind": bson.M{
				"path":                       "$parent",
				"preserveNullAndEmptyArrays": true,
			},
		},
		{
			"$addFields": bson.M{
				"action_order": bson.M{
					"$switch": bson.M{
						"branches": []bson.M{
							{"case": bson.M{"$eq": []interface{}{"$action", "list"}}, "then": 1},
							{"case": bson.M{"$eq": []interface{}{"$action", "create"}}, "then": 2},
							{"case": bson.M{"$eq": []interface{}{"$action", "edit"}}, "then": 3},
							{"case": bson.M{"$eq": []interface{}{"$action", "delete"}}, "then": 4},
						},
						"default": 5,
					},
				},
			},
		},
		{
			"$sort": bson.M{
				"module":       1,
				"action_order": 1,
			},
		},
	}
	return pipeline
}
