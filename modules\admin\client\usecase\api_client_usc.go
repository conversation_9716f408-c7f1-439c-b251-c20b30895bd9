package usecase

import (
	"context"
	"errors"
	"fmt"
	"godsp/conf"
	"godsp/modules/admin/client/common/errs"
	"godsp/modules/admin/client/common/pipelines"
	"godsp/modules/admin/client/entity"
	"godsp/modules/admin/client/mapping"
	"godsp/modules/admin/client/transport/requests"
	"godsp/modules/admin/client/transport/responses"
	"godsp/pkg/gos/utils"

	"github.com/dev-networldasia/dspgos/sctx"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
	"gorm.io/gorm"
)

type ApiClientRepo interface {
	FindClientsWithPipelineRepo(ctx context.Context, pipeline []bson.M, opts ...*options.AggregateOptions) (*[]entity.ClientEntity, error)
	InsertUserRepo(ctx context.Context, user *entity.ClientCreationEntity) (*primitive.ObjectID, error)
	UpdateOneClientRepo(ctx context.Context, filter interface{}, client *entity.ClientUpdateEntity, opts ...*options.UpdateOptions) error
	DeleteUserRepo(ctx context.Context, filter interface{}, update bson.M) error
	GetMongoDb() *mongo.Database
}

type apiClientUsc struct {
	mongo             *mongo.Database
	repo              ApiClientRepo
	userRepo          UserRepo
	adAccRepo         AdAccountRepo
	pageRepo          PageRepo
	pixelRepo         PixelRepo
	catalogueRepo     CatalogueRepo
	tikAdvertiserRepo TiktokAdvertiserRepo
	tikPresetColRepo  TiktokPresetColumnRepo

	logger sctx.Logger
}

func NewApiClientUsc(deps ApiClientUscDeps) *apiClientUsc {
	return &apiClientUsc{
		mongo:             deps.Mongo,
		repo:              deps.Repo,
		userRepo:          deps.UserRepo,
		adAccRepo:         deps.AdAccRepo,
		pageRepo:          deps.PageRepo,
		pixelRepo:         deps.PixelRepo,
		catalogueRepo:     deps.CatalogueRepo,
		tikAdvertiserRepo: deps.TikAdvertiserRepo,
		tikPresetColRepo:  deps.TikPresetColRepo,
		logger:            deps.Logger,
	}
}

var (
	ErrStartSession      = errors.New("failed to start session")
	ErrTransactionFailed = errors.New("transaction failed")
	ErrUpdateAdAccount   = errors.New("adaccount update failed")
	ErrUpdatePage        = errors.New("page update failed")
	ErrUpdatePixel       = errors.New("pixel update failed")
	ErrUpdateCatalogue   = errors.New("catalogue update failed")
)

/**
 * Create Client
 * 1. Create
 * 2. Mapper
 * 3. Create
 */
func (usc *apiClientUsc) CreateApiClientUsc(ctx context.Context, payload *requests.PayloadClientCreation) error {

	userEntity := mapping.MapperCreateClient(payload)
	_, err := usc.repo.InsertUserRepo(ctx, userEntity)
	if err != nil {
		usc.logger.Error(err)
		go usc.deleteImgCreate(payload)
		return err
	}

	return nil}

/**
 * Edit Client
 */
func (usc *apiClientUsc) UpdateApiClientUsc(ctx context.Context, payload *requests.PayloadClientUpdating) (*entity.ClientUpdateEntity, error) {

	clientUpdate := mapping.MapperUpdateClient(payload)

	err := usc.repo.UpdateOneClientRepo(ctx, bson.M{"_id": payload.ID}, clientUpdate)
	if err != nil {
		if errors.Is(err, gorm.ErrDuplicatedKey) {
			usc.logger.Error(err)
			return nil, errs.ErrEmailDuplicate
		}
		usc.logger.Error(err)
		return nil, err
	}
	return clientUpdate, nil

}// delete img if create error
func (usc *apiClientUsc) deleteImgCreate(payload *requests.PayloadClientCreation) {
	if payload.FileImg != nil {
		if err := utils.RemoveFile(conf.UploadPathPublic, payload.Logo); err != nil {
			usc.logger.Error(err)
		}
	}
}

/**
 * Api Client list datatable
 */
func (usc *apiClientUsc) ListDatatablClienteApiUsc(ctx context.Context) (*[]responses.ClientDataTable, error) {

	pipeline := pipelines.PipelineListTableClients()

	results, err := usc.repo.FindClientsWithPipelineRepo(ctx, pipeline)

	if err != nil {
		usc.logger.Error(err)
		return nil, err
	}

	return mapping.MapperClientToDatatable(results), nil

}

/**
 * Api Client list datatable
 */
func (usc *apiClientUsc) DeletelClienteApiUsc(ctx context.Context, payload *requests.PayloadClientDelete) error {

	filter := bson.M{
		"_id": payload.ID,
	}
	update := bson.M{
		"$set": bson.M{
			"updated_by": payload.UpdatedBy,
			"updated_at": payload.UpdatedAt,
			"status":     payload.Status,
		},
	}

	err := usc.repo.DeleteUserRepo(ctx, filter, update)

	if err != nil {
		usc.logger.Error(err)
		return err
	}

	return nil

}

/**
 * Update User Id, Client Id for Adaccount, Pages, Pixels, Catalogues
 */

// func (usc *apiClientUsc) UpsertUserToMultipleCollectionsWithTransactionApiUsc(ctx context.Context, payload requests.UpdateUserIdClientIdFacebookResource) error {

// 	session, err := usc.mongo.Client().StartSession()
// 	if err != nil {
// 		return fmt.Errorf("failed to start session: %w", err)
// 	}
// 	defer session.EndSession(ctx)

// 	callback := func(sessCtx mongo.SessionContext) (interface{}, error) {
// 		updateData := bson.M{
// 			"$addToSet": bson.M{
// 				"list_user_ids": bson.M{"$each": payload.ListUserIDs},
// 				"client_ids":    bson.M{"$each": payload.ClientIDs},
// 			},
// 			"$set": bson.M{
// 				"updated_by": payload.UpdatedBy,
// 				"updated_at": payload.UpdatedAt,
// 			},
// 		}

// 		if payload.AdaccountIDs != nil && len(*payload.AdaccountIDs) > 0 {
// 			if err := usc.adAccRepo.UpdateManyAdAccountRepo(sessCtx, bson.M{"account_id": bson.M{"$in": payload.AdaccountIDs}}, updateData); err != nil {
// 				return nil, fmt.Errorf("%w: %v", ErrUpdateAdAccount, err)
// 			}
// 		}

// 		if payload.PageIDs != nil && len(*payload.PageIDs) > 0 {
// 			if err := usc.pageRepo.UpdateManyPageRepo(sessCtx, bson.M{"page_id": bson.M{"$in": payload.PageIDs}}, updateData); err != nil {
// 				return nil, fmt.Errorf("%w: %v", ErrUpdatePage, err)
// 			}
// 		}

// 		if payload.PixelIDs != nil && len(*payload.PixelIDs) > 0 {
// 			if err := usc.pixelRepo.UpdateManyPixelRepo(sessCtx, bson.M{"id": bson.M{"$in": payload.PixelIDs}}, updateData); err != nil {
// 				return nil, fmt.Errorf("%w: %v", ErrUpdatePixel, err)
// 			}
// 		}

// 		if payload.CatalogueIDs != nil && len(*payload.CatalogueIDs) > 0 {
// 			if err := usc.catalogueRepo.UpdateManyCatalogueRepo(sessCtx, bson.M{"catalogue_id": bson.M{"$in": payload.CatalogueIDs}}, updateData); err != nil {
// 				return nil, fmt.Errorf("%w: %v", ErrUpdateCatalogue, err)
// 			}
// 		}

// 		return nil, nil
// 	}

// 	// Thực thi transaction
// 	_, err = session.WithTransaction(ctx, callback)
// 	if err != nil {
// 		return fmt.Errorf("transaction failed: %w", err)
// 	}

// 	return nil
// }

// func (usc *apiClientUsc) UpsertUserToMultipleCollectionsWithTransactionApiUsc(ctx context.Context, payload requests.UpdateUserIdClientIdFacebookResource) error {

// 	session, err := usc.mongo.Client().StartSession()
// 	if err != nil {
// 		return fmt.Errorf("failed to start session: %w", err)
// 	}
// 	defer session.EndSession(ctx)

// 	callback := func(sessCtx mongo.SessionContext) (interface{}, error) {
// 		updateData := bson.M{
// 			"$addToSet": bson.M{
// 				"list_user_ids": bson.M{"$each": payload.ListUserIDs},
// 				"client_ids":    bson.M{"$each": payload.ClientIDs},
// 			},
// 			"$set": bson.M{
// 				"updated_by": payload.UpdatedBy,
// 				"updated_at": payload.UpdatedAt,
// 			},
// 		}

// 		if payload.AdaccountIDs != nil && len(*payload.AdaccountIDs) > 0 {
// 			if err := usc.adAccRepo.UpdateManyAdAccountRepo(sessCtx, bson.M{"account_id": bson.M{"$in": payload.AdaccountIDs}}, updateData); err != nil {
// 				return nil, fmt.Errorf("%w: %v", ErrUpdateAdAccount, err)
// 			}
// 		}

// 		if payload.PageIDs != nil && len(*payload.PageIDs) > 0 {
// 			if err := usc.pageRepo.UpdateManyPageRepo(sessCtx, bson.M{"page_id": bson.M{"$in": payload.PageIDs}}, updateData); err != nil {
// 				return nil, fmt.Errorf("%w: %v", ErrUpdatePage, err)
// 			}
// 		}

// 		if payload.PixelIDs != nil && len(*payload.PixelIDs) > 0 {
// 			if err := usc.pixelRepo.UpdateManyPixelRepo(sessCtx, bson.M{"id": bson.M{"$in": payload.PixelIDs}}, updateData); err != nil {
// 				return nil, fmt.Errorf("%w: %v", ErrUpdatePixel, err)
// 			}
// 		}

// 		if payload.CatalogueIDs != nil && len(*payload.CatalogueIDs) > 0 {
// 			if err := usc.catalogueRepo.UpdateManyCatalogueRepo(sessCtx, bson.M{"catalogue_id": bson.M{"$in": payload.CatalogueIDs}}, updateData); err != nil {
// 				return nil, fmt.Errorf("%w: %v", ErrUpdateCatalogue, err)
// 			}
// 		}

// 		return nil, nil
// 	}

// 	// Thực thi transaction
// 	_, err = session.WithTransaction(ctx, callback)
// 	if err != nil {
// 		return fmt.Errorf("transaction failed: %w", err)
// 	}

// 	return nil
// }

type updateTarget struct {
	filter     bson.M
	collection string
}

/**
 * Update Facebook Resources
 */

func (usc *apiClientUsc) updateFBResources(ctx context.Context, updates []updateTarget, updateData bson.M, isDisconnect bool) error {
	for _, upd := range updates {
		if len(upd.filter) == 0 {
			continue
		}

		var err error
		switch upd.collection {
		case "adaccount":
			err = usc.adAccRepo.UpdateManyAdAccountRepo(ctx, upd.filter, updateData)
		case "page":
			err = usc.pageRepo.UpdateManyPageRepo(ctx, upd.filter, updateData)
		case "pixel":
			err = usc.pixelRepo.UpdateManyPixelRepo(ctx, upd.filter, updateData)
		case "catalogue":
			err = usc.catalogueRepo.UpdateManyCatalogueRepo(ctx, upd.filter, updateData)
		}

		if err != nil {
			return fmt.Errorf("update %s failed: %w", upd.collection, err)
		}
	}
	return nil
}

/**
 * Disconnect Facebook Resource
 */
func (usc *apiClientUsc) DisconnectFBResource(ctx context.Context, payload requests.UpdateUserIdClientIdFacebookResource) error {
	disconnect := payload.Disconnect
	if disconnect == nil {
		return nil
	}

	emptyUpdate := bson.M{
		"$set": bson.M{
			"updated_by":    payload.UpdatedBy,
			"updated_at":    payload.UpdatedAt,
			"client_ids":    []string{},
			"list_user_ids": []string{},
		},
	}

	updates := []updateTarget{}

	if disconnect.AdaccountIDs != nil && len(*disconnect.AdaccountIDs) > 0 {
		updates = append(updates, updateTarget{
			filter:     bson.M{"account_id": bson.M{"$in": disconnect.AdaccountIDs}},
			collection: "adaccount",
		})
	}

	if disconnect.PageIDs != nil && len(*disconnect.PageIDs) > 0 {
		updates = append(updates, updateTarget{
			filter:     bson.M{"page_id": bson.M{"$in": disconnect.PageIDs}},
			collection: "page",
		})
	}

	if disconnect.PixelIDs != nil && len(*disconnect.PixelIDs) > 0 {
		updates = append(updates, updateTarget{
			filter:     bson.M{"id": bson.M{"$in": disconnect.PixelIDs}},
			collection: "pixel",
		})
	}

	if disconnect.CatalogueIDs != nil && len(*disconnect.CatalogueIDs) > 0 {
		updates = append(updates, updateTarget{
			filter:     bson.M{"catalogue_id": bson.M{"$in": disconnect.CatalogueIDs}},
			collection: "catalogue",
		})
	}

	return usc.updateFBResources(ctx, updates, emptyUpdate, true)
}

/**
 * connect Facebook Resource
 */
func (usc *apiClientUsc) UpsertUserToMultipleCollectionsWithTransactionApiUsc(ctx context.Context, payload requests.UpdateUserIdClientIdFacebookResource) error {
	if payload.Disconnect != nil {
		if err := usc.DisconnectFBResource(ctx, payload); err != nil {
			return err
		}
	}

	updateSet := bson.M{
		"updated_by": payload.UpdatedBy,
		"updated_at": payload.UpdatedAt,
		"client_ids": payload.ClientIDs,
	}

	if payload.ListUserIDs != nil && len(*payload.ListUserIDs) > 0 {
		updateSet["list_user_ids"] = payload.ListUserIDs
	} else {
		updateSet["list_user_ids"] = []string{}
	}

	updateData := bson.M{"$set": updateSet}

	updates := []updateTarget{}

	if payload.AdaccountIDs != nil && len(*payload.AdaccountIDs) > 0 {
		updates = append(updates, updateTarget{
			filter:     bson.M{"account_id": bson.M{"$in": payload.AdaccountIDs}},
			collection: "adaccount",
		})
	}

	if payload.PageIDs != nil && len(*payload.PageIDs) > 0 {
		updates = append(updates, updateTarget{
			filter:     bson.M{"page_id": bson.M{"$in": payload.PageIDs}},
			collection: "page",
		})
	}

	if payload.PixelIDs != nil && len(*payload.PixelIDs) > 0 {
		updates = append(updates, updateTarget{
			filter:     bson.M{"id": bson.M{"$in": payload.PixelIDs}},
			collection: "pixel",
		})
	}

	if payload.CatalogueIDs != nil && len(*payload.CatalogueIDs) > 0 {
		updates = append(updates, updateTarget{
			filter:     bson.M{"catalogue_id": bson.M{"$in": payload.CatalogueIDs}},
			collection: "catalogue",
		})
	}

	return usc.updateFBResources(ctx, updates, updateData, false)
}

/* ---------------------------- TIKTOK RESOURCE ---------------------------- */

/**
 * Update Facebook Resources
 */

func (usc *apiClientUsc) updateTikTokResources(ctx context.Context, updates []updateTarget, updateData bson.M, isDisconnect bool) error {
	for _, upd := range updates {
		if len(upd.filter) == 0 {
			continue
		}

		var err error
		switch upd.collection {
		case "advertiser":
			err = usc.tikAdvertiserRepo.UpdateManyAdvertiserRepo(ctx, upd.filter, updateData)
			if err != nil {
				return fmt.Errorf("update %s failed: %w", upd.collection, err)
			}
		case "preset_column":
			err = usc.tikPresetColRepo.UpdateManyPresetColumnRepo(ctx, upd.filter, updateData)
			if err != nil {
				return fmt.Errorf("update %s failed: %w", upd.collection, err)
			}
		}
	}
	return nil
}

/**
 * Disconnect Tiktok Resource
 */
func (usc *apiClientUsc) DisconnectTikTokResource(ctx context.Context, payload requests.UpdateUserIdClientIdTiktokResource) error {
	disconnect := payload.Disconnect
	if disconnect == nil {
		return nil
	}

	emptyUpdate := bson.M{
		"$set": bson.M{
			"updated_by":    payload.UpdatedBy,
			"updated_at":    payload.UpdatedAt,
			"client_ids":    []string{},
			"list_user_ids": []string{},
		},
	}

	updates := []updateTarget{}

	if disconnect.AdaccountIDs != nil && len(*disconnect.AdaccountIDs) > 0 {
		updates = append(updates, updateTarget{
			filter:     bson.M{"advertiser_id": bson.M{"$in": disconnect.AdaccountIDs}},
			collection: "advertiser",
		})
	}

	// Mapping preset column data
	if disconnect.PresetColumnIDs != nil && len(*disconnect.PresetColumnIDs) > 0 {
		updates = append(updates, updateTarget{
			filter:     bson.M{"_id": bson.M{"$in": disconnect.PresetColumnIDs}},
			collection: "preset_column",
		})
	}

	return usc.updateTikTokResources(ctx, updates, emptyUpdate, true)
}

/**
 * connect Tiktok Resource
 */
func (usc *apiClientUsc) UpsertUserToMultipleCollectionsWithTransactionTikTokApiUsc(ctx context.Context, payload requests.UpdateUserIdClientIdTiktokResource) error {
	if payload.Disconnect != nil {
		if err := usc.DisconnectTikTokResource(ctx, payload); err != nil {
			return err
		}
	}

	updateSet := bson.M{
		"updated_by": payload.UpdatedBy,
		"updated_at": payload.UpdatedAt,
		"client_ids": payload.ClientIDs,
	}

	if payload.ListUserIDs != nil && len(*payload.ListUserIDs) > 0 {
		updateSet["list_user_ids"] = payload.ListUserIDs
	} else {
		updateSet["list_user_ids"] = []string{}
	}

	updateData := bson.M{"$set": updateSet}

	updates := []updateTarget{}

	// Mapping addacount data
	if payload.AdaccountIDs != nil && len(*payload.AdaccountIDs) > 0 {
		updates = append(updates, updateTarget{
			filter:     bson.M{"advertiser_id": bson.M{"$in": payload.AdaccountIDs}},
			collection: "advertiser",
		})
	}

	// Mapping preset column data
	if payload.PresetColumnIDs != nil && len(*payload.PresetColumnIDs) > 0 {
		updates = append(updates, updateTarget{
			filter:     bson.M{"_id": bson.M{"$in": payload.PresetColumnIDs}},
			collection: "preset_column",
		})
	}

	return usc.updateTikTokResources(ctx, updates, updateData, false)
}

// Backup Method
// func (usc *apiClientUsc) DisconnectFBResource(ctx context.Context, payload requests.UpdateUserIdClientIdFacebookResource) error {

// 	disconnect := payload.Disconnect

// 	updateData := bson.M{
// 		"$set": bson.M{
// 			"updated_by":    payload.UpdatedBy,
// 			"updated_at":    payload.UpdatedAt,
// 			"client_ids":    []string{},
// 			"list_user_ids": []string{},
// 		},
// 	}

// 	if disconnect.AdaccountIDs != nil && len(*disconnect.AdaccountIDs) > 0 {
// 		filterAdaccount := bson.M{"account_id": bson.M{"$in": disconnect.AdaccountIDs}}
// 		if err := usc.adAccRepo.UpdateManyAdAccountRepo(ctx, filterAdaccount, updateData); err != nil {
// 			return err
// 		}
// 	}

// 	if disconnect.PageIDs != nil && len(*disconnect.PageIDs) > 0 {
// 		filterPage := bson.M{"page_id": bson.M{"$in": disconnect.PageIDs}}
// 		if err := usc.pageRepo.UpdateManyPageRepo(ctx, filterPage, updateData); err != nil {
// 			return err
// 		}
// 	}

// 	if disconnect.PixelIDs != nil && len(*disconnect.PixelIDs) > 0 {
// 		filterPixel := bson.M{"id": bson.M{"$in": disconnect.PixelIDs}}
// 		if err := usc.pixelRepo.UpdateManyPixelRepo(ctx, filterPixel, updateData); err != nil {
// 			return err
// 		}
// 	}

// 	if disconnect.CatalogueIDs != nil && len(*disconnect.CatalogueIDs) > 0 {
// 		filterCatalogue := bson.M{"catalogue_id": bson.M{"$in": disconnect.CatalogueIDs}}
// 		if err := usc.catalogueRepo.UpdateManyCatalogueRepo(ctx, filterCatalogue, updateData); err != nil {
// 			return err
// 		}
// 	}

// 	return nil
// }

// Connect Facebook Resource
// func (usc *apiClientUsc) UpsertUserToMultipleCollectionsWithTransactionApiUsc(ctx context.Context, payload requests.UpdateUserIdClientIdFacebookResource) error {

// 	if payload.Disconnect != nil {
// 		err := usc.DisconnectFBResource(ctx, payload)
// 		if err != nil {
// 			return err
// 		}
// 	}

// 	filterAdaccount := bson.M{"account_id": bson.M{"$in": payload.AdaccountIDs}}
// 	filterPage := bson.M{"page_id": bson.M{"$in": payload.PageIDs}}
// 	filterPixel := bson.M{"id": bson.M{"$in": payload.PixelIDs}}
// 	filterCatalogue := bson.M{"catalogue_id": bson.M{"$in": payload.CatalogueIDs}}

// 	updateData := bson.M{
// 		"$set": bson.M{
// 			"updated_by": payload.UpdatedBy,
// 			"updated_at": payload.UpdatedAt,
// 			"client_ids": payload.ClientIDs,
// 		},
// 	}

// 	if payload.ListUserIDs != nil && len(*payload.ListUserIDs) > 0 {
// 		updateData["$set"].(bson.M)["list_user_ids"] = payload.ListUserIDs
// 	} else {
// 		updateData["$set"].(bson.M)["list_user_ids"] = []string{}
// 	}

// 	if payload.AdaccountIDs != nil && len(*payload.AdaccountIDs) > 0 {
// 		if err := usc.adAccRepo.UpdateManyAdAccountRepo(ctx, filterAdaccount, updateData); err != nil {
// 			return err
// 		}
// 	}

// 	if payload.PageIDs != nil && len(*payload.PageIDs) > 0 {
// 		if err := usc.pageRepo.UpdateManyPageRepo(ctx, filterPage, updateData); err != nil {
// 			return err
// 		}
// 	}

// 	if payload.PixelIDs != nil && len(*payload.PixelIDs) > 0 {
// 		if err := usc.pixelRepo.UpdateManyPixelRepo(ctx, filterPixel, updateData); err != nil {
// 			return err
// 		}
// 	}

// 	if payload.CatalogueIDs != nil && len(*payload.CatalogueIDs) > 0 {
// 		if err := usc.catalogueRepo.UpdateManyCatalogueRepo(ctx, filterCatalogue, updateData); err != nil {
// 			return err
// 		}
// 	}

// 	return nil
// }
