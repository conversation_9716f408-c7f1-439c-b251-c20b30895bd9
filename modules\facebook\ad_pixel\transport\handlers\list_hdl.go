package handlers

import (
	"godsp/conf"
	"godsp/pkg/gos/utils"

	"github.com/gofiber/fiber/v2"
)

func (h *adPixelHdl) ListAdPixelHandler() fiber.Handler {
	return func(c *fiber.Ctx) error {
		user, err := utils.GetInfoUserBasic(c.Context())
		if err != nil && user.RoleName != conf.SysConf.RoleAdmin {
			c.Redirect("/page-permission-denied")
		}

		return c.Render("facebook/ad_pixel/index", fiber.Map{})
	}
}
