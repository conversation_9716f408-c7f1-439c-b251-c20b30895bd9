package mongo

import (
	"context"
	"errors"
	"godsp/modules/facebook/ad/entity"
	adSetEntity "godsp/modules/facebook/adset/entity"

	"godsp/modules/facebook/common/fberrs"
	"godsp/pkg/sctx/core"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
)

type adRepo struct {
	DB         *mongo.Database
	Collection *mongo.Collection
}

func NewAdRepo(DB *mongo.Database) *adRepo {
	return &adRepo{
		DB:         DB,
		Collection: DB.Collection(entity.AdEntity{}.CollectionName()),
	}
}

/**
 * upsert ad
 */
func (r *adRepo) UpsertAdRepo(ctx context.Context, filter bson.M, update bson.M) error {
	opts := options.Update().SetUpsert(true)
	_, err := r.Collection.UpdateOne(ctx, filter, update, opts)
	if err != nil {

		return err
	}

	return nil
}

/***
 * insert ad
 */
func (r *adRepo) InsertAdRepo(ctx context.Context, adEntity *entity.AdEntity) error {
	_, err := r.Collection.InsertOne(ctx, adEntity)

	if err != nil {
		return err
	}

	return nil
}

/***
 * update one Ad
 */
func (r *adRepo) UpdateOneAdRepo(ctx context.Context, filter interface{}, adEntity *entity.AdEntity, opts ...*options.UpdateOptions) error {
	update := bson.M{
		"$set": *adEntity,
	}
	_, err := r.Collection.UpdateOne(ctx, filter, update)
	if err != nil {
		return err
	}
	return nil
}

/**
 * FindOne ad
 */
func (r *adRepo) FindOneAdRepo(ctx context.Context, filter interface{}, opts ...*options.FindOneOptions) (*entity.AdEntity, error) {
	var ad entity.AdEntity
	err := r.Collection.FindOne(ctx, filter, opts...).Decode(&ad)
	if errors.Is(err, mongo.ErrNoDocuments) {
		return nil, core.ErrNotFound
	}

	// Fetch the adset related to this ad
	adsetCollection := r.DB.Collection(adSetEntity.AdsetEntity{}.CollectionName())
	var adset adSetEntity.AdsetEntity
	adsetFilter := bson.M{"adset_id": ad.AdsetID}
	err = adsetCollection.FindOne(ctx, adsetFilter).Decode(&adset)
	if err != nil && !errors.Is(err, mongo.ErrNoDocuments) {
		return nil, err
	}
	ad.AdSet = &adset

	return &ad, nil
}

/***
 *
 * skip := (page * limit) - limit
	opts := &options.FindOptions{
		AllowDiskUse: &fbenums.ALLOW_DISK_USE_TRUE,
		Skip:         &skip,
		Limit:        &limit,
	}
	if sort != nil {
		opts.SetSort(sort)
	}
*/

func (r *adRepo) FindAdRepo(ctx context.Context, filter bson.M, opts ...*options.FindOptions) (*[]entity.AdEntity, error) {
	var ads []entity.AdEntity

	cursor, err := r.Collection.Find(ctx, filter, opts...)
	if err != nil {
		return nil, err
	}
	defer cursor.Close(ctx)

	if cursor == nil {
		return nil, fberrs.ErrNilCursorValue
	}

	err = cursor.All(ctx, &ads)

	if err != nil {
		return nil, err
	}

	// Fetch the associated adset for each ad
	adsetCollection := r.DB.Collection(adSetEntity.AdsetEntity{}.CollectionName())
	for i := range ads {
		adsetFilter := bson.M{"adset_id": ads[i].AdsetID}
		var adset adSetEntity.AdsetEntity
		err := adsetCollection.FindOne(ctx, adsetFilter).Decode(&adset)
		if err != nil && !errors.Is(err, mongo.ErrNoDocuments) {
			return nil, err // Return if there's an unexpected error
		}

		if err == nil {
			ads[i].AdSet = &adset // Assign the fetched adset to the ad
		}
	}

	return &ads, nil
}

/**
 * count Ad Account
 */
func (r *adRepo) CountAdRepo(ctx context.Context, filter interface{}) (int64, error) {
	return r.Collection.CountDocuments(ctx, filter)
}

func (r *adRepo) UpdateAdNameStatusRepo(ctx context.Context, filter interface{}, data bson.M) error {

	result, err := r.Collection.UpdateOne(ctx, filter, data, options.Update().SetUpsert(false))

	if err != nil {
		return err
	}

	if result.MatchedCount == 0 {
		return core.ErrNotFound
	}

	return nil

}

func (r *adRepo) DeleteAdRepo(ctx context.Context, filter interface{}) error {
	if _, err := r.Collection.DeleteMany(ctx, filter); err != nil {
		return err
	}
	return nil
}

/**
 * aggreate ad repo for detail ad with adset, campaign
 */
func (r *adRepo) AggregateAdRepo(ctx context.Context, pipeline []bson.M, opts ...*options.AggregateOptions) (*entity.AdDetailReviewEntity, error) {

	var ad entity.AdDetailReviewEntity

	cursor, err := r.Collection.Aggregate(ctx, pipeline, opts...)

	if err != nil {
		return nil, err
	}

	defer cursor.Close(ctx)

	if cursor.Next(ctx) {
		if err := cursor.Decode(&ad); err != nil {
			return nil, err
		}
	}
	// jsonData, _ := json.Marshal(ad)

	// fmt.Printf("\n -----------  adEntity  ----------- %+v \n", string(jsonData))

	if errors.Is(err, mongo.ErrNoDocuments) {
		return nil, core.ErrNotFound
	}

	return &ad, nil

	// var ad entity.AdDetailReviewEntity
	// adD := bson.M{}

	// cursor, err := r.Collection.Aggregate(ctx, pipeline, opts...)

	// if err != nil {
	// 	return nil, err
	// }

	// defer cursor.Close(ctx)

	// if cursor.Next(ctx) {
	// 	if err := cursor.Decode(&ad); err != nil {
	// 		return nil, err
	// 	}
	// }

	// if cursor.Next(ctx) {
	// 	if err := cursor.Decode(&adD); err != nil {
	// 		return nil, err
	// 	}
	// }
	// jsonData, _ := json.Marshal(adD)

	// fmt.Printf("\n -----------  adEntity  ----------- %+v \n", string(jsonData))

	// if errors.Is(err, mongo.ErrNoDocuments) {
	// 	return nil, core.ErrNotFound
	// }

	// return &ad, nil
}

func (r *adRepo) FindListTableAggregateRepo(ctx context.Context, pipeline []bson.M, opts ...*options.AggregateOptions) (*[]entity.AdEntity, error) {
	var ads []entity.AdEntity

	cursor, err := r.Collection.Aggregate(ctx, pipeline, opts...)
	if err != nil {
		return nil, err
	}
	defer cursor.Close(ctx)

	if cursor == nil {
		return nil, fberrs.ErrNilCursorValue
	}

	if err = cursor.All(ctx, &ads); err != nil {
		return nil, err
	}

	return &ads, nil
}
