package errs

import "errors"

var (
	ErrIDUserValidate                = errors.New("user not exists")
	ErrCreateUser                    = errors.New("add user fail")
	ErrDeleteUser                    = errors.New("delete user fail")
	ErrUpdateUser                    = errors.New("update user fail")
	ErrDeleteUserLoginValidate       = errors.New("you cannot delete your account")
	ErrUpdateStatusUserLoginValidate = errors.New("you cannot update your own status")
	ErrEditStatusDeleteUser          = errors.New("cannot be edited when the status is set to delete")

	ErrEmailDuplicate            = errors.New("email already exists")
	ErrBirthdayValidate          = errors.New("update birthday fail")
	ErrFirstNameValidate         = errors.New("first name is not empty")
	ErrLastNameValidate          = errors.New("last name is not empty")
	ErrNameUserValidate          = errors.New("name is not valid")
	ErrEmailValidate             = errors.New("email is not empty")
	ErrGenderValidate            = errors.New("gender not exists")
	ErrRoleIDExistsValidate      = errors.New("role not exists")
	ErrClientExistsValidate      = errors.New("client not exists")
	ErrRoleIDFormat              = errors.New("invalid role format")
	ErrAdAccountIDFormat         = errors.New("invalid ad adaccount format")
	ErrUsernameValidate          = errors.New("username is not valid")
	ErrDuplicateUsernameValidate = errors.New("a user with that username already exists")
	ErrPhoneValidate             = errors.New("please enter the correct phone number")
	ErrPasswordValidate          = errors.New("password is not valid")

	ErrListPageIdsValidate  = errors.New("list page ids is not valid")
	ErrListPixelIdsValidate = errors.New("list pixel ids is not valid")
)
