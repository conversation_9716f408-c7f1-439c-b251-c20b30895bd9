package enums

const (
	// Optimization goal types
	OPTIMIZATION_GOAL_NONE                                 = "NONE"
	OPTIMIZATION_GOAL_APP_INSTALLS                         = "APP_INSTALLS"
	OPTIMIZATION_GOAL_AD_RECALL_LIFT                       = "AD_RECALL_LIFT"
	OPTIMIZATION_GOAL_ENGAGED_USERS                        = "ENGAGED_USERS"
	OPTIMIZATION_GOAL_EVENT_RESPONSES                      = "EVENT_RESPONSES"
	OPTIMIZATION_GOAL_IMPRESSIONS                          = "IMPRESSIONS"
	OPTIMIZATION_GOAL_LEAD_GENERATION                      = "LEAD_GENERATION"
	OPTIMIZATION_GOAL_QUALITY_LEAD                         = "QUALITY_LEAD"
	OPTIMIZATION_GOAL_LINK_CLICKS                          = "LINK_CLICKS"
	OPTIMIZATION_GOAL_OFFSITE_CONVERSIONS                  = "OFFSITE_CONVERSIONS"
	OPTIMIZATION_GOAL_PAGE_LIKES                           = "PAGE_LIKES"
	OPTIMIZATION_GOAL_POST_ENGAGEMENT                      = "POST_ENGAGEMENT"
	OPTIMIZATION_GOAL_QUALITY_CALL                         = "QUALITY_CALL"
	OPTIMIZATION_GOAL_REACH                                = "REACH"
	OPTIMIZATION_GOAL_LANDING_PAGE_VIEWS                   = "LANDING_PAGE_VIEWS"
	OPTIMIZATION_GOAL_VISIT_INSTAGRAM_PROFILE              = "VISIT_INSTAGRAM_PROFILE"
	OPTIMIZATION_GOAL_VALUE                                = "VALUE"
	OPTIMIZATION_GOAL_THRUPLAY                             = "THRUPLAY"
	OPTIMIZATION_GOAL_DERIVED_EVENTS                       = "DERIVED_EVENTS"
	OPTIMIZATION_GOAL_APP_INSTALLS_AND_OFFSITE_CONVERSIONS = "APP_INSTALLS_AND_OFFSITE_CONVERSIONS"
	OPTIMIZATION_GOAL_CONVERSATIONS                        = "CONVERSATIONS"
	OPTIMIZATION_GOAL_IN_APP_VALUE                         = "IN_APP_VALUE"
	OPTIMIZATION_GOAL_MESSAGING_PURCHASE_CONVERSION        = "MESSAGING_PURCHASE_CONVERSION"
	OPTIMIZATION_GOAL_SUBSCRIBERS                          = "SUBSCRIBERS"
	OPTIMIZATION_GOAL_REMINDERS_SET                        = "REMINDERS_SET"
	OPTIMIZATION_GOAL_MEANINGFUL_CALL_ATTEMPT              = "MEANINGFUL_CALL_ATTEMPT"
	OPTIMIZATION_GOAL_PROFILE_VISIT                        = "PROFILE_VISIT"
	OPTIMIZATION_GOAL_MESSAGING_APPOINTMENT_CONVERSION     = "MESSAGING_APPOINTMENT_CONVERSION"
	OPTIMIZATION_GOAL_TWO_SECOND_CONTINUOUS_VIDEO_VIEWS    = "TWO_SECOND_CONTINUOUS_VIDEO_VIEWS"
)

var (
	OptimizationGoal = map[string]string{
		OPTIMIZATION_GOAL_NONE:                                 OPTIMIZATION_GOAL_NONE,
		OPTIMIZATION_GOAL_APP_INSTALLS:                         OPTIMIZATION_GOAL_APP_INSTALLS,
		OPTIMIZATION_GOAL_AD_RECALL_LIFT:                       OPTIMIZATION_GOAL_AD_RECALL_LIFT,
		OPTIMIZATION_GOAL_ENGAGED_USERS:                        OPTIMIZATION_GOAL_ENGAGED_USERS,
		OPTIMIZATION_GOAL_EVENT_RESPONSES:                      OPTIMIZATION_GOAL_EVENT_RESPONSES,
		OPTIMIZATION_GOAL_IMPRESSIONS:                          OPTIMIZATION_GOAL_IMPRESSIONS,
		OPTIMIZATION_GOAL_LEAD_GENERATION:                      OPTIMIZATION_GOAL_LEAD_GENERATION,
		OPTIMIZATION_GOAL_QUALITY_LEAD:                         OPTIMIZATION_GOAL_QUALITY_LEAD,
		OPTIMIZATION_GOAL_LINK_CLICKS:                          OPTIMIZATION_GOAL_LINK_CLICKS,
		OPTIMIZATION_GOAL_OFFSITE_CONVERSIONS:                  OPTIMIZATION_GOAL_OFFSITE_CONVERSIONS,
		OPTIMIZATION_GOAL_PAGE_LIKES:                           OPTIMIZATION_GOAL_PAGE_LIKES,
		OPTIMIZATION_GOAL_POST_ENGAGEMENT:                      OPTIMIZATION_GOAL_POST_ENGAGEMENT,
		OPTIMIZATION_GOAL_QUALITY_CALL:                         OPTIMIZATION_GOAL_QUALITY_CALL,
		OPTIMIZATION_GOAL_REACH:                                OPTIMIZATION_GOAL_REACH,
		OPTIMIZATION_GOAL_LANDING_PAGE_VIEWS:                   OPTIMIZATION_GOAL_LANDING_PAGE_VIEWS,
		OPTIMIZATION_GOAL_VISIT_INSTAGRAM_PROFILE:              OPTIMIZATION_GOAL_VISIT_INSTAGRAM_PROFILE,
		OPTIMIZATION_GOAL_VALUE:                                OPTIMIZATION_GOAL_VALUE,
		OPTIMIZATION_GOAL_THRUPLAY:                             OPTIMIZATION_GOAL_THRUPLAY,
		OPTIMIZATION_GOAL_DERIVED_EVENTS:                       OPTIMIZATION_GOAL_DERIVED_EVENTS,
		OPTIMIZATION_GOAL_APP_INSTALLS_AND_OFFSITE_CONVERSIONS: OPTIMIZATION_GOAL_APP_INSTALLS_AND_OFFSITE_CONVERSIONS,
		OPTIMIZATION_GOAL_CONVERSATIONS:                        OPTIMIZATION_GOAL_CONVERSATIONS,
		OPTIMIZATION_GOAL_IN_APP_VALUE:                         OPTIMIZATION_GOAL_IN_APP_VALUE,
		OPTIMIZATION_GOAL_MESSAGING_PURCHASE_CONVERSION:        OPTIMIZATION_GOAL_MESSAGING_PURCHASE_CONVERSION,
		OPTIMIZATION_GOAL_SUBSCRIBERS:                          OPTIMIZATION_GOAL_SUBSCRIBERS,
		OPTIMIZATION_GOAL_REMINDERS_SET:                        OPTIMIZATION_GOAL_REMINDERS_SET,
		OPTIMIZATION_GOAL_MEANINGFUL_CALL_ATTEMPT:              OPTIMIZATION_GOAL_MEANINGFUL_CALL_ATTEMPT,
		OPTIMIZATION_GOAL_PROFILE_VISIT:                        OPTIMIZATION_GOAL_PROFILE_VISIT,
		OPTIMIZATION_GOAL_MESSAGING_APPOINTMENT_CONVERSION:     OPTIMIZATION_GOAL_MESSAGING_APPOINTMENT_CONVERSION,
		OPTIMIZATION_GOAL_TWO_SECOND_CONTINUOUS_VIDEO_VIEWS:    OPTIMIZATION_GOAL_TWO_SECOND_CONTINUOUS_VIDEO_VIEWS,
	}
)

const (
	// Destination types
	DESTINATION_TYPE_WEBSITE                                       = "WEBSITE"
	DESTINATION_TYPE_APP                                           = "APP"
	DESTINATION_TYPE_MESSENGER                                     = "MESSENGER"
	DESTINATION_TYPE_APPLINKS_AUTOMATIC                            = "APPLINKS_AUTOMATIC"
	DESTINATION_TYPE_WHATSAPP                                      = "WHATSAPP"
	DESTINATION_TYPE_INSTAGRAM_DIRECT                              = "INSTAGRAM_DIRECT"
	DESTINATION_TYPE_FACEBOOK                                      = "FACEBOOK"
	DESTINATION_TYPE_MESSAGING_MESSENGER_WHATSAPP                  = "MESSAGING_MESSENGER_WHATSAPP"
	DESTINATION_TYPE_MESSAGING_INSTAGRAM_DIRECT_MESSENGER          = "MESSAGING_INSTAGRAM_DIRECT_MESSENGER"
	DESTINATION_TYPE_MESSAGING_INSTAGRAM_DIRECT_MESSENGER_WHATSAPP = "MESSAGING_INSTAGRAM_DIRECT_MESSENGER_WHATSAPP"
	DESTINATION_TYPE_MESSAGING_INSTAGRAM_DIRECT_WHATSAPP           = "MESSAGING_INSTAGRAM_DIRECT_WHATSAPP"
	DESTINATION_TYPE_SHOP_AUTOMATIC                                = "SHOP_AUTOMATIC"
	DESTINATION_TYPE_ON_AD                                         = "ON_AD"
	DESTINATION_TYPE_ON_POST                                       = "ON_POST"
	DESTINATION_TYPE_ON_EVENT                                      = "ON_EVENT"
	DESTINATION_TYPE_ON_VIDEO                                      = "ON_VIDEO"
	DESTINATION_TYPE_ON_PAGE                                       = "ON_PAGE"
	DESTINATION_TYPE_INSTAGRAM_PROFILE                             = "INSTAGRAM_PROFILE"
	DESTINATION_TYPE_FACEBOOK_PAGE                                 = "FACEBOOK_PAGE"
	DESTINATION_TYPE_INSTAGRAM_PROFILE_AND_FACEBOOK_PAGE           = "INSTAGRAM_PROFILE_AND_FACEBOOK_PAGE"
	DESTINATION_TYPE_CALLS                                         = "CALLS"
	DESTINATION_TYPE_ON_REMINDER                                   = "ON_REMINDER"
	DESTINATION_TYPE_PHONE_CALL                                    = "PHONE_CALL"
)

var (
	DestinationType = map[string]string{
		DESTINATION_TYPE_WEBSITE:                                       DESTINATION_TYPE_WEBSITE,
		DESTINATION_TYPE_APP:                                           DESTINATION_TYPE_APP,
		DESTINATION_TYPE_MESSENGER:                                     DESTINATION_TYPE_MESSENGER,
		DESTINATION_TYPE_APPLINKS_AUTOMATIC:                            DESTINATION_TYPE_APPLINKS_AUTOMATIC,
		DESTINATION_TYPE_WHATSAPP:                                      DESTINATION_TYPE_WHATSAPP,
		DESTINATION_TYPE_INSTAGRAM_DIRECT:                              DESTINATION_TYPE_INSTAGRAM_DIRECT,
		DESTINATION_TYPE_FACEBOOK:                                      DESTINATION_TYPE_FACEBOOK,
		DESTINATION_TYPE_MESSAGING_MESSENGER_WHATSAPP:                  DESTINATION_TYPE_MESSAGING_MESSENGER_WHATSAPP,
		DESTINATION_TYPE_MESSAGING_INSTAGRAM_DIRECT_MESSENGER:          DESTINATION_TYPE_MESSAGING_INSTAGRAM_DIRECT_MESSENGER,
		DESTINATION_TYPE_MESSAGING_INSTAGRAM_DIRECT_MESSENGER_WHATSAPP: DESTINATION_TYPE_MESSAGING_INSTAGRAM_DIRECT_MESSENGER_WHATSAPP,
		DESTINATION_TYPE_MESSAGING_INSTAGRAM_DIRECT_WHATSAPP:           DESTINATION_TYPE_MESSAGING_INSTAGRAM_DIRECT_WHATSAPP,
		DESTINATION_TYPE_SHOP_AUTOMATIC:                                DESTINATION_TYPE_SHOP_AUTOMATIC,
		DESTINATION_TYPE_ON_AD:                                         DESTINATION_TYPE_ON_AD,
		DESTINATION_TYPE_ON_POST:                                       DESTINATION_TYPE_ON_POST,
		DESTINATION_TYPE_ON_EVENT:                                      DESTINATION_TYPE_ON_EVENT,
		DESTINATION_TYPE_ON_VIDEO:                                      DESTINATION_TYPE_ON_VIDEO,
		DESTINATION_TYPE_ON_PAGE:                                       DESTINATION_TYPE_ON_PAGE,
		DESTINATION_TYPE_INSTAGRAM_PROFILE:                             DESTINATION_TYPE_INSTAGRAM_PROFILE,
		DESTINATION_TYPE_FACEBOOK_PAGE:                                 DESTINATION_TYPE_FACEBOOK_PAGE,
		DESTINATION_TYPE_INSTAGRAM_PROFILE_AND_FACEBOOK_PAGE:           DESTINATION_TYPE_INSTAGRAM_PROFILE_AND_FACEBOOK_PAGE,
		DESTINATION_TYPE_CALLS:                                         DESTINATION_TYPE_CALLS,
		DESTINATION_TYPE_ON_REMINDER:                                   DESTINATION_TYPE_ON_REMINDER,
		DESTINATION_TYPE_PHONE_CALL:                                    DESTINATION_TYPE_PHONE_CALL,
	}
)

const (
	LOWEST_COST_WITHOUT_CAP   = "LOWEST_COST_WITHOUT_CAP"
	LOWEST_COST_WITH_BID_CAP  = "LOWEST_COST_WITH_BID_CAP"
	COST_CAP                  = "COST_CAP"
	LOWEST_COST_WITH_MIN_ROAS = "LOWEST_COST_WITH_MIN_ROAS"
)

var (
	BidStrategy = map[string]string{
		LOWEST_COST_WITHOUT_CAP:   "LOWEST_COST_WITHOUT_CAP",
		LOWEST_COST_WITH_BID_CAP:  "LOWEST_COST_WITH_BID_CAP",
		COST_CAP:                  "COST_CAP",
		LOWEST_COST_WITH_MIN_ROAS: "LOWEST_COST_WITH_MIN_ROAS",
	}
)

const (
	BEHAVIORS             = "behaviors"
	INTERESTS             = "interests"
	DEMOGRAPHICS          = "demographics"
	LIFE_EVENTS           = "life_events"
	EDUCATION_MAJORS      = "education_majors"
	EDUCATION_STATUSES    = "education_statuses"
	RELATIONSHIP_STATUSES = "relationship_statuses"
	WORK_EMPLOYERS        = "work_employers"
	FAMILY_STATUSES       = "family_statuses"
	WORK_POSITIONS        = "work_positions"
	POLITICS              = "politics"
	CUSTOM_AUDIENCES      = "custom_audiences"
	EDUCATION_SCHOOLS     = "education_schools"
	INCOME                = "income"
	INDUSTRIES            = "industries"
	USER_ADCLUSTERS       = "user_adclusters"
	COLLEGE_YEARS         = "college_years"
)
