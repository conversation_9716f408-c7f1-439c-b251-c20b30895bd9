package service

import (
	"context"
	"fmt"
	"godsp/modules/email/common"
	"godsp/modules/email/ifaces"

	"gopkg.in/gomail.v2" // Import gomail
)

type EmailService struct {
	config common.EmailConfig
	dialer *gomail.Dialer // Store the dialer
}

func NewEmailService(cfg common.EmailConfig) *EmailService {
	d := gomail.NewDialer(cfg.SMTPHost, cfg.SMTPPort, cfg.Username, cfg.Password)
	return &EmailService{config: cfg, dialer: d}
}

func (s *EmailService) SendBK(email ifaces.EmailInfoSend) error {
	if s.dialer == nil {
		return fmt.Errorf("email service not properly initialized")
	}

	m := gomail.NewMessage()
	m.SetHeader("From", s.config.From) // Or m.FormatAddress(s.config.From, "Your App Name")
	m.SetHeader("To", email.To)
	m.SetHeader("Subject", email.Subject)
	m.Set<PERSON>ody("text/html", email.Body)
	if err := s.dialer.DialAndSend(m); err != nil {
		return fmt.Errorf("failed to send email to %s: with action: %s, raw: %s", email.To, email.Subject, err)
	}
	return nil
}

func (s *EmailService) Send(ctx context.Context, email ifaces.EmailInfoSend) error {
	if s.dialer == nil {
		return fmt.Errorf("email service not initialized")
	}

	done := make(chan error, 1)

	go func() {
		m := gomail.NewMessage()
		defer m.Reset()

		m.SetHeader("From", s.config.From)
		m.SetHeader("To", email.To)
		m.SetHeader("Subject", email.Subject)
		m.SetBody("text/html", email.Body)

		done <- s.dialer.DialAndSend(m)
		fmt.Println("Email sent to:", email.To, "with subject:", email.Subject)
	}()

	select {
	case err := <-done:
		if err != nil {
			return fmt.Errorf("failed to send email to %s: subject [%s], err: %w", email.To, email.Subject, err)
		}
		return nil
	case <-ctx.Done():
		return fmt.Errorf("send mail timeout or cancelled: %w", ctx.Err())
	}
}
