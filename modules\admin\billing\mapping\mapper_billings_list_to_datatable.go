package mapping

import (
	"godsp/modules/admin/billing/transport/response"
)

func MapperBillingsListToDatatable(list interface{}) *response.DataListBillingResponse {
	return nil
	//defaultData := &response.DataListBillingResponse{
	//	DataResponse: response.DataResponse{
	//		Data:   []response.BillingDataResponse{},
	//		Msg:    "",
	//		Status: "",
	//		Total:  0,
	//	},
	//}
	//if list == nil {
	//	return defaultData
	//}
	//return defaultData
	//listSlice, ok := list["data"].data.([]interface{})
	//if !ok {
	//	return defaultData
	//}
	//
	//var datas []response.BillingDataResponse
	//for _, item := range listSlice {
	//	datatable := response.BillingDataResponse{
	//		ID: item.advertiser_id,
	//		CreatedAt: item.CreatedAt,
	//		UpdatedAt: item.UpdatedAt,
	//		AdvertiserID: item["advertiser_id"],
	//		Platform: item.Platform,
	//		MClientID: item.MClientID,
	//		MCreatedBy: item.MCreatedBy,
	//		ClientID: item.ClientID,
	//		Title: item.Title,
	//		Notes: item.Notes,
	//		Description: item.Description,
	//		BillingDate: item.BillingDate,
	//		Amount: item.Amount,
	//		Balance: item.Balance,
	//		Currency: item.Currency,
	//		BillingType: item.BillingType,
	//		ServiceFee:
	//		ManagementFee
	//		OtherFee
	//		PlatformFee
	//		TechnicalFee
	//	}
	//	BillNumber
	//	BankCodeID
	//	BankCode
	//	MerchantName
	//	Status
	//	IsIpn
	//	IsActive
	//	UpdatedBy
	//	ApprovedBy
	//	ClientName
	//		//ID:          item.ID,
	//		//Method:      item.Method,
	//		//Path:        item.Path,
	//		//RouteName:   item.RouteName,
	//		//Group:       item.Group,
	//		//Module:      item.Module,
	//		//Description: item.Description,
	//		//Ordering:    item.Ordering,
	//		//IsBlock:     item.IsBlock,
	//		//IsAcp:       item.IsAcp,
	//	}
	//
	//	if item.Parent != nil {
	//		datatable.ParentName = item.Parent.RouteName
	//	}
	//
	//	if item.UserCreated != nil {
	//		datatable.UserCreated = item.UserCreated.FullName
	//	}
	//
	//	if item.UserUpdated != nil {
	//		datatable.UserUpdated = item.UserUpdated.FullName
	//	}
	//
	//	if !item.ParentID.IsZero() {
	//		datatable.ParentID = item.ParentID
	//	}
	//
	//	datas = append(datas, datatable)
	//}
	//
	//return &datas
}
