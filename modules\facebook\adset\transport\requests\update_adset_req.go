package requests

import (
	clientErrs "godsp/modules/admin/client/common/errs"
	"godsp/modules/facebook/common/fbenums"
	"godsp/modules/facebook/common/fbrules"
	"godsp/pkg/fb-marketing/fb"
	v20 "godsp/pkg/fb-marketing/marketing/v20"
	"godsp/pkg/gos/utils"
	"strings"
	"time"

	"github.com/go-playground/validator/v10"
	"go.mongodb.org/mongo-driver/bson/primitive"

	"godsp/modules/facebook/adset/common/errs"
	"godsp/modules/facebook/adset/common/rules"
)

type UpdateAdsetReq struct {
	AccountID      string   `json:"account_id" validate:"required"`
	CampaignID     string   `json:"campaign_id" validate:"required"`
	AdsetID        string   `json:"adset_id" validate:"required"`
	Name           *string  `json:"name"`
	DailyBudget    *float64 `json:"daily_budget,omitempty" validate:"omitempty,gte=0"`
	LifetimeBudget *float64 `json:"lifetime_budget,omitempty" validate:"omitempty,gte=0"`
	BidAmount      *uint64  `json:"bid_amount,omitempty" validate:"omitempty,gte=0"`
	BidStrategy    *string  `json:"bid_strategy"`
	BillingEvent   *string  `json:"billing_event,omitempty"`
	Status         *string  `json:"status"`
	// StartTime    *fb.Time       `json:"start_time"`
	EndTime                 *fb.Time       `json:"end_time,omitempty"`
	Targeting               *v20.Targeting `json:"targeting,omitempty" bson:"targeting"`
	IsBudgetScheduleEnabled bool           `json:"is_budget_schedule_enabled"`
	// FrequencyControlSpecs   []v20.FrequencyControlSpec `json:"frequency_control_specs,omitempty"`

	LifetimeMinSpendTarget *uint64 `json:"lifetime_min_spend_target,omitempty" validate:"omitempty,gte=0"`
	LifeTimeSpendCap       *uint64 `json:"lifetime_spend_cap,omitempty" validate:"omitempty,gte=0"`
	LifetimeImps           *uint64 `json:"lifetime_imps,omitempty" validate:"omitempty,gte=0"`

	DailyMinSpendTarget *uint64 `json:"daily_min_spend_target,omitempty" validate:"omitempty,gte=0"`
	DailySpendCap       *uint64 `json:"daily_spend_cap,omitempty" validate:"omitempty,gte=0"`

	UserId primitive.ObjectID `json:"-"`

	ClientIDStr string             `json:"client_id" form:"client_id"`
	ClientID    primitive.ObjectID `json:"-" form:"-"`
}

func (req *UpdateAdsetReq) Validate() []string {
	validate := validator.New()
	validate.RegisterValidation("ruleBidStrategy", rules.RuleBidStrategy)

	if req.EndTime != nil {
		rule := map[string]string{
			"EndTime": `validate:"gt_now`,
		}
		validate.RegisterStructValidationMapRules(rule, updateGtNow)
	}

	var validationErrors []string
	err := validate.Struct(req)
	if err != nil {
		for _, err := range err.(validator.ValidationErrors) {
			switch err.Field() {
			case "AccountID":
				validationErrors = append(validationErrors, errs.ErrAdsetAccountID.Error())
			case "CampaignID":
				validationErrors = append(validationErrors, errs.ErrAdsetCampaignID.Error())
			case "AdsetID":
				validationErrors = append(validationErrors, errs.ErrReloadAdsetAdsetId.Error())
			case "EndTime":
				validationErrors = append(validationErrors, errs.ErrAdsetEndTime.Error())
			case "BidStrategy":
				validationErrors = append(validationErrors, errs.ErrAdsetBidStrategy.Error())
			case "LifetimeMinSpendTarget":
				validationErrors = append(validationErrors, errs.ErrAdsetLifetimeMinSpendTarget.Error())
			case "LifeTimeSpendCap":
				validationErrors = append(validationErrors, errs.ErrAdsetLifeTimeSpendCap.Error())
			case "LifetimeBudget":
				validationErrors = append(validationErrors, errs.ErrAdsetLifetimeBudget.Error())
			case "DailyBudget":
				validationErrors = append(validationErrors, errs.ErrAdsetDailyBudget.Error())
			case "DailyMinSpendTarget":
				validationErrors = append(validationErrors, errs.ErrAdsetDailyMinSpendTarget.Error())
			case "DailySpendCap":
				validationErrors = append(validationErrors, errs.ErrAdsetDailyMinCap.Error())
			}
		}
	}

	if req.ClientIDStr == "" {
		validationErrors = append(validationErrors, clientErrs.ErrClientIdEmpty.Error())
	}

	if req.ClientIDStr != "" && !utils.ValidateObjectID(req.ClientIDStr) {
		validationErrors = append(validationErrors, clientErrs.ErrIDClientValidate.Error())
	}

	if req.ClientIDStr != "" {
		clientID, _ := primitive.ObjectIDFromHex(req.ClientIDStr)
		req.ClientID = clientID
	}

	// Kiểm tra nếu tất cả các trường không bắt buộc đều không có giá trị
	if req.Name == nil &&
		req.DailyBudget == nil &&
		req.BidAmount == nil &&
		req.BillingEvent == nil &&
		req.Status == nil &&
		req.Targeting == nil {
		validationErrors = append(validationErrors, errs.ErrAdsetUpdateFieldEmpty.Error())
	}

	if req.Name != nil && *req.Name == "" {
		validationErrors = append(validationErrors, errs.ErrAdsetName.Error())
	}

	if req.Status != nil && *req.Status != "" {
		if status, ok := fbenums.StatusFB[*req.Status]; !ok {
			validationErrors = append(validationErrors, errs.ErrAdsetStatus.Error())
		} else {
			req.Status = &status
		}
	}

	if req.Targeting != nil {
		if errs := validateTargetingAdset(req.Targeting); errs != nil {
			validationErrors = append(validationErrors, errs...)
		}
	}

	return validationErrors

}

func updateGtNow(fl validator.FieldLevel) bool {
	t, ok := fl.Field().Interface().(fb.Time)
	if !ok {
		return false
	}
	return time.Time(t).After(time.Now())
}

// validate targeting
func validateTargetingAdset(targeting *v20.Targeting) []string {
	return nil
}

type UpdateNameStatusAdsetReq struct {
	AdsetId string             `json:"adset_id" validate:"required,numeric,gte=0"`
	Name    string             `json:"name" validate:"omitempty"`
	Status  string             `json:"status"  validate:"omitempty,ruleStatus"`
	UserId  primitive.ObjectID `json:"-"`
}

func (req *UpdateNameStatusAdsetReq) Validate() []string {
	validate := validator.New()

	validate.RegisterValidation("ruleStatus", fbrules.RuleFBStatus)
	var validationErrors []string
	req.Name = strings.TrimSpace(req.Name)
	err := validate.Struct(req)
	if err != nil {
		for _, err := range err.(validator.ValidationErrors) {
			switch err.Field() {
			case "Name":
				validationErrors = append(validationErrors, errs.ErrAdsetName.Error())
			case "Status":
				validationErrors = append(validationErrors, errs.ErrAdsetStatus.Error())
			}
		}
	}
	if req.Status == "" && req.Name == "" {
		validationErrors = append(validationErrors, errs.ErrFBAdsetUpdateInvalidate.Error())
	}
	return validationErrors
}
