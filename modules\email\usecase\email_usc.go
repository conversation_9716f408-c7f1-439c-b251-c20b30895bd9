package usecase

import (
	"fmt"
	payloadtopic "godsp/modules/admin/common/payload_topic"
	"godsp/modules/email/ifaces"
	"godsp/modules/email/service"

	"github.com/dev-networldasia/dspgos/sctx"
	"github.com/dev-networldasia/dspgos/sctx/component/watermillapp/redisstream"
)

type EmailUsecase struct {
	service        *service.EmailService
	logger         sctx.Logger
	redisStreamPub redisstream.RedisStreamPublisher
	userRepo       UserRepo
	hasher         Hasher
}

func NewEmailUsecase(srv *service.EmailService, logger sctx.Logger, userRepo UserRepo, redisStreamPub redisstream.RedisStreamPublisher, hasher Hasher) *EmailUsecase {
	u := &EmailUsecase{
		service:        srv,
		logger:         logger,
		userRepo:       userRepo,
		redisStreamPub: redisStreamPub,
		hasher:         hasher,
	}

	return u
}

func (u *EmailUsecase) SendEmail(to, subject, body string) error {
	email := ifaces.EmailInfoSend{
		To:      to,
		Subject: subject,
		Body:    body,
	}

	err := u.service.SendBK(email)
	if u.logger != nil {
		u.logger.Error(err)
		return err
	}

	return nil
}

func (u *EmailUsecase) SendEmailResetPasswordUsc(payload *payloadtopic.PayloadResetPassword) error {
	subject := "Reset Password"
	body := fmt.Sprintf(`
        <h3>Hello,</h3>
        <p>Your password has been reset by the system administrator.</p>
        <p><b>New Password:</b> %s</p>
        <p>Please log in and change your password as soon as possible.</p>
        <br/>
        <p>Regards,<br/>Admin Team</p>
    `, payload.NewPassword)
	err := u.SendEmail(payload.ToEmail, subject, body)
	if err != nil {
		return err
	}
	return nil
}

func (u *EmailUsecase) SendEmailResetPasswordSubUsc(payload *payloadtopic.PayloadResetPassword) error {
	subject := "Reset Password"
	body := fmt.Sprintf(`
        <h3>Hello,</h3>
        <p>Your password has been reset by the system administrator.</p>
        <p><b>New Password:</b> %s</p>
        <p>Please log in and change your password as soon as possible.</p>
        <br/>
        <p>Regards,<br/>Admin Team</p>
    `, payload.NewPassword)
	err := u.SendEmail(payload.ToEmail, subject, body)
	if err != nil {
		return err
	}

	return nil
}
