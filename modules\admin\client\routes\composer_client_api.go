package routes

import (
	"godsp/modules/admin/client/repository/mongo"
	"godsp/modules/admin/client/transport/api"
	"godsp/modules/admin/client/usecase"
	userMongo "godsp/modules/admin/user/repository/mongo"
	adAccR "godsp/modules/facebook/ad_account/repository/mongo"
	pixelR "godsp/modules/facebook/ad_pixel/repository/mongo"
	catalogueR "godsp/modules/facebook/catalogue/repository/mongo"
	pageR "godsp/modules/facebook/pages/repository/mongo"
	tikAdverMongo "godsp/modules/tiktok/advertiser/repository/mongo"
	tikPresetColMongo "godsp/modules/tiktok/custom_column_table/repository/mongo"

	"github.com/dev-networldasia/dspgos/sctx/component/mongodb"

	"github.com/dev-networldasia/dspgos/sctx/configs"

	"github.com/dev-networldasia/dspgos/sctx"

	"github.com/gofiber/fiber/v2"
)

type ComposerClientApi interface {
	CreateClientApi() fiber.Handler
	UpdateUserApi() fiber.Handler
	DeleteOneClientApi() fiber.Handler
	ListClientsApi() fiber.Handler
	UpdateUserIdClientIdFacebookResourceApi() fiber.Handler
	UpdateUserIdClientIdTiktokResourceApi() fiber.Handler
}

func ComposerClientApiService(serviceCtx sctx.ServiceContext) ComposerClientApi {
	mongoDB := serviceCtx.MustGet(configs.KeyCompMongoDB).(mongodb.MongoComponent).GetDatabase()
	logger := serviceCtx.MustGet(configs.KeyLoggerDaily).(sctx.AppLoggerDaily).GetLogger("webfb")

	repo := mongo.NewClientRepo(mongoDB)
	pageRepo := pageR.NewPageRepo(mongoDB)
	pixelRepo := pixelR.NewAdPixelRepo(mongoDB)
	adAccRepo := adAccR.NewAdAccountRepo(mongoDB)
	catalogueRepo := catalogueR.NewCatalogueRepo(mongoDB)
	userRepo := userMongo.NewUserRepo(mongoDB)
	tikAdvertiserRepo := tikAdverMongo.NewAdvertiserRepo(mongoDB)
	tikPresetColRepo := tikPresetColMongo.NewTikTokPresetColumnTableRepo(mongoDB)

	uscParams := usecase.ApiClientUscDeps{
		Mongo:             mongoDB,
		Logger:            logger,
		Repo:              repo,
		UserRepo:          userRepo,
		AdAccRepo:         adAccRepo,
		PageRepo:          pageRepo,
		PixelRepo:         pixelRepo,
		CatalogueRepo:     catalogueRepo,
		TikAdvertiserRepo: tikAdvertiserRepo,
		TikPresetColRepo:  tikPresetColRepo,
	}

	usc := usecase.NewApiClientUsc(uscParams)
	api := api.NewClientApi(usc)

	return api
}
