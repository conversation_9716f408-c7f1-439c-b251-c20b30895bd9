package pipelines

import (
	"godsp/modules/facebook/ad/transport/requests"

	"go.mongodb.org/mongo-driver/bson"
)

func PipelineListTableAds(req *requests.ListAdTableReq, filter bson.M) []bson.M {
	pipeline := []bson.M{}

	// Join các user tạo/cập nhật
	pipeline = append(pipeline,
		bson.M{
			"$lookup": bson.M{
				"from":         "fb_campaigns",
				"localField":   "campaign_id",
				"foreignField": "campaign_id",
				"as":           "campaign",
			},
		},
		bson.M{
			"$unwind": bson.M{
				"path":                       "$campaign",
				"preserveNullAndEmptyArrays": true,
			},
		},
		bson.M{
			"$lookup": bson.M{
				"from":         "fb_adsets",
				"localField":   "adset_id",
				"foreignField": "adset_id",
				"as":           "adset",
			},
		},
		bson.M{
			"$unwind": bson.M{
				"path":                       "$adset",
				"preserveNullAndEmptyArrays": true,
			},
		},
	)

	// <PERSON>i<PERSON><PERSON> kiện lọc
	// match := bson.M{
	// 	"status": bson.M{
	// 		"$in": []string{
	// 			fbenums.FB_STATUS_ACTIVE,
	// 			fbenums.FB_STATUS_PAUSED,
	// 		},
	// 	},
	// }
	match := filter

	// if !isAdmin {
	// 	match["account_id"] = user.AdAccountId
	// 	if user.RoleName == conf.SysConf.RoleClientAdmin || user.RoleName == conf.SysConf.RoleClientAdminViewer {
	// 		match["client_id"] = user.ClientId
	// 	} else {
	// 		match["list_user_ids"] = user.UserId
	// 		match["client_id"] = user.ClientId
	// 	}
	// }

	// if req.SearchValue != nil && *req.SearchValue != "" {
	// 	match["name"] = bson.M{"$regex": *req.SearchValue, "$options": "i"}
	// }

	// if len(req.Filter.AdsetIds) > 0 {
	// 	match["adset_id"] = bson.M{"$in": req.Filter.AdsetIds}
	// } else if req.Filter.AdsetId != "" {
	// 	match["adset_id"] = req.Filter.AdsetId
	// } else if len(req.Filter.CampaignIds) > 0 {
	// 	match["campaign_id"] = bson.M{"$in": req.Filter.CampaignIds}
	// } else if req.Filter.CampaignId != "" {
	// 	match["campaign_id"] = req.Filter.CampaignId
	// }

	// if req.StartTime != nil && req.EndTime != nil {
	// 	match["updated_at"] = bson.M{"$gte": *req.StartTime, "$lte": *req.EndTime}
	// }

	// if req.Filter.Status != "" {
	// 	match["status"] = req.Filter.Status
	// }

	// if req.Filter.Name != "" {
	// 	match["name"] = bson.M{
	// 		"$regex":   req.Filter.Name,
	// 		"$options": "i",
	// 	}
	// }

	// // Thời gian (nếu có)
	// if req.StartTime != nil && req.EndTime != nil {
	// 	match["created_at"] = bson.M{
	// 		"$gte": *req.StartTime,
	// 		"$lte": *req.EndTime,
	// 	}
	// }
	// // Tìm kiếm theo từ khóa
	// if req.SearchValue != nil && *req.SearchValue != "" {
	// 	search := *req.SearchValue
	// 	match["$or"] = []bson.M{
	// 		{"loc": bson.M{"$regex": search, "$options": "i"}},
	// 		{"host": bson.M{"$regex": search, "$options": "i"}},
	// 		{"user_created.full_name": bson.M{"$regex": search, "$options": "i"}},
	// 	}
	// }

	// Áp dụng $match
	pipeline = append(pipeline, bson.M{"$match": match})

	// Sắp xếp
	sortField := "updated_time"
	if req.SortField != "" && req.SortField == "name" {
		sortField = req.SortField
	}
	sortOrder := -1
	if req.SortOrder == 1 {
		sortOrder = 1
	}
	pipeline = append(pipeline, bson.M{
		"$sort": bson.M{
			sortField: sortOrder,
		},
	})

	// Phân trang
	if req.Start > 0 {
		pipeline = append(pipeline, bson.M{
			"$skip": req.Start,
		})
	}
	if req.Length > 0 {
		pipeline = append(pipeline, bson.M{
			"$limit": req.Length,
		})
	}

	// jsonData, _ := json.MarshalIndent(pipeline, "", "  ")
	// fmt.Printf("pipeline ------->: %v\n", string(jsonData))

	return pipeline
}
