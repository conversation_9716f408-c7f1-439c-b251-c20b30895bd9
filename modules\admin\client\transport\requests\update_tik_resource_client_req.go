package requests

import (
	"time"

	"godsp/modules/admin/client/common/errs"
	"godsp/pkg/gos/utils"

	"github.com/go-playground/validator/v10"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

type UpdateUserIdClientIdTiktokResource struct {
	AdaccountIDs       *[]string `json:"adaccount_ids,omitempty" validate:"omitempty,dive,numeric,min=5,max=25"`
	PresetColumnIDsStr *[]string `json:"preset_column_ids,omitempty" validate:"omitempty,RuleStrObjectIDSlice"`

	UpdatedBy primitive.ObjectID `json:"-"`
	UpdatedAt time.Time          `json:"-"`

	ListUserIDsStr *[]string `json:"list_user_ids" validate:"omitempty,RuleStrObjectIDSlice"`
	ClientIDsStr   *[]string `json:"client_ids" validate:"omitempty,RuleStrObjectIDSlice"`

	PresetColumnIDs *[]primitive.ObjectID `json:"-"`
	ClientIDs       *[]primitive.ObjectID `json:"-"`
	ListUserIDs     *[]primitive.ObjectID `json:"-"`

	Disconnect *TiktokoResource `json:"disconnect,omitempty"`
}

type TiktokoResource struct {
	AdaccountIDs       *[]string             `json:"adaccount_ids,omitempty" validate:"omitempty,dive,numeric,min=5,max=25"`
	PresetColumnIDsStr *[]string             `json:"preset_column_ids,omitempty" validate:"omitempty,RuleStrObjectIDSlice"`
	PresetColumnIDs    *[]primitive.ObjectID `json:"-"`

	// PresetColumnIDs *[]primitive.ObjectID `json:"preset_column_ids,omitempty" validate:"omitempty,RuleStrObjectIDSlice"`
}

func (req *UpdateUserIdClientIdTiktokResource) Validate() []*string {
	validate := validator.New()
	var validationErrors []*string

	// Register custom validation
	validate.RegisterValidation("RuleStrObjectIDSlice", utils.RuleStrObjectIDSlice)

	// Validate struct
	err := validate.Struct(req)
	if err != nil {
		for _, err := range err.(validator.ValidationErrors) {
			switch err.Field() {
			case "ClientIDsStr":
				msg := errs.ErrIDClientValidate.Error()
				validationErrors = append(validationErrors, &msg)
			case "PresetColumnIDsStr":
				msg := "Invalid preset column IDs"
				validationErrors = append(validationErrors, &msg)
			case "ListUserIDsStr":
				msg := errs.ErrIdUserInvalidate.Error()
				validationErrors = append(validationErrors, &msg)
			case "AdaccountIDs":
				msg := errs.ErrAdvertiserIdNotExist.Error()
				validationErrors = append(validationErrors, &msg)
			}
		}
	}

	if len(validationErrors) == 0 {
		// Convert PresetColumnIDs
		if req.PresetColumnIDsStr != nil && len(*req.PresetColumnIDsStr) > 0 {
			presetIDs, err := utils.ConvertToObjectIDs(*req.PresetColumnIDsStr)
			if err != nil {
				msg := "Invalid preset column IDs"
				validationErrors = append(validationErrors, &msg)
			} else {
				req.PresetColumnIDs = &presetIDs
			}
		}

		if req.Disconnect != nil && req.Disconnect.PresetColumnIDsStr != nil && len(*req.Disconnect.PresetColumnIDsStr) > 0 {
			presetIDs, err := utils.ConvertToObjectIDs(*req.Disconnect.PresetColumnIDsStr)
			if err != nil {
				msg := "Invalid preset column IDs"
				validationErrors = append(validationErrors, &msg)
			} else {
				req.Disconnect.PresetColumnIDs = &presetIDs
			}
		}

		// Convert ListUserIDs
		if req.ListUserIDsStr != nil && len(*req.ListUserIDsStr) > 0 {
			userIDs, err := utils.ConvertToObjectIDs(*req.ListUserIDsStr)
			if err != nil {
				msg := errs.ErrIdUserInvalidate.Error()
				validationErrors = append(validationErrors, &msg)
			} else {
				req.ListUserIDs = &userIDs
			}
		}

		// Convert ClientIDs
		if req.ClientIDsStr != nil && len(*req.ClientIDsStr) > 0 {
			clientIDs, err := utils.ConvertToObjectIDs(*req.ClientIDsStr)
			if err != nil {
				msg := errs.ErrIDClientValidate.Error()
				validationErrors = append(validationErrors, &msg)
			} else {
				req.ClientIDs = &clientIDs
			}
		}
	}

	return validationErrors
}

// func (req *UpdateUserIdClientIdTiktokResource) Validate() []*string {
// 	validate := validator.New()
// 	var validationErrors []*string
// 	validate.RegisterValidation("RuleStrObjectIDSlice", utils.RuleStrObjectIDSlice)

// 	err := validate.Struct(req)
// 	if err != nil {
// 		for _, err := range err.(validator.ValidationErrors) {
// 			switch err.Field() {
// 			case "ClientIDsStr":
// 				errMsg := errs.ErrIDClientValidate.Error()
// 				validationErrors = append(validationErrors, &errMsg)
// 			case "PresetColumnIDs":
// 				errMsg := "Invalid preset column IDs"
// 				validationErrors = append(validationErrors, &errMsg)
// 			case "ListUserIDsStr":
// 				errMsg := errs.ErrIdUserInvalidate.Error()
// 				validationErrors = append(validationErrors, &errMsg)
// 			case "AdaccountIDs":
// 				errMsg := errs.ErrAdvertiserIdNotExist.Error()
// 				validationErrors = append(validationErrors, &errMsg)
// 			}
// 		}
// 	}
// 	if len(validationErrors) == 0 {
// 		if req.PresetColumnIDsStr != nil && len(*req.PresetColumnIDsStr) > 0 {
// 			// Convert preset column IDs
// 			presetIds, err := utils.ConvertToObjectIDs(*req.PresetColumnIDsStr)
// 			if err != nil {
// 				msg := "Invalid preset column IDs"
// 				validationErrors = append(validationErrors, &msg)
// 			} else {
// 				req.PresetColumnIDs = &presetIds
// 			}
// 		}

// 		// Convert client_ids
// 		userIds, err := utils.ConvertToObjectIDs(*req.ListUserIDsStr)
// 		if err != nil {
// 			msg := errs.ErrIDClientValidate.Error()
// 			validationErrors = append(validationErrors, &msg)
// 		} else {
// 			req.ListUserIDs = &userIds
// 		}

// 		// Convert client_ids
// 		clientIds, err := utils.ConvertToObjectIDs(*req.ClientIDsStr)
// 		if err != nil {
// 			msg := errs.ErrIDClientValidate.Error()
// 			validationErrors = append(validationErrors, &msg)
// 		} else {
// 			req.ClientIDs = &clientIds
// 		}
// 	}

// 	return validationErrors
// }
