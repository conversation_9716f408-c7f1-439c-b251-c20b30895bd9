package mapping

import (
	"godsp/modules/facebook/budget_schedule/transport/requests"
	"godsp/modules/facebook/budget_schedule/transport/responses"
)

func MapperUpdateBudgetScheduleByCampaignAdsetIdToFB(buds *requests.UpdateListScheduleReq, campAdsetId string, typeAction string) ([]*requests.CreateBudgetScheduleReq, *requests.DeleteBudgetScheduleReq, []string) {
	scheduleUpdateRemove := &requests.DeleteBudgetScheduleReq{
		HighDemandPeriodIds: []string{},
	}
	if typeAction == "campaign" {
		scheduleUpdateRemove.CampaignId = campAdsetId
	} else {
		scheduleUpdateRemove.AdsetId = campAdsetId
	}
	var schedulesUpdate []*requests.CreateBudgetScheduleReq
	var idsUpdateRemove []string
	for _, bud := range *buds {
		idsUpdateRemove = append(idsUpdateRemove, bud.HighDemandPeriodId)
		scheduleUpdateRemove.HighDemandPeriodIds = append(scheduleUpdateRemove.HighDemandPeriodIds, bud.HighDemandPeriodId)
		schedulesUpdate = append(schedulesUpdate, &requests.CreateBudgetScheduleReq{
			BudgetValue:     bud.BudgetValue,
			BudgetValueType: bud.BudgetValueType,
			TimeStart:       bud.TimeStart,
			TimeEnd:         bud.TimeEnd,
			TimeStartInt:    bud.TimeStart.Unix(),
			TimeEndInt:      bud.TimeEnd.Unix(),
		})
	}

	return schedulesUpdate, scheduleUpdateRemove, idsUpdateRemove
}
func includes(slice []string, value string) bool {
	for _, v := range slice {
		if v == value {
			return true
		}
	}
	return false
}
func MapperBudgetScheduleBKToFB(budsBK *[]responses.HighDemandPeriodEntity, idsRemove []string) *requests.CreateListBudgetScheduleReq {

	countDown := len(idsRemove)
	var schedulesCreate requests.CreateListBudgetScheduleReq
	for _, schedule := range *budsBK {
		if countDown > 0 {
			if includes(idsRemove, schedule.HighDemandPeriodID) {
				item := &requests.CreateBudgetScheduleReq{
					CampaignId:      schedule.CampaignID,
					BudgetValue:     int64(schedule.BudgetValue),
					BudgetValueType: schedule.BudgetValueType,
					TimeStart:       schedule.TimeStart,
					TimeEnd:         schedule.TimeEnd,
					TimeStartInt:    schedule.TimeStart.Unix(),
					TimeEndInt:      schedule.TimeEnd.Unix(),
				}
				schedulesCreate = append(schedulesCreate, item)
				countDown--
			}
		} else {
			break
		}
	}

	return &schedulesCreate
}
