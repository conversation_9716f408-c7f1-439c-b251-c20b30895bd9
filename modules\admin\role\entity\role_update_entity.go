package entity

import (
	"godsp/pkg/gos/utils"
	"time"

	"go.mongodb.org/mongo-driver/bson/primitive"
)

type RoleUpdate struct {
	ID        string             `json:"id"`
	Name      string             `json:"name" bson:"name"`
	RoleName  string             `json:"role_name" bson:"role_name"`
	Status    int                `json:"status" bson:"status"`
	Ordering  int64              `json:"ordering" bson:"ordering"`
	UpdatedBy primitive.ObjectID `json:"updated_by" bson:"updated_by"`
	UpdatedAt time.Time          `json:"updated_at" bson:"updated_at"`
}

func (RoleUpdate) CollectionName() string {
	return RoleEntity{}.CollectionName()
}

func (r *RoleUpdate) BeforeUpdate() {
	now := utils.TimeNowLocationHCM()
	if r.UpdatedAt.IsZero() {
		r.UpdatedAt = now
	}
	r.UpdatedAt = now
}
