package entity

import (
	"encoding/json"
	v20 "godsp/pkg/fb-marketing/marketing/v20"
	"time"

	"go.mongodb.org/mongo-driver/bson/primitive"
)

type AdCreativeEntity struct {
	ID primitive.ObjectID `json:"_id,omitempty" bson:"_id,omitempty"`

	AdCreativeID string `json:"ad_creative_id,omitempty" bson:"ad_creative_id,omitempty"`
	AccountID    string `json:"account_id,omitempty" bson:"account_id,omitempty"`
	Name         string `json:"name,omitempty" bson:"name,omitempty"`
	Status       string `json:"status,omitempty" bson:"status,omitempty"`
	ActorID      string `json:"actor_id,omitempty" bson:"actor_id,omitempty"`

	ApplinkTreatment            string `json:"applink_treatment,omitempty" bson:"applink_treatment,omitempty"`
	Body                        string `json:"body,omitempty" bson:"body,omitempty"`
	BrandedContentSponsorPageID string `json:"branded_content_sponsor_page_id,omitempty" bson:"branded_content_sponsor_page_id,omitempty"`
	CallToActionType            string `json:"call_to_action_type,omitempty" bson:"call_to_action_type,omitempty"`
	EffectiveInstagramStoryID   string `json:"effective_instagram_story_id,omitempty" bson:"effective_instagram_story_id,omitempty"`

	EffectiveObjectStoryID    string                         `json:"effective_object_story_id,omitempty" bson:"effective_object_story_id,omitempty"`
	ImageHash                 string                         `json:"image_hash,omitempty" bson:"image_hash,omitempty"`
	ImageURL                  string                         `json:"image_url,omitempty" bson:"image_url,omitempty"`
	InstagramActorID          string                         `json:"instagram_actor_id,omitempty" bson:"instagram_actor_id,omitempty"`
	InstagramPermalinkURL     string                         `json:"instagram_permalink_url,omitempty" bson:"instagram_permalink_url,omitempty"`
	InstagramStoryID          string                         `json:"instagram_story_id,omitempty" bson:"instagram_story_id,omitempty"`
	InteractiveComponentsSpec *v20.InteractiveComponentsSpec `json:"interactive_components_spec,omitempty" bson:"interactive_components_spec,omitempty"`
	LinkOgID                  string                         `json:"link_og_id,omitempty" bson:"link_og_id,omitempty"`
	LinkURL                   string                         `json:"link_url,omitempty" bson:"link_url,omitempty"`
	MessengerSponsoredMessage string                         `json:"messenger_sponsored_message,omitempty" bson:"messenger_sponsored_message,omitempty"`

	ObjectID      string `json:"object_id,omitempty" bson:"object_id,omitempty"`
	ObjectStoryID string `json:"object_story_id,omitempty" bson:"object_story_id,omitempty"`
	ObjectType    string `json:"object_type,omitempty" bson:"object_type,omitempty"`
	ObjectURL     string `json:"object_url,omitempty" bson:"object_url,omitempty"`
	ProductSetID  string `json:"product_set_id,omitempty" bson:"product_set_id,omitempty"`

	TemplateURL          string               `json:"template_url,omitempty" bson:"template_url,omitempty"`
	ThumbnailURL         string               `json:"thumbnail_url,omitempty" bson:"thumbnail_url,omitempty"`
	Title                string               `json:"title,omitempty" bson:"title,omitempty"`
	URLTags              string               `json:"url_tags,omitempty" bson:"url_tags,omitempty"`
	VideoID              string               `json:"video_id,omitempty" bson:"video_id,omitempty"`
	UsePageActorOverride bool                 `json:"use_page_actor_override,omitempty" bson:"use_page_actor_override,omitempty"`
	ImageCrops           json.RawMessage      `json:"image_crops,omitempty" bson:"image_crops,omitempty"`
	ObjectStorySpec      *v20.ObjectStorySpec `json:"object_story_spec,omitempty" bson:"object_story_spec,omitempty"`
	AssetFeedSpec        map[string]any       `json:"asset_feed_spec,omitempty" bson:"asset_feed_spec,omitempty"`

	PlatformCustomizations json.RawMessage           `json:"platform_customizations,omitempty" bson:"platform_customizations,omitempty"`
	RecommenderSettings    json.RawMessage           `json:"recommender_settings,omitempty" bson:"recommender_settings,omitempty"`
	TemplateURLSpec        json.RawMessage           `json:"template_url_spec,omitempty" bson:"template_url_spec,omitempty"`
	Adlabels               []json.RawMessage         `json:"adlabels,omitempty" bson:"adlabels,omitempty"`
	DegreesOfFreedomSpec   *v20.DegreesOfFreedomSpec `json:"degrees_of_freedom_spec,omitempty" bson:"degrees_of_freedom_spec,omitempty"`
	ContextualMultiAds     *v20.ContextualMultiAds   `json:"contextual_multi_ads,omitempty" bson:"contextual_multi_ads,omitempty"`

	CreatedBy primitive.ObjectID `json:"created_by" bson:"created_by"`
	CreatedAt time.Time          `json:"created_at" bson:"created_at"`
	UpdatedBy primitive.ObjectID `json:"updated_by" bson:"updated_by"`
	UpdatedAt time.Time          `json:"updated_at" bson:"updated_at"`

	ListUserIDs []primitive.ObjectID `json:"list_user_ids,omitempty" bson:"list_user_ids"`
	ClientID    *primitive.ObjectID  `json:"client_id,omitempty" bson:"client_id"`
}

func (AdCreativeEntity) CollectionName() string {
	return "fb_ad_creatives"
}
