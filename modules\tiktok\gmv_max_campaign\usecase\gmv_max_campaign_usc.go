package usecase

import (
	"context"
	clientE "godsp/modules/admin/client/entity"

	"github.com/dev-networldasia/dspgos/sctx"
	v13 "github.com/dev-networldasia/tiktokservice/marketing/v13"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo/options"
)

type ClientRepo interface {
	FindClientRepo(ctx context.Context, filter interface{}, opts ...*options.FindOptions) ([]*clientE.ClientEntity, error)
}

type ParamApiCampaignUsc struct {
	TiktokService *v13.Service
	Repo          GmvMaxCampaignRepo
	Logger        sctx.Logger
	ClientRepo    ClientRepo
}

type gmvMaxCampaignUsc struct {
	logger     sctx.Logger
	tiktokSv   *v13.Service
	repo       GmvMaxCampaignRepo
	clientRepo ClientRepo
}

func NewCampaignUsc(params ParamApiCampaignUsc) *gmvMaxCampaignUsc {
	return &gmvMaxCampaignUsc{
		logger:     params.Logger,
		tiktokSv:   params.TiktokService,
		repo:       params.Repo,
		clientRepo: params.ClientRepo,
	}
}

func (usc *gmvMaxCampaignUsc) GetClientList(ctx context.Context) ([]*clientE.ClientEntity, error) {
	return usc.clientRepo.FindClientRepo(ctx, bson.M{})
}
