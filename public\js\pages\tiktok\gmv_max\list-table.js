/**
 * GMV Max List Table JavaScript Module
 * Handles tab switching, table loading, and status filtering
 */

import {
    GMV_STATUS,
    getStatusDisplay
} from '/static/js/tiktok/constants/gmv-status.js';

/**
 * Initialize GMV Max List Table functionality
 */
export function initGMVMaxListTable() {
    console.log('Initializing GMV Max List Table...');

    // Initialize tab switching
    initTabSwitching();

    // Initialize search and filters
    initSearchAndFilters();

    // Load initial table
    loadCampaignTable('product');
}

/**
 * Initialize tab switching functionality
 */
function initTabSwitching() {
    const campaignTabs = document.querySelectorAll('#campaignTabs button[data-bs-toggle="tab"]');

    campaignTabs.forEach(tab => {
        tab.addEventListener('shown.bs.tab', function (event) {
            const targetTab = event.target.getAttribute('data-bs-target');
            const tabType = targetTab === '#product-gmv' ? 'product' : 'live';

            console.log(`Switching to ${tabType} GMV Max tab`);

            // Load table based on tab type
            loadCampaignTable(tabType);
        });
    });
}

/**
 * Initialize search and filter functionality
 */
function initSearchAndFilters() {
    // Product GMV Max search
    const productSearchInput = document.getElementById('productSearchInput');
    if (productSearchInput) {
        productSearchInput.addEventListener('input', function (e) {
            console.log('Product search:', e.target.value);
            handleSearch('product', e.target.value);
        });
    }

    // LIVE GMV Max search
    const liveSearchInput = document.getElementById('liveSearchInput');
    if (liveSearchInput) {
        liveSearchInput.addEventListener('input', function (e) {
            console.log('Live search:', e.target.value);
            handleSearch('live', e.target.value);
        });
    }

    // Custom dropdown status filters are handled in the template inline script
    // This ensures proper integration with the custom dropdown functionality
}

/**
 * Load campaign table based on type
 * @param {string} type - 'product' or 'live'
 */
function loadCampaignTable(type) {
    const containerId = type === 'product' ? 'product-gmv-table-container' : 'live-gmv-table-container';
    const container = document.getElementById(containerId);

    if (!container) {
        console.error(`Container ${containerId} not found`);
        return;
    }

    // Show loading state
    showLoadingState(container, type);

    // Simulate API call (replace with actual implementation)
    setTimeout(() => {
        showTablePlaceholder(container, type);

        // Trigger custom event for table loaded
        const event = new CustomEvent('gmvTableLoaded', {
            detail: {
                type: type,
                containerId: containerId
            }
        });
        document.dispatchEvent(event);
    }, 1000);
}

/**
 * Show loading state in container
 * @param {HTMLElement} container - Container element
 * @param {string} type - Table type
 */
function showLoadingState(container, type) {
    container.innerHTML = `
        <div class="text-center py-4">
            <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">Loading...</span>
            </div>
            <p class="mt-2 text-muted">Loading ${type === 'product' ? 'Product' : 'LIVE'} GMV Max campaigns...</p>
        </div>
    `;
}

/**
 * Show table placeholder
 * @param {HTMLElement} container - Container element
 * @param {string} type - Table type
 */
function showTablePlaceholder(container, type) {
    container.innerHTML = `
        <div class="text-center py-5">
            <i class="ri-table-line text-muted" style="font-size: 3rem;"></i>
            <h6 class="mt-3 text-muted">${type === 'product' ? 'Product' : 'LIVE'} GMV Max Table</h6>
            <p class="text-muted">Table content will be loaded here</p>
            <small class="text-muted">Status filter values: ${Object.values(GMV_STATUS).join(', ')}</small>
        </div>
    `;
}

/**
 * Handle search functionality
 * @param {string} type - 'product' or 'live'
 * @param {string} searchValue - Search input value
 */
function handleSearch(type, searchValue) {
    // Implement search logic here
    console.log(`Searching ${type} campaigns for: "${searchValue}"`);

    // You can add debouncing here if needed
    // debounce(() => {
    //     loadCampaignTable(type);
    // }, 300);
}



/**
 * Format campaign status for display with status-item-circle
 * @param {string} primaryStatus - Primary status value
 * @returns {string} Formatted HTML
 */
export function formatCampaignStatus(primaryStatus) {
    const displayText = getStatusDisplay(primaryStatus);
    let circleClass = 'status-item-circle';

    // Add appropriate class based on status
    switch (primaryStatus) {
        case GMV_STATUS.STATUS_DELIVERY_OK:
            circleClass += ' active';
            break;
        case GMV_STATUS.STATUS_DISABLE:
            circleClass += ' inactive';
            break;
        case GMV_STATUS.STATUS_DELETE:
            circleClass += ' deleted';
            break;
        case GMV_STATUS.STATUS_DELIVERY_NOT:
            circleClass += ' not-delivering';
            break;
        default:
            circleClass += ' not-delivering';
    }

    return `
        <span class="status-badge">
            <span class="${circleClass}"></span>
            <span>${displayText}</span>
        </span>
    `;
}

// Auto-initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', function () {
    initGMVMaxListTable();
});

// Export for external use
export default {
    initGMVMaxListTable,
    loadCampaignTable,
    formatCampaignStatus
};
