package payloadtopic

import (
	adAccountEntity "godsp/modules/facebook/ad_account/entity"

	"go.mongodb.org/mongo-driver/bson/primitive"
)

const (
	TOPIC_SEND_EMAIL_APPROVAL_TOPUP         = "topic_send_email_approval_topup"
	TOPIC_SEND_EMAIL_FULL_BALANCE_ADACCOUNT = "topic_send_email_full_balance_ad_account"
	TOPIC_SEND_EMAIL_RESET_PASSWORD         = "topic_send_email_reset_password"
	TOPIC_SEND_EMAIL_BILLING_DETAIL         = "topic_send_email_billing_detail"
	TOPIC_SEND_EMAIL_BILLING                = "topic_send_email_billing"
)

type PayloadSentEmailApprovalTopup struct {
	ID         primitive.ObjectID               `json:"_id" bson:"_id"`
	ToEmail    string                           `json:"email" bson:"email"`
	ClientName string                           `json:"client_name" bson:"client_name"`
	Company    string                           `json:"compamy" bson:"compamy"`
	Amount     int64                            `json:"amount" bson:"amount"`
	Balance    int64                            `json:"balance" bson:"balance"`
	AdAccount  *adAccountEntity.AdAccountEntity `json:"ad_account,omitempty" bson:"ad_account,omitempty"`
	ReceiptID  primitive.ObjectID               `json:"receipt_id,omitempty" bson:"receipt_id,omitempty"`
	InvoiceID  primitive.ObjectID               `json:"invoice_id,omitempty" bson:"invoice_id,omitempty"`
	Retry      int                              `json:"retry" bson:"retry"`
}

type PayloadResetPassword struct {
	ID                primitive.ObjectID `json:"_id" bson:"_id"`
	ToEmail           string             `json:"email" bson:"email"`
	FullName          string             `json:"full_name" bson:"full_name"`
	Salt              string             `json:"salt" bson:"salt"`
	OldPasswordHashed string             `json:"old_password_hashed" bson:"old_password_hashed"`
	NewPassword       string             `json:"new_password" bson:"new_password"`
	Retry             int                `json:"retry" bson:"retry"`
}

type PayloadSendEmailAdAccountFullBalance struct {
	ID             primitive.ObjectID `json:"_id" bson:"_id"`
	ToEmail        string             `json:"email" bson:"email"`
	ClientName     string             `json:"client_name" bson:"client_name"`
	Company        string             `json:"compamy" bson:"compamy"`
	AdAccountID    string             `json:"ad_account_id" bson:"ad_account_id"`
	AdAcountName   string             `json:"ad_account_name" bson:"ad_account_name"`
	CurrentBalance string             `json:"current_balance" bson:"current_balance"`
}

type PayloadUpdateBillingDetail struct {
	ToEmail       string                 `json:"email" bson:"email"`
	AdAccountInfo map[string]interface{} `json:"ad_account_info" bson:"ad_account_info"`
	ClientInfo    map[string]interface{} `json:"client_info" bson:"client_info"`
	Amount        int64                  `json:"amount" bson:"amount"`
	BillNumber    string                 `json:"bill_number" bson:"bill_number"`
	BillingID     string                 `json:"billing_id" bson:"billing_id"`
	Currency      string                 `json:"currency" bson:"currency"`
	Notes         string                 `json:"notes" bson:"notes"`
	Status        string                 `json:"status" bson:"status"`
	Retry         int                    `json:"retry" bson:"retry"`
}

type PayloadUpdateBilling struct {
	ToEmail    string                 `json:"email" bson:"email"`
	ClientInfo map[string]interface{} `json:"client_info" bson:"client_info"`
	BillNumber string                 `json:"bill_number" bson:"bill_number"`
	BillingID  string                 `json:"billing_id" bson:"billing_id"`
	Currency   string                 `json:"currency" bson:"currency"`
	Notes      string                 `json:"notes" bson:"notes"`
	Status     string                 `json:"status" bson:"status"`
	Retry      int                    `json:"retry" bson:"retry"`
}
