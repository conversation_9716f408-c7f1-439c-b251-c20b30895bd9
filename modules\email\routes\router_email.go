package routes

import (
	"github.com/dev-networldasia/dspgos/sctx"

	"github.com/gofiber/fiber/v2"
)

func SetupRoutesEmail(app *fiber.App, serviceCtx sctx.ServiceContext, midds ...fiber.Handler) {

	group := app.Group("email")
	{
		if len(midds) > 0 {
			for _, midd := range midds {
				group.Use(midd)
			}
		}

		compEmail := ComposerEmailService(serviceCtx)
		group.Post("send-reset-password", compEmail.SendEmailResetPasswordHdl()).Name("email.api.send_reset_password")

	}

	apiGroup := app.Group("api/v1/email")
	{
		if len(midds) > 0 {
			for _, midd := range midds {
				apiGroup.Use(midd)
			}
		}

		compEmailApi := ComposerEmailApiService(serviceCtx)
		apiGroup.Post("send-login-example", compEmailApi.SendEmailLoginExampleApi()).Name("email.api.send_login_example")
		apiGroup.Post("send-example", compEmailApi.SendEmailExampleApi()).Name("email.api.send_login_example")
	}
}
