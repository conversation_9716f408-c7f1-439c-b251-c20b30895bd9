package usecase

import (
	"context"
	"godsp/modules/admin/permission/common/consts"
	"godsp/modules/admin/permission/common/pipelines"
	"godsp/modules/admin/permission/entity"
	"godsp/modules/admin/permission/mapping"
	"godsp/modules/admin/permission/transport/requests"
	"godsp/modules/admin/permission/transport/responses"
	"godsp/modules/facebook/common/fbenums"

	"github.com/dev-networldasia/dspgos/sctx"

	"github.com/gofiber/fiber/v2"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
)

type ApiPermissionRepo interface {
	UpsertPermissionsRepo(ctx context.Context, permissions []mongo.WriteModel) error
	GetMaxRefreshID(ctx context.Context) (int64, error)
	FindWithPipelinePermissionRepo(ctx context.Context, pipeline []bson.M, opts ...*options.AggregateOptions) (*[]entity.PermissionEntity, error)
	FindOnePermissionRepo(ctx context.Context, filter interface{}, opts ...*options.FindOneOptions) (*entity.PermissionEntity, error)
	UpdateOnePermissionRepo(ctx context.Context, filter interface{}, permission *entity.PermissionEntity, opts ...*options.UpdateOptions) error
	UpdatePermissionRepo(ctx context.Context, filter interface{}, permission *entity.PermissionUpdate, opts ...*options.UpdateOptions) error

	DeleteManyPermissionRepo(ctx context.Context, filter interface{}, opts ...*options.DeleteOptions) (*int64, error)
}

type apiPermissionUsc struct {
	repo   ApiPermissionRepo
	logger sctx.Logger
}

func NewApiPermissionUsc(repo ApiPermissionRepo, logger sctx.Logger) *apiPermissionUsc {
	return &apiPermissionUsc{
		repo:   repo,
		logger: logger,
	}
}

/**
 * Use for refresh permissions
 * rule: router.Name = group.module.action
 * rule: router.Name = group.module.api.action
 *
 * 1. Get max refresh_id
 * 2. Upsert refresh_id+1
 * 3. Delete permissions with refresh_id = new refresh_id (route permission is deleted)
 */
func (u *apiPermissionUsc) UpsertApiPermissionsUsc(ctx context.Context, routes []fiber.Route, userId primitive.ObjectID) error {
	maxRefresh, err := u.repo.GetMaxRefreshID(ctx)
	if err != nil {
		return err
	}

	datas := mapping.MapperRefreshPermissionUpsertWriteModel(routes, userId, maxRefresh+1)
	if err := u.repo.UpsertPermissionsRepo(ctx, datas); err != nil {
		return err
	}

	filter := bson.M{
		"refresh_id": maxRefresh,
	}
	_, err = u.repo.DeleteManyPermissionRepo(ctx, filter)
	if err != nil {
		u.logger.Error(err)
		return err
	}

	return nil
}

/**
 * Use for show list permissions datatable
 */
func (u *apiPermissionUsc) FindDataTableApiPermissionUsc(ctx context.Context) (*[]responses.PermissionDataTable, error) {
	allowDiskUse := fbenums.ALLOW_DISK_USE_TRUE
	opts := &options.AggregateOptions{
		AllowDiskUse: &allowDiskUse,
	}

	pipeline := pipelines.PipelineListTableUsers()

	permissions, err := u.repo.FindWithPipelinePermissionRepo(ctx, pipeline, opts)
	if err != nil {
		u.logger.Error(err)
		return nil, err
	}

	return mapping.MapperPermissionToDatatable(permissions), nil
}

func (u *apiPermissionUsc) UpdateIsBlockApiPermissionUsc(ctx context.Context, data *requests.PayloadPermissionUpdateField) error {
	filter := bson.M{
		"_id": data.ID,
	}

	detail, err := u.repo.FindOnePermissionRepo(ctx, &filter)
	if err != nil {
		u.logger.Error(err)
		return err
	}

	if detail.IsBlock == consts.IS_BLOCK_YES {
		detail.IsBlock = consts.IS_BLOCK_NO
	} else {
		detail.IsBlock = consts.IS_BLOCK_YES
	}

	detail.UpdatedBy = data.UserID

	err = u.repo.UpdateOnePermissionRepo(ctx, filter, detail)

	if err != nil {
		u.logger.Error(err)
		return err
	}

	return nil
}

func (u *apiPermissionUsc) UpdateIsAcpApiPermissionUsc(ctx context.Context, data *requests.PayloadPermissionUpdateField) error {
	filter := bson.M{
		"_id": data.ID,
	}

	detail, err := u.repo.FindOnePermissionRepo(ctx, &filter)
	if err != nil {
		u.logger.Error(err)
		return err
	}

	if detail.IsAcp == consts.IS_ACP_FULL_ACCESS {
		detail.IsAcp = consts.IS_ACP_LIMIT_ACCESS
	} else {
		detail.IsAcp = consts.IS_ACP_FULL_ACCESS
	}

	detail.UpdatedBy = data.UserID

	err = u.repo.UpdateOnePermissionRepo(ctx, filter, detail)

	if err != nil {
		u.logger.Error(err)
		return err
	}

	return nil
}

func (u *apiPermissionUsc) UpdateApiPermissionUsc(ctx context.Context, payload *requests.PayloadPermissionUpdate) error {

	// filter := utils.Filters{
	// 	Conds: &map[string]interface{}{
	// 		"id": data.ID,
	// 	},
	// }
	// _, err := u.permissionRepo.FirstPermissionRepo(ctx, &filter)
	// if err != nil {
	// 	return aerrs.ErrIDPermission
	// }

	permission := mapping.MapperUpdatePermission(payload)
	filter := bson.M{
		"_id": payload.ID,
	}
	err := u.repo.UpdatePermissionRepo(ctx, filter, permission)
	if err != nil {
		u.logger.Error(err)
		return err
	}

	return nil
}
