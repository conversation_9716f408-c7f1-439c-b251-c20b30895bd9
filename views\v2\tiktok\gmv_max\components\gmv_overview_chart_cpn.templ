package components

import (
	"godsp/views/v2/tiktok/tableAds/components"
)

templ GmvOverviewChartCpn() {
    <div class="row mt-4 fade-in">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <div class="d-flex justify-content-between align-items-center">
                        <h5 class="card-title mb-0">Overview</h5>
                        <div class="d-flex align-items-center gap-2">
                            <small class="text-muted">(UTC+07:00) Indochina time</small>
                            @components.DateRangeFilterTable()
                        </div>
                    </div>
                    <p class="text-muted mb-0 mt-2">
                        Reporting includes GMV Max ads created on TikTok Ads Manager and TikTok
                        Seller Center.
                    </p>
                </div>
                <div class="card-body">
                    <!-- Metrics Cards -->
                    <div class="row g-3 mb-4">
                        <div class="col-lg-2 col-md-4 col-sm-6">
                            <div class="metric-card p-3 border rounded position-relative" data-metric="cost">
                                <div class="form-check position-absolute top-0 end-0 mt-2 me-2">
                                    <input class="form-check-input metric-checkbox" type="checkbox" id="costCheck"
                                        checked />
                                </div>
                                <div class="d-flex align-items-center mb-2">
                                    <h6 class="mb-0 me-2">Cost</h6>
                                    <i class="ri-information-line text-muted"></i>
                                </div>
                                <h4 class="mb-1 fw-bold">10,230,801 VND</h4>
                                <small class="text-muted">vs last 7 days</small>
                                <span class="text-danger ms-1">-31.35%</span>
                            </div>
                        </div>
                        <div class="col-lg-2 col-md-4 col-sm-6">
                            <div class="metric-card p-3 border rounded position-relative" data-metric="orders">
                                <div class="form-check position-absolute top-0 end-0 mt-2 me-2">
                                    <input class="form-check-input metric-checkbox" type="checkbox" id="ordersCheck"
                                        checked />
                                </div>
                                <div class="d-flex align-items-center mb-2">
                                    <h6 class="mb-0 me-2">Orders (Current shop)</h6>
                                    <i class="ri-information-line text-muted"></i>
                                </div>
                                <h4 class="mb-1 fw-bold">218</h4>
                                <small class="text-muted">vs last 7 days</small>
                                <span class="text-danger ms-1">-39.84%</span>
                            </div>
                        </div>
                        <div class="col-lg-2 col-md-4 col-sm-6">
                            <div class="metric-card p-3 border rounded position-relative" data-metric="cost_per_order">
                                <div class="form-check position-absolute top-0 end-0 mt-2 me-2">
                                    <input class="form-check-input metric-checkbox" type="checkbox"
                                        id="costPerOrderCheck" />
                                </div>
                                <div class="d-flex align-items-center mb-2">
                                    <h6 class="mb-0 me-2">Cost per order</h6>
                                    <i class="ri-information-line text-muted"></i>
                                </div>
                                <h4 class="mb-1 fw-bold">46,930 VND</h4>
                                <small class="text-muted">vs last 7 days</small>
                                <span class="text-success ms-1">+14.31%</span>
                            </div>
                        </div>
                        <div class="col-lg-2 col-md-4 col-sm-6">
                            <div class="metric-card p-3 border rounded position-relative" data-metric="gross_revenue">
                                <div class="form-check position-absolute top-0 end-0 mt-2 me-2">
                                    <input class="form-check-input metric-checkbox" type="checkbox"
                                        id="grossRevenueCheck" />
                                </div>
                                <div class="d-flex align-items-center mb-2">
                                    <h6 class="mb-0 me-2">Gross revenue (Current shop)</h6>
                                    <i class="ri-information-line text-muted"></i>
                                </div>
                                <h4 class="mb-1 fw-bold">48,509,851 VND</h4>
                                <small class="text-muted">vs last 7 days</small>
                                <span class="text-danger ms-1">-39.96%</span>
                            </div>
                        </div>
                        <div class="col-lg-2 col-md-4 col-sm-6">
                            <div class="metric-card p-3 border rounded position-relative" data-metric="roi">
                                <div class="form-check position-absolute top-0 end-0 mt-2 me-2">
                                    <input class="form-check-input metric-checkbox" type="checkbox" id="roiCheck" />
                                </div>
                                <div class="d-flex align-items-center mb-2">
                                    <h6 class="mb-0 me-2">ROI (Current shop)</h6>
                                    <i class="ri-information-line text-muted"></i>
                                </div>
                                <h4 class="mb-1 fw-bold">4.74</h4>
                                <small class="text-muted">vs last 7 days</small>
                                <span class="text-danger ms-1">-12.55%</span>
                            </div>
                        </div>
                    </div>
                    <!-- Chart Container -->
                    <div class="chart-container">
                        <div id="gmv_max_line_chart" class="apex-charts" dir="ltr"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>
}
