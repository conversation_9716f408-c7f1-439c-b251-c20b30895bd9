package pipelines

import (
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

func PipelineGetDetatilInfoUser(id primitive.ObjectID) []bson.M {
	pipeline := []bson.M{
		{
			"$match": bson.M{
				"_id": id,
			},
		},
		{
			"$lookup": bson.M{
				"from":         "admin_roles",
				"localField":   "role_id",
				"foreignField": "_id",
				"as":           "roles",
			},
		},
		{
			"$unwind": bson.M{
				"path":                       "$roles",
				"includeArrayIndex":          "string",
				"preserveNullAndEmptyArrays": true,
			},
		},
		{
			"$project": bson.M{
				"first_name":    1,
				"last_name":     1,
				"full_name":     1,
				"image":         1,
				"ad_account_id": 1,
				"email":         1,
				"gender":        1,
				"username":      1,
				"role_name":     "$roles.role_name",
			},
		},
	}

	return pipeline
}
