/**
 * GMV Max Campaign Status Constants - Simplified
 * Maps to backend enum values in modules/tiktok/gmv_max_campaign/common/enums/enums.go
 */

// Primary status enum values from TikTok API
export const GMV_STATUS = {
    STATUS_DELIVERY_OK: "STATUS_DELIVERY_OK",   // Active
    STATUS_DISABLE: "STATUS_DISABLE",           // Inactive
    STATUS_DELETE: "STATUS_DELETE",             // Deleted
    STATUS_DELIVERY_NOT: "STATUS_DELIVERY_NOT"  // Not delivering
};

// Status display mapping
export const GMV_STATUS_DISPLAY = {
    [GMV_STATUS.STATUS_DELIVERY_OK]: "Active",
    [GMV_STATUS.STATUS_DISABLE]: "Inactive",
    [GMV_STATUS.STATUS_DELETE]: "Deleted",
    [GMV_STATUS.STATUS_DELIVERY_NOT]: "Not delivering"
};

/**
 * Get display text for status
 * @param {string} primaryStatus - The primary status value
 * @returns {string} Display text
 */
export function getStatusDisplay(primaryStatus) {
    return GMV_STATUS_DISPLAY[primaryStatus] || "Unknown";
}
