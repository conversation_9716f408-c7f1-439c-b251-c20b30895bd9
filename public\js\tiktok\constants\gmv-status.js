/**
 * GMV Max Campaign Status Constants
 * Maps to backend enum values in modules/tiktok/gmv_max_campaign/common/enums/enums.go
 */

// Primary status enum values from TikTok API
export const GMV_STATUS = {
    STATUS_DELIVERY_OK: "STATUS_DELIVERY_OK", // Active
    STATUS_DISABLE: "STATUS_DISABLE",         // Inactive
    STATUS_DELETE: "STATUS_DELETE"            // Deleted
};

// Simple status mapping - if not in these 3 cases, it's "Not delivering"
export const GMV_STATUS_DISPLAY = {
    [GMV_STATUS.STATUS_DELIVERY_OK]: "Active",
    [GMV_STATUS.STATUS_DISABLE]: "Inactive",
    [GMV_STATUS.STATUS_DELETE]: "Deleted"
};

/**
 * Get display text for status
 * @param {string} primaryStatus - The primary status value
 * @returns {string} Display text
 */
export function getStatusDisplay(primaryStatus) {
    return GMV_STATUS_DISPLAY[primaryStatus] || "Not delivering";
}

/**
 * Get status circle class
 * @param {string} primaryStatus - The primary status value
 * @returns {string} CSS class for status circle
 */
export function getStatusCircleClass(primaryStatus) {
    switch (primaryStatus) {
        case GMV_STATUS.STATUS_DELIVERY_OK:
            return "status-item-circle active";
        case GMV_STATUS.STATUS_DISABLE:
            return "status-item-circle inactive";
        case GMV_STATUS.STATUS_DELETE:
            return "status-item-circle deleted";
        default:
            return "status-item-circle not-delivering";
    }
}

/**
 * Format status for display in tables
 * @param {string} primaryStatus - The primary status value
 * @returns {string} HTML string for status display
 */
export function formatStatusForTable(primaryStatus) {
    const displayText = getStatusDisplay(primaryStatus);
    const circleClass = getStatusCircleClass(primaryStatus);

    return `
        <span class="status-badge">
            <span class="${circleClass}"></span>
            ${displayText}
        </span>
    `;
}
