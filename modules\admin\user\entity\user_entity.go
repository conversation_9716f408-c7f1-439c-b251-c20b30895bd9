package entity

import (
	"encoding/json"
	"godsp/conf"
	"godsp/modules/admin/role/entity"
	"time"

	"go.mongodb.org/mongo-driver/bson/primitive"
)

type UserEntity struct {
	ID                 primitive.ObjectID `json:"_id,omitempty" bson:"_id,omitempty"`
	Username           *string            `json:"username,omitempty" bson:"username,omitempty"`
	Email              string             `json:"email,omitempty" bson:"email"`
	FirstName          string             `json:"first_name,omitempty" bson:"first_name"`
	LastName           string             `json:"last_name,omitempty" bson:"last_name"`
	FullName           string             `json:"full_name,omitempty" bson:"full_name"`
	Phone              *string            `json:"phone,omitempty" bson:"phone,omitempty"`
	Image              string             `json:"image,omitempty" bson:"image"`
	Gender             int                `json:"gender,omitempty" bson:"gender"`
	Birthday           *time.Time         `json:"birthday,omitempty" bson:"birthday,omitempty"`
	Status             int                `json:"status,omitempty" bson:"status"`
	Permission         json.RawMessage    `json:"permission,omitempty" bson:"permission"`
	IsPermission       int                `json:"is_permission,omitempty" bson:"is_permission"`
	AuthType           string             `json:"auth_type,omitempty" bson:"auth_type"`
	Salt               string             `json:"salt,omitempty" bson:"salt"`
	Password           string             `json:"password,omitempty" bson:"password"`
	RoleID             primitive.ObjectID `json:"role_id,omitempty" bson:"role_id"`
	AdAccountID        string             `json:"ad_account_id,omitempty" bson:"ad_account_id"`
	TiktokAdvertiserID string             `json:"tiktok_advertiser_id,omitempty" bson:"tiktok_advertiser_id"`
	CreatedBy          primitive.ObjectID `json:"created_by,omitempty" bson:"created_by" `
	UpdatedBy          primitive.ObjectID `json:"updated_by,omitempty" bson:"updated_by"`
	Role               *entity.RoleEntity `json:"role,omitempty" bson:"role,omitempty"`
	UserCreated        *UserEntity        `json:"user_created,omitempty" bson:"user_created,omitempty"`
	UserUpdated        *UserEntity        `json:"user_updated,omitempty" bson:"user_updated,omitempty"`
	Client             *ClientInfo        `json:"client,omitempty" bson:"client,omitempty"`
	CreatedAt          time.Time          `json:"created_at,omitempty" bson:"created_at"`
	UpdatedAt          time.Time          `json:"updated_at,omitempty" bson:"updated_at"`

	ListPageIds  []string `json:"list_page_ids,omitempty" bson:"list_page_ids"`
	ListPixelIds []string `json:"list_pixel_ids,omitempty" bson:"list_pixel_ids"`

	ClientID primitive.ObjectID `json:"client_id,omitempty" bson:"client_id,omitempty"`
}

type ClientInfo struct {
	ID      primitive.ObjectID `bson:"_id,omitempty" json:"id,omitempty"`
	Name    string             `json:"name" bson:"name" `
	Company string             `json:"company" bson:"company"`
	Brand   *[]string          `json:"brand" bson:"brand"`
	Email   string             `json:"email" bson:"email,omitempty"`
	Logo    string             `json:"logo,omitempty" bson:"logo,omitempty"`
}

func (UserEntity) CollectionName() string {
	return "admin_users"
}

func (u *UserEntity) PathImg() {
	if u.Image == "" {
		u.Image = "/static/" + conf.PathImgUserDefault
	}

	u.Image = "/static/" + u.Image
}
