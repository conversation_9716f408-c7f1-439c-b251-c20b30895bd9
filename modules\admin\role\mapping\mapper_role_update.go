package mapping

import (
	"godsp/modules/admin/common/admconst"
	"godsp/modules/admin/role/entity"
	"godsp/modules/admin/role/transport/requests"
)

func MapperUpdateRole(req *requests.PayloadRoleEdition) *entity.RoleUpdate {
	return &entity.RoleUpdate{
		ID:        req.ID.Hex(),
		UpdatedBy: req.UserID,
		Name:      req.Name,
		Status:    admconst.StatusFullNameValue[req.Status],
		RoleName:  req.RoleName,
		Ordering:  req.Ordering,
	}
}
