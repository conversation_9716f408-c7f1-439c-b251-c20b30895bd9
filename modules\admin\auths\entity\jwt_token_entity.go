package entity

type Token struct {
	Token     string `json:"token"`
	ExpiredIn int    `json:"expire_in"`
}

type TokenResponse struct {
	AccessToken Token `json:"access_token"`
	// RefreshToken *Token        `json:"-"`
	User        *UserResponse `json:"user"`
	RedirectUrl string        `json:"redirect_url"`
}

type UserResponse struct {
	FirstName string `json:"first_name"`
	LastName  string `json:"last_name"`
	Image     string `json:"image"`
	Email     string `json:"email"`
	RoleName  string `json:"role_name"`
	ClientId  string `json:"client_id"`
}
