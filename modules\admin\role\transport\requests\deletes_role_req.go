package requests

import (
	"context"
	"godsp/modules/admin/role/common/errs"
	"godsp/pkg/gos/utils"

	"github.com/go-playground/validator/v10"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

type PayloadRoleDeletes struct {
	Ids    []primitive.ObjectID `json:"ids" validate:"required"`
	UserId primitive.ObjectID   `json:"-"`
}

func (req *PayloadRoleDeletes) Validate(c context.Context) error {
	validate := validator.New()

	err := validate.Struct(req)
	if err != nil {
		for _, err := range err.(validator.ValidationErrors) {
			switch err.Field() {
			case "Ids":
				return errs.ErrValidateRoleID
			}
		}
	}

	userId, err := utils.GetUserIdPrimitive(c)
	if err != nil {
		return err
	}
	req.UserId = userId

	return nil
}
