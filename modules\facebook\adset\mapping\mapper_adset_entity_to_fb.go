package mapping

import (
	"godsp/modules/facebook/adset/entity"
	"godsp/pkg/fb-marketing/fb"
	v20 "godsp/pkg/fb-marketing/marketing/v20"
)

func MapperEntityToAdsetFB(adsetEntity *entity.AdsetEntity) *v20.Adset {
	return &v20.Adset{
		ID:              adsetEntity.IDAdset,
		Name:            adsetEntity.Name,
		Status:          adsetEntity.Status,
		AccountID:       adsetEntity.AccountID,
		AttributionSpec: adsetEntity.AttributionSpec,

		BidAmount:       adsetEntity.BidAmount,
		BidStrategy:     adsetEntity.BidStrategy,
		BillingEvent:    adsetEntity.BillingEvent,
		BudgetRemaining: adsetEntity.BudgetRemaining,

		CampaignID:          adsetEntity.CampaignID,
		ConfiguredStatus:    adsetEntity.ConfiguredStatus,
		CreatedTime:         fb.Time(adsetEntity.CreatedTime),
		DailyBudget:         adsetEntity.DailyBudget,
		DailyMinSpendTarget: adsetEntity.DailyMinSpendTarget,

		DailySpendCap:    adsetEntity.DailySpendCap,
		DestinationType:  adsetEntity.DestinationType,
		DeliveryEstimate: adsetEntity.DeliveryEstimate,
		EffectiveStatus:  adsetEntity.EffectiveStatus,
		// EndTime:                    fb.Time(adsetEntity.EndTime),

		FrequencyControlSpecs:  adsetEntity.FrequencyControlSpecs,
		LifetimeBudget:         adsetEntity.LifetimeBudget,
		LifetimeMinSpendTarget: adsetEntity.LifetimeMinSpendTarget,
		LifeTimeSpendCap:       adsetEntity.LifeTimeSpendCap,
		LifetimeImps:           adsetEntity.LifetimeImps,

		// OptimizationGoal:         adsetEntity.OptimizationGoal,
		PacingType: adsetEntity.PacingType,
		// PromotedObject:           adsetEntity.PromotedObject,
		RecurringBudgetSemantics: adsetEntity.RecurringBudgetSemantics,
		// StartTime:                fb.Time(adsetEntity.StartTime),

		Targeting:                  adsetEntity.Targeting,
		UpdatedTime:                fb.Time(adsetEntity.UpdatedTime),
		TargetingOptimizationTypes: adsetEntity.TargetingOptimizationTypes,
		DSABeneficiary:             adsetEntity.DSABeneficiary,
		DSAPayor:                   adsetEntity.DSAPayor,

		IsBudgetScheduleEnabled: adsetEntity.IsBudgetScheduleEnabled,

		IsDynamicCreative: adsetEntity.IsDynamicCreative,
	}
}
