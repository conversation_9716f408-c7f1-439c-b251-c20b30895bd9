package usecase

import (
	"context"
	"fmt"
	ePermission "godsp/modules/admin/permission/entity"
	"godsp/modules/admin/role/common/pipelines"
	"godsp/modules/admin/role/entity"
	"godsp/modules/facebook/common/fbenums"

	"github.com/dev-networldasia/dspgos/sctx"

	"godsp/modules/admin/common/admconst"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo/options"
)

type RoleRepo interface {
	MaxOrderingRepo(ctx context.Context, filter interface{}) (int64, error)
	FindOneRoleRepo(ctx context.Context, filter interface{}, opts ...*options.FindOneOptions) (*entity.RoleEntity, error)
}

type PermissionRepo interface {
	FindWithPipelinePermissionRepo(ctx context.Context, pipeline []bson.M, opts ...*options.AggregateOptions) (*[]ePermission.PermissionEntity, error)
}
type roleUsc struct {
	repo           RoleRepo
	permissionRepo PermissionRepo
	logger         sctx.Logger
}

func NewRoleUsc(repo RoleRepo, logger sctx.Logger, permissionRepo PermissionRepo) *roleUsc {
	return &roleUsc{
		repo:           repo,
		logger:         logger,
		permissionRepo: permissionRepo,
	}
}

/***
 * max ordering
 */
func (usc *roleUsc) MaxOrderingRoleUsc(ctx context.Context) (int64, error) {
	return usc.repo.MaxOrderingRepo(ctx, nil)
}

func (usc *roleUsc) FindOneRoleUsc(ctx context.Context, id string) (*entity.RoleEntity, error) {
	idObject, err := primitive.ObjectIDFromHex(id)
	if err != nil {
		return nil, err
	}
	filter := bson.M{
		"_id":    idObject,
		"status": bson.M{"$ne": admconst.STATUS_DELETE},
	}

	data, err := usc.repo.FindOneRoleRepo(ctx, filter)
	if err != nil {
		msg := fmt.Sprintf("Err FindOneRoleUsc FindOneRoleRepo: %s", err.Error())
		usc.logger.Error(msg)
		return nil, err
	}

	return data, nil
}

func (usc *roleUsc) FindGroupModulePermissionRoleUsc(ctx context.Context) (*[]ePermission.PermissionEntity, error) {
	allowDiskUse := fbenums.ALLOW_DISK_USE_TRUE
	opts := &options.AggregateOptions{
		AllowDiskUse: &allowDiskUse,
	}

	pipeline := pipelines.PipelineGetModulesByGroup()

	data, err := usc.permissionRepo.FindWithPipelinePermissionRepo(ctx, pipeline, opts)
	if err != nil {
		usc.logger.Error(err)
		return nil, err
	}

	return data, nil
}
