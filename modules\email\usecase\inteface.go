package usecase

import (
	"context"
	userEnt "godsp/modules/admin/user/entity"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo/options"
)

type UserRepo interface {
	UpdateOneUserByBsonMRepo(ctx context.Context, filter interface{}, data bson.M, opts ...*options.UpdateOptions) error
	FindOneUserWithPipelineRepo(ctx context.Context, pipeline []bson.M, opts ...*options.AggregateOptions) (*userEnt.UserEntity, error)
}

type Hasher interface {
	RandomStr(length int) (string, error)
	HashPassword(salt, password string) (string, error)
	CompareHashPassword(hashedPassword, salt, password string) bool
}
