package routes

import (
	"github.com/dev-networldasia/dspgos/sctx"

	"github.com/gofiber/fiber/v2"
)

func SetupRoutesHomePages(app *fiber.App, serviceCtx sctx.ServiceContext, midds ...fiber.Handler) {
	group := app.Group("")
	{
		if len(midds) > 0 {
			for _, midd := range midds {
				group.Use(midd)
			}
		}

		compPage := ComposerPageService(serviceCtx)
		group.Get("/admins", compPage.HomePageHdl()).Name("admins.pages.home")
		group.Get("/", func(c *fiber.Ctx) error {
			return c.Redirect("/admins", fiber.StatusFound)
		}).Name("admins.pages.home_page")
	}
}
