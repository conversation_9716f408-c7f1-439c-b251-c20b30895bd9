package entity

import (
	"time"

	"go.mongodb.org/mongo-driver/bson/primitive"
)

type AdPixelEntity struct {
	ID        primitive.ObjectID `json:"_id,omitempty" bson:"_id,omitempty"`
	AccountID string             `json:"account_id" bson:"account_id"`
	PixelID   string             `json:"id" bson:"id"`
	Name      string             `json:"name" bson:"name"`

	CreatedBy primitive.ObjectID `json:"created_by" bson:"created_by"`
	CreatedAt time.Time          `json:"created_at" bson:"created_at"`
	UpdatedBy primitive.ObjectID `json:"updated_by" bson:"updated_by"`
	UpdatedAt time.Time          `json:"updated_at" bson:"updated_at"`

	ListUserIDs []primitive.ObjectID `json:"list_user_ids,omitempty" bson:"list_user_ids"`
	ClientIDs   []primitive.ObjectID `json:"client_ids,omitempty" bson:"client_ids"`
}

func (AdPixelEntity) CollectionName() string {
	return "fb_ad_pixels"
}
