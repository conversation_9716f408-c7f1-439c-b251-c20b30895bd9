package handlers

import (
	"context"
	"godsp/modules/admin/billing/common/consts"
	"godsp/modules/admin/billing/transport/response"
	clientE "godsp/modules/admin/client/entity"
	"godsp/modules/admin/common/admconst"
	"godsp/modules/admin/common/admfunc"
	adAccE "godsp/modules/facebook/ad_account/entity"
	"godsp/pkg/gos/utils"
	"godsp/pkg/sctx/core"

	"github.com/gofiber/fiber/v2"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

type BillingUsc interface {
	MaxOrderingBillingUsc(ctx context.Context) (int64, error)
	FindOneBillingUsc(ctx context.Context, id string) (*response.BillingDataTable, error)
	ListAdAccountOfClientUsc(ctx context.Context, clientId primitive.ObjectID) ([]*adAccE.AdAccountEntity, error)
	ListClientUsc(ctx context.Context) ([]*clientE.ClientEntity, error)
}

type billingHdl struct {
	usc BillingUsc
}

func NewBillingHdl(usc BillingUsc) *billingHdl {
	return &billingHdl{
		usc: usc,
	}
}

/**
 * List billing hdl
 */
func (h *billingHdl) ListBillingHdl() fiber.Handler {
	return func(c *fiber.Ctx) error {
		roleGroup, err := admfunc.GetRoleGroup(c.Context(), nil)
		if err != nil {
			return err
		}

		var defaultStatus string
		if roleGroup == admconst.ROLE_GROUP_ADMIN {
			defaultStatus = consts.BILLING_STATUS_TYPES[consts.BILLING_STATUS_COMPLETED].Prefix
		}

		userInfo, err := utils.GetInfoUserAuth(c.Context())
		if err != nil {
			c.Redirect("/page-permission-denied")
		}

		clients, _ := h.usc.ListClientUsc(c.Context())

		return c.Render("admins/billings/index", fiber.Map{
			"authPermission":    core.GetPermission(c.Context()).GetPermissions(),
			"defaultImg":        consts.DEFAULT_IMG_ADMIN,
			"defaultStatus":     defaultStatus,
			"roleName":          roleGroup,
			"isSystemRoleGroup": roleGroup != admconst.ROLE_GROUP_CLIENT,
			"statuses":          consts.BILLING_STATUS_TYPES,
			"columns":           consts.BILLING_COLUMNS_BY_ROLE[roleGroup],
			"userInfo":          userInfo,
			"clients":           clients,
		})
	}
}

/**
 * Create billing hdl
 */
func (h *billingHdl) CreateBillingHdl() fiber.Handler {
	return func(c *fiber.Ctx) error {
		clientId := utils.GetClientIdPrimitive(c.Context())

		if clientId == nil || clientId.IsZero() {
			return c.Redirect("page-permission-denied", fiber.StatusForbidden)
		}

		adAccounts, err := h.usc.ListAdAccountOfClientUsc(c.Context(), *clientId)
		if err != nil {
			return core.ReturnErrForApi(c, err.Error())
		}

		ordering, err := h.usc.MaxOrderingBillingUsc(c.Context())
		if err != nil {
			return err
		}

		userInfo, err := utils.GetInfoUserAuth(c.Context())
		if err != nil {
			c.Redirect("/page-permission-denied")
		}

		return c.Render("admins/billings/create", fiber.Map{
			"authPermission": core.GetPermission(c.Context()).GetPermissions(),
			"ordering":       ordering + 1,
			"adAccounts":     adAccounts,
			"status":         admconst.StatusCreateName,
			"userInfo":       userInfo,
		})
	}
}

/**
 * Edit billing hdl
 */
func (h *billingHdl) EditBillingHdl() fiber.Handler {
	return func(c *fiber.Ctx) error {
		id := c.Params("billingId")
		billing, err := h.usc.FindOneBillingUsc(c.Context(), id)
		if err != nil {
			return err
		}

		userInfo, err := utils.GetInfoUserAuth(c.Context())
		if err != nil {
			c.Redirect("/page-permission-denied")
		}

		roleGroup, err := admfunc.GetRoleGroup(c.Context(), nil)
		if err != nil {
			return err
		}

		if roleGroup == admconst.ROLE_GROUP_CLIENT && billing.MClientID != userInfo.ClientId.Hex() {
			c.Redirect("/page-permission-denied")
		}

		return c.Render("admins/billings/edit", fiber.Map{
			"authPermission":    core.GetPermission(c.Context()).GetPermissions(),
			"billing":           billing,
			"status":            admconst.StatusFullName,
			"isSystemRoleGroup": roleGroup != admconst.ROLE_GROUP_CLIENT,
			"isAdmin":           userInfo.RoleName == admconst.ROLE_ADMIN,
			"isChecking":        billing.IsCheckingStatus(),
			"userInfo":          userInfo,
			"statusLabelHtml":   billing.GetStatusLabelHtml(),
			"activeLabelHtml":   billing.GetActiveLabelHtml(),
			"ipnLabelHtml":      billing.GetIpnLabelHtml(),
		})
	}
}
