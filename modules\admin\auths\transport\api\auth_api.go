package api

import (
	"context"
	"godsp/conf"
	"godsp/modules/admin/auths/entity"
	"godsp/modules/admin/auths/transport/requests"
	"godsp/pkg/sctx/core"
	"net/http"
	"time"

	"github.com/dev-networldasia/dspgos/sctx/configs"

	"github.com/gofiber/fiber/v2"
)

type ApiAuthUsc interface {
	ChangePasswordAuthApiUsc(ctx context.Context, payload *requests.PayloadAuthChangePassword) error
	LoginAuthUsc(ctx context.Context, payload *requests.PayloadAuthLogin) (*entity.TokenResponse, error)
	ResetPasswordAuthApiUsc(ctx context.Context, payload *requests.PayloadAuthResetPassword) error
}
type authApi struct {
	usc ApiAuthUsc
}

func NewAuthApi(usc ApiAuthUsc) *authApi {
	return &authApi{
		usc: usc,
	}
}

/**
 * Method: POST
 * Login auth
 * URI: /auth/login
 */

func (a *authApi) LoginApiHdl() fiber.Handler {
	return func(c *fiber.Ctx) error {
		var payload requests.PayloadAuthLogin
		if err := c.Body<PERSON>er(&payload); err != nil {
			return core.ReturnErrForApi(c, err.Error())
		}

		//validate
		validationErrors := payload.Validate()
		if len(validationErrors) > 0 {
			return core.ReturnErrsForApi(c, validationErrors)
		}

		response, err := a.usc.LoginAuthUsc(c.Context(), &payload)
		if err != nil {
			return core.WriteErrorResponse(c, err)
		}

		isPrd := configs.AppEnv == configs.AppProd
		cookie := fiber.Cookie{
			Name:     "brx_token",
			Value:    response.AccessToken.Token,
			Expires:  time.Now().Add(time.Duration(response.AccessToken.ExpiredIn * int(time.Second))),
			HTTPOnly: true,
			Secure:   isPrd,
			SameSite: "Lax",
		}

		c.Cookie(&cookie)

		if response.User != nil && response.User.Image == "" {
			response.User.Image = "/static/" + conf.PathImgUserDefault
		}
		return c.Status(http.StatusOK).JSON(core.ResponseData(response))
		// return c.Status(http.StatusOK).JSON(core.ResponseData(map[string]interface{}{
		// 	"message":  "Create client successfully!",
		// 	"redirect": "/admins/clients/list",
		// }))
		// return c.Status(http.StatusOK).JSON(core.ResponseData(response))
	}
}

/**
 * Change Password
 */
func (a *authApi) ChangePassworddApi() fiber.Handler {
	return func(c *fiber.Ctx) error {
		var payload requests.PayloadAuthChangePassword
		if err := c.BodyParser(&payload); err != nil {
			return core.ReturnErrForApi(c, err.Error())
		}

		//validate
		validationErrors := payload.Validate()
		if len(validationErrors) > 0 {
			return core.ReturnErrsForApi(c, validationErrors)
		}

		err := a.usc.ChangePasswordAuthApiUsc(c.Context(), &payload)
		if err != nil {
			return core.ReturnErrsForApi(c, err.Error())
		}

		// isPrd := configs.AppEnv == configs.AppProd
		// cookie := fiber.Cookie{
		// 	Name:     "brx_token",
		// 	Value:    response.AccessToken.Token,
		// 	Expires:  time.Now().Add(time.Duration(response.AccessToken.ExpiredIn * int(time.Second))),
		// 	HTTPOnly: true,
		// 	Secure:   isPrd,
		// 	SameSite: "Lax",
		// }

		// c.Cookie(&cookie)
		// roleName := utils.GetRoleName(c.Context())
		// redirect := "/login"
		// if roleName != nil && *roleName == "ADMIN" {
		// 	redirect = ""
		// }

		return c.Status(http.StatusOK).JSON(core.ResponseData(map[string]interface{}{
			"msg": "Change password successfully.",
			// "data": bson.M{
			// 	"redirect": redirect,
			// },
		}))
	}
}

/**
 * Change Password
 */
func (a *authApi) ResetPassworddApi() fiber.Handler {
	return func(c *fiber.Ctx) error {
		var payload requests.PayloadAuthResetPassword
		if err := c.BodyParser(&payload); err != nil {
			return core.ReturnErrForApi(c, err.Error())
		}

		//validate
		validationErrors := payload.Validate()
		if len(validationErrors) > 0 {
			return core.ReturnErrsForApi(c, validationErrors)
		}

		err := a.usc.ResetPasswordAuthApiUsc(c.Context(), &payload)
		if err != nil {
			return core.ReturnErrsForApi(c, err.Error())
		}

		return c.Status(http.StatusOK).JSON(core.ResponseData(map[string]interface{}{
			"msg": "Reset password successfully.",
		}))
	}
}
