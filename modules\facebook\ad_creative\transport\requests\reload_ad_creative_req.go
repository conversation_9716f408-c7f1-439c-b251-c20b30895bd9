package requests

import (
	"godsp/modules/facebook/ad_creative/common/errs"

	"github.com/go-playground/validator/v10"
)

type ReloadAdCreativeReq struct {
	CreativeID string `json:"creative_id,omitempty"`
	AccountID  string `json:"account_id,omitempty"`
}

func (req *ReloadAdCreativeReq) Validate() []string {
	validate := validator.New()
	var validationErrors []string

	if req.AccountID == "" && req.CreativeID == "" {
		validationErrors = append(validationErrors, errs.ErrReloadAdCreativeAdAccountIdCreativeId.Error())
		return validationErrors
	}

	err := validate.Struct(req)
	if err != nil {
		for _, err := range err.(validator.ValidationErrors) {
			switch err.Field() {
			case "AccountID":
				validationErrors = append(validationErrors, errs.ErrReloadAdCreativeAdAccountId.Error())
			case "CreativeID":
				validationErrors = append(validationErrors, errs.ErrReloadAdCreativeCreativeId.Error())
			}
		}
	}

	return validationErrors
}
