package mapping

import (
	"fmt"
	"godsp/conf"
	"godsp/modules/admin/user/entity"
	"godsp/modules/admin/user/transport/responses"
	"godsp/pkg/gos/goconst"
	"godsp/pkg/gos/utils"
)

/***
 * Mapper user entity to datatable
 */
func MapperUserToList(users *[]entity.UserEntity) *[]responses.ListUser {
	if users == nil {
		return nil
	}

	var datas []responses.ListUser
	for _, user := range *users {
		img := fmt.Sprintf("/static/%s", conf.PathImgUserDefault)
		if user.Image != "" {
			img = fmt.Sprintf("/static/%s", user.Image)
		}
		userDatatable := responses.ListUser{
			ID:       user.ID,
			Email:    user.Email,
			Image:    img,
			FullName: user.FullName,
			Phone:    user.Phone,
			Username: user.Username,
			Status:   user.Status,
			Gender:   user.Gender,
			// RoleName:  consts.UNKNOWN_ROLE,
			// CreatedAt: utils.FormatTimeToString(user.CreatedAt, goconst.YYYY_MM_DD_HH_MM),
		}

		// if user.CreatedAt != user.UpdatedAt {
		// 	userDatatable.UpdatedAt = utils.FormatTimeToString(user.UpdatedAt, goconst.YYYY_MM_DD_HH_MM)
		// }

		if user.Phone != nil {
			userDatatable.Phone = user.Phone
		}

		if user.Username != nil {
			userDatatable.Username = user.Username
		}

		if user.Birthday != nil {
			birthday := utils.FormatTimeToString(*user.Birthday, goconst.DD_MM_YYYY)
			userDatatable.Birthday = &birthday
		}

		// if user.UserCreated != nil {
		// 	userDatatable.UserCreated = user.UserCreated.FullName
		// }

		// if user.UserUpdated != nil {
		// 	userDatatable.UserUpdated = user.UserUpdated.FullName
		// }

		if user.Role != nil {
			userDatatable.RoleName = user.Role.RoleName
		}

		datas = append(datas, userDatatable)
	}

	return &datas
}
