package mapping

import (
	"encoding/json"
	"fmt"
	"godsp/modules/admin/client/entity"
	"godsp/modules/admin/client/transport/requests"
	"godsp/modules/admin/common/admconst"
)

func MapperUpdateClient(req *requests.PayloadClientUpdating) *entity.ClientUpdateEntity {
	data := &entity.ClientUpdateEntity{
		Name:    req.Name,
		Company: req.Company,
		Brand:   req.Brand,
		Email:   req.Email,
		Status:  admconst.StatusCreateNameValue[req.Status],

		Phone:    req.Phone,
		Domain:   req.Domain,
		Position: req.Position,
		Address:  req.Address,

		AdAccountIDs:  req.AdAccountIDs,
		ClientUserIDs: req.ClientUserIDs,

		UpdatedBy: req.UserID,
	}

	if req.Logo != "" {
		data.Logo = req.Logo
	}

	data.BeforeUpdate()
	jsonData, _ := json.MarshalIndent(data, "", "  ")
	fmt.Printf("MapperUpdateClient ------->: %v\n", string(jsonData))

	return data
}
