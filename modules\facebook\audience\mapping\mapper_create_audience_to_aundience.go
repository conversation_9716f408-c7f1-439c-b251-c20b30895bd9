package mapping

import (
	"godsp/modules/facebook/audience/entity"
	"godsp/modules/facebook/audience/transport/requests"
	v20 "godsp/pkg/fb-marketing/marketing/v20"
	"time"

	"go.mongodb.org/mongo-driver/bson/primitive"
)

func MapperCreateAudienceReqToAundience(req *requests.CreateCustomAudienceReq, userId primitive.ObjectID) (*entity.CustomAudienceEntity, *v20.CustomAudience) {
	audienceEntity := entity.CustomAudienceEntity{
		Name:        req.Name,
		AccountID:   req.AccountID,
		Description: req.Description,
		Rule:        req.Rule,

		ClientID:  &req.ClientID,
		CreatedBy: req.CreatedBy,
		CreatedAt: time.Now(),
	}

	audienceFB := v20.CustomAudience{
		Name:        req.Name,
		AccountID:   req.AccountID,
		Description: req.Description,
		Rule:        req.Rule,
	}

	return &audienceEntity, &audienceFB

}
