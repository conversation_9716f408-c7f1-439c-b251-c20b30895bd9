package requests

import (
	"time"

	"github.com/go-playground/validator/v10"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

type UpdateCustomAudienceReq struct {
	AudienceID  string `json:"audience_id,omitempty"`
	Name        string `json:"name,omitempty" bson:"name"`
	Description string `json:"description,omitempty" bson:"description"`
	Rule        string `json:"rule,omitempty" bson:"rule"`
	Subtype     string `json:"subtype,omitempty" bson:"subtype,omitempty"`

	AccountID string             `json:"-"`
	ClientID  primitive.ObjectID `json:"-"`
	UpdatedBy primitive.ObjectID `json:"-"`
	UpdatedAt time.Time          `json:"-"`
}

type UpdateCustomAudienceFB struct {
	Name        string `json:"name,omitempty"`
	Description string `json:"description,omitempty"`
	Rule        string `json:"rule,omitempty"`
	UpdateTime  int64  `json:"time_updated,omitempty"`
}

func (req *UpdateCustomAudienceReq) Validate() []string {
	validate := validator.New()
	var validationErrors []string

	err := validate.Struct(req)
	if err != nil {
		for _, err := range err.(validator.ValidationErrors) {
			fieldName := err.Field()

			switch fieldName {
			case "AudienceID":
				validationErrors = append(validationErrors, "AudienceID is required")
			case "AccountID":
				validationErrors = append(validationErrors, "AccountID is required")
			case "Name":
				validationErrors = append(validationErrors, "Name must be between 3 and 100 characters")
			case "Description":
				validationErrors = append(validationErrors, "Description must be at most 255 characters")
			case "Rule":
				validationErrors = append(validationErrors, "Rule must be a valid JSON string")
			case "Subtype":
				validationErrors = append(validationErrors, "Subtype must be a valid value")
			default:
				validationErrors = append(validationErrors, "Invalid value for field: "+fieldName)
			}
		}
	}

	return validationErrors
}
