package requests

import (
	"godsp/modules/facebook/campaign/common/errs"

	"github.com/go-playground/validator/v10"
)

type ApproveCampaignReq struct {
	CampaignID string `json:"campaign_id"`
	AccountID  string `json:"account_id"`
	Name       string `json:"name"`
	// Status     string `json:"status"`
	// Approve    campE.ApproveStatus `json:"approve"`
}

type ApproveManyCampaignReq struct {
	Campaigns []ApproveCampaignReq `json:"campaigns"`
}

func (req *ApproveManyCampaignReq) Validate() []string {
	validate := validator.New()
	var validationErrors []string

	err := validate.Struct(req)
	if err != nil {
		for _, err := range err.(validator.ValidationErrors) {
			switch err.Field() {
			case "CampaignID":
				validationErrors = append(validationErrors, errs.ErrCampaignID.Error())
				return validationErrors
			case "AccountID":
				validationErrors = append(validationErrors, errs.ErrCampaignAccountID.Error())
			case "Name":
				validationErrors = append(validationErrors, errs.ErrCampaignName.Error())
			case "Status":
				validationErrors = append(validationErrors, errs.ErrCampaignStatus.Error())
			}
		}
	}

	return validationErrors
}
