package mapping

import (
	"fmt"
	v20 "godsp/pkg/fb-marketing/marketing/v20"
	"time"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

func MapperHighDemandPeriodFBUpsertReload(period *v20.HighDemandPeriod, userId primitive.ObjectID, userIds []primitive.ObjectID, budgetId string, typeAction string) bson.M {
	now := time.Now()
	fmt.Printf("\n -----------  mapping  ----------- %+v \n", period)
	updateData := bson.M{
		"high_demand_period_id": period.ID,
		"budget_value":          period.BudgetValue,
		"budget_value_type":     period.BudgetValueType,
		"recurrence_type":       period.RecurrenceType,
		"time_start":            time.Time(period.TimeStart),
		"time_end":              time.Time(period.TimeEnd),
		"updated_by":            userId,
		"updated_at":            now,
		"list_user_ids":         userIds,
	}
	if typeAction == "campaign" {
		updateData["campaign_id"] = budgetId
	} else {
		updateData["adset_id"] = budgetId
	}

	createData := bson.M{
		"created_by": userId,
		"created_at": now,
	}

	return bson.M{
		"$set":         updateData,
		"$setOnInsert": createData,
	}
}

// utils.ConvertIntToTime(period.TimeStart),
// 		"time_end":              utils.ConvertIntToTime(period.TimeEnd),
