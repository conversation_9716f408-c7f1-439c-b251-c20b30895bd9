package api

import (
	"godsp/modules/facebook/campaign/transport/requests"
	"godsp/pkg/gos/utils"
	"godsp/pkg/sctx/core"
	"net/http"

	"github.com/gofiber/fiber/v2"
)

/***
 * Create camp api
 */

func (a *campaignApi) DeleteCampaignApi() fiber.Handler {
	return func(c *fiber.Ctx) error {
		var payload requests.DeleteCampaignReq

		// payload.AccountID = conf.FBConf.Act
		payload.AccountID = *utils.GetAdAccount(c.Context())

		if err := c.BodyParser(&payload); err != nil {
			return core.ReturnErrsForApi(c, err)
		}

		//validate
		validationErrors := payload.Validate()

		if len(validationErrors) > 0 {
			return core.ReturnErrsForApi(c, validationErrors)
		}

		userId, err := utils.GetUserId(c.Context())
		if err != nil {
			return core.ReturnErrsForApi(c, err)
		}

		payload.UserId = userId

		removeIds, err := a.usc.DeleteCampaignUsc(c.Context(), &payload)
		if err != nil {
			return core.ReturnErrsForApi(c, err)
		}

		return c.Status(http.StatusOK).JSON(core.ResponseData(map[string]interface{}{
			"msg":  "Delete Campaign successfully",
			"data": removeIds,
		}))
	}
}
