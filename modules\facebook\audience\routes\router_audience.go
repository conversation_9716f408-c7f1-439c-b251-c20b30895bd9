package routes

import (
	"github.com/dev-networldasia/dspgos/sctx"

	"github.com/gofiber/fiber/v2"
)

func SetupRoutesAudience(app *fiber.App, serviceCtx sctx.ServiceContext, midds ...fiber.Handler) {
	group := app.Group("dsp/facebook/audiences")
	{
		if len(midds) > 0 {
			for _, midd := range midds {
				group.Use(midd)
			}
		}

		comp := ComposerAudienceService(serviceCtx)
		group.Get("/list", comp.ListAudienceHandler()).Name("fb.audience.list")
	}

	apiGroup := app.Group("/dsp/facebook/api/audiences")
	{
		if len(midds) > 0 {
			for _, midd := range midds {
				apiGroup.Use(midd)
			}
		}

		compApi := ComposerAudienceApiService(serviceCtx)
		apiGroup.Patch("reload", compApi.ReloadAudienceApi()).Name("fb.audience.api.reload")
		apiGroup.Post("list", compApi.ListAudiencesApi()).Name("fb.audience.api.list")
		apiGroup.Post("/list-table", compApi.ListTableAudienceApi()).Name("fb.audience.list_table")

		apiGroup.Post("/details", compApi.GetDetailAudienceApi()).Name("fb.audience.api.detailAudience")
		apiGroup.Post("/create-custom-audience", compApi.CreateCustomAudienceApi()).Name("fb.audience.api.create-custom-audience")
		apiGroup.Post("/update-custom-audience", compApi.UpdateCustomAudienceApi()).Name("fb.audience.api.update-custom-audience")
		apiGroup.Delete("/delete-custom-audience", compApi.DeleteCustomAudienceApi()).Name("fb.audience.api.delete-custom-audience")

	}
}
