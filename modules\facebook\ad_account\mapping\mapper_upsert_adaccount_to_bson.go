package mapping

import (
	v20 "godsp/pkg/fb-marketing/marketing/v20"
	"time"

	"go.mongodb.org/mongo-driver/bson"
)

func MapperUpsertAdAccountToBson(adAccountFb *v20.AdAccount) bson.M {

	updateAt, _ := time.Parse(time.RFC3339, time.Now().Format(time.RFC3339))
	userId := 1

	updateData := bson.M{
		"name":          adAccountFb.Name,
		"currency":      adAccountFb.Currency,
		"timezone_name": adAccountFb.TimeZoneName,
		"updated_at":    updateAt,
		"updated_by":    userId,
	}

	createData := bson.M{
		"created_at": updateAt,
		"created_by": userId,
	}

	data := bson.M{
		"$set":         updateData,
		"$setOnInsert": createData,
	}

	return data
}
