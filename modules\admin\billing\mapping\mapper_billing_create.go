package mapping

import (
	"encoding/json"
	"godsp/conf"
	"godsp/modules/admin/billing/common/consts"
	"godsp/modules/admin/billing/model"
	"godsp/modules/admin/billing/transport/requests"
	"godsp/pkg/gos/utils"
	"os"
	"strconv"

	"go.mongodb.org/mongo-driver/bson/primitive"
)

func MapperCreateBilling(
	req *requests.PayloadBillingCreation,
	userId primitive.ObjectID,
	clientId primitive.ObjectID,
) model.PayloadCreatePaymentVTB {
	timeNow := utils.TimeNowLocationHCM()
	locale := os.Getenv("PAYMENT_LOCALE_VN")
	if req.Currency == "USD" {
		locale = os.Getenv("PAYMENT_LOCALE_EN")
	}

	var advertiserIDs []int64
	for _, accStr := range req.AdAccount { // req.AdAccount is now []string
		id, err := strconv.ParseInt(accStr, 10, 64)
		if err == nil { // Consider more robust error handling here
			advertiserIDs = append(advertiserIDs, id)
		} else {
			// Log error or handle as per business logic (e.g., skip, return error)
			// fmt.Printf("Warning: Could not parse ad account ID '%s' to int64: %v\n", accStr, err)
		}
	}

	return model.PayloadCreatePaymentVTB{
		AdvertiserId:      advertiserIDs,
		ClientId:          int64(0),
		PaymentAmount:     req.Amount,
		PaymentMethod:     conf.PaymentMethod,
		PaymentTitle:      conf.PaymentBankCodeId,
		PaymentLocale:     locale,
		PaymentOrderInfo:  req.Description,
		PaymentBankCode:   conf.VTBMerchantCode,
		PaymentBankCodeId: conf.PaymentBankCodeId,
		PaymentBillNumber: "",
		PayLoad:           conf.PaymentPayload,
		PointOIMethod:     conf.PaymentPointMethod,
		MasterMerchant:    conf.VTBMasterMerchant,
		MerchantCC:        conf.VTBMerchantCC,
		Ccy:               conf.PaymentCCY,
		CountryCode:       conf.PaymentCoutryCode,
		MerchantName:      conf.VTBMerchantName,
		MerchantCity:      conf.VTBMerchantCity,
		PinCode:           conf.PaymentPinCode,
		StoreID:           conf.VTBStoreID,
		TerminalID:        conf.VTBTerminalID,
		CurrencyCode:      req.Currency,
		PaymentIpAddr:     "127.0.0.1",
		MCreatedBy:        userId.Hex(),
		Platform:          consts.PLATFORM_FB,
		MClientId:         clientId.Hex(),
		ServiceFee:        getDefaultServiceFee(),
		CreatedAt:         timeNow,
		CreatedBy:         userId.Hex(),
	}
}

func getDefaultServiceFee() string {
	serviceFees := map[string]int64{
		"platform_fee":   10,
		"management_fee": 15,
		"technical_fee":  5,
		"other_fee":      2,
	}

	serviceFeeBytes, err := json.Marshal(serviceFees)
	if err != nil {
		return "{}" // Or an empty string ""
	}
	return string(serviceFeeBytes)
}
