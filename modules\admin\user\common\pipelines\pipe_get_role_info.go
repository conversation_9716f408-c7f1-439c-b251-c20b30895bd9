package pipelines

import (
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

func PipelineGetRoleInfo(id primitive.ObjectID) []bson.M {
	pipeline := []bson.M{
		{
			"$match": bson.M{
				"_id": id,
			},
		},
		{
			"$lookup": bson.M{
				"from":         "admin_roles",
				"localField":   "role_id",
				"foreignField": "_id",
				"as":           "roles",
			},
		},
		{
			"$unwind": bson.M{
				"path":                       "$roles",
				"includeArrayIndex":          "string",
				"preserveNullAndEmptyArrays": true,
			},
		},
		{
			"$project": bson.M{
				"_id":       "$roles._id",
				"role_name": "$roles.role_name",
			},
		},
	}
	return pipeline
}
