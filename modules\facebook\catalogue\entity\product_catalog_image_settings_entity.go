package entity

type ProductCatalogImageSettingsEntity struct {
	CarouselAd *ProductCatalogImageSettingsOperation `json:"carousel_ad,omitempty" bson:"carousel_ad,omitempty"`
	SingleAd   *ProductCatalogImageSettingsOperation `json:"single_ad,omitempty" bson:"single_ad,omitempty"`
}

type ProductCatalogImageSettingsOperation struct {
	TransformationType string `json:"transformation_type,omitempty" bson:"transformation_type,omitempty"`
}
