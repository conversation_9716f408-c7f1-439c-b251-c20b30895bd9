package mapping

import (
	"encoding/json"
	"fmt"
	"godsp/modules/facebook/adset/entity"
	v20 "godsp/pkg/fb-marketing/marketing/v20"
	"time"

	"go.mongodb.org/mongo-driver/bson/primitive"
)

func MapperAdsetFBToEntity(adset *v20.Adset, userId primitive.ObjectID) *entity.AdsetEntity {
	now := time.Now()
	adsetUpsert := &entity.AdsetEntity{
		ID: primitive.NewObjectID(),

		IDAdset:             adset.ID,
		Name:                adset.Name,
		Status:              adset.Status,
		AccountID:           adset.AccountID,
		AttributionSpec:     adset.AttributionSpec,
		BidAmount:           adset.BidAmount,
		BidStrategy:         adset.BidStrategy,
		BillingEvent:        adset.BillingEvent,
		BudgetRemaining:     adset.BudgetRemaining,
		// Campaign:            adset.Campaign,
		CampaignID:          adset.CampaignID,
		ConfiguredStatus:    adset.ConfiguredStatus,
		CreatedTime:         time.Time(adset.CreatedTime),
		DailyBudget:         adset.DailyBudget,
		DailyMinSpendTarget: adset.DailyMinSpendTarget,
		DailySpendCap:       adset.DailySpendCap,
		DestinationType:     adset.DestinationType,
		DeliveryEstimate:    adset.DeliveryEstimate,
		EffectiveStatus:     adset.EffectiveStatus,
		// EndTime:                    time.Time(adset.EndTime),
		FrequencyControlSpecs:      adset.FrequencyControlSpecs,
		LifetimeBudget:             adset.LifetimeBudget,
		LifetimeMinSpendTarget:     adset.LifetimeMinSpendTarget,
		LifeTimeSpendCap:           adset.LifeTimeSpendCap,
		LifetimeImps:               adset.LifetimeImps,
		OptimizationGoal:           adset.OptimizationGoal,
		PacingType:                 adset.PacingType,
		PromotedObject:             adset.PromotedObject,
		RecurringBudgetSemantics:   adset.RecurringBudgetSemantics,
		StartTime:                  time.Time(*adset.StartTime),
		Targeting:                  adset.Targeting,
		UpdatedTime:                time.Time(adset.UpdatedTime),
		TargetingOptimizationTypes: adset.TargetingOptimizationTypes,
		DSABeneficiary:             adset.DSABeneficiary,
		DSAPayor:                   adset.DSAPayor,
		IsDynamicCreative:          adset.IsDynamicCreative,

		IsBudgetScheduleEnabled: adset.IsBudgetScheduleEnabled,

		CreatedBy: userId,
		CreatedAt: now,
		UpdatedAt: now,
		UpdatedBy: userId,
	}

	fmt.Printf("adset FB ------->: %v\n", *adset.EndTime)

	if adset.EndTime != nil {
		endTime := time.Time(*adset.EndTime)
		adsetUpsert.EndTime = &endTime
	}

	jsonData, _ := json.MarshalIndent(adsetUpsert, "", "  ")
	fmt.Printf("adsetUpsert ------->: %v\n", string(jsonData))

	return adsetUpsert
}
