package internal

import (
	payloadtopic "godsp/modules/admin/common/payload_topic"
	"godsp/modules/email/routes"

	"github.com/dev-networldasia/dspgos/sctx/component/watermillapp/redisstream"

	"github.com/dev-networldasia/dspgos/sctx"
)

func SetEmailHandlerConsumer(sc sctx.ServiceContext) map[string]redisstream.NoPublishHandlerFunc {
	comp := routes.ComposerEmailSubService(sc)

	handlers := map[string]redisstream.NoPublishHandlerFunc{
		payloadtopic.TOPIC_SEND_EMAIL_RESET_PASSWORD:         comp.SendEmailResetPasswordSub(),
		payloadtopic.TOPIC_SEND_EMAIL_APPROVAL_TOPUP:         comp.SendEmailNotifyApprovalTopupSub(),
		payloadtopic.TOPIC_SEND_EMAIL_FULL_BALANCE_ADACCOUNT: comp.SendEmailNotifyFullBalanceAdAccountSub(),
		payloadtopic.TOPIC_SEND_EMAIL_BILLING_DETAIL:         comp.SendEmailBillingDetailSub(),
		payloadtopic.TOPIC_SEND_EMAIL_BILLING:                comp.SendEmailBillingSub(),
	}

	return handlers
}
