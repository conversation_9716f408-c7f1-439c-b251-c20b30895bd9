package requests

import (
	"godsp/modules/admin/common/admerrs"

	"github.com/go-playground/validator/v10"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

type PayloadBillingEdition struct {
	ID       primitive.ObjectID `json:"id,omitempty"`
	Status   int                `json:"status" `
	Note     string             `json:"note,omitempty"`
	Ordering int64              `json:"ordering,omitempty" validate:"gt=0"`

	UserID primitive.ObjectID `json:"-"`
}

func (req *PayloadBillingEdition) Validate() []*string {
	validate := validator.New()
	var validationErrors []*string
	err := validate.Struct(req)
	if err != nil {
		for _, err := range err.(validator.ValidationErrors) {
			switch err.Field() {
			// case "Amount":
			// 	errName := errs.ErrAmountBillingValidate.Error()
			// 	validationErrors = append(validationErrors, &errName)
			// case "Type":
			// 	errMsg := errs.ErrTypeBillingValidate.Error()
			// 	validationErrors = append(validationErrors, &errMsg)
			case "Status":
				errStatus := admerrs.ErrStatusNotFound.Error()
				validationErrors = append(validationErrors, &errStatus)
				// case "Ordering":
				// 	errStatus := errs.ErrOrderingBillingValidate.Error()
				// 	validationErrors = append(validationErrors, &errStatus)
			}
		}
	}

	return validationErrors
}
