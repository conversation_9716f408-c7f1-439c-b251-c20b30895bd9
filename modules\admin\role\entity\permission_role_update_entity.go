package entity

import (
	"godsp/pkg/gos/utils"
	"time"

	"go.mongodb.org/mongo-driver/bson/primitive"
)

type PermissionRoleUpdate struct {
	ID          primitive.ObjectID `json:"_id,omitempty" bson:"_id,omitempty"`
	Permissions *PermissionMapInt  `json:"permissions,omitempty" bson:"permissions,omitempty"`
	UpdatedBy   primitive.ObjectID `json:"updated_by" bson:"updated_by"`
	UpdatedAt   time.Time          `json:"updated_at" bson:"updated_at"`
}

func (PermissionRoleUpdate) CollectionName() string {
	return RoleEntity{}.CollectionName()
}

func (r *PermissionRoleUpdate) BeforeUpdate() {
	now := utils.TimeNowLocationHCM()
	if r.UpdatedAt.IsZero() {
		r.UpdatedAt = now
	}
	r.UpdatedAt = now
}
