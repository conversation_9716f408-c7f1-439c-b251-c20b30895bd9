package mongo

import (
	"context"
	"errors"
	"fmt"
	"godsp/modules/facebook/adset/entity"
	"godsp/modules/facebook/common/fberrs"
	"godsp/pkg/sctx/core"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
)

type adsetRepo struct {
	DB         *mongo.Database
	Collection *mongo.Collection
}

func NewAdsetRepo(DB *mongo.Database) *adsetRepo {
	return &adsetRepo{
		DB:         DB,
		Collection: DB.Collection(entity.AdsetEntity{}.CollectionName()),
	}
}

/***
 * insert adset
 */
func (r *adsetRepo) InsertAdsetRepo(ctx context.Context, adset *entity.AdsetEntity) error {
	_, err := r.Collection.InsertOne(ctx, adset)
	if err != nil {
		return err
	}

	return nil
}

func (r *adsetRepo) UpsertAdsetRepo(ctx context.Context, filter bson.M, update bson.M) error {
	opts := options.Update().SetUpsert(true)
	_, err := r.Collection.UpdateOne(ctx, filter, update, opts)
	if err != nil {
		return err
	}

	return nil
}

/***
 * update one adset
 */
func (r *adsetRepo) UpdateAdsetRepo(ctx context.Context, filter interface{}, adset *entity.AdsetEntity, opts ...*options.UpdateOptions) error {
	update := bson.M{
		"$set": *adset,
	}
	_, err := r.Collection.UpdateOne(ctx, filter, update)
	if err != nil {
		return err
	}

	return nil
}

/**
 * FindOne adset
 */
func (r *adsetRepo) FindOneAdsetRepo(ctx context.Context, filter interface{}, opts ...*options.FindOneOptions) (*entity.AdsetEntity, error) {
	var adset entity.AdsetEntity
	err := r.Collection.FindOne(ctx, filter, opts...).Decode(&adset)
	if errors.Is(err, mongo.ErrNoDocuments) {
		return nil, core.ErrNotFound
	}

	return &adset, nil
}

/***
 *
 * skip := (page * limit) - limit
	opts := &options.FindOptions{
		AllowDiskUse: &fbenums.ALLOW_DISK_USE_TRUE,
		Skip:         &skip,
		Limit:        &limit,
	}
	if sort != nil {
		opts.SetSort(sort)
	}

*/

func (r *adsetRepo) FindAdsetRepo(ctx context.Context, filter interface{}, opts ...*options.FindOptions) (*[]entity.AdsetEntity, error) {
	var adsets []entity.AdsetEntity

	// for i, opt := range opts {
	// 	fmt.Printf("opts[%d]: %+v\n", i, opt)
	// }

	cursor, err := r.Collection.Find(ctx, filter, opts...)
	if err != nil {
		return nil, err
	}
	defer cursor.Close(ctx)

	if cursor == nil {
		return nil, fberrs.ErrNilCursorValue
	}
	err = cursor.All(ctx, &adsets)
	if err != nil {
		return nil, err
	}

	return &adsets, nil
}

func (r *adsetRepo) FindListTableAggregateRepo(ctx context.Context, pipeline []bson.M, opts ...*options.AggregateOptions) (*[]entity.AdsetEntity, error) {
	var adsets []entity.AdsetEntity

	cursor, err := r.Collection.Aggregate(ctx, pipeline, opts...)
	if err != nil {
		return nil, err
	}
	defer cursor.Close(ctx)

	if cursor == nil {
		return nil, fberrs.ErrNilCursorValue
	}

	if err = cursor.All(ctx, &adsets); err != nil {
		return nil, err
	}

	return &adsets, nil
}

/**
 * count Adset
 */
func (r *adsetRepo) CountAdsetRepo(ctx context.Context, filter interface{}) (int64, error) {
	return r.Collection.CountDocuments(ctx, filter)
}

/**
 * Find One Adset have subtype audience
 */
func (r *adsetRepo) AggregateAdsetRepo(ctx context.Context, pipeline []bson.M, opts ...*options.AggregateOptions) (*entity.AdsetDetailViewEntity, error) {
	var adset entity.AdsetDetailViewEntity

	cursor, err := r.Collection.Aggregate(ctx, pipeline, opts...)

	if err != nil {
		return nil, err
	}

	defer cursor.Close(ctx)

	if !cursor.Next(ctx) {
		return nil, nil
	}

	if err := cursor.Decode(&adset); err != nil {
		return nil, err
	}

	return &adset, nil
}

func (r *adsetRepo) UpdateAdsetNameStatusRepo(ctx context.Context, filter interface{}, data bson.M) error {
	// update := bson.M{"$set": bson.M{"status": status}}

	// jsonData, _ := json.Marshal(filter)
	fmt.Printf("\n filter: %#v\n", filter)

	result, err := r.Collection.UpdateOne(ctx, filter, data, options.Update().SetUpsert(false))

	fmt.Printf("\n error Update DB: %#v\n", err)

	if err != nil {
		return err
	}

	if result.MatchedCount == 0 {
		return core.ErrNotFound
	}

	return nil

}

func (r *adsetRepo) DeleteAdsetRepo(ctx context.Context, filter interface{}) error {
	fmt.Printf("fitler %+v ", filter)
	if _, err := r.Collection.DeleteMany(ctx, filter); err != nil {
		return err
	}
	return nil
}
