package usecase

import (
	"context"
	"fmt"

	userEt "godsp/modules/admin/user/entity"
	userRp "godsp/modules/admin/user/repository/mongo"

	"godsp/modules/facebook/ad_pixel/common/errs"
	"godsp/modules/facebook/ad_pixel/entity"
	"godsp/modules/facebook/ad_pixel/mapping"
	"godsp/modules/facebook/ad_pixel/transport/response"
	v20 "godsp/pkg/fb-marketing/marketing/v20"
	"godsp/pkg/gos/utils"

	"github.com/dev-networldasia/dspgos/sctx"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo/options"
)

type ApiAdPixelRepo interface {
	FindOneAdPixelRepo(ctx context.Context, filter interface{}, opts ...*options.FindOneOptions) (*entity.AdPixelEntity, error)
	InsertAdPixelRepo(ctx context.Context, adPixelEntity *entity.AdPixelEntity) error
	UpdateOneAdPixelRepo(ctx context.Context, filter interface{}, adPixelEntity *entity.AdPixelEntity, opts ...*options.UpdateOptions) error
	UpsertAdPixelRepo(ctx context.Context, filter bson.M, update bson.M) error
	FindAdPixelRepo(ctx context.Context, filter interface{}, opts ...*options.FindOptions) ([]*entity.AdPixelEntity, error)
	CountAdPixelRepo(ctx context.Context, filter interface{}) (int64, error)
}

type apiFBAdPixelUsc struct {
	fbService *v20.Service
	repo      ApiAdPixelRepo
	userRepo  ApiUserRepo
	logger    sctx.Logger
}
type ApiUserRepo interface {
	FindOneUserRepo(ctx context.Context, filter interface{}, opts ...*options.FindOneOptions) (*userEt.UserEntity, error)
	GetRoleOfUser(ctx context.Context, userId primitive.ObjectID, opts ...*options.AggregateOptions) (*userRp.RoleInfo, error)
}

func NewFBMarketingUsc(fbService *v20.Service, repo ApiAdPixelRepo, userRepo ApiUserRepo, logger sctx.Logger) *apiFBAdPixelUsc {
	return &apiFBAdPixelUsc{
		fbService: fbService,
		repo:      repo,
		userRepo:  userRepo,
		logger:    logger,
	}
}

/**
 * Reload Ad Pixel Facebook V20
 * - Nếu ko có tạo mới
 * - Nếu có rồi thì update
 */
func (usc *apiFBAdPixelUsc) ReloadAdPixelUsc(ctx context.Context, accountId string, userId primitive.ObjectID) []string {
	var errs []string

	if usc.fbService == nil || usc.fbService.AdPixels == nil {
		errs = append(errs, "AdPixelService is not initialized")
		return errs
	}

	adPixels, err := usc.fbService.AdPixels.List(accountId).Do(ctx)
	if err != nil {
		errs = append(errs, err.Error())
		return errs
	}

	for _, ap := range adPixels {

		updatePixel := mapping.MapperUpsertAdPixelToBson(&ap, userId)
		err := usc.repo.UpsertAdPixelRepo(ctx, bson.M{"id": ap.ID}, updatePixel)
		if err != nil {
			errs = append(errs, err.Error())
		}

		// filter := bson.M{"pixel_id": ap.ID}
		// adPixel, err := usc.repo.FindOneAdPixelRepo(ctx, filter)
		// if err != nil && !errors.Is(err, core.ErrNotFound) {
		// 	usc.logger.Error(err)
		// 	errs = append(errs, err.Error())
		// 	break
		// }

		// if errors.Is(err, core.ErrNotFound) {
		// 	adPixelCreate := mapping.MapperAdPixelFBToEntity(&ap, accountId, userId)
		// 	err := usc.repo.InsertAdPixelRepo(ctx, adPixelCreate)
		// 	if err != nil {
		// 		errs = append(errs, err.Error())
		// 	}
		// } else {
		// 	mapping.MapperAdPixelFBUpdate(&ap, adPixel, userId)
		// 	err := usc.repo.UpdateOneAdPixelRepo(ctx, filter, adPixel)
		// 	if err != nil {
		// 		errs = append(errs, err.Error())
		// 	}
		// }
	}

	return errs
}

/**
 * list data table ad pixel
 *
 * @return response data table
 */
func (usc *apiFBAdPixelUsc) ListDatatableAdPixelUsc(ctx context.Context) (*response.AdPixelListResponse, error) {
	opts := options.FindOptions{}
	opts.SetProjection(bson.M{
		"_id":           1,
		"name":          1,
		"currency":      1,
		"timezone_name": 1,
		"updated_at":    1,
	})

	adPixels, err := usc.repo.FindAdPixelRepo(ctx, bson.M{}, &opts)
	if err != nil {
		usc.logger.Error(err)
		return nil, errs.ErrFindAdPixel
	}

	total, err := usc.repo.CountAdPixelRepo(ctx, bson.M{})
	if err != nil {
		usc.logger.Error(err)
		return nil, errs.ErrCountAdPixel
	}

	adPixelDatatables := response.AdPixelListResponse{
		Count:       total,
		CountFilter: total,
	}

	var datas []*response.AdPixelDataTable
	for _, adc := range adPixels {
		datas = append(datas, response.MapperDatatable(adc))
	}
	adPixelDatatables.AdPixelDataTables = datas

	return &adPixelDatatables, nil
}

/**
 * Api list ad pixels
 */
func (usc *apiFBAdPixelUsc) ListAdPixelUsc(ctx context.Context) (*[]response.AdPixelView, error) {

	user, err := utils.GetInfoUserAuth(ctx)
	if err != nil {
		return nil, err
	}

	filter := utils.GetFilterFBResource(ctx, user)

	ops := &options.FindOptions{}
	ops.SetSort(bson.M{
		"created_at": -1, // sort by created_at in descending order
	})
	fmt.Printf("filter %+v \n", filter)

	pixels, err := usc.repo.FindAdPixelRepo(ctx, filter)
	if err != nil {
		usc.logger.Error(err)
		return nil, err
	}

	pixelsView := mapping.MapperAdPixelEntityToListView(pixels)

	fmt.Printf("pixels %+v \n", pixels)
	return pixelsView, nil

}

// func (usc *apiFBAdPixelUsc) ListAdPixelUsc(ctx context.Context, payload *requests.ListAdPixelReq) ([]*entity.AdPixelEntity, error) {

// 	if payload == nil {
// 		return nil, errs.ErrAdPixelAdAccount
// 	}

// 	filter := bson.M{}
// 	//filter
// 	if payload.AccountID != "" {
// 		filter["account_id"] = payload.AccountID
// 	}

// 	pixels, err := usc.repo.FindAdPixelRepo(ctx, filter)
// 	if err != nil {
// 		return nil, err
// 	}

// 	return pixels, nil
// }
