package pipelines

import (
	"godsp/modules/facebook/common/fbenums"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

var (
	GetDetailsCampaignPipeline2 = []bson.D{
		{
			{Key: "$lookup", Value: bson.M{
				"from":         "fb_adsets",
				"localField":   "campaign_id",
				"foreignField": "campaign_id",
				"as":           "adsets",
			}},
		},
		{
			{Key: "$set",
				Value: bson.M{
					"adsets": bson.M{
						"$filter": bson.M{
							"input": "$adsets",
							"as":    "adset",
							"cond": bson.M{
								"$not": bson.M{
									"$in": bson.A{"$$adset.status", bson.A{"ARCHIVED", "DELETED"}},
								},
							},
						},
					},
				}},
		},
		{
			{Key: "$lookup", Value: bson.M{
				"from":         "fb_ads",
				"localField":   "adsets.adset_id",
				"foreignField": "adset_id",
				"as":           "ads",
			}},
		},
		{
			{Key: "$set",
				Value: bson.M{
					"ads": bson.M{
						"$filter": bson.M{
							"input": "$ads",
							"as":    "ad",
							"cond": bson.M{
								"$in": bson.A{"$$ad.status", bson.A{"ACTIVE", "PAUSED"}},
							},
						},
					},
				}},
		},
		// {
		// 	"$match": bson.M{
		// 		"$or": bson.A{
		// 			bson.M{"ads.ad_id": "120210152533740180"},
		// 		},
		// 		"status": bson.M{
		// 			"$nin": bson.A{
		// 				"ARCHIVED",
		// 				"DELETED",
		// 			},
		// 		},
		// 	},
		// },
		{
			bson.E{Key: "$addFields", Value: bson.M{
				"adsets": bson.M{
					"$map": bson.M{
						"input": "$adsets",
						"as":    "adset",
						"in": bson.M{
							"adset_id": "$$adset.adset_id",
							"name":     "$$adset.name",
							"ads": bson.M{
								"$map": bson.M{
									"input": bson.M{
										"$filter": bson.M{
											"input": "$ads",
											"as":    "ad",
											"cond": bson.M{
												"$eq": bson.A{
													"$$ad.adset_id",
													"$$adset.adset_id",
												},
											},
										},
									},
									"as": "ad",
									"in": bson.M{
										"ad_id": "$$ad.ad_id",
										"name":  "$$ad.name",
									},
								},
							},
						},
					},
				},
			}},
		},
		{
			bson.E{Key: "$unset", Value: bson.A{
				"ads",
			}},
		},
		{
			bson.E{Key: "$project", Value: bson.M{"_id": 0}},
		},
	}
)

func GetDetailsCampaignPipeline(filter bson.M, userId primitive.ObjectID, isAdmin bool) []bson.M {

	filterStatus := []string{fbenums.FB_STATUS_ACTIVE, fbenums.FB_STATUS_PAUSED}
	filterAdset := []bson.M{
		{
			"$in": []interface{}{
				"$$adset.status",
				filterStatus,
			},
		},
	}

	filterAd := []bson.M{
		{
			"$in": []interface{}{
				"$$ad.status",
				filterStatus,
			},
		},
	}

	if !isAdmin {
		filterAdset = append(filterAdset, bson.M{
			"$in": []interface{}{userId,
				"$$adset.list_user_ids",
			},
		})
		filterAd = append(filterAd, bson.M{
			"$in": []interface{}{userId,
				"$$ad.list_user_ids",
			},
		})
	}

	pipeline := []bson.M{
		{
			"$lookup": bson.M{
				"from":         "fb_adsets",
				"localField":   "campaign_id",
				"foreignField": "campaign_id",
				"as":           "adsets",
			},
		},
		{
			"$set": bson.M{
				"adsets": bson.M{
					"$filter": bson.M{
						"input": "$adsets",
						"as":    "adset",
						"cond": bson.M{
							"$and": filterAdset,
						},
					},
				},
			},
		},
		{
			"$lookup": bson.M{
				"from":         "fb_ads",
				"localField":   "adsets.adset_id",
				"foreignField": "adset_id",
				"as":           "ads",
			},
		},
		{
			"$match": filter,
		},
		{
			"$set": bson.M{
				"ads": bson.M{
					"$filter": bson.M{
						"input": "$ads",
						"as":    "ad",
						"cond": bson.M{
							"$and": filterAd,
						},
					},
				},
			},
		},
		{
			"$addFields": bson.M{
				"adsets": bson.M{
					"$map": bson.M{
						"input": "$adsets",
						"as":    "adset",
						"in": bson.M{
							"adset_id": "$$adset.adset_id",
							"name":     "$$adset.name",
							"ads": bson.M{
								"$map": bson.M{
									"input": bson.M{
										"$filter": bson.M{
											"input": "$ads",
											"as":    "ad",
											"cond": bson.M{
												"$eq": []interface{}{
													"$$ad.adset_id",
													"$$adset.adset_id",
												},
											},
										},
									},
									"as": "ad",
									"in": bson.M{
										"ad_id": "$$ad.ad_id",
										"name":  "$$ad.name",
									},
								},
							},
						},
					},
				},
			},
		},
		{
			"$unset": "ads",
		},
		{
			"$project": bson.M{
				"_id": 0,
			},
		},
	}
	return pipeline
}
