package api

import (
	"context"
	"fmt"
	"godsp/modules/admin/permission/transport/requests"
	"godsp/modules/admin/permission/transport/responses"
	"godsp/pkg/gos/utils"
	"godsp/pkg/sctx/core"
	"net/http"

	"github.com/gofiber/fiber/v2"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

type permissionApiUsc interface {
	UpsertApiPermissionsUsc(ctx context.Context, routes []fiber.Route, userId primitive.ObjectID) error
	FindDataTableApiPermissionUsc(ctx context.Context) (*[]responses.PermissionDataTable, error)
	UpdateIsBlockApiPermissionUsc(ctx context.Context, data *requests.PayloadPermissionUpdateField) error
	UpdateIsAcpApiPermissionUsc(ctx context.Context, data *requests.PayloadPermissionUpdateField) error
	UpdateApiPermissionUsc(ctx context.Context, data *requests.PayloadPermissionUpdate) error
}

type permissionApi struct {
	usc permissionApiUsc
}

func NewPermissionApi(usc permissionApiUsc) *permissionApi {
	return &permissionApi{
		usc: usc,
	}
}

/**
 * func receives all routes except those with method HEAD
 */
func filterRoutes(routes []fiber.Route) []fiber.Route {
	var filteredRoutes []fiber.Route
	for _, route := range routes {
		if route.Method != "HEAD" {
			filteredRoutes = append(filteredRoutes, route)
		}
	}
	return filteredRoutes
}

func (a *permissionApi) RefreshPermissionApi() fiber.Handler {
	return func(c *fiber.Ctx) error {
		routes := filterRoutes(c.App().GetRoutes(true))

		userId, err := utils.GetUserIdPrimitive(c.Context())
		if err != nil {
			return core.ReturnErrForApi(c, err.Error())
		}

		if err := a.usc.UpsertApiPermissionsUsc(c.Context(), routes, userId); err != nil {
			return core.ReturnErrForApi(c, err.Error())
		}

		return c.Status(http.StatusOK).JSON(core.ResponseData(map[string]interface{}{
			"msg": "Refresh permission successfully!",
		}))
	}
}

func (a *permissionApi) ListPermissionApi() fiber.Handler {
	return func(c *fiber.Ctx) error {
		permissions, err := a.usc.FindDataTableApiPermissionUsc(c.Context())
		if err != nil {
			return c.JSON(fiber.Map{
				"data": nil,
			})
		}

		return c.JSON(fiber.Map{
			"data": permissions,
		})
	}
}

func (a *permissionApi) UpdateIsBlockPermissionApi() fiber.Handler {
	return func(c *fiber.Ctx) error {
		var param requests.PayloadPermissionUpdateField
		err := c.BodyParser(&param)
		if err != nil {
			return core.ReturnErrForApi(c, err.Error())
		}

		if err := param.Validate(); err != nil {
			return core.ReturnErrForApi(c, err.Error())
		}

		userId, err := utils.GetUserIdPrimitive(c.Context())
		if err != nil {
			return core.ReturnErrForApi(c, err.Error())
		}

		param.UserID = userId

		err = a.usc.UpdateIsBlockApiPermissionUsc(c.Context(), &param)
		if err != nil {
			return core.ReturnErrForApi(c, err.Error())
		}
		return c.Status(http.StatusOK).JSON(core.ResponseData(map[string]interface{}{
			"msg": "Update successful!",
		}))
	}
}

func (a *permissionApi) UpdateIsAcpPermissionApi() fiber.Handler {
	return func(c *fiber.Ctx) error {
		var param requests.PayloadPermissionUpdateField
		err := c.BodyParser(&param)
		if err != nil {
			return core.ReturnErrForApi(c, err.Error())
		}

		if err := param.Validate(); err != nil {
			return core.ReturnErrForApi(c, err.Error())
		}

		userId, err := utils.GetUserIdPrimitive(c.Context())
		if err != nil {
			return core.ReturnErrForApi(c, err.Error())
		}

		param.UserID = userId

		err = a.usc.UpdateIsAcpApiPermissionUsc(c.Context(), &param)
		if err != nil {
			return core.ReturnErrForApi(c, err.Error())
		}
		return c.Status(http.StatusOK).JSON(core.ResponseData(map[string]interface{}{
			"msg": "Update successful!",
		}))
	}
}

func (a *permissionApi) UpdatePermissionApi() fiber.Handler {
	return func(c *fiber.Ctx) error {
		var param requests.PayloadPermissionUpdate
		if err := c.BodyParser(&param); err != nil {
			return core.ReturnErrForApi(c, err.Error())
		}

		fmt.Printf("=-=-=-=%+v\n", param)

		//validate
		validationErrors := param.Validate()
		if len(validationErrors) > 0 {
			return core.ReturnErrsForApi(c, validationErrors)
		}

		userId, err := utils.GetUserIdPrimitive(c.Context())
		if err != nil {
			return core.ReturnErrForApi(c, err.Error())
		}

		param.UserID = userId

		err = a.usc.UpdateApiPermissionUsc(c.Context(), &param)
		if err != nil {
			return core.ReturnErrForApi(c, err.Error())
		}

		return c.Status(http.StatusOK).JSON(core.ResponseData(map[string]interface{}{
			"msg": "Update permission successfully!",
		}))
	}
}
