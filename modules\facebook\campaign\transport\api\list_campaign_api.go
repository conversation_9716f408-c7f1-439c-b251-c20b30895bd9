package api

import (
	"godsp/modules/facebook/campaign/transport/requests"
	"godsp/pkg/sctx/core"
	"net/http"

	"github.com/gofiber/fiber/v2"
)

/**
 * List campaign api
 */
func (a *campaignApi) ListCampaignApi() fiber.Handler {
	return func(c *fiber.Ctx) error {
		var payload requests.CampaignReq

		if err := c.BodyParser(&payload); err != nil {
			return core.ReturnErrForApi(c, err.Error())
		}

		camps, err := a.usc.ListCampaignUsc(c.Context(), &payload)
		if err != nil {
			return core.ReturnErrForApi(c, err.Error())
		}

		// Respond with the list of pages in JSON format
		return c.Status(http.StatusOK).JSON(core.ResponseData(camps))
	}
}
