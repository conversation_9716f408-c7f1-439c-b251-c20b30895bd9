package routes

import (
	userR "godsp/modules/admin/user/repository/mongo"
	campR "godsp/modules/facebook/campaign/repository/mongo"

	"godsp/modules/facebook/budget_schedule/repository/mongo"
	"godsp/modules/facebook/budget_schedule/transport/api"
	"godsp/modules/facebook/budget_schedule/usecase"
	"godsp/pkg/sctx/component/facebook/fbmarketing"

	"github.com/dev-networldasia/dspgos/sctx/component/mongodb"

	"github.com/dev-networldasia/dspgos/sctx/configs"

	"github.com/dev-networldasia/dspgos/sctx"

	"github.com/gofiber/fiber/v2"
)

type ComposerBudgetScheduleApi interface {
	ReloadBudgetScheduleByCampaignAdsetIdApi() fiber.Handler
	GetByCampaignAdsetIdApiBudgetScheduleApi() fiber.Handler
	CreateBudgetScheduleApi() fiber.Handler
	DeleteByIdBudgetScheduleApi() fiber.Handler
	UpdateListBudgetScheduleApi() fiber.Handler
}

func ComposerBudgetScheduleApiService(serviceCtx sctx.ServiceContext) ComposerBudgetScheduleApi {
	mongoDB := serviceCtx.MustGet(configs.KeyCompMongoDB).(mongodb.MongoComponent).GetDatabase()
	logger := serviceCtx.MustGet(configs.KeyLoggerDaily).(sctx.AppLoggerDaily).GetLogger("webfb")
	fbService := serviceCtx.MustGet(configs.KeyFBMarketingV20).(fbmarketing.FBMarketingServices).GetFBMarketingService()

	repo := mongo.NewHighDemandPeriodRepo(mongoDB)
	campaignRepo := campR.NewCampaignRepo(mongoDB)
	userRepo := userR.NewUserRepo(mongoDB)
	usc := usecase.NewApiBudgetScheduleUsc(fbService, repo, userRepo, campaignRepo, logger)
	api := api.NewBudgetScheduleApi(usc)

	return api
}
