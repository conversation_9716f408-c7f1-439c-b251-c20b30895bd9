package requests

import (
	"time"

	"godsp/modules/admin/client/common/errs"
	"godsp/pkg/gos/utils"

	"github.com/go-playground/validator/v10"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

type UpdateUserIdClientIdFacebookResource struct {
	AdaccountIDs *[]string `json:"adaccount_ids,omitempty" validate:"omitempty,RuleFacebookIDSlice"`
	PageIDs      *[]string `json:"page_ids,omitempty"  validate:"omitempty,RuleFacebookIDSlice"`
	PixelIDs     *[]string `json:"pixel_ids,omitempty"  validate:"omitempty,RuleFacebookIDSlice"`
	CatalogueIDs *[]string `json:"catalogue_ids,omitempty" validate:"omitempty,RuleFacebookIDSlice"`

	UpdatedBy primitive.ObjectID `json:"-"`
	UpdatedAt time.Time          `json:"-"`

	ListUserIDsStr *[]string `json:"list_user_ids" validate:"omitempty,RuleStrObjectIDSlice"`
	ClientIDsStr   *[]string `json:"client_ids" validate:"omitempty,RuleStrObjectIDSlice"`

	ClientIDs   *[]primitive.ObjectID `json:"-"`
	ListUserIDs *[]primitive.ObjectID `json:"-"`

	Disconnect *FaceboookResource `json:"disconnect,omitempty"`
}

type FaceboookResource struct {
	AdaccountIDs *[]string `json:"adaccount_ids,omitempty" validate:"omitempty,RuleFacebookIDSlice"`
	PageIDs      *[]string `json:"page_ids,omitempty"  validate:"omitempty,RuleFacebookIDSlice"`
	PixelIDs     *[]string `json:"pixel_ids,omitempty"  validate:"omitempty,RuleFacebookIDSlice"`
	CatalogueIDs *[]string `json:"catalogue_ids,omitempty" validate:"omitempty,RuleFacebookIDSlice"`
}

func (req *UpdateUserIdClientIdFacebookResource) Validate() []*string {
	validate := validator.New()
	var validationErrors []*string
	validate.RegisterValidation("RuleFacebookIDSlice", utils.RuleFacebookIDSlice)
	validate.RegisterValidation("RuleStrObjectIDSlice", utils.RuleStrObjectIDSlice)

	err := validate.Struct(req)
	if err != nil {
		for _, err := range err.(validator.ValidationErrors) {
			switch err.Field() {
			case "ClientIDsStr":
				errMsg := errs.ErrIDClientValidate.Error()
				validationErrors = append(validationErrors, &errMsg)
			case "ListUserIDsStr":
				errMsg := errs.ErrIdUserInvalidate.Error()
				validationErrors = append(validationErrors, &errMsg)
			case "AdaccountIDs":
				errMsg := errs.ErrAdAccountIdNotExist.Error()
				validationErrors = append(validationErrors, &errMsg)
			case "PageIDs":
				errMsg := errs.ErrPageIdNotExist.Error()
				validationErrors = append(validationErrors, &errMsg)
			case "PixelIDs":
				errMsg := errs.ErrPixelIdNotExist.Error()
				validationErrors = append(validationErrors, &errMsg)
			case "CatalogueIDs":
				errMsg := errs.ErrCatalogueIdNotExist.Error()
				validationErrors = append(validationErrors, &errMsg)
			}

		}
	}

	if len(validationErrors) == 0 {

		// if req.AdaccountIDs == nil && req.PageIDs == nil && req.PixelIDs == nil && req.CatalogueIDs == nil {
		// 	msg := errs.ErrIDClientValidate.Error()
		// 	validationErrors = append(validationErrors, &msg)
		// 	return validationErrors
		// }

		// Convert client_ids
		userIds, err := utils.ConvertToObjectIDs(*req.ListUserIDsStr)
		if err != nil {
			msg := errs.ErrIDClientValidate.Error()
			validationErrors = append(validationErrors, &msg)
		} else {
			req.ListUserIDs = &userIds
		}

		// Convert client_ids
		clientIds, err := utils.ConvertToObjectIDs(*req.ClientIDsStr)
		if err != nil {
			msg := errs.ErrIDClientValidate.Error()
			validationErrors = append(validationErrors, &msg)
		} else {
			req.ClientIDs = &clientIds
		}
	}

	return validationErrors
}
