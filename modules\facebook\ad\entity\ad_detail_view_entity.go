package entity

import (
	adsetEt "godsp/modules/facebook/adset/entity"
	campEt "godsp/modules/facebook/campaign/entity"
	v20 "godsp/pkg/fb-marketing/marketing/v20"
	"time"

	"go.mongodb.org/mongo-driver/bson/primitive"
)

type AdDetailReviewEntity struct {
	// ID primitive.ObjectID `json:"-" bson:"_id,omitempty"`

	AccountID  string `json:"account_id,omitempty" bson:"account_id"`
	CampaignID string `json:"campaign_id,omitempty" bson:"campaign_id"`
	AdsetID    string `json:"adset_id,omitempty" bson:"adset_id"`
	AdID       string `json:"ad_id,omitempty" bson:"ad_id"`
	CreativeID string `json:"creative_id,omitempty" bson:"creative_id"`

	Name          string                      `json:"name,omitempty" bson:"name"`
	Status        string                      `json:"status,omitempty" bson:"status"`
	TrackingSpecs []v20.ConversionActionQuery `json:"tracking_specs,omitempty" bson:"tracking_specs"`

	CreatedBy primitive.ObjectID `json:"-" bson:"-"`
	CreatedAt time.Time          `json:"-" bson:"-"`
	UpdatedBy primitive.ObjectID `json:"-" bson:"-"`
	UpdatedAt time.Time          `json:"-" bson:"-"`

	AdScheduleStartTime time.Time `json:"ad_schedule_start_time,omitempty" bson:"ad_schedule_start_time,omitempty"`
	AdScheduleEndTime   time.Time `json:"ad_schedule_end_time,omitempty" bson:"ad_schedule_end_time,omitempty"`

	AdSet       adsetEt.AdsetEntity   `json:"ad_set,omitempty" bson:"ad_set"`
	Campaign    campEt.CampaignEntity `json:"campaign,omitempty" bson:"campaign"`
	ListUserIDs []primitive.ObjectID  `json:"list_user_ids,omitempty" bson:"list_user_ids"`
	ClientID    primitive.ObjectID    `json:"client_id,omitempty" bson:"client_id"`
}

func (AdDetailReviewEntity) CollectionName() string {
	return "fb_ads"
}
