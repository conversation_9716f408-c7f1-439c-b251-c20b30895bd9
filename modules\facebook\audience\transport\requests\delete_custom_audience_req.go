package requests

import (
	"time"

	"github.com/go-playground/validator/v10"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

type DeleteCustomAudienceReq struct {
	AudienceIDs []string `json:"audience_id,omitempty"`

	UserId    primitive.ObjectID `json:"-"`
	AccountID string             `json:"-"`
	UpdatedBy primitive.ObjectID `json:"-"`
	UpdatedAt time.Time          `json:"-"`
	ClientID  primitive.ObjectID `json:"-"`
	Status    int                `json:"-"`
}

func (req *DeleteCustomAudienceReq) Validate() []string {
	validate := validator.New()
	var validationErrors []string

	err := validate.Struct(req)

	if err != nil {
		for _, err := range err.(validator.ValidationErrors) {
			switch err.Field() {
			case "AudienceID":
				validationErrors = append(validationErrors, "AudienceID is required")
			}
		}
	}
	return validationErrors
}
