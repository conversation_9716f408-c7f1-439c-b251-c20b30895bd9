/**
 * GMV Status Circle Indicators for Dropdown Options
 * Handles dynamic circle creation and status display
 */

import { 
    GMV_STATUS, 
    getStatusDisplay, 
    getStatusCircleClass 
} from '/static/js/tiktok/constants/gmv-status.js';

/**
 * Initialize status dropdown with circle indicators
 */
function initStatusDropdowns() {
    // Initialize both product and live status filters
    initStatusDropdown('productStatusFilter');
    initStatusDropdown('liveStatusFilter');
}

/**
 * Initialize individual status dropdown
 * @param {string} dropdownId - The ID of the dropdown element
 */
function initStatusDropdown(dropdownId) {
    const dropdown = document.getElementById(dropdownId);
    if (!dropdown) return;

    // Add event listener for status change
    dropdown.addEventListener('change', function(e) {
        const selectedValue = e.target.value;
        console.log(`${dropdownId} changed to:`, selectedValue);
        
        if (selectedValue) {
            const displayText = getStatusDisplay(selectedValue);
            console.log(`Status display: ${displayText}`);
        }
    });
}

/**
 * Create status badge with circle indicator
 * @param {string} primaryStatus - The primary status value
 * @returns {HTMLElement} Status badge element
 */
function createStatusBadge(primaryStatus) {
    const badge = document.createElement('span');
    badge.className = 'status-badge';
    
    const circle = document.createElement('span');
    circle.className = getStatusCircleClass(primaryStatus);
    
    const text = document.createElement('span');
    text.textContent = getStatusDisplay(primaryStatus);
    
    badge.appendChild(circle);
    badge.appendChild(text);
    
    return badge;
}

/**
 * Format status for table display
 * @param {string} primaryStatus - The primary status value
 * @returns {string} HTML string for status display
 */
function formatStatusForTable(primaryStatus) {
    const displayText = getStatusDisplay(primaryStatus);
    const circleClass = getStatusCircleClass(primaryStatus);
    
    return `
        <span class="status-badge">
            <span class="${circleClass}"></span>
            ${displayText}
        </span>
    `;
}

/**
 * Get status color for circle
 * @param {string} primaryStatus - The primary status value
 * @returns {string} Color hex code
 */
function getStatusColor(primaryStatus) {
    switch (primaryStatus) {
        case GMV_STATUS.STATUS_DELIVERY_OK:
            return '#28a745'; // Green
        case GMV_STATUS.STATUS_DISABLE:
            return '#6c757d'; // Gray
        case GMV_STATUS.STATUS_DELETE:
            return '#dc3545'; // Red
        default:
            return '#fd7e14'; // Orange (Not delivering)
    }
}

/**
 * Update dropdown option text with colored circle
 * @param {HTMLSelectElement} dropdown - The dropdown element
 * @param {string} value - Option value
 * @param {string} text - Option text
 * @param {string} color - Circle color
 */
function updateDropdownOption(dropdown, value, text, color) {
    const option = dropdown.querySelector(`option[value="${value}"]`);
    if (option) {
        // Use Unicode bullet character with color
        option.textContent = `● ${text}`;
        option.style.color = color;
    }
}

/**
 * Apply colors to dropdown options
 */
function applyDropdownColors() {
    const dropdowns = ['productStatusFilter', 'liveStatusFilter'];
    
    dropdowns.forEach(dropdownId => {
        const dropdown = document.getElementById(dropdownId);
        if (!dropdown) return;
        
        // Apply colors to each status option
        updateDropdownOption(dropdown, GMV_STATUS.STATUS_DELIVERY_OK, 'Active', '#28a745');
        updateDropdownOption(dropdown, GMV_STATUS.STATUS_DISABLE, 'Inactive', '#6c757d');
        updateDropdownOption(dropdown, GMV_STATUS.STATUS_DELETE, 'Deleted', '#dc3545');
        updateDropdownOption(dropdown, 'not_delivering', 'Not delivering', '#fd7e14');
    });
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    initStatusDropdowns();
    applyDropdownColors();
});

// Export functions for external use
export {
    initStatusDropdowns,
    createStatusBadge,
    formatStatusForTable,
    getStatusColor,
    applyDropdownColors
};
