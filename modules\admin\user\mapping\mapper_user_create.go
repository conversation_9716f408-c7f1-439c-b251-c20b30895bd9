package mapping

import (
	"godsp/modules/admin/common/admconst"
	"godsp/modules/admin/user/common/consts"
	"godsp/modules/admin/user/entity"
	"godsp/modules/admin/user/transport/requests"
)

func MapperCreateUser(req *requests.PayloadUserCreation, salt, passHasher string) *entity.UserCreation {
	data := &entity.UserCreation{
		FirstName:   req.FirstName,
		LastName:    req.LastName,
		FullName:    req.LastName + " " + req.FirstName,
		Email:       req.Email,
		Status:      admconst.StatusCreateNameValue[req.Status],
		Gender:      req.Gender,
		RoleID:      req.RoleID,
		AdAccountID: req.AdAccountID,
		CreatedBy:   req.UserID,
		AuthType:    consts.AUTH_TYPE_EMAIL_PASSWORD,
		Salt:        salt,
		Password:    passHasher,
		Image:       req.Image,

		ListPageIds:  req.ListPageIds,
		ListPixelIds: req.ListPixelIds,
		ClientID:     req.ClientID,
	}
	username := req.Username
	if username != "" {
		data.Username = &username
	}

	return data
}
