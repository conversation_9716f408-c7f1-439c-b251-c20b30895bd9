package entity

import (
	"go.mongodb.org/mongo-driver/bson/primitive"
)

type PermissionUpdate struct {
	ID          primitive.ObjectID `json:"_id,omitempty" bson:"_id,omitempty"`
	Action      string             `json:"action" bson:"action"`
	Description string             `json:"description" bson:"description"`
	Ordering    int64              `json:"ordering" bson:"ordering"`
	ParentID    primitive.ObjectID `json:"parent_id" bson:"parent_id"`
	// IsApi       int                `json:"is_api" bson:"is_api"`
	IsBlock   int                `json:"is_block" bson:"is_block"`
	IsAcp     int                `json:"is_acp" bson:"is_acp"`
	UpdatedBy primitive.ObjectID `json:"updated_by" bson:"updated_by"`
}

func (PermissionUpdate) CollectionName() string {
	return PermissionEntity{}.CollectionName()
}
