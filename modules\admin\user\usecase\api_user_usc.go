package usecase

import (
	"context"
	"errors"
	"fmt"
	"godsp/conf"
	"godsp/modules/admin/user/common/errs"
	"godsp/modules/admin/user/common/pipelines"
	"godsp/modules/admin/user/entity"
	"godsp/modules/facebook/common/fbenums"

	"godsp/modules/admin/user/mapping"
	userRp "godsp/modules/admin/user/repository/mongo"
	"godsp/modules/admin/user/transport/requests"
	"godsp/modules/admin/user/transport/responses"
	"godsp/pkg/gos/utils"

	"github.com/dev-networldasia/dspgos/sctx"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo/options"
	"gorm.io/gorm"
)

type ApiUserRepo interface {
	InsertUserRepo(ctx context.Context, user *entity.UserCreation) (*primitive.ObjectID, error)
	FindOneUserRepo(ctx context.Context, filter interface{}, opts ...*options.FindOneOptions) (*entity.UserEntity, error)
	FindUserRepo(ctx context.Context, filter interface{}, opts ...*options.FindOptions) (*[]entity.UserEntity, error)
	FindUsersWithPipelineRepo(ctx context.Context, pipeline []bson.M, opts ...*options.AggregateOptions) (*[]entity.UserEntity, error)
	UpdateOneUserRepo(ctx context.Context, filter interface{}, user *entity.UserUpdate, opts ...*options.UpdateOptions) error
	GetRoleOfUser(ctx context.Context, userId primitive.ObjectID, opts ...*options.AggregateOptions) (*userRp.RoleInfo, error)
	FindOneDetailUserWithPipelineRepo(ctx context.Context, pipeline []bson.M, opts ...*options.AggregateOptions) (*responses.DetailsUser, error)
	UpdateOneUserByBsonMRepo(ctx context.Context, filter interface{}, data bson.M, opts ...*options.UpdateOptions) error
}

type Hasher interface {
	RandomStr(length int) (string, error)
	HashPassword(salt, password string) (string, error)
	CompareHashPassword(hashedPassword, salt, password string) bool
}

type apiUserUsc struct {
	logger        sctx.Logger
	hasher        Hasher
	repo          ApiUserRepo
	roleRepo      RoleRepo
	adAcc         AdAccountRepo
	pageRepo      PageRepo
	pixelRepo     PixelRepo
	catalogueRepo CatalogueRepo
}

func NewApiUserUsc(logger sctx.Logger, hasher Hasher, repo ApiUserRepo, roleRepo RoleRepo, adAcc AdAccountRepo, pageRepo PageRepo, pixelRepo PixelRepo, catalogueRepo CatalogueRepo) *apiUserUsc {
	return &apiUserUsc{
		logger:        logger,
		hasher:        hasher,
		repo:          repo,
		adAcc:         adAcc,
		pageRepo:      pageRepo,
		pixelRepo:     pixelRepo,
		catalogueRepo: catalogueRepo,
	}
}

/**
 * Create user
 * 1. Create salt and hash password
 * 2. Mapper
 * 3. Create
 */
func (usc *apiUserUsc) CreateApiUserUsc(ctx context.Context, payload *requests.PayloadUserCreation) error {
	salt, err := usc.hasher.RandomStr(16)
	if err != nil {
		usc.logger.Error(err)
		return err
	}

	passHasher, err := usc.hasher.HashPassword(salt, payload.Password)
	if err != nil {
		usc.logger.Error(err)
		return err
	}

	userEntity := mapping.MapperCreateUser(payload, salt, passHasher)
	_, err = usc.repo.InsertUserRepo(ctx, userEntity)
	if err != nil {
		usc.logger.Error(err)
		go usc.deleteImgCreate(payload)
		return err
	}

	// if len(payload.ListPageIds) > 0 {
	// 	usc.upSertUserIdToPage(ctx, *userId, payload.ListPageIds)
	// }

	// if len(payload.ListPixelIds) > 0 {
	// 	usc.upSertUserIdToPixel(ctx, *userId, payload.ListPixelIds)
	// }

	return nil
}

// delete img if create error
func (usc *apiUserUsc) deleteImgCreate(payload *requests.PayloadUserCreation) {
	if payload.FileImg != nil {
		if err := utils.RemoveFile(conf.UploadPathPublic, payload.Image); err != nil {
			usc.logger.Error(err)
		}
	}
}

/**
 * Api user list datatable
 */
func (usc *apiUserUsc) ListDatatableApiUserUsc(ctx context.Context) (*[]responses.UserDataTable, error) {
	allowDiskUse := fbenums.ALLOW_DISK_USE_TRUE
	opts := &options.AggregateOptions{
		AllowDiskUse: &allowDiskUse,
	}

	pipeline := pipelines.PipelineListTableUsers()

	users, err := usc.repo.FindUsersWithPipelineRepo(ctx, pipeline, opts)

	if err != nil {
		usc.logger.Error(err)
		return nil, err
	}

	return mapping.MapperUserToDatatable(users), nil
}

/**
 * Api user list
 */
func (usc *apiUserUsc) GetListUserUsc(ctx context.Context) (*[]responses.ListUser, error) {
	userId, err := utils.GetUserId(ctx)
	if err != nil {
		return nil, err
	}

	role, err := usc.repo.GetRoleOfUser(ctx, userId)
	if role.RoleName != "ADMIN" || err != nil {
		return nil, err
	}

	allowDiskUse := fbenums.ALLOW_DISK_USE_TRUE
	opts := &options.AggregateOptions{
		AllowDiskUse: &allowDiskUse,
	}

	pipeline := pipelines.PipelineListTableUsers()

	users, err := usc.repo.FindUsersWithPipelineRepo(ctx, pipeline, opts)

	if err != nil {
		usc.logger.Error(err)
		return nil, err
	}

	return mapping.MapperUserToList(users), nil
}

func (usc *apiUserUsc) UpdateUserUsc(ctx context.Context, payload *requests.PayloadUserUpdate) (*entity.UserUpdate, error) {

	user, err := usc.repo.FindOneUserRepo(ctx, bson.M{
		"_id": payload.ID,
	})

	if err != nil {
		usc.logger.Error(err)
		return nil, err
	}

	if payload.ClientID == nil || user.ClientID != *payload.ClientID {
		fmt.Printf("\n \n Client Id: %v \n\n", user.ClientID)
		if user.ClientID != primitive.NilObjectID {

			err = usc.disconnectUserFBResource(ctx, user.ID, user.ClientID)
			if err != nil {
				usc.logger.Error(err)
				return nil, err
			}
			fmt.Printf("\n \n Disconnect FB Resource User Id Success: %v \n\n", user.ID)
		}
	}

	userUpdate, err := mapping.MapperUpdateUser(payload)

	if err != nil {
		usc.logger.Error(err)
		return nil, err
	}

	err = usc.repo.UpdateOneUserRepo(ctx, bson.M{"_id": payload.ID}, userUpdate)
	if err != nil {
		if errors.Is(err, gorm.ErrDuplicatedKey) {
			usc.logger.Error(err)
			return nil, errs.ErrEmailDuplicate
		}
		usc.logger.Error(err)
		return nil, err
	}

	if user.UpdatedBy == user.ID {
		return userUpdate, nil
	}

	return nil, nil
}

func (usc *apiUserUsc) UpdateBsonUserUsc(ctx context.Context, payload *requests.PayloadBsonUserUpdate) error {

	updateData := mapping.MapperUpdateUserBson(payload)

	if payload.ClientID == nil {
		user, err := usc.repo.FindOneUserRepo(ctx, bson.M{
			"_id": payload.ID,
		})

		if err != nil {
			usc.logger.Error(err)
			return err
		}

		fmt.Printf("\n \n Client Id: %v \n\n", user.ClientID)
		if user.ClientID != primitive.NilObjectID {

			err = usc.disconnectUserFBResource(ctx, user.ID, user.ClientID)
			if err != nil {
				usc.logger.Error(err)
				return err
			}
			fmt.Printf("\n \n Disconnect FB Resource User Id Success: %v \n\n", user.ID)
		}

	}

	err := usc.repo.UpdateOneUserByBsonMRepo(ctx, bson.M{"_id": payload.ID}, *updateData)
	if err != nil {
		usc.logger.Error(err)
		return err
	}

	return nil
}

/**
 * Upsert UserId to Pixel - STATUS - REMOVE
 */
func (usc *apiUserUsc) disconnectUserFBResource(ctx context.Context, userID primitive.ObjectID, clientId primitive.ObjectID) error {
	filter := bson.M{
		"$and": []bson.M{
			{"client_ids": bson.M{"$in": []primitive.ObjectID{clientId}}},
			{"list_user_ids": bson.M{"$in": []primitive.ObjectID{userID}}},
		},
	}

	update := bson.M{
		"$pull": bson.M{"list_user_ids": userID},
	}

	if err := usc.adAcc.UpdateManyAdAccountRepo(ctx, filter, update); err != nil {
		return err
	}

	if err := usc.pageRepo.UpdateManyPageRepo(ctx, filter, update); err != nil {
		return err
	}

	if err := usc.pixelRepo.UpdateManyPixelRepo(ctx, filter, update); err != nil {
		return err
	}

	if err := usc.catalogueRepo.UpdateManyCatalogueRepo(ctx, filter, update); err != nil {
		return err
	}

	return nil
}

/**
 * Upsert UserId to Pixel - STATUS - REMOVE
 */
// func (usc *apiUserUsc) upSertUserIdToPage(ctx context.Context, userID primitive.ObjectID, pageIds []string) error {
// 	filter := bson.M{"page_id": bson.M{"$in": pageIds}}
// 	update := bson.M{
// 		// "$set":      bson.M{"updated_by": userID},
// 		"$addToSet": bson.M{"list_user_ids": userID},
// 	}
// 	if err := usc.pageRepo.UpdateManyPageRepo(ctx, filter, update); err != nil {
// 		return err
// 	}
// 	return nil
// }

/**
 * Upsert UserId to Pixel - STATUS - REMOVE
 */
// func (usc *apiUserUsc) upSertUserIdToPixel(ctx context.Context, userID primitive.ObjectID, pixelIds []string) error {
// 	filter := bson.M{"id": bson.M{"$in": pixelIds}}
// 	update := bson.M{
// 		"$set":      bson.M{"updated_by": userID},
// 		"$addToSet": bson.M{"list_user_ids": userID},
// 	}

// 	if err := usc.pixelRepo.UpdateManyPixelRepo(ctx, filter, update); err != nil {
// 		return err
// 	}
// 	return nil
// }

/**
 * Get List Page of User
 */

func (usc *apiUserUsc) GetListPageApiUserUsc(ctx context.Context, userId primitive.ObjectID) ([]string, error) {

	filter := bson.M{}
	page, err := usc.repo.FindOneUserRepo(ctx, filter)
	if err != nil {
		usc.logger.Error(err)
		return nil, err
	}

	return page.ListPageIds, nil
}

/**
 * Get List Role User
 */

func (usc *apiUserUsc) GetRoleUserUsc(ctx context.Context, userId primitive.ObjectID) (string, error) {
	filter := bson.M{}
	role, err := usc.roleRepo.FindOneRoleRepo(ctx, filter)
	if err != nil {
		usc.logger.Error(err)
		return "", err
	}

	return role.Name, nil
}

/**
 * Get User by ID
 */
func (usc *apiUserUsc) GetUserByIDUsc(ctx context.Context, userId primitive.ObjectID) (*responses.DetailsUser, error) {

	pipeline := pipelines.PipelineGetDetatilInfoUser(userId)

	user, err := usc.repo.FindOneDetailUserWithPipelineRepo(ctx, pipeline)
	if err != nil {
		usc.logger.Error(err)
		return nil, err
	}

	return user, nil
}
