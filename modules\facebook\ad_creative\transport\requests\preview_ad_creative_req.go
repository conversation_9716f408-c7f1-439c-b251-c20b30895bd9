package requests

import (
	"godsp/modules/facebook/ad_creative/common/errs"

	"github.com/go-playground/validator/v10"
)

type PreviewAdCreativeReq struct {
	Creative  map[string]interface{} `json:"creative" validate:"required"`
	AdFormat  string                 `json:"ad_format" validate:"required"`
	AccountID string                 `json:"account_id" validate:"omitempty"`
}

func (req *PreviewAdCreativeReq) Validate() error {
	validate := validator.New()

	err := validate.Struct(req)
	if err != nil {
		for _, err := range err.(validator.ValidationErrors) {
			switch err.Field() {
			case "Creative":
				return errs.ErrGetCreativeObj
			case "AdFormat":
				return errs.ErrGetAdFormat
			case "AccountID":
				return errs.ErrReloadAdCreativeAdAccountId
			}
		}
	}

	return nil
}
