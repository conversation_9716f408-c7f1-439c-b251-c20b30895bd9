package requests

import (
	"godsp/modules/facebook/ad/common/errs"

	"github.com/go-playground/validator/v10"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

type DeleteAdReq struct {
	AdIDs  []string           `json:"ad_ids" validate:"required,dive,numeric,gt=0"`
	UserId primitive.ObjectID `json:"-"`
}

func (req *DeleteAdReq) Validate() []string {
	validate := validator.New()
	var validationErrors []string

	err := validate.Struct(req)

	if err != nil {
		for _, err := range err.(validator.ValidationErrors) {
			switch err.Field() {
			case "AdID":
				validationErrors = append(validationErrors, errs.ErrAdId.Error())
			}
		}
	}
	return validationErrors
}
