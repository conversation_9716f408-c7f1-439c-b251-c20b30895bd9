package requests

import (
	"godsp/modules/facebook/ad_pixel/common/errs"

	"github.com/go-playground/validator/v10"
)

type ListAdPixelReq struct {
	AccountID string `json:"account_id" validate:"required,numeric,gt=0"`
}

func (req *ListAdPixelReq) Validate() error {
	validate := validator.New()

	err := validate.Struct(req)
	if err != nil {
		for _, err := range err.(validator.ValidationErrors) {
			switch err.Field() {
			case "AccountID":
				return errs.ErrAdPixelAdAccount
			}
		}
	}

	return nil
}
