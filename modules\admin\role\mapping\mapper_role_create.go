package mapping

import (
	"godsp/modules/admin/common/admconst"
	"godsp/modules/admin/role/entity"
	"godsp/modules/admin/role/transport/requests"
	"godsp/pkg/gos/utils"
)

func MapperCreateRole(req *requests.PayloadRoleCreation) (*entity.RoleCreation, error) {
	timeNow := utils.TimeNowLocationHCM()

	status := admconst.StatusCreateNameValue[req.Status]
	data := &entity.RoleCreation{
		CreatedBy: req.UserID,
		CreatedAt: timeNow,
		UpdatedAt: timeNow,
		Name:      req.Name,
		RoleName:  req.RoleName,
		Status:    status,
		Ordering:  req.Ordering,
	}
	return data, nil
}
