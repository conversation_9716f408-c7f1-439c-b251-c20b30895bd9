package internal

import (
	"context"
	"godsp/modules/admin/permission"
	"godsp/modules/admin/role"
	"godsp/modules/admin/user"
	"godsp/modules/facebook/targeting/routes"

	"github.com/dev-networldasia/dspgos/sctx"
)

/**
 * Index mongodb for database
 */
func SetIndexMongoDatabase(serviceCtx sctx.ServiceContext) error {
	//role
	ctx := context.Background()
	roleIndex := role.SetupIndexMongoRole(serviceCtx)
	if err := roleIndex.CreateRoleIndex(ctx); err != nil {
		return err
	}

	userIndex := user.SetupIndexMongoUser(serviceCtx)
	if err := userIndex.CreateUserIndex(ctx); err != nil {
		return err
	}

	permissionIndex := permission.SetupIndexMongoPermission(serviceCtx)
	if err := permissionIndex.CreatePermissionIndex(ctx); err != nil {
		return err
	}

	citySubCitiesIndex := routes.SetupIndexMongoCitySubCitie(serviceCtx)
	if err := citySubCitiesIndex.CreateCitySubCitiesSearchIndex(ctx); err != nil {
		return err
	}

	return nil
}
