package responses

import (
	"godsp/modules/admin/user/entity"

	"go.mongodb.org/mongo-driver/bson/primitive"
)

type UserDataTable struct {
	ID          primitive.ObjectID `json:"_id"`
	Email       string             `json:"email"`
	RoleName    string             `json:"role_name"`
	Image       string             `json:"image"`
	FullName    string             `json:"full_name"`
	Phone       *string            `json:"phone,omitempty"`
	Birthday    *string            `json:"birthday,omitempty"`
	Username    *string            `json:"username,omitempty"`
	Status      int                `json:"status"`
	Gender      int                `json:"gender"`
	UserCreated string             `json:"created_by"`
	UserUpdated string             `json:"updated_by"`
	CreatedAt   string             `json:"created_at"`
	UpdatedAt   string             `json:"updated_at"`
	Client      *entity.ClientInfo `json:"client"`
}
