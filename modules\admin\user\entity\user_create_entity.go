package entity

import (
	"godsp/pkg/gos/utils"
	"time"

	"go.mongodb.org/mongo-driver/bson/primitive"
)

type UserCreation struct {
	FirstName   string             `json:"first_name" bson:"first_name"`
	LastName    string             `json:"last_name" bson:"last_name"`
	FullName    string             `json:"full_name" bson:"full_name"`
	Username    *string            `json:"username,omitempty" bson:"username,omitempty"`
	Image       string             `json:"image" bson:"image"`
	Status      int                `json:"status" bson:"status"`
	RoleID      primitive.ObjectID `json:"role_id" bson:"role_id"`
	AdAccountID string             `json:"ad_account_id" bson:"ad_account_id"`
	Email       string             `json:"email" bson:"email"`
	Gender      int                `json:"gender" bson:"gender"`
	AuthType    string             `json:"auth_type" bson:"auth_type"`
	Salt        string             `json:"salt" bson:"salt"`
	Password    string             `json:"password" bson:"password"`
	CreatedBy   primitive.ObjectID `json:"created_by" bson:"created_by"`
	CreatedAt   time.Time          `json:"created_at" bson:"created_at"`
	UpdatedAt   time.Time          `json:"updated_at" bson:"updated_at"`

	ListPageIds  []string            `json:"list_page_ids" bson:"list_page_ids"`
	ListPixelIds []string            `json:"list_pixel_ids" bson:"list_pixel_ids"`
	ClientID     *primitive.ObjectID `json:"client_id,omitempty" bson:"client_id,omitempty"`
}

func (UserCreation) CollectionName() string {
	return UserEntity{}.CollectionName()
}

func (u *UserCreation) BeforeSave() {
	now := utils.TimeNowLocationHCM()
	if u.CreatedAt.IsZero() {
		u.CreatedAt = now
	}
	u.UpdatedAt = now
}
