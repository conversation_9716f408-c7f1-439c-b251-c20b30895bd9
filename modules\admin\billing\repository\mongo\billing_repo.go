package mongo

import (
	"context"
	"errors"
	"fmt"
	"godsp/modules/admin/billing/entity"
	userEntity "godsp/modules/admin/user/entity"
	adAccountEntity "godsp/modules/facebook/ad_account/entity"

	"godsp/modules/facebook/common/fberrs"
	"godsp/pkg/sctx/core"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
)

type billingRepo struct {
	DB         *mongo.Database
	Collection *mongo.Collection
}

func NewBillingRepo(DB *mongo.Database) *billingRepo {
	return &billingRepo{
		DB:         DB,
		Collection: DB.Collection(entity.BillingEntity{}.CollectionName()),
	}
}

/**
 * Create index
 */
func (r *billingRepo) CreateBillingIndex(ctx context.Context) error {
	cursor, err := r.Collection.Indexes().List(ctx)
	if err != nil {
		return err
	}
	defer cursor.Close(ctx)

	for cursor.Next(ctx) {
		var index bson.M
		if err := cursor.Decode(&index); err != nil {
			return err
		}

		if indexName, ok := index["description"].(string); ok && indexName == "billing_description_index" {
			return nil
		}

		if indexName, ok := index["amount"].(string); ok && indexName == "billing_amount_index" {
			continue
		}
	}

	descriptionIndexModel := mongo.IndexModel{
		Keys:    bson.M{"description": 1},
		Options: options.Index().SetUnique(true).SetName("billing_description_index"),
	}

	amountIndexModel := mongo.IndexModel{
		Keys:    bson.M{"amount": 1},
		Options: options.Index().SetUnique(true).SetName("billing_amount_index"),
	}

	_, err = r.Collection.Indexes().CreateOne(ctx, descriptionIndexModel)
	if err != nil {
		return err
	}

	_, err = r.Collection.Indexes().CreateOne(ctx, amountIndexModel)
	return err
}

/**
 * Max ordering
 */
func (r *billingRepo) MaxOrderingRepo(ctx context.Context, filter interface{}) (int64, error) {
	var result struct {
		MaxOrdering int64 `bson:"maxOrdering"`
	}

	pipeline := []bson.M{}
	if filter != nil {
		pipeline = append(pipeline, bson.M{"$match": filter})
	}

	pipeline = append(pipeline, bson.M{
		"$group": bson.M{
			"_id":         nil,
			"maxOrdering": bson.M{"$max": "$ordering"},
		},
	})

	cursor, err := r.Collection.Aggregate(ctx, pipeline)
	if err != nil {
		return 0, err
	}
	defer cursor.Close(ctx)

	if cursor.Next(ctx) {
		if err := cursor.Decode(&result); err != nil {
			return 0, err
		}
		return result.MaxOrdering, nil
	}

	return 0, nil
}

/**
 * FindOne billing
 */
func (r *billingRepo) FindOneBillingRepo(ctx context.Context, filter interface{}, opts ...*options.FindOneOptions) (*entity.BillingEntity, error) {
	// Fetch the billing document
	var billingEntity entity.BillingEntity
	err := r.Collection.FindOne(ctx, filter, opts...).Decode(&billingEntity)
	if errors.Is(err, mongo.ErrNoDocuments) {
		return nil, core.ErrNotFound
	}
	if err != nil {
		return nil, err
	}

	// Fetch the user who created the billing entity
	if !billingEntity.CreatedBy.IsZero() {
		userCollection := r.DB.Collection(userEntity.UserEntity{}.CollectionName())

		var user userEntity.UserEntity
		projection := bson.M{
			"full_name":     1,
			"email":         1,
			"ad_account_id": 1,
			"phone":         1,
		}
		err = userCollection.FindOne(ctx, bson.M{"_id": billingEntity.CreatedBy}, options.FindOne().SetProjection(projection)).Decode(&user)
		if err != nil && !errors.Is(err, mongo.ErrNoDocuments) {
			return nil, err
		}
		if err == nil {
			billingEntity.UserCreated = &user
		}

		// Fetch ad account details if ad_account_id exists
		if user.AdAccountID != "" {
			adAccountCollection := r.DB.Collection(adAccountEntity.AdAccountEntity{}.CollectionName())

			var adAccount adAccountEntity.AdAccountEntity
			err = adAccountCollection.FindOne(ctx, bson.M{"account_id": user.AdAccountID}).Decode(&adAccount)
			if err != nil && !errors.Is(err, mongo.ErrNoDocuments) {
				return nil, err
			}
			if err == nil {
				billingEntity.AdAccount = &adAccount
			}
		}

	}

	return &billingEntity, nil
}

/**
 * Find billing
 */
func (r *billingRepo) FindBillingRepo(ctx context.Context, filter interface{}, opts ...*options.FindOptions) (*[]entity.BillingEntity, error) {
	var billings []entity.BillingEntity

	cursor, err := r.Collection.Find(ctx, filter, opts...)
	if err != nil {
		return nil, err
	}
	defer cursor.Close(ctx)

	if cursor == nil {
		return nil, fberrs.ErrNilCursorValue
	}
	err = cursor.All(ctx, &billings)
	if err != nil {
		return nil, err
	}

	return &billings, nil
}

/***
 * insert billing
 */
func (r *billingRepo) InsertBillingRepo(ctx context.Context, billing *entity.BillingEntity) error {
	_, err := r.Collection.InsertOne(ctx, billing)
	if err != nil {
		if mongo.IsDuplicateKeyError(err) {
			return core.ErrDuplicate
		}
		return err
	}

	return nil
}

/***
 * update one filter interface{}, update interface{}, opts ...*options.UpdateOptions
 */
func (r *billingRepo) UpdateOneBillingRepo(ctx context.Context, filter interface{}, billing *entity.BillingEntity, opts ...*options.UpdateOptions) error {
	update := bson.M{
		"$set": *billing,
	}
	_, err := r.Collection.UpdateOne(ctx, filter, update)
	if err != nil {
		if mongo.IsDuplicateKeyError(err) {
			return core.ErrNotFound
		}
		return err
	}

	return nil
}

func (r *billingRepo) UpdateOneBillingRepoWithAttr(ctx context.Context, filter interface{}, billing *entity.BillingEntity, opts ...*options.UpdateOptions) error {
	updateFields := bson.M{}

	// Only update non-zero fields (those fields that have been changed)
	if billing.UpdatedBy != primitive.NilObjectID {
		if updateFields["$set"] == nil {
			updateFields["$set"] = bson.M{}
		}
		updateFields["$set"].(bson.M)["updated_by"] = billing.UpdatedBy
	}
	if !billing.UpdatedAt.IsZero() {
		if updateFields["$set"] == nil {
			updateFields["$set"] = bson.M{}
		}
		updateFields["$set"].(bson.M)["updated_at"] = billing.UpdatedAt
	}
	if billing.Status != 0 {
		if updateFields["$set"] == nil {
			updateFields["$set"] = bson.M{}
		}
		updateFields["$set"].(bson.M)["status"] = billing.Status
	}
	if billing.Note != "" {
		if updateFields["$set"] == nil {
			updateFields["$set"] = bson.M{}
		}
		updateFields["$set"].(bson.M)["note"] = billing.Note
	}
	if billing.Ordering != 0 {
		if updateFields["$set"] == nil {
			updateFields["$set"] = bson.M{}
		}
		updateFields["$set"].(bson.M)["ordering"] = billing.Ordering
	}

	// If no fields are set, return an error
	if len(updateFields) == 0 || len(updateFields["$set"].(bson.M)) == 0 {
		return fmt.Errorf("no fields to update")
	}

	// Execute the update
	_, err := r.Collection.UpdateOne(ctx, filter, updateFields, opts...)
	if err != nil {
		if mongo.IsDuplicateKeyError(err) {
			return core.ErrNotFound
		}
		return err
	}

	return nil
}
