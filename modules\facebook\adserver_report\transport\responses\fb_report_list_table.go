package responses

import "godsp/modules/facebook/adserver_report/entity"

// FacebookAdInsight maps all available Facebook Insights fields into a MongoDB document
type FBReportDetailTableItem struct {
	ID               string                `json:"_id,omitempty" bson:"_id,omitempty"`
	AccountID        string                `json:"account_id,omitempty" bson:"account_id"`
	CampaignID       string                `json:"campaign_id,omitempty" bson:"campaign_id"`
	AdsetID          string                `json:"adset_id,omitempty" bson:"adset_id"`
	AdID             string                `json:"ad_id,omitempty" bson:"ad_id"`
	CampaignName     string                `json:"campaign_name,omitempty" bson:"campaign_name"`
	AdsetName        string                `json:"adset_name,omitempty" bson:"adset_name"`
	AdName           string                `json:"ad_name,omitempty" bson:"ad_name"`
	Objective        string                `json:"objective,omitempty" bson:"objective"`
	BuyingType       string                `json:"buying_type,omitempty" bson:"buying_type"`
	OptimizationGoal string                `json:"optimization_goal,omitempty" bson:"optimization_goal"`
	Actions          *[]entity.ActionStats `json:"actions,omitempty" bson:"actions"`
	Clicks           int32                 `json:"clicks,omitempty" bson:"clicks"`
	CPC              float64               `json:"cpc,omitempty" bson:"cpc"`
	CPM              float64               `json:"cpm,omitempty" bson:"cpm"`
	CTR              float64               `json:"ctr,omitempty" bson:"ctr"`
	Impressions      int32                 `json:"impressions,omitempty" bson:"impressions"`
	Reach            int32                 `json:"reach,omitempty" bson:"reach"`
	SocialSpend      int32                 `json:"social_spend,omitempty" bson:"social_spend"`
	Spend            int32                 `json:"spend,omitempty" bson:"spend"`
	Date             string                `json:"date,omitempty" bson:"date"`

	Frequency float64 `json:"frequency,omitempty" bson:"frequency"`

	Video25WatcheActions        float64 `json:"video_p25_watched_actions,omitempty" bson:"video_p25_watched_actions"`
	Video50WatcheActions        float64 `json:"video_p50_watched_actions,omitempty" bson:"video_p50_watched_actions"`
	Video75WatcheActions        float64 `json:"video_p75_watched_actions,omitempty" bson:"video_p75_watched_actions"`
	Video95WatcheActions        float64 `json:"video_p95_watched_actions,omitempty" bson:"video_p95_watched_actions"`
	Video100WatcheActions       float64 `json:"video_p100_watched_actions,omitempty" bson:"video_p100_watched_actions"`
	CostPer15SecVideoView       float64 `json:"cost_per_15_sec_video_view,omitempty" bson:"cost_per_15_sec_video_view"`
	CostPerThruplay             float64 `json:"cost_per_thruplay,omitempty" bson:"cost_per_thruplay"`
	VideoAvgTimeWatchedActions  float64 `json:"video_avg_time_watched_actions,omitempty" bson:"video_avg_time_watched_actions"`
	InlineLinkClicks            int32   `json:"inline_link_clicks,omitempty" bson:"inline_link_clicks"`
	InlineLinkClickCtr          float64 `json:"inline_link_click_ctr,omitempty" bson:"inline_link_click_ctr"`
	CostPerInlinePostEngagement float64 `json:"cost_per_inline_post_engagement,omitempty" bson:"cost_per_inline_post_engagement"`
	CostPerInlineLinkClick      float64 `json:"cost_per_inline_link_click,omitempty" bson:"cost_per_inline_link_click"`
}
