package responses

type DatatableAudienceResq struct {
	Draw int                  `json:"draw"`
	Data *[]AudienceDatatable `json:"data"`
	Msg  string               `json:"msg,omitempty"`
}

type AudienceDatatable struct {
	DTRowId                    string `json:"DT_RowId"`
	AudienceID                 string `json:"audience_id"`
	Name                       string `json:"name"`
	Description                string `json:"description"`
	TimeUpdated                string `json:"time_updated,omitempty"`
	Type                       string `json:"type"`
	Status                     string `json:"status"`
	ApproximateCountUpperBound int    `json:"approximate_count_upper_bound"`
	ApproximateCountLowerBound int    `json:"approximate_count_lower_bound"`
	DeleteTime                 int    `json:"delete_time,omitempty"`
	Subtype                    string `json:"subtype"`
	Rule                       string `json:"rule"`
}
