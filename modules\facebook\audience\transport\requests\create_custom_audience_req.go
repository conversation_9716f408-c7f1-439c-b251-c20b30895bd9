package requests

import (
	"time"

	"github.com/go-playground/validator/v10"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

type CreateCustomAudienceReq struct {
	AccountID   string `json:"account_id,omitempty" bson:"account_id"`
	Name        string `json:"name,omitempty" bson:"name"`
	Description string `json:"description,omitempty" bson:"description"`
	Rule        string `json:"rule,omitempty" bson:"rule"`
	Subtype     string `json:"subtype,omitempty" bson:"subtype,omitempty"`

	// Client
	ClientID  primitive.ObjectID `json:"-"`
	CreatedBy primitive.ObjectID `json:"-"`
	CreatedAt time.Time          `json:"-"`
}

func (req *CreateCustomAudienceReq) Validate() []string {
	validate := validator.New()
	var validationErrors []string

	err := validate.Struct(req)
	if err != nil {
		for _, err := range err.(validator.ValidationErrors) {
			fieldName := err.Field()

			switch fieldName {
			case "AccountID":
				validationErrors = append(validationErrors, "AccountID is required")
			case "Name":
				validationErrors = append(validationErrors, "Name must be between 3 and 100 characters")
			case "Description":
				validationErrors = append(validationErrors, "Description must be at most 255 characters")
			case "Rule":
				validationErrors = append(validationErrors, "Rule must be a valid JSON string")
			case "Subtype":
				validationErrors = append(validationErrors, "Subtype must be a valid value")
			default:
				validationErrors = append(validationErrors, "Invalid value for field: "+fieldName)
			}
		}
	}

	return validationErrors
}
