package entity

// InteractiveComponentsSpec is mainly used for Video Poll Ads.
type InteractiveComponentsSpec struct {
	Components []*Component `json:"components" bson:"components"`
}

// Component of the Interactive component struct.
type Component struct {
	Type         string        `json:"type" bson:"type"`
	PositionSpec *PositionSpec `json:"position_spec" bson:"position_spec"`
	PollSpec     *PollSpec     `json:"poll_spec" bson:"poll_spec"`
}

// PollSpec represents the questions and answers of a poll.
type PollSpec struct {
	QuestionText        string              `json:"question_text" bson:"question_text"`
	OptionAText         string              `json:"option_a_text" bson:"option_a_text"`
	OptionBText         string              `json:"option_b_text" bson:"option_b_text"`
	ThemeColor          string              `json:"theme_color,omitempty" bson:"theme_color,omitempty"`
	OptionACallToAction *OptionCallToAction `json:"option_a_call_to_action,omitempty" bson:"option_a_call_to_action,omitempty"`
	OptionBCallToAction *OptionCallToAction `json:"option_b_call_to_action,omitempty" bson:"option_b_call_to_action,omitempty"`
	LinkDisplay         string              `json:"link_display,omitempty" bson:"link_display,omitempty"`
}

// PositionSpec describes the position of an interactive component.
type PositionSpec struct {
	X        float64 `json:"x" bson:"x"`
	Y        float64 `json:"y" bson:"y"`
	Width    float64 `json:"width" bson:"width"`
	Height   float64 `json:"height" bson:"height"`
	Rotation float64 `json:"rotation" bson:"rotation"`
}

// OptionCallToAction represents the action and call to action of an answer of a poll.
type OptionCallToAction struct {
	Value *OptionCallToActionValue `json:"value" bson:"value"`
	Type  string                   `json:"type" bson:"type"`
}

// OptionCallToActionValue describes the link of a call to action answer.
type OptionCallToActionValue struct {
	Link       string `json:"link" bson:"link"`
	LinkFormat string `json:"link_format,omitempty" bson:"link_format,omitempty"`
}
