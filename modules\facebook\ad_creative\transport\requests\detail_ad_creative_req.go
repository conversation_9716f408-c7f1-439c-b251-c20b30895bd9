package requests

import (
	"godsp/modules/facebook/ad_creative/common/errs"

	"github.com/go-playground/validator/v10"
)

type DetailAdCreativeReq struct {
	CreativeID string `json:"creative_id"`
}

func (req *DetailAdCreativeReq) Validate() error {
	validate := validator.New()

	err := validate.Struct(req)
	if err != nil {
		for _, err := range err.(validator.ValidationErrors) {
			switch err.Field() {
			case "CreativeID":
				return errs.ErrReloadAdCreativeCreativeId
			}
		}
	}

	return nil
}
