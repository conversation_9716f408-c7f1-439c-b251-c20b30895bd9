package api

import (
	"godsp/conf"
	"godsp/modules/facebook/campaign/transport/requests"
	"godsp/pkg/gos/utils"
	"godsp/pkg/sctx/core"
	"net/http"
	"strconv"

	"github.com/gofiber/fiber/v2"
)

/***
 * Get Details camp api
 */

func (a *campaignApi) GetDetailCampaignApi() fiber.Handler {
	return func(c *fiber.Ctx) error {
		campaignId := c.Params("campaign_id")

		intID, err := strconv.Atoi(campaignId)
		if err != nil || intID < 0 {
			return core.ReturnErrForPermissionDenied(c)
		}

		// userId := utils.GetUserId(c.Context())
		// payload.UserId = userId

		camp, err := a.usc.FindOneCampaignUsc(c.Context(), campaignId)
		if err != nil {
			return core.ReturnErrForApi(c, "Campaign not found")
		}

		return c.Status(http.StatusOK).JSON(core.ResponseData(map[string]interface{}{
			"msg":  "Get Campaign successfully",
			"data": camp,
		}))
	}
}

/***
 * Get Details camp have adset and ad api
 */

func (a *campaignApi) GetDetailAdsetAdCampaignApi() fiber.Handler {
	return func(c *fiber.Ctx) error {
		var payload requests.GetDetailCampaignReq
		if err := c.BodyParser(&payload); err != nil {
			// fmt.Printf("\n \n Error parsing body: %+v\n", err)
			return core.ReturnErrsForApi(c, err)
		}

		validationErrors := payload.Validate()
		if len(validationErrors) > 0 {
			return core.ReturnErrsForApi(c, validationErrors)
		}

		user, err := utils.GetInfoUserBasic(c.Context())
		// fmt.Printf("User Info error : %+v\n", err)
		if err != nil && user.RoleName != conf.SysConf.RoleAdmin {
			return core.ReturnErrForPermissionDenied(c)
		}

		payload.UserId = *user.UserId

		camp, err := a.usc.FindOneDetailsCampaignUsc(c.Context(), &payload)
		if err != nil {
			return core.ReturnErrForPermissionDenied(c)
		}

		return c.Status(http.StatusOK).JSON(core.ResponseData(map[string]interface{}{
			"msg":  "Get Campaign successfully!",
			"data": camp,
		}))
	}
}

// func (a *campaignApi) GetDetailAdsetAdCampaignApi() fiber.Handler {
// 	return func(c *fiber.Ctx) error {
// 		campaignId := c.Params("campaign_id")

// 		intID, err := strconv.Atoi(campaignId)
// 		if err != nil || intID < 0 {
// 			return core.ErrNotFound
// 		}

// 		// userId := utils.GetUserId(c.Context())
// 		// payload.UserId = userId

// 		camp, err := a.usc.FindOneDetailsCampaignUsc(c.Context(), campaignId)
// 		if err != nil {
// 			return core.ErrNotFound
// 		}

// 		return c.Status(http.StatusOK).JSON(core.ResponseData(map[string]interface{}{
// 			"msg":  "Get Campaign successfully",
// 			"data": camp,
// 		}))
// 	}
// }
