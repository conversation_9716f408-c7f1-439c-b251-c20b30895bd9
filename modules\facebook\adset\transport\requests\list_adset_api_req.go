package requests

import (
	"godsp/modules/admin/client/common/errs"
	"godsp/modules/facebook/adset/common/consts"
	"godsp/pkg/gos/utils"
	"time"

	"go.mongodb.org/mongo-driver/bson/primitive"
)

type ListAdsetReq struct {
	CampaignIds []string `json:"campaign_ids,omitempty" form:"campaign_ids"`
	Draw        int      `json:"draw" form:"draw"`
	Length      int64    `json:"length" form:"length"`
	Start       int64    `json:"start" form:"start"`
	SearchValue *string  `json:"search_value" form:"search_value"`
	Order       []Order  `json:"order"`
	SortField   string   `json:"-" form:"-"`
	SortOrder   int      `json:"-" form:"-"`

	Time      []string   `json:"time,omitempty" form:"time"`
	StartTime *time.Time `json:"-" form:"-"`
	EndTime   *time.Time `json:"-" form:"-"`

	UserId primitive.ObjectID `json:"-" form:"-"`

	AccountID   string             `json:"accountId,omitempty" form:"accountId"`
	ClientIDStr string             `json:"clientId,omitempty" form:"clientId"`
	ClientID    primitive.ObjectID `json:"-" form:"-"`
}

type Order struct {
	Column int    `json:"column" form:"column"`
	Dir    string `json:"dir" form:"dir"`
	Name   string `json:"name" form:"name"`
}

func (req *ListAdsetReq) Validate() error {

	if req.ClientIDStr != "" && !utils.ValidateObjectID(req.ClientIDStr) {
		return errs.ErrIDClientValidate
	}

	if req.ClientIDStr != "" {
		clientID, _ := primitive.ObjectIDFromHex(req.ClientIDStr)
		req.ClientID = clientID
	}

	req.SortOrder = -1 // mặc định giảm dần
	if len(req.Order) == 0 {
		req.SortField = consts.ADSET_ORDER_DEFAULT // ví dụ "updated_time"
	} else {
		orderColumn := req.Order[0].Column
		sortField, ok := consts.FieldAdsetSort[orderColumn]
		if !ok {
			req.SortField = consts.ADSET_ORDER_DEFAULT
		} else {
			req.SortField = sortField
		}

		if req.Order[0].Dir == "asc" {
			req.SortOrder = 1
		}
	}

	// req.SortOrder = 1
	// if req.Order == nil && len(req.Order) == 0 {
	// 	req.SortField = consts.ADSET_ORDER_DEFAULT
	// } else {
	// 	orderColumn := req.Order[0].Column
	// 	if sortField, ok := consts.FieldAdsetSort[orderColumn]; !ok {
	// 		req.SortField = consts.ADSET_ORDER_DEFAULT
	// 	} else {
	// 		req.SortField = sortField
	// 	}

	// 	if sortOrder := req.Order[0].Dir; sortOrder == "desc" {
	// 		req.SortOrder = -1
	// 	}

	// 	if req.SortField == consts.FieldAdsetSort[1] {
	// 		req.SortOrder = -1
	// 	}
	// }

	if len(req.Time) < 2 {
		return nil
	}

	startTime, err := time.Parse(time.RFC3339, req.Time[0])
	if err != nil {
		return nil
	}
	req.StartTime = &startTime

	endTime, err := time.Parse(time.RFC3339, req.Time[1])
	if err != nil {
		req.StartTime = nil
		return nil
	}
	req.EndTime = &endTime

	return nil
}
