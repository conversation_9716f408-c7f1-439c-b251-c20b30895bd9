package api

import (
	"godsp/conf"
	"godsp/modules/facebook/ad/transport/requests"
	"godsp/modules/facebook/ad/transport/responses"
	"godsp/pkg/gos/utils"
	"godsp/pkg/sctx/core"

	"github.com/gofiber/fiber/v2"
)

/**
 * Api load list page
 */
func (a *adApi) ListAdDataTableApi() fiber.Handler {
	return func(c *fiber.Ctx) error {
		resData := responses.DataTableAdsRes{}
		var payload requests.ListAdTableReq
		if err := c.BodyParser(&payload); err != nil {
			resData.Msg = err.Error()
			return c.Status(fiber.StatusBadRequest).JSON(core.ResponseData(map[string]interface{}{
				"msg":  err.Error(),
				"data": nil,
			}))
		}

		userInfo, _ := utils.GetInfoUserAuth(c.Context())
		if userInfo.RoleName == conf.SysConf.RoleAdmin {
			// fmt.Println("admin", payload.AccountID)
			if payload.AccountID == "" && payload.ClientIDStr == "" {
				return c.Status(fiber.StatusBadRequest).JSON(core.ResponseData(map[string]interface{}{
					"msg":  "Account ID or Client ID is required",
					"data": nil,
				}))
			}
		}

		payload.UserId = *userInfo.UserId
		resData.Draw = payload.Draw
		if err := payload.Validate(); err != nil {
			resData.Msg = err.Error()
			return c.JSON(resData)
		}

		ads, err := a.usc.ListDatatableAdsUsc(c.Context(), &payload)
		if err != nil {
			resData.Msg = err.Error()
			return c.JSON(resData)
		}

		return c.JSON(ads)
	}
}
