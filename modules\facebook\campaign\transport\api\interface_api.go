package api

import (
	"context"
	"godsp/modules/facebook/campaign/entity"
	"godsp/modules/facebook/campaign/transport/requests"
	"godsp/modules/facebook/campaign/transport/responses"

	"go.mongodb.org/mongo-driver/bson/primitive"
)

type ApiCampaignUsc interface {
	ReloadCampaignUsc(ctx context.Context, accountId string, userId primitive.ObjectID) []string
	ReloadCampaignDetail(ctx context.Context, campaignId string, userId primitive.ObjectID) error
	CreateCampaignUsc(ctx context.Context, payload *requests.CreateCampaignReq) (*entity.CampaignEntity, error)
	UpdateCampaignUsc(ctx context.Context, payload *requests.UpdateCampaignReq) error
	UpdateNameOrStatusCampaignUsc(ctx context.Context, payload *requests.UpdateNameStatusCampaignReq) error
	FindOneCampaignUsc(ctx context.Context, campaignId string) (*entity.CampaignEntity, error)
	FindOneDetailsCampaignUsc(ctx context.Context, payload *requests.GetDetailCampaignReq) (*entity.CampaignDetailResponseEntity, error)
	ListDatatableCampaignUsc(ctx context.Context, payload *requests.ListTableCampaignReq) (*responses.DatatableCampaignResq, error)
	ListCampaignUsc(ctx context.Context, payload *requests.CampaignReq) (*[]entity.CampaignEntity, error)
	DeleteCampaignUsc(ctx context.Context, payload *requests.DeleteCampaignReq) ([]string, error)
	GetRoleByUserId(ctx context.Context, userId primitive.ObjectID) (*string, error)
	ApproveCampaignUsc(ctx context.Context, payload *requests.ApproveCampaignReq, userId primitive.ObjectID) error
}

type campaignApi struct {
	usc ApiCampaignUsc
}

func NewCampaignApi(usc ApiCampaignUsc) *campaignApi {
	return &campaignApi{
		usc: usc,
	}
}
