package entity

type ObjectStorySpec struct {
	PageID           string               `json:"page_id,omitempty" bson:"page_id,omitempty"`
	InstagramActorID string               `json:"instagram_actor_id,omitempty" bson:"instagram_actor_id,omitempty"`
	VideoData        *VideoData           `json:"video_data,omitempty" bson:"video_data,omitempty"`
	LinkData         *AdCreativeLinkData  `json:"link_data,omitempty" bson:"link_data,omitempty"`
	PhotoData        *AdCreativePhotoData `json:"photo_data,omitempty" bson:"photo_data,omitempty"`
	TemplateData     *AdCreativeLinkData  `json:"template_data,omitempty" bson:"template_data,omitempty"`
}

type VideoData struct {
	ImageHash       string                          `json:"image_hash,omitempty" bson:"image_hash,omitempty"`
	ImageURL        string                          `json:"image_url,omitempty" bson:"image_url,omitempty"`
	LinkDescription string                          `json:"link_description,omitempty" bson:"link_description,omitempty"`
	Message         string                          `json:"message,omitempty" bson:"message,omitempty"`
	Title           string                          `json:"title,omitempty" bson:"title,omitempty"`
	VideoID         string                          `json:"video_id,omitempty" bson:"video_id,omitempty"`
	CallToAction    *AdCreativeLinkDataCallToAction `json:"call_to_action,omitempty" bson:"call_to_action,omitempty"`
}

type AdCreativeLinkDataCallToAction struct {
	Type  string                               `json:"type,omitempty" bson:"type,omitempty"`
	Value *AdCreativeLinkDataCallToActionValue `json:"value,omitempty" bson:"value,omitempty"`
}

type AdCreativeLinkDataCallToActionValue struct {
	AppDestination string `json:"app_destination,omitempty" bson:"app_destination,omitempty"`
	AppLink        string `json:"app_link,omitempty" bson:"app_link,omitempty"`
	Application    string `json:"application,omitempty" bson:"application,omitempty"`
	EventID        string `json:"event_id,omitempty" bson:"event_id,omitempty"`
	LeadGenFormID  string `json:"lead_gen_form_id,omitempty" bson:"lead_gen_form_id,omitempty"`
	Link           string `json:"link,omitempty" bson:"link,omitempty"`
	LinkCaption    string `json:"link_caption,omitempty" bson:"link_caption,omitempty"`
	LinkFormat     string `json:"link_format,omitempty" bson:"link_format,omitempty"`
	Page           string `json:"page,omitempty" bson:"page,omitempty"`
	ProductLink    string `json:"product_link,omitempty" bson:"product_link,omitempty"`
}

type AdCreativeLinkData struct {
	AdditionalImageIndex                int32                               `json:"additional_image_index,omitempty" bson:"additional_image_index,omitempty"`
	AppLinkSpec                         *AdCreativeLinkDataAppLinkSpec      `json:"app_link_spec,omitempty" bson:"app_link_spec,omitempty"`
	AttachmentStyle                     string                              `json:"attachment_style,omitempty" bson:"attachment_style,omitempty"`
	BrandedContentSharedToSponsorStatus string                              `json:"branded_content_shared_to_sponsor_status,omitempty" bson:"branded_content_shared_to_sponsor_status,omitempty"`
	BrandedContentSponsorPageID         string                              `json:"branded_content_sponsor_page_id,omitempty" bson:"branded_content_sponsor_page_id,omitempty"`
	BrandedContentSponsorRelationship   string                              `json:"branded_content_sponsor_relationship,omitempty" bson:"branded_content_sponsor_relationship,omitempty"`
	CallToAction                        *AdCreativeLinkDataCallToAction     `json:"call_to_action,omitempty" bson:"call_to_action,omitempty"`
	Caption                             string                              `json:"caption,omitempty" bson:"caption,omitempty"`
	ChildAttachments                    []AdCreativeLinkDataChildAttachment `json:"child_attachments,omitempty" bson:"child_attachments,omitempty"`
	Description                         string                              `json:"description,omitempty" bson:"description,omitempty"`
	EventID                             string                              `json:"event_id,omitempty" bson:"event_id,omitempty"`
	ForceSingleLink                     bool                                `json:"force_single_link,omitempty" bson:"force_single_link,omitempty"`
	ImageHash                           string                              `json:"image_hash,omitempty" bson:"image_hash,omitempty"`
	Link                                string                              `json:"link,omitempty" bson:"link,omitempty"`
	Message                             string                              `json:"message,omitempty" bson:"message,omitempty"`
	MultiShareEndCard                   bool                                `json:"multi_share_end_card,omitempty" bson:"multi_share_end_card,omitempty"`
	MultiShareOptimized                 bool                                `json:"multi_share_optimized,omitempty" bson:"multi_share_optimized,omitempty"`
	Name                                string                              `json:"name,omitempty" bson:"name,omitempty"`
	OfferID                             string                              `json:"offer_id,omitempty" bson:"offer_id,omitempty"`
	PageWelcomeMessage                  string                              `json:"page_welcome_message,omitempty" bson:"page_welcome_message,omitempty"`
	Picture                             string                              `json:"picture,omitempty" bson:"picture,omitempty"`
	RetailerItemIDs                     []string                            `json:"retailer_item_ids,omitempty" bson:"retailer_item_ids,omitempty"`
	ShowMultipleImages                  bool                                `json:"show_multiple_images,omitempty" bson:"show_multiple_images,omitempty"`
	FormatOption                        string                              `json:"format_option,omitempty" bson:"format_option,omitempty"`
}

type AdCreativePhotoData struct {
	BrandedContentSharedToSponsorStatus string `json:"branded_content_shared_to_sponsor_status" bson:"branded_content_shared_to_sponsor_status"`
	BrandedContentSponsorPageID         string `json:"branded_content_sponsor_page_id" bson:"branded_content_sponsor_page_id"`
	BrandedContentSponsorRelationship   string `json:"branded_content_sponsor_relationship" bson:"branded_content_sponsor_relationship"`
	Caption                             string `json:"caption" bson:"caption"`
	ImageHash                           string `json:"image_hash" bson:"image_hash"`
	PageWelcomeMessage                  string `json:"page_welcome_message" bson:"page_welcome_message"`
	URL                                 string `json:"url" bson:"url"`
}

type AdCreativeLinkDataAppLinkSpec struct {
	// Native deeplinks to use on Android
	Android []AndroidAppLink `json:"android,omitempty" bson:"android,omitempty"`
	// Native deeplinks to use on iOS
	Ios []IosAppLink `json:"ios,omitempty" bson:"ios,omitempty"`
	// Native deeplinks to use on iPad
	Ipad []IosAppLink `json:"ipad,omitempty" bson:"ipad,omitempty"`
	// Native deeplinks to use on iPhone
	Iphone []IosAppLink `json:"iphone,omitempty" bson:"iphone,omitempty"`
}

type AndroidAppLink struct {
	// The native apps name in the Android store.
	AppName string `json:"app_name,omitempty" bson:"app_name,omitempty"`
	// The fully classified class name of the app for intent generation.
	Class string `json:"class,omitempty" bson:"class,omitempty"`
	// The fully classified package name of the app for intent generation.
	Package string `json:"package,omitempty" bson:"package,omitempty"`
	// The native Android URL that will be navigated to.
	URL string `json:"url,omitempty"`
}

type IosAppLink struct {
	// The native apps name in the iTunes store.
	AppName string `json:"app_name,omitempty" bson:"app_name,omitempty"`
	// The native apps ID in the iTunes store.
	AppStoreID string `json:"app_store_id,omitempty" bson:"app_store_id,omitempty"`
	// The native iOS URL that will be navigated to.
	URL string `json:"url,omitempty" bson:"url,omitempty"`
}

type AdCreativeLinkDataChildAttachment struct {
	// Call to action of this attachment. On Facebook, we support one optional CTA per attachment. If it not specified, there will be no CTA for this attachment. On Instagram, there is one CTA per attachment. If the CTA is not specified, a CTA will be created by the system, using "Learn more" as the type, and the link from this child attachment as the link. If the CTA is specified, its link must be the same as the link of this child attachment.
	CallToAction *AdCreativeLinkDataCallToAction `json:"call_to_action,omitempty" bson:"call_to_action,omitempty"`
	// The display url shown at the end of a video, if the attachment is a video
	Caption string `json:"caption,omitempty" bson:"caption,omitempty"`
	// Overwrites the description of each attachment on Facebook, not used on Instagram.
	Description string `json:"description,omitempty" bson:"description,omitempty"`
	// Image crops, using the crop spec with 100x100 key for Carousel ads. If no 100x100 crop spec is provided, the image would be cropped automatically, unless the image is square already. The final cropped image size needs to be at least 200x200 pixels for Facebook, or 600x600 for Instagram.
	// ImageCrops *AdsImageCrops `json:"image_crops,omitempty"`
	// The image hash of an uploaded image for this attachment. For an ad on Facebook, if neither picture nor image_hash is set, the image of link_data above will be used. For an ad on Instagram, either picture or image_hash is required.
	ImageHash string `json:"image_hash,omitempty" bson:"image_hash,omitempty"`
	// The link of this attachment.
	Link string `json:"link,omitempty" bson:"link,omitempty"`
	// Overwrites the title of the attachment on Facebook, not used on Instagram.
	Name string `json:"name,omitempty" bson:"name,omitempty"`
	// The url of an image for this attachment. For an ad on Facebook, if neither picture nor image_hash is set, the image specified in link_data above will be used. For an ad on Instagram, either picture or image_hash is required.
	Picture string `json:"picture,omitempty" bson:"picture,omitempty"`
	// Whether to force the card to render statically, even in a dynamic ad.
	StaticCard bool `json:"static_card,omitempty" bson:"static_card,omitempty"`
	// ID of an uploaded video, if this attachment is a video. Not supported for Instagram ads.
	VideoID   string                       `json:"video_id,omitempty" bson:"video_id,omitempty"`
	PlaceData *AdCreativeLinkDataPlaceData `json:"place_data,omitempty" bson:"place_data,omitempty"`
}

type AdCreativeLinkDataPlaceData struct {
	LocationSourceID string `json:"location_source_id,omitempty" bson:"location_source_id,omitempty"`
	Type             string `json:"type,omitempty" bson:"type,omitempty"`
}
