package mapping

import (
	"godsp/modules/admin/client/entity"
	"godsp/modules/admin/client/transport/requests"
	"godsp/modules/admin/common/admconst"
)

func MapperCreateClient(req *requests.PayloadClientCreation) *entity.ClientCreationEntity {
	data := &entity.ClientCreationEntity{
		Name:    req.Name,
		Company: req.Company,
		Brand:   req.Brand,
		Email:   req.Email,
		Status:  admconst.StatusCreateNameValue[req.Status],

		Phone:    req.Phone,
		Domain:   req.Domain,
		Logo:     req.Logo,
		Position: req.Position,
		Address:  req.Address,

		AdAccountIDs:  req.AdAccountIDs,
		ClientUserIDs: req.ClientUserIDs,

		CreatedBy: req.UserID,
	}

	return data
}
