package errs

import "errors"

var (
	ErrFBBudgetSchedule         = errors.New("error budget schedule facebook")
	ErrFBBudgetScheduleNotFound = errors.New("error budget schedule facebook not found")

	//validate
	ErrCampaignIdBudgetSchedule      = errors.New("validate campaign id by budget schedule required")
	ErrCampaignIdAdsetBudgetSchedule = errors.New("validate campaign id or adset id by budget schedule required")
	ErrHighDemandPeriodId            = errors.New("The High Demand Period ID is in a valid format.")

	ErrValidateBudgetValue     = errors.New("validate field 'budget_value' must be a positive number")
	ErrValidateBudgetValueType = errors.New("validate field 'budget_value_type' must be 'ABSOLUTE' or 'MULTIPLIER'")
	ErrValidateTimeStart       = errors.New("validate field 'time_start' must be greater than the current time")
	ErrValidateTimeEnd         = errors.New("validate field 'time_end' must be greater than 'time_start' and the current time")

	ErrBudgetScheduleUpdateUnknown = errors.New("Unknown error update budget schedule")
	ErrValidateIsOverlap           = errors.New("Validate date and time range cannot overlap with existing entries")
)
