package entity

import (
	"godsp/pkg/gos/utils"
	"time"

	"go.mongodb.org/mongo-driver/bson/primitive"
)

type RoleCreation struct {
	Name      string             `json:"name" bson:"name"`
	RoleName  string             `json:"role_name" bson:"role_name"`
	Status    int                `json:"status" bson:"status"`
	Ordering  int64              `json:"ordering" bson:"ordering"`
	CreatedBy primitive.ObjectID `json:"created_by" bson:"created_by"`
	CreatedAt time.Time          `json:"created_at" bson:"created_at"`
	UpdatedAt time.Time          `json:"updated_at" bson:"updated_at"`
}

func (RoleCreation) CollectionName() string {
	return RoleEntity{}.CollectionName()
}

func (r *RoleCreation) BeforeSave() {
	now := utils.TimeNowLocationHCM()
	if r.CreatedAt.Is<PERSON>ero() {
		r.CreatedAt = now
	}
	r.UpdatedAt = now
}
