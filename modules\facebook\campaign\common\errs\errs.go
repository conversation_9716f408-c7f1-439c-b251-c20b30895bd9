package errs

import "errors"

var (
	ErrFBCampaignUpdateInvalidate = errors.New("invalidate campaign update")
	ErrFBCampaignList             = errors.New("error list campaign facebook")
	ErrReloadCampaignIdFBNotExist = errors.New("facebook campaign id not exist")

	// Update camp
	ErrCampaignID = errors.New("campaign id is required")
	ErrAdsetID    = errors.New("adset id is required")
	ErrAdID       = errors.New("ad id is required")

	//create camp
	ErrCampaignAccountID            = errors.New("validate ad account campaign required")
	ErrCampaignSpendCapReq          = errors.New("spending limit is required")
	ErrCampaignSpendCapFor          = errors.New("spending is not in the correct format")
	ErrCampaignSpendActiveFor       = errors.New("active spending limit is not in the correct format")
	ErrCampaignSpendActiveIsEnabled = errors.New("active spending limit is not enabled")
	ErrCampaignName                 = errors.New("validate name campaign required")
	ErrCampaignStatus               = errors.New("status is not in the correct format")
	ErrCampaignObjective            = errors.New("validate objective campaign required")
	ErrCampaignSpecialAdCategories  = errors.New("special_ad_categories is not in the correct format")
	ErrCampaignBidStrategy          = errors.New("bid strategy is not in the correct format")
	ErrCampaignBudget               = errors.New("budget is not in the correct format")
	// Budget
	ErrCampaignBudgetToSmall = errors.New("your budget is too small. Increase your budget to continue. ")

	//reload
	ErrReloadCampaignAccountId            = errors.New("validate campaign account id must be a number greater than 0")
	ErrReloadCampaignCampaignId           = errors.New("validate campaign id must be a number greater than 0")
	ErrReloadCampaignAccountIdCampaignId  = errors.New("validate campaign account id or campaign id must be provided")
	ErrCampaignMustBeApprovalBeforeActive = errors.New("approval is required before activating the campaign")

	// list api
	ErrCampaignDataTableEmpty = errors.New("payload datatable not empty")
)
