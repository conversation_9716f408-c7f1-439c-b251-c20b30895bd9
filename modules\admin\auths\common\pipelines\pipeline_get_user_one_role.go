package pipelines

import (
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

// func PipelineGetUserOneRole(email *string, id *primitive.ObjectID) []bson.M {
// 	var match bson.M

// 	if email != nil {
// 		match = bson.M{
// 			"email": email,
// 		}
// 	}
// 	if id != nil {
// 		match = bson.M{
// 			"_id": id,
// 		}
// 	}

// 	pipeline := []bson.M{
// 		{
// 			"$match": match,
// 		},
// 		{
// 			"$lookup": bson.M{
// 				"from":         "admin_roles",
// 				"localField":   "role_id",
// 				"foreignField": "_id",
// 				"as":           "role",
// 			},
// 		},
// 		{
// 			"$lookup": bson.M{
// 				"from":         "fb_ad_accounts",
// 				"localField":   "_id",
// 				"foreignField": "list_user_ids",
// 				"as":           "fb_ad_accounts",
// 			},
// 		},
// 		{
// 			"$unwind": bson.M{
// 				"path":                       "$role",
// 				"preserveNullAndEmptyArrays": true,
// 			},
// 		},
// 		{
// 			"$unwind": bson.M{
// 				"path":                       "$fb_ad_accounts",
// 				"preserveNullAndEmptyArrays": true,
// 			},
// 		},
// 		{
// 			"$addFields": bson.M{
// 				"ad_account_id": "$fb_ad_accounts.account_id",
// 			},
// 		},
// 		{
// 			"$unset": "fb_ad_accounts",
// 		},
// 	}

// 	return pipeline
// }

func PipelineGetUserOneRole(email *string, id *primitive.ObjectID) []bson.M {
	match := bson.M{}

	if email != nil {
		match["email"] = *email
	}
	if id != nil {
		match["_id"] = *id
	}

	pipeline := []bson.M{
		{"$match": match},
		{"$lookup": bson.M{
			"from":         "admin_roles",
			"localField":   "role_id",
			"foreignField": "_id",
			"as":           "role",
		}},
		{"$lookup": bson.M{
			"from":         "fb_ad_accounts",
			"localField":   "_id",
			"foreignField": "list_user_ids",
			"as":           "fb_ad_accounts",
		}},
		{"$lookup": bson.M{
			"from":         "tiktok_advertisers",
			"localField":   "_id",
			"foreignField": "list_user_ids",
			"as":           "tiktok_advertisers",
		}},
		{"$unwind": bson.M{
			"path":                       "$role",
			"preserveNullAndEmptyArrays": true,
		}},
		{"$unwind": bson.M{
			"path":                       "$fb_ad_accounts",
			"preserveNullAndEmptyArrays": true,
		}},
		{"$unwind": bson.M{
			"path":                       "$tiktok_advertisers",
			"preserveNullAndEmptyArrays": true,
		}},
		{"$addFields": bson.M{
			"ad_account_id":        "$fb_ad_accounts.account_id",
			"tiktok_advertiser_id": "$tiktok_advertisers.advertiser_id",
		}},
		{"$unset": bson.A{"fb_ad_accounts", "tiktok_advertisers"}},
	}

	return pipeline
}
