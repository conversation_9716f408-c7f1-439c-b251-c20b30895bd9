package responses

import (
	"encoding/json"
	adserverReportE "godsp/modules/facebook/adserver_report/entity"
	adsetCamp "godsp/modules/facebook/campaign/entity"
	v20 "godsp/pkg/fb-marketing/marketing/v20"
	"time"
)

type ListAdsetsResp struct {
	Draw            int                `json:"draw"`
	Data            *[]AdsetsDatatable `json:"data"`
	RecordsTotal    int64              `json:"recordsTotal"`
	RecordsFiltered int64              `json:"recordsFiltered"`
	Msg             string             `json:"msg,omitempty"`
}

type AdsetsDatatable struct {
	DTRowId string `json:"DT_RowId"`

	// ========== Adset Fields ==========
	Name   string `json:"name,omitempty" bson:"name"`
	Status string `json:"status,omitempty" bson:"status"`

	AttributionSpec json.RawMessage `json:"attribution_spec,omitempty" bson:"attribution_spec"`
	BidAmount       uint64          `json:"bid_amount,omitempty" bson:"bid_amount"`
	BidStrategy     string          `json:"bid_strategy,omitempty" bson:"bid_strategy"`
	BillingEvent    string          `json:"billing_event,omitempty" bson:"billing_event"`
	BudgetRemaining float64         `json:"budget_remaining,omitempty,string" bson:"budget_remaining"`

	ConfiguredStatus           string                     `json:"configured_status,omitempty" bson:"configured_status"`
	DailyBudget                float64                    `json:"daily_budget,omitempty,string" bson:"daily_budget"`
	DailyMinSpendTarget        uint64                     `json:"daily_min_spend_target,omitempty,string" bson:"daily_min_spend_target"`
	DailySpendCap              uint64                     `json:"daily_spend_cap,omitempty,string" bson:"daily_spend_cap"`
	DestinationType            string                     `json:"destination_type,omitempty" bson:"destination_type"`
	DeliveryEstimate           *v20.DeliveryEstimate      `json:"delivery_estimate,omitempty" bson:"delivery_estimate"`
	EffectiveStatus            string                     `json:"effective_status,omitempty" bson:"effective_status"`
	EndTime                    *time.Time                 `json:"end_time,omitempty" bson:"end_time"`
	FrequencyControlSpecs      []v20.FrequencyControlSpec `json:"frequency_control_specs,omitempty" bson:"frequency_control_specs"`
	LifetimeBudget             float64                    `json:"lifetime_budget,omitempty,string" bson:"lifetime_budget"`
	LifetimeMinSpendTarget     uint64                     `json:"lifetime_min_spend_target,omitempty,string" bson:"lifetime_min_spend_target"`
	LifeTimeSpendCap           uint64                     `json:"lifetime_spend_cap,omitempty,string" bson:"lifetime_spend_cap"`
	LifetimeImps               uint64                     `json:"lifetime_imps,omitempty" bson:"lifetime_imps"`
	OptimizationGoal           string                     `json:"optimization_goal,omitempty" bson:"optimization_goal"`
	PacingType                 []string                   `json:"pacing_type,omitempty" bson:"pacing_type"`
	PromotedObject             *v20.PromotedObject        `json:"promoted_object,omitempty" bson:"promoted_object"`
	RecurringBudgetSemantics   bool                       `json:"recurring_budget_semantics,omitempty" bson:"recurring_budget_semantics"`
	StartTime                  time.Time                  `json:"start_time,omitempty" bson:"start_time"`
	Targeting                  *v20.Targeting             `json:"targeting,omitempty" bson:"targeting"`
	TargetingOptimizationTypes map[string]int32           `json:"targeting_optimization_types,omitempty" bson:"targeting_optimization_types"`
	DSABeneficiary             string                     `json:"dsa_beneficiary,omitempty" bson:"dsa_beneficiary"`
	DSAPayor                   string                     `json:"dsa_payor,omitempty" bson:"dsa_payor"`
	IsDynamicCreative          bool                       `json:"is_dynamic_creative,omitempty" bson:"is_dynamic_creative"`
	IsBudgetScheduleEnabled    bool                       `json:"is_budget_schedule_enabled" bson:"is_budget_schedule_enabled"`
	UpdatedAt                  time.Time                  `json:"updated_at,omitempty" bson:"updated_at"`
	CreatedAt                  time.Time                  `json:"created_at,omitempty" bson:"created_at"`

	Campaign *adsetCamp.CampaignEntity `json:"campaign,omitempty" bson:"campaign"`

	// ========== Insight Metadata ==========
	CampaignID      string `json:"campaign_id,omitempty" bson:"campaign_id,omitempty"`
	CampaignName    string `json:"campaign_name,omitempty" bson:"campaign_name,omitempty"`
	AccountID       string `json:"account_id,omitempty" bson:"account_id,omitempty"`
	AccountName     string `json:"account_name,omitempty" bson:"account_name,omitempty"`
	AccountCurrency string `json:"account_currency,omitempty" bson:"account_currency,omitempty"`

	// ========== Mentric Prority ==========
	Impressions    int32 `json:"impressions,omitempty" bson:"impressions,omitempty"`
	Reach          int32 `json:"reach,omitempty" bson:"reach,omitempty"`
	VideoView      int32 `json:"video_view,omitempty" bson:"video_view,omitempty"` // ----> 3 Second video plays
	PostReaction   int32 `json:"post_reaction,omitempty" bson:"post_reaction,omitempty"`
	PostComment    int32 `json:"post_comment,omitempty" bson:"comment,omitempty"`
	PostShare      int32 `json:"post_share,omitempty" bson:"post,omitempty"` // post share
	Clicks         int32 `json:"clicks,omitempty" bson:"clicks,omitempty"`   // click all
	LinkClick      int32 `json:"link_click,omitempty" bson:"link_click,omitempty"`
	PostEngagement int32 `json:"post_engagement,omitempty" bson:"post_engagement,omitempty"`
	PostSave       int32 `json:"post_save,omitempty" bson:"onsite_conversion.post_save,omitempty"` // post share
	PageLike       int32 `json:"page_like,omitempty" bson:"like,omitempty"`

	// ID                 primitive.ObjectID `json:"_id,omitempty" bson:"_id,omitempty"`
	AdID               string `json:"ad_id,omitempty" bson:"ad_id,omitempty"`
	AdName             string `json:"ad_name,omitempty" bson:"ad_name,omitempty"`
	AdSetID            string `json:"adset_id,omitempty" bson:"adset_id,omitempty"`
	AdSetName          string `json:"adset_name,omitempty" bson:"adset_name,omitempty"`
	Objective          string `json:"objective,omitempty" bson:"objective,omitempty"`
	BuyingType         string `json:"buying_type,omitempty" bson:"buying_type,omitempty"`
	AttributionSetting string `json:"attribution_setting,omitempty" bson:"attribution_setting,omitempty"`
	DateStart          string `json:"date_start,omitempty" bson:"date_start,omitempty"`
	DateStop           string `json:"date_stop,omitempty" bson:"date_stop,omitempty"`

	// Field Update
	Date                    string `bson:"date,omitempty" json:"date,omitempty"`
	CanvasAvgViewPercent    string `json:"canvas_avg_view_percent,omitempty"`
	CanvasAvgViewTime       string `json:"canvas_avg_view_time,omitempty"`
	CostPerOutboundClick    string `json:"cost_per_outbound_click,omitempty"`
	CostPerUniqueActionType string `json:"cost_per_unique_action_type,omitempty"`
	CostPerUniqueClick      string `json:"cost_per_unique_click,omitempty"`
	CreatedTime             string `json:"created_time,omitempty"`
	UpdateTime              string `json:"updated_time,omitempty"`
	OutboundClicksCtr       string `json:"outbound_clicks_ctr,omitempty"`
	RunDate                 string `json:"run_date,omitempty"`
	WebsitePurchaseRoas     string `json:"website_purchase_roas,omitempty"`

	// ========== Demographics ==========
	Age               string `json:"age,omitempty" bson:"age,omitempty"`
	Gender            string `json:"gender,omitempty" bson:"gender,omitempty"`
	Country           string `json:"country,omitempty" bson:"country,omitempty"`
	Placement         string `json:"placement,omitempty" bson:"placement,omitempty"`
	DevicePlatform    string `json:"device_platform,omitempty" bson:"device_platform,omitempty"`
	PublisherPlatform string `json:"publisher_platform,omitempty" bson:"publisher_platform,omitempty"`
	ImpressionDevice  string `json:"impression_device,omitempty" bson:"impression_device,omitempty"`

	// ========== Performance Metrics ==========
	Frequency   float64 `json:"frequency,omitempty" bson:"frequency,omitempty"`
	Spend       float64 `json:"spend,omitempty" bson:"spend,omitempty"`
	SocialSpend int32   `json:"social_spend,omitempty" bson:"social_spend,omitempty"`
	CPM         float64 `json:"cpm,omitempty" bson:"cpm,omitempty"`
	CTR         float64 `json:"ctr,omitempty" bson:"ctr,omitempty"`
	CPC         float64 `json:"cpc,omitempty" bson:"cpc,omitempty"`
	CPP         float64 `json:"cpp,omitempty" bson:"cpp,omitempty"`
	WishBid     float64 `json:"wish_bid,omitempty" bson:"wish_bid,omitempty"`

	// ========== Engagement ==========
	InlineLinkClicks        int32                          `json:"inline_link_clicks,omitempty" bson:"inline_link_clicks,omitempty"`
	InlineLinkClickCtr      float64                        `json:"inline_link_click_ctr,omitempty" bson:"inline_link_click_ctr,omitempty"`
	InlinePostEngagement    float64                        `json:"inline_post_engagement,omitempty" bson:"inline_post_engagement,omitempty"`
	UniqueClicks            int32                          `json:"unique_clicks,omitempty" bson:"unique_clicks,omitempty"`
	UniqueCTR               float64                        `json:"unique_ctr,omitempty" bson:"unique_ctr,omitempty"`
	UniqueOutboundClicks    int32                          `json:"unique_outbound_clicks,omitempty" bson:"unique_outbound_clicks,omitempty"`
	WebsiteCTR              []adserverReportE.ActionStats  `json:"website_ctr,omitempty" bson:"website_ctr,omitempty"`
	Actions                 *[]adserverReportE.ActionStats `json:"actions,omitempty" bson:"actions,omitempty"`
	ActionValues            []adserverReportE.ActionStats  `json:"action_values,omitempty" bson:"action_values,omitempty"`
	Conversions             []adserverReportE.ActionStats  `json:"conversions,omitempty" bson:"conversions,omitempty"`
	ConversionValues        []adserverReportE.ActionStats  `json:"conversion_values,omitempty" bson:"conversion_values,omitempty"`
	CostPerActionType       []adserverReportE.ActionStats  `json:"cost_per_action_type,omitempty" bson:"cost_per_action_type,omitempty"`
	CostPerConversion       []adserverReportE.ActionStats  `json:"cost_per_conversion,omitempty" bson:"cost_per_conversion,omitempty"`
	CostPerInlineLinkClick  float64                        `json:"cost_per_inline_link_click,omitempty" bson:"cost_per_inline_link_click,omitempty"`
	CostPerInlinePostEngage float64                        `json:"cost_per_inline_post_engagement,omitempty" bson:"cost_per_inline_post_engagement,omitempty"`

	// ========== Video Metrics ==========
	CostPerThruplay            float64                       `json:"cost_per_thruplay,omitempty" bson:"cost_per_thruplay,omitempty"`
	CostPer15SecVideoView      float64                       `json:"cost_per_15_sec_video_view,omitempty" bson:"cost_per_15_sec_video_view,omitempty"`
	VideoAvgTimeWatchedActions float64                       `json:"video_avg_time_watched_actions,omitempty" bson:"video_avg_time_watched_actions,omitempty"`
	VideoPlayActions           []adserverReportE.ActionStats `json:"video_play_actions,omitempty" bson:"video_play_actions,omitempty"`
	Video30SecWatched          []adserverReportE.ActionStats `json:"video_30_sec_watched_actions,omitempty" bson:"video_30_sec_watched_actions,omitempty"`
	VideoTimeWatched           []adserverReportE.ActionStats `json:"video_time_watched_actions,omitempty" bson:"video_time_watched_actions,omitempty"`
	VideoRetention15s          []adserverReportE.Histogram   `json:"video_play_retention_0_to_15s_actions,omitempty" bson:"video_play_retention_0_to_15s_actions,omitempty"`
	VideoRetention60s          []adserverReportE.Histogram   `json:"video_play_retention_20_to_60s_actions,omitempty" bson:"video_play_retention_20_to_60s_actions,omitempty"`
	VideoRetentionGraph        []adserverReportE.Histogram   `json:"video_play_retention_graph_actions,omitempty" bson:"video_play_retention_graph_actions,omitempty"`
	VideoPlayCurveActions      []adserverReportE.Histogram   `json:"video_play_curve_actions,omitempty" bson:"video_play_curve_actions,omitempty"`
	VideoP25Watched            float64                       `json:"video_p25_watched_actions,omitempty" bson:"video_p25_watched_actions,omitempty"`
	VideoP50Watched            float64                       `json:"video_p50_watched_actions,omitempty" bson:"video_p50_watched_actions,omitempty"`
	VideoP75Watched            float64                       `json:"video_p75_watched_actions,omitempty" bson:"video_p75_watched_actions,omitempty"`
	VideoP95Watched            float64                       `json:"video_p95_watched_actions,omitempty" bson:"video_p95_watched_actions,omitempty"`
	VideoP100Watched           float64                       `json:"video_p100_watched_actions,omitempty" bson:"video_p100_watched_actions,omitempty"`
}
