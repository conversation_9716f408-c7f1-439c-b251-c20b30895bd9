package mongo

import (
	"context"
	"errors"
	"godsp/modules/facebook/ad_account/entity"
	"godsp/modules/facebook/ad_account/transport/response"
	"godsp/modules/facebook/common/fberrs"
	"godsp/pkg/sctx/core"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
)

type adAccountRepo struct {
	DB         *mongo.Database
	Collection *mongo.Collection
}

func NewAdAccountRepo(DB *mongo.Database) *adAccountRepo {
	return &adAccountRepo{
		DB:         DB,
		Collection: DB.Collection(entity.AdAccountEntity{}.CollectionName()),
	}
}

/***
 * insert ad account
 */
func (r *adAccountRepo) InsertAdAccountRepo(ctx context.Context, adAccountEntity *entity.AdAccountEntity) error {
	_, err := r.Collection.InsertOne(ctx, adAccountEntity)
	if err != nil {
		return err
	}

	return nil
}

/***
 * update one filter interface{}, update interface{}, opts ...*options.UpdateOptions
 */
func (r *adAccountRepo) UpdateOneAdAccountRepo(ctx context.Context, filter interface{}, adAccountEntity *entity.AdAccountEntity, opts ...*options.UpdateOptions) error {
	update := bson.M{
		"$set": *adAccountEntity,
	}
	_, err := r.Collection.UpdateOne(ctx, filter, update)
	if err != nil {
		return err
	}

	return nil
}

/** Upsert Ad Account */
func (r *adAccountRepo) UpsertAdAccountRepo(ctx context.Context, filter bson.M, update bson.M) error {
	opts := options.Update().SetUpsert(true)
	_, err := r.Collection.UpdateOne(ctx, filter, update, opts)
	if err != nil {
		return err
	}

	return nil
}

/**
 * FindOne ad accout
 */
func (r *adAccountRepo) FindOneAdAccountRepo(ctx context.Context, filter interface{}, opts ...*options.FindOneOptions) (*entity.AdAccountEntity, error) {
	var adAccount entity.AdAccountEntity
	err := r.Collection.FindOne(ctx, filter, opts...).Decode(&adAccount)
	if errors.Is(err, mongo.ErrNoDocuments) {
		return nil, core.ErrNotFound
	}

	return &adAccount, nil
}

func (r *adAccountRepo) FindAdAccountRepo(ctx context.Context, filter interface{}, opts ...*options.FindOptions) ([]*entity.AdAccountEntity, error) {
	var adAccounts []*entity.AdAccountEntity
	cursor, err := r.Collection.Find(ctx, filter, opts...)
	if err != nil {
		return nil, err
	}
	defer cursor.Close(ctx)
	if cursor == nil {
		return nil, fberrs.ErrNilCursorValue
	}
	err = cursor.All(ctx, &adAccounts)
	if err != nil {
		return nil, err
	}

	if len(adAccounts) == 0 {
		return nil, core.ErrNotFound
	}
	return adAccounts, nil

}

/**
 * FindAdAccountRepo
 */
func (r *adAccountRepo) FindAdAccountAggregateRepo(ctx context.Context, filter interface{}, opts ...*options.FindOptions) ([]*response.AdAccountWithFullname, error) {
	// var adAccounts2 []*entity.AdAccountEntity

	var adAccounts []*response.AdAccountWithFullname

	pipeline := bson.A{
		bson.M{
			"$lookup": bson.M{
				"from":         "admin_users",
				"localField":   "created_by",
				"foreignField": "_id",
				"as":           "creadted_by_user",
			},
		},
		bson.M{
			"$unwind": bson.M{
				"path":                       "$creadted_by_user",
				"preserveNullAndEmptyArrays": true,
			},
		},
		bson.M{
			"$lookup": bson.M{
				"from":         "admin_users",
				"localField":   "updated_by",
				"foreignField": "_id",
				"as":           "updated_by_user",
			},
		},
		bson.M{
			"$unwind": bson.M{
				"path":                       "$updated_by_user",
				"preserveNullAndEmptyArrays": true,
			},
		},
		bson.M{
			"$project": bson.M{
				"_id":               1,
				"name":              1,
				"account_id":        1,
				"currency":          1,
				"timezone_name":     1,
				"created_by":        1,
				"creator_full_name": "$creadted_by_user.full_name",
				"created_at":        1,
				"updated_by":        1,
				"updater_full_name": "$updated_by_user.full_name",
				"updated_at":        1,
			},
		},
	}

	// if filter != nil {
	// 	pipeline = append(pipeline, bson.M{"$match": filter})
	// }

	cursor, err := r.Collection.Aggregate(ctx, pipeline)
	if err != nil {
		return nil, err
	}
	defer cursor.Close(ctx)

	if cursor == nil {
		return nil, fberrs.ErrNilCursorValue
	}
	err = cursor.All(ctx, &adAccounts)
	if err != nil {
		return nil, err
	}

	return adAccounts, nil
}

/**
 * FindAdAccountEditClientRepo
 */
func (r *adAccountRepo) FindAdAccountEditClientRepo(ctx context.Context, filter interface{}, opts ...*options.FindOptions) ([]*response.AdAccountEditClient, error) {
	// var adAccounts2 []*entity.AdAccountEntity

	var adAccounts []*response.AdAccountEditClient

	pipeline := bson.A{
		bson.M{
			"$lookup": bson.M{
				"from":         "admin_users",
				"localField":   "created_by",
				"foreignField": "_id",
				"as":           "creadted_by_user",
			},
		},
		bson.M{
			"$unwind": bson.M{
				"path":                       "$creadted_by_user",
				"preserveNullAndEmptyArrays": true,
			},
		},
		bson.M{
			"$lookup": bson.M{
				"from":         "admin_users",
				"localField":   "updated_by",
				"foreignField": "_id",
				"as":           "updated_by_user",
			},
		},
		bson.M{
			"$unwind": bson.M{
				"path":                       "$updated_by_user",
				"preserveNullAndEmptyArrays": true,
			},
		},
		bson.M{
			"$project": bson.M{
				"_id":               1,
				"name":              1,
				"account_id":        1,
				"currency":          1,
				"timezone_name":     1,
				"client_ids":        1,
				"list_user_ids":     1,
				"created_by":        1,
				"creator_full_name": "$creadted_by_user.full_name",
				"created_at":        1,
				"updated_by":        1,
				"updater_full_name": "$updated_by_user.full_name",
				"updated_at":        1,
			},
		},
	}

	cursor, err := r.Collection.Aggregate(ctx, pipeline)
	if err != nil {
		return nil, err
	}
	defer cursor.Close(ctx)

	if cursor == nil {
		return nil, fberrs.ErrNilCursorValue
	}
	err = cursor.All(ctx, &adAccounts)
	if err != nil {
		return nil, err
	}

	return adAccounts, nil
}

/**
 * count Ad Account
 */
func (r *adAccountRepo) CountAdAccountRepo(ctx context.Context, filter interface{}) (int64, error) {
	return r.Collection.CountDocuments(ctx, filter)
}

/**
 * Update BsonM Filed for AdAccount
 */
func (r *adAccountRepo) UpdateManyAdAccountRepo(ctx context.Context, filter interface{}, update interface{}) error {

	result, err := r.Collection.UpdateMany(ctx, filter, update, options.Update().SetUpsert(false))
	if err != nil {
		return err
	}

	// if result.MatchedCount == 0 {
	// 	return nil // bạn có thể custom lỗi ở đây
	// }

	if result.MatchedCount == 0 && result.ModifiedCount == 0 {
		return nil
	}

	return nil
}
