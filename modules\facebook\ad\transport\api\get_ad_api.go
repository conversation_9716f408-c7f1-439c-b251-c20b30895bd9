package api

import (
	"godsp/pkg/sctx/core"
	"net/http"
	"strconv"

	"github.com/gofiber/fiber/v2"
)

/***
 * Update camp api
 */

func (a *adApi) GetDetailAdApi() fiber.Handler {
	return func(c *fiber.Ctx) error {
		adId := c.Params("ad_id")

		intID, err := strconv.Atoi(adId)
		if err != nil || intID < 0 {
			return core.ErrNotFound
		}

		ad, err := a.usc.FindOneAdDetailUsc(c.Context(), adId)
		if err != nil {
			return core.ErrNotFound
		}

		return c.Status(http.StatusOK).JSON(core.ResponseData(map[string]interface{}{
			"msg":  "Get Ad successfully",
			"data": ad,
		}))
	}
}
