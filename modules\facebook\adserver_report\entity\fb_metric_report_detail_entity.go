package entity

// FacebookAdInsight maps all available Facebook Insights fields into a MongoDB document
type FBRMetriceportDetailEntity struct {
	// ========== Insight Metadata ==========
	CampaignID      string `json:"campaign_id,omitempty" bson:"campaign_id,omitempty"`
	CampaignName    string `json:"campaign_name,omitempty" bson:"campaign_name,omitempty"`
	AccountID       string `json:"account_id,omitempty" bson:"account_id,omitempty"`
	AccountName     string `json:"account_name,omitempty" bson:"account_name,omitempty"`
	AccountCurrency string `json:"account_currency,omitempty" bson:"account_currency,omitempty"`

	// ========== Mentric Prority ==========
	Impressions    int32 `json:"impressions,omitempty" bson:"impressions,omitempty"`
	Reach          int32 `json:"reach,omitempty" bson:"reach,omitempty"`
	VideoView      int32 `json:"video_view,omitempty" bson:"video_view,omitempty"` // ----> 3 Second video plays
	PostReaction   int32 `json:"post_reaction,omitempty" bson:"post_reaction,omitempty"`
	PostComment    int32 `json:"post_comment,omitempty" bson:"comment,omitempty"`
	PostShare      int32 `json:"post_share,omitempty" bson:"post,omitempty"`                       // post share
	PostSave       int32 `json:"post_save,omitempty" bson:"onsite_conversion.post_save,omitempty"` // post share
	Clicks         int32 `json:"clicks,omitempty" bson:"clicks,omitempty"`                         // click all
	LinkClick      int32 `json:"link_click,omitempty" bson:"link_click,omitempty"`
	PostEngagement int32 `json:"post_engagement,omitempty" bson:"post_engagement,omitempty"`

	// ID                 primitive.ObjectID `json:"_id,omitempty" bson:"_id,omitempty"`
	AdID               string `json:"ad_id,omitempty" bson:"ad_id,omitempty"`
	AdName             string `json:"ad_name,omitempty" bson:"ad_name,omitempty"`
	AdSetID            string `json:"adset_id,omitempty" bson:"adset_id,omitempty"`
	AdSetName          string `json:"adset_name,omitempty" bson:"adset_name,omitempty"`
	Objective          string `json:"objective,omitempty" bson:"objective,omitempty"`
	BuyingType         string `json:"buying_type,omitempty" bson:"buying_type,omitempty"`
	AttributionSetting string `json:"attribution_setting,omitempty" bson:"attribution_setting,omitempty"`
	DateStart          string `json:"date_start,omitempty" bson:"date_start,omitempty"`
	DateStop           string `json:"date_stop,omitempty" bson:"date_stop,omitempty"`
	Date               string `bson:"date,omitempty" json:"date"`

	// ========== Demographics ==========
	Age               string `json:"age,omitempty" bson:"age,omitempty"`
	Gender            string `json:"gender,omitempty" bson:"gender,omitempty"`
	Country           string `json:"country,omitempty" bson:"country,omitempty"`
	Placement         string `json:"placement,omitempty" bson:"placement,omitempty"`
	DevicePlatform    string `json:"device_platform,omitempty" bson:"device_platform,omitempty"`
	PublisherPlatform string `json:"publisher_platform,omitempty" bson:"publisher_platform,omitempty"`
	ImpressionDevice  string `json:"impression_device,omitempty" bson:"impression_device,omitempty"`

	// ========== Performance Metrics ==========
	Frequency        float64 `json:"frequency,omitempty" bson:"frequency,omitempty"`
	Spend            float64 `json:"spend,omitempty" bson:"spend,omitempty"`
	SocialSpend      int32   `json:"social_spend,omitempty" bson:"social_spend,omitempty"`
	CPM              float64 `json:"cpm,omitempty" bson:"cpm,omitempty"`
	CTR              float64 `json:"ctr,omitempty" bson:"ctr,omitempty"`
	CPC              float64 `json:"cpc,omitempty" bson:"cpc,omitempty"`
	CPP              float64 `json:"cpp,omitempty" bson:"cpp,omitempty"`
	WishBid          float64 `json:"wish_bid,omitempty" bson:"wish_bid,omitempty"`
	OptimizationGoal string  `bson:"optimization_goal,omitempty" json:"optimization_goal"`

	// ========== Engagement ==========
	InlineLinkClicks        int32   `json:"inline_link_clicks,omitempty" bson:"inline_link_clicks,omitempty"`
	InlineLinkClickCtr      float64 `json:"inline_link_click_ctr,omitempty" bson:"inline_link_click_ctr,omitempty"`
	InlinePostEngagement    float64 `json:"inline_post_engagement,omitempty" bson:"inline_post_engagement,omitempty"`
	UniqueClicks            int32   `json:"unique_clicks,omitempty" bson:"unique_clicks,omitempty"`
	UniqueCTR               float64 `json:"unique_ctr,omitempty" bson:"unique_ctr,omitempty"`
	UniqueOutboundClicks    int32   `json:"unique_outbound_clicks,omitempty" bson:"unique_outbound_clicks,omitempty"`
	CostPerInlineLinkClick  float64 `json:"cost_per_inline_link_click,omitempty" bson:"cost_per_inline_link_click,omitempty"`
	CostPerInlinePostEngage float64 `json:"cost_per_inline_post_engagement,omitempty" bson:"cost_per_inline_post_engagement,omitempty"`

	// ========== Video Metrics ==========
	CostPerThruplay            float64 `json:"cost_per_thruplay,omitempty" bson:"cost_per_thruplay,omitempty"`
	CostPer15SecVideoView      float64 `json:"cost_per_15_sec_video_view,omitempty" bson:"cost_per_15_sec_video_view,omitempty"`
	VideoAvgTimeWatchedActions float64 `json:"video_avg_time_watched_actions,omitempty" bson:"video_avg_time_watched_actions,omitempty"`
	VideoP25Watched            float64 `json:"video_p25_watched_actions,omitempty" bson:"video_p25_watched_actions,omitempty"`
	VideoP50Watched            float64 `json:"video_p50_watched_actions,omitempty" bson:"video_p50_watched_actions,omitempty"`
	VideoP75Watched            float64 `json:"video_p75_watched_actions,omitempty" bson:"video_p75_watched_actions,omitempty"`
	VideoP95Watched            float64 `json:"video_p95_watched_actions,omitempty" bson:"video_p95_watched_actions,omitempty"`
	VideoP100Watched           float64 `json:"video_p100_watched_actions,omitempty" bson:"video_p100_watched_actions,omitempty"`

	// ========== New Metrics ==========
	VideoViewsAt25                           int32  `json:"video_views_at_25,omitempty" bson:"video_views_at_25,omitempty"`
	VideoViewsAt50                           int32  `json:"video_views_at_50,omitempty" bson:"video_views_at_50,omitempty"`
	VideoViewsAt75                           int32  `json:"video_views_at_75,omitempty" bson:"video_views_at_75,omitempty"`
	VideoViewsAt100                          int32  `json:"video_views_at_100,omitempty" bson:"video_views_at_100,omitempty"`
	AdvertiserCurrency                       string `json:"advertiser_currency,omitempty" bson:"advertiser_currency,omitempty"`
	OmniAppInstall                           int32  `json:"omni_app_install,omitempty" bson:"omni_app_install,omitempty"`
	FBPixelLead                              int32  `json:"fb_pixel_lead,omitempty" bson:"fb_pixel_lead,omitempty"`
	LandingPageView                          int32  `json:"landing_page_view,omitempty" bson:"landing_page_view,omitempty"`
	Like                                     int32  `json:"like,omitempty" bson:"like,omitempty"`
	MessagingConversationStarted7d           int32  `json:"messaging_conversation_started_7d,omitempty" bson:"messaging_conversation_started_7d,omitempty"`
	OnsiteWebLead                            int32  `json:"onsite_web_lead,omitempty" bson:"onsite_web_lead,omitempty"`
	Lead                                     int32  `json:"lead,omitempty" bson:"lead,omitempty"`
	LeadGrouped                              int32  `json:"lead_grouped,omitempty" bson:"lead_grouped,omitempty"`
	LeadgenGrouped                           int32  `json:"leadgen_grouped,omitempty" bson:"leadgen_grouped,omitempty"`
	PageEngagement                           int32  `json:"page_engagement,omitempty" bson:"page_engagement,omitempty"`
	CatalogSegmentActionsOmniAddToCart       int32  `json:"catalog_segment_actions:omni_add_to_cart,omitempty" bson:"catalog_segment_actions:omni_add_to_cart,omitempty"`
	CatalogSegmentActionsFBPixelAddToCart    int32  `json:"catalog_segment_actions:fb_pixel_add_to_cart,omitempty" bson:"catalog_segment_actions:fb_pixel_add_to_cart,omitempty"`
	CatalogSegmentActionsFBMobileAddToCart   int32  `json:"catalog_segment_actions:fb_mobile_add_to_cart,omitempty" bson:"catalog_segment_actions:fb_mobile_add_to_cart,omitempty"`
	CatalogSegmentActionsOmniViewContent     int32  `json:"catalog_segment_actions:omni_view_content,omitempty" bson:"catalog_segment_actions:omni_view_content,omitempty"`
	CatalogSegmentActionsFBPixelViewContent  int32  `json:"catalog_segment_actions:fb_pixel_view_content,omitempty" bson:"catalog_segment_actions:fb_pixel_view_content,omitempty"`
	CatalogSegmentActionsFBMobileContentView int32  `json:"catalog_segment_actions:fb_mobile_content_view,omitempty" bson:"catalog_segment_actions:fb_mobile_content_view,omitempty"`
	CatalogSegmentActionsOmniPurchase        int32  `json:"catalog_segment_actions:omni_purchase,omitempty" bson:"catalog_segment_actions:omni_purchase,omitempty"`
	CatalogSegmentActionsFBPixelPurchase     int32  `json:"catalog_segment_actions:fb_pixel_purchase,omitempty" bson:"catalog_segment_actions:fb_pixel_purchase,omitempty"`
	CatalogSegmentActionsFBMobilePurchase    int32  `json:"catalog_segment_actions:fb_mobile_purchase,omitempty" bson:"catalog_segment_actions:fb_mobile_purchase,omitempty"`
	CatalogSegmentActionsPurchase            int32  `json:"catalog_segment_actions:purchase,omitempty" bson:"catalog_segment_actions:purchase,omitempty"`
	CatalogSegmentValueOmniAddToCart         int32  `json:"catalog_segment_value:omni_add_to_cart,omitempty" bson:"catalog_segment_value:omni_add_to_cart,omitempty"`
	CatalogSegmentValueFBPixelAddToCart      int32  `json:"catalog_segment_value:fb_pixel_add_to_cart,omitempty" bson:"catalog_segment_value:fb_pixel_add_to_cart,omitempty"`
	CatalogSegmentValueFBMobileAddToCart     int32  `json:"catalog_segment_value:fb_mobile_add_to_cart,omitempty" bson:"catalog_segment_value:fb_mobile_add_to_cart,omitempty"`
	CatalogSegmentValueOmniPurchaseROAS      int32  `json:"catalog_segment_value_omni_purchase_roas:omni_purchase,omitempty" bson:"catalog_segment_value_omni_purchase_roas:omni_purchase,omitempty"`
	CatalogSegmentValueWebsitePurchaseROAS   int32  `json:"catalog_segment_value_website_purchase_roas:fb_pixel_purchase,omitempty" bson:"catalog_segment_value_website_purchase_roas:fb_pixel_purchase,omitempty"`
	CatalogSegmentValueMobilePurchaseROAS    int32  `json:"catalog_segment_value_mobile_purchase_roas:fb_mobile_purchase,omitempty" bson:"catalog_segment_value_mobile_purchase_roas:fb_mobile_purchase,omitempty"`
	VideoContinuous2SecWatchedActions        int32  `json:"video_continuous_2_sec_watched_actions,omitempty" bson:"video_continuous_2_sec_watched_actions,omitempty"`
}
