package entity

import (
	"godsp/conf"
	userEntity "godsp/modules/admin/user/entity"
	"time"

	"go.mongodb.org/mongo-driver/bson/primitive"
)

type ClientEntity struct {
	ID      primitive.ObjectID `bson:"_id,omitempty" json:"id,omitempty"`
	Name    string             `json:"name" bson:"name" `
	Company string             `json:"company" bson:"company"`
	Brand   *[]string          `json:"brand" bson:"brand"`
	Email   string             `json:"email" bson:"email,omitempty"`
	Status  int                `json:"status" bson:"status"`

	Phone    string `json:"phone,omitempty" bson:"phone,omitempty"`
	Domain   string `json:"domain,omitempty" bson:",omitempty"`
	Logo     string `json:"logo,omitempty" bson:"logo,omitempty"`
	Position string `json:"position,omitempty" bson:"position,omitempty"`
	Address  string `json:"address,omitempty" bson:"address,omitempty"`

	AdAccountIDs  []string             `json:"ad_account_ids,omitempty" bson:"ad_account_ids,omitempty"`
	ClientUserIDs []primitive.ObjectID `json:"client_user_ids,omitempty" bson:"client_use_ids,omitempty" `

	CreatedBy   primitive.ObjectID            `json:"created_by" bson:"created_by" `
	UpdatedBy   primitive.ObjectID            `json:"updated_by" bson:"updated_by"`
	UserCreated *userEntity.UserCreatedEntity `json:"user_created,omitempty" bson:"user_created,omitempty"`
	UserUpdated *userEntity.UserCreatedEntity `json:"user_updated,omitempty" bson:"user_updated,omitempty"`
	CreatedAt   time.Time                     `json:"created_at" bson:"created_at"`
	UpdatedAt   time.Time                     `json:"updated_at" bson:"updated_at"`
}

func (u *ClientEntity) PathImg() {
	if u.Logo == "" {
		u.Logo = "/static/" + conf.PathImgUserDefault
	}

	u.Logo = "/static/" + u.Logo
}

func (ClientEntity) CollectionName() string {
	return "admin_client"
}
