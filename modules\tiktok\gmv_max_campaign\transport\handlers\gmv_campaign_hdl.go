package handlers

import (
	"context"
	clientE "godsp/modules/admin/client/entity"
	"godsp/pkg/gos/utils"
	"godsp/pkg/sctx/core"
	"godsp/views/v2/tiktok/gmv_max"

	"github.com/dev-networldasia/dspgos/gos/templates"
	"github.com/gofiber/fiber/v2"
	"github.com/gofiber/fiber/v2/middleware/session"
)

type GmvMaxCampaignUsc interface {
	GetClientList(ctx context.Context) ([]*clientE.ClientEntity, error)
}

type gmvMaxCampaignHdl struct {
	usc GmvMaxCampaignUsc
}

func NewCampaignHdl(usc GmvMaxCampaignUsc) *gmvMaxCampaignHdl {
	return &gmvMaxCampaignHdl{
		usc: usc,
	}
}

func (hdl *gmvMaxCampaignHdl) ListGmvMaxCampaignHdl(store *session.Store) fiber.Handler {
	return func(c *fiber.Ctx) error {
		flashMsg := utils.GetFlashMessage(c, store, "campaign_list_flash_msg")
		msg := ""
		if _, ok := flashMsg.(string); ok {
			msg = flashMsg.(string)
		}

		userInfo, err := utils.GetInfoUserAuth(c.Context())
		if err != nil {
			return core.ReturnErrForPermissionDenied(c)
		}

		authPermission := core.GetPermission(c.Context()).GetPermissions()
		clients := make([]*clientE.ClientEntity, 0)

		if authPermission == nil {
			clients, _ = hdl.usc.GetClientList(c.Context())
		}

		return templates.Render(c, gmv_max.ListDatatableGmvMaxCamp(&gmv_max.ListTableGmvMaxLayoutData{
			FlashMsg:       msg,
			AuthPermission: core.GetPermission(c.Context()).GetPermissions(),
			UserInfo:       userInfo,
			Clients:        clients,
		}))
	}
}
