package permission

import (
	"context"
	"godsp/modules/admin/permission/repository/mongo"
	"godsp/modules/admin/permission/transport/api"
	"godsp/modules/admin/permission/transport/handlers"
	"godsp/modules/admin/permission/usecase"

	"github.com/dev-networldasia/dspgos/sctx/component/mongodb"

	"github.com/dev-networldasia/dspgos/sctx/configs"

	"github.com/dev-networldasia/dspgos/sctx"

	"github.com/gofiber/fiber/v2"
	"github.com/gofiber/fiber/v2/middleware/session"
)

// index mongo for permission
type composerIndexMongoPermission interface {
	CreatePermissionIndex(ctx context.Context) error
}

func SetupIndexMongoPermission(serviceCtx sctx.ServiceContext) composerIndexMongoPermission {
	mongoDB := serviceCtx.MustGet(configs.KeyCompMongoDB).(mongodb.MongoComponent).GetDatabase()
	repo := mongo.NewPermissionRepo(mongoDB)

	return repo
}

type composerPermission interface {
	ListPermissionHdl() fiber.Handler
}

func composerPermissionService(serviceCtx sctx.ServiceContext) composerPermission {
	hdl := handlers.NewPermissionHdl()
	return hdl
}

type composerApiPermission interface {
	RefreshPermissionApi() fiber.Handler
	ListPermissionApi() fiber.Handler
	UpdateIsBlockPermissionApi() fiber.Handler
	UpdateIsAcpPermissionApi() fiber.Handler
	UpdatePermissionApi() fiber.Handler
}

func composerApiPermissionService(serviceCtx sctx.ServiceContext) composerApiPermission {
	logger := serviceCtx.MustGet(configs.KeyLoggerDaily).(sctx.AppLoggerDaily).GetLogger("admins")
	mongoDB := serviceCtx.MustGet(configs.KeyCompMongoDB).(mongodb.MongoComponent).GetDatabase()
	repo := mongo.NewPermissionRepo(mongoDB)
	usc := usecase.NewApiPermissionUsc(repo, logger)
	ap := api.NewPermissionApi(usc)

	return ap
}

func SetupRoutesPermission(app *fiber.App, serviceCtx sctx.ServiceContext, store *session.Store, midds ...fiber.Handler) {
	comp := composerPermissionService(serviceCtx)
	compApi := composerApiPermissionService(serviceCtx)
	group := app.Group("admins/permissions")
	{
		if len(midds) > 0 {
			for _, midd := range midds {
				group.Use(midd)
			}
		}

		group.Get("/list", comp.ListPermissionHdl()).Name("admins.permission.list")
	}
	gapi := app.Group("api/admins/permissions")
	{
		if len(midds) > 0 {
			for _, midd := range midds {
				group.Use(midd)
			}
		}
		gapi.Post("/refresh", compApi.RefreshPermissionApi()).Name("admins.api.permission.refresh")
		gapi.Post("/list-datatable", compApi.ListPermissionApi()).Name("admins.api.permission.list_datatable")
		gapi.Patch("/update-block", compApi.UpdateIsBlockPermissionApi()).Name("admins.api.permission.update_block")
		gapi.Patch("/update-acp", compApi.UpdateIsAcpPermissionApi()).Name("admins.api.permission.update_acp")
		gapi.Put("/edit", compApi.UpdatePermissionApi()).Name("admins.api.permission.edit")

	}
}
