import {
    GMV_STATUS,
    getStatusDisplay
} from '/static/js/tiktok/constants/gmv-status.js';

/**
 * GMV Max Campaign Detail Table JavaScript Module
 * Handles detail view functionality, charts, and ad groups management
 */

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    console.log('GMV Max Detail Table initialized');
    
    // Initialize components
    initDetailComponents();
});

/**
 * Initialize all detail page components
 */
function initDetailComponents() {
    // Custom dropdown status filters are handled in the template inline script
    // This ensures proper integration with the custom dropdown functionality
    
    // Initialize search functionality
    initSearchFunctionality();
    
    // Initialize action buttons
    initActionButtons();
}

/**
 * Initialize search functionality
 */
function initSearchFunctionality() {
    // Ad Group search
    const adGroupSearchInput = document.getElementById('adGroupSearchInput');
    if (adGroupSearchInput) {
        adGroupSearchInput.addEventListener('input', function(e) {
            console.log('Ad group search:', e.target.value);
            handleAdGroupSearch(e.target.value);
        });
    }
}

/**
 * Handle ad group search
 * @param {string} searchTerm - Search term
 */
function handleAdGroupSearch(searchTerm) {
    console.log(`Searching ad groups for: "${searchTerm}"`);
    
    // Implement search logic here
    // This could filter the ad groups table or make an API call
    
    // Example: debounced search
    clearTimeout(window.adGroupSearchTimeout);
    window.adGroupSearchTimeout = setTimeout(() => {
        // loadAdGroupsTable(searchTerm);
        console.log('Executing search for:', searchTerm);
    }, 300);
}

/**
 * Initialize action buttons
 */
function initActionButtons() {
    // Edit Campaign button
    const editCampaignBtn = document.querySelector('button:has(.ri-edit-line)');
    if (editCampaignBtn) {
        editCampaignBtn.addEventListener('click', function() {
            console.log('Edit campaign clicked');
            // Implement edit campaign functionality
        });
    }
    
    // Create Ad Group button
    const createAdGroupBtns = document.querySelectorAll('button:has(.ri-add-line)');
    createAdGroupBtns.forEach(btn => {
        btn.addEventListener('click', function() {
            console.log('Create ad group clicked');
            // Implement create ad group functionality
        });
    });
    
    // Grid view button
    const gridViewBtn = document.querySelector('button:has(.ri-grid-line)');
    if (gridViewBtn) {
        gridViewBtn.addEventListener('click', function() {
            console.log('Grid view clicked');
            // Implement grid view toggle
        });
    }
    
    // External link button
    const externalLinkBtn = document.querySelector('button:has(.ri-external-link-line)');
    if (externalLinkBtn) {
        externalLinkBtn.addEventListener('click', function() {
            console.log('External link clicked');
            // Implement external link functionality
        });
    }
    
    // Copy button
    const copyBtn = document.querySelector('button:has(.ri-file-copy-line)');
    if (copyBtn) {
        copyBtn.addEventListener('click', function() {
            console.log('Copy clicked');
            // Implement copy functionality
        });
    }
}

/**
 * Load ad groups table
 * @param {string} searchTerm - Optional search term
 * @param {string} statusFilter - Optional status filter
 */
function loadAdGroupsTable(searchTerm = '', statusFilter = '') {
    const container = document.getElementById('ad-groups-table-container');
    
    if (!container) {
        console.error('Ad groups table container not found');
        return;
    }
    
    // Show loading state
    showAdGroupsLoadingState(container);
    
    // Simulate API call (replace with actual implementation)
    setTimeout(() => {
        showAdGroupsPlaceholder(container);
    }, 1000);
}

/**
 * Show loading state for ad groups table
 * @param {HTMLElement} container - Container element
 */
function showAdGroupsLoadingState(container) {
    container.innerHTML = `
        <div class="text-center py-4">
            <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">Loading...</span>
            </div>
            <p class="mt-2 text-muted">Loading ad groups...</p>
        </div>
    `;
}

/**
 * Show placeholder content for ad groups table
 * @param {HTMLElement} container - Container element
 */
function showAdGroupsPlaceholder(container) {
    container.innerHTML = `
        <div class="table-responsive">
            <table class="table table-hover">
                <thead class="table-light">
                    <tr>
                        <th>Ad Group Name</th>
                        <th>Status</th>
                        <th>Budget</th>
                        <th>Bid Strategy</th>
                        <th>Cost</th>
                        <th>Revenue</th>
                        <th>Orders</th>
                        <th>ROI</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td colspan="9" class="text-center py-4">
                            <div class="text-muted">
                                <i class="ri-folder-open-line fs-1 mb-3 d-block"></i>
                                <h6>No ad groups found</h6>
                                <p class="mb-0">Create your first ad group to get started.</p>
                            </div>
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>
    `;
}

/**
 * Format campaign status for display with status-item-circle
 * @param {string} primaryStatus - Primary status value
 * @returns {string} Formatted HTML
 */
export function formatAdGroupStatus(primaryStatus) {
    const displayText = getStatusDisplay(primaryStatus);
    let circleClass = 'status-item-circle';
    
    // Add appropriate class based on status
    switch (primaryStatus) {
        case GMV_STATUS.STATUS_DELIVERY_OK:
            circleClass += ' active';
            break;
        case GMV_STATUS.STATUS_DISABLE:
            circleClass += ' inactive';
            break;
        case GMV_STATUS.STATUS_DELETE:
            circleClass += ' deleted';
            break;
        case GMV_STATUS.STATUS_DELIVERY_NOT:
            circleClass += ' not-delivering';
            break;
        default:
            circleClass += ' not-delivering';
    }
    
    return `
        <span class="status-badge">
            <span class="${circleClass}"></span>
            <span>${displayText}</span>
        </span>
    `;
}

/**
 * Handle status filter for ad groups
 * @param {string} statusValue - Selected status value
 */
export function handleAdGroupStatusFilter(statusValue) {
    console.log(`Filtering ad groups by status: "${statusValue}"`);
    
    if (statusValue) {
        const statusDisplay = getStatusDisplay(statusValue);
        console.log(`Status display: ${statusDisplay}`);
        
        // Reload ad groups table with filter
        loadAdGroupsTable('', statusValue);
    }
}

/**
 * Navigate to ad group detail
 * @param {string} adGroupId - Ad group ID
 */
export function navigateToAdGroupDetail(adGroupId) {
    console.log(`Navigating to ad group detail: ${adGroupId}`);
    // Implement navigation logic
    // window.location.href = `/dsp/tiktok/adgroup/detail?id=${adGroupId}`;
}

/**
 * Export ad groups data
 * @param {string} format - Export format (csv, xlsx, etc.)
 */
export function exportAdGroupsData(format = 'csv') {
    console.log(`Exporting ad groups data in ${format} format`);
    // Implement export functionality
}

// Export functions for external use
window.GMVDetailTable = {
    loadAdGroupsTable,
    formatAdGroupStatus,
    handleAdGroupStatusFilter,
    navigateToAdGroupDetail,
    exportAdGroupsData
};
