package usecase

import (
	"context"
	clientE "godsp/modules/admin/client/entity"
	roleE "godsp/modules/admin/role/entity"
	roleEnity "godsp/modules/admin/role/entity"
	"godsp/modules/facebook/ad_account/transport/response"

	"godsp/modules/admin/user/entity"
	"godsp/modules/admin/user/transport/responses"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo/options"
)

// System Repo
type RoleRepo interface {
	FindOneRoleRepo(ctx context.Context, filter interface{}, opts ...*options.FindOneOptions) (*roleEnity.RoleEntity, error)
	FindRoleRepo(ctx context.Context, filter interface{}, opts ...*options.FindOptions) (*[]roleE.RoleEntity, error)
}

type UserRepo interface {
	FindOneUserWithPipelineRepo(ctx context.Context, pipeline []bson.M, opts ...*options.AggregateOptions) (*entity.UserEntity, error)
	FindOneUserProfileWithPipelineRepo(ctx context.Context, pipeline []bson.M, opts ...*options.AggregateOptions) (*responses.DetailsUserProfile, error)
}
type ClientRepo interface {
	FindClientRepo(ctx context.Context, filter interface{}, opts ...*options.FindOptions) ([]*clientE.ClientEntity, error)
}

// Facebook Repo
type AdAccountRepo interface {
	UpdateManyAdAccountRepo(ctx context.Context, filter interface{}, update interface{}) error
	FindAdAccountAggregateRepo(ctx context.Context, filter interface{}, opts ...*options.FindOptions) ([]*response.AdAccountWithFullname, error)
}
type PageRepo interface {
	UpdateManyPageRepo(ctx context.Context, filter interface{}, update interface{}) error
}
type PixelRepo interface {
	UpdateManyPixelRepo(ctx context.Context, filter interface{}, update interface{}) error
}

type CatalogueRepo interface {
	UpdateManyCatalogueRepo(ctx context.Context, filter interface{}, update interface{}) error
}
