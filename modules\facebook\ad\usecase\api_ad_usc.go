package usecase

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"godsp/conf"
	userEt "godsp/modules/admin/user/entity"
	userRp "godsp/modules/admin/user/repository/mongo"
	"godsp/modules/facebook/ad/common/errs"
	"godsp/modules/facebook/ad/common/pipelines"
	"godsp/modules/facebook/ad/entity"
	"godsp/modules/facebook/ad/mapping"
	"godsp/modules/facebook/ad/transport/requests"
	"godsp/modules/facebook/ad/transport/responses"
	adserverReportE "godsp/modules/facebook/adserver_report/entity"
	"godsp/modules/facebook/common/fbenums"
	v20 "godsp/pkg/fb-marketing/marketing/v20"
	"godsp/pkg/gos/utils"
	"godsp/pkg/sctx/core"
	"time"

	"github.com/dev-networldasia/dspgos/sctx"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo/options"
)

type ApiAdRepo interface {
	InsertAdRepo(ctx context.Context, adEntity *entity.AdEntity) error
	UpsertAdRepo(ctx context.Context, filter bson.M, update bson.M) error
	FindOneAdRepo(ctx context.Context, filter interface{}, opts ...*options.FindOneOptions) (*entity.AdEntity, error)
	FindAdRepo(ctx context.Context, filter bson.M, opts ...*options.FindOptions) (*[]entity.AdEntity, error)
	CountAdRepo(ctx context.Context, filter interface{}) (int64, error)
	UpdateAdNameStatusRepo(ctx context.Context, filter interface{}, data bson.M) error
	DeleteAdRepo(ctx context.Context, filter interface{}) error
	AggregateAdRepo(ctx context.Context, pipeline []bson.M, opts ...*options.AggregateOptions) (*entity.AdDetailReviewEntity, error)
	FindListTableAggregateRepo(ctx context.Context, pipeline []bson.M, opts ...*options.AggregateOptions) (*[]entity.AdEntity, error)
}

type ApiFBReportDetailRepo interface {
	FindFBReportDetailRepo(ctx context.Context, filter bson.M, opts ...*options.FindOptions) (*[]adserverReportE.FBReportDetailEntity, error)
	GetMetricsColumnForAdsFBReportDetailRepo(ctx context.Context, filter bson.M) (*[]adserverReportE.FBReportDetailEntity, error)
}

type ApiUserRepo interface {
	FindOneUserRepo(ctx context.Context, filter interface{}, opts ...*options.FindOneOptions) (*userEt.UserEntity, error)
	GetRoleOfUser(ctx context.Context, userId primitive.ObjectID, opts ...*options.AggregateOptions) (*userRp.RoleInfo, error)
}

type apiAdUsc struct {
	fbService          *v20.Service
	repo               ApiAdRepo
	userRepo           ApiUserRepo
	fbReportDetailRepo ApiFBReportDetailRepo
	logger             sctx.Logger
}

// DeleteAdUsc implements api.ApiAdUsc.

func NewApiAdUsc(fbService *v20.Service, repo ApiAdRepo, fbReportDetailRepo ApiFBReportDetailRepo, userRepo ApiUserRepo, logger sctx.Logger) *apiAdUsc {
	return &apiAdUsc{
		fbService:          fbService,
		repo:               repo,
		userRepo:           userRepo,
		fbReportDetailRepo: fbReportDetailRepo,
		logger:             logger,
	}
}

/**
 * Get Role By UserId
 * @return string
 */
func (usc *apiAdUsc) GetRoleByUserId(ctx context.Context, userId primitive.ObjectID) (*string, error) {
	role, err := usc.userRepo.GetRoleOfUser(ctx, userId)
	if err != nil {
		return nil, err
	}
	return &role.RoleName, nil
}

/**
 * reload
 */
func (usc *apiAdUsc) ReloadWithAccountAdsUsc(ctx context.Context, accountId string) []string {
	// _, _, _, _, err := utils.GetInfoUser(ctx)
	// if err != nil {
	// 	return nil
	// }

	var errStr []string
	ads, err := usc.fbService.Ads.List(accountId).Do(ctx)

	jsonData, _ := json.MarshalIndent(ads, "", "  ")
	fmt.Println("\n-------- AdsFromFB -------> \n", string(jsonData))

	if err != nil {
		usc.logger.Error(err)
		errStr = append(errStr, err.Error())
		return errStr
	}

	if ads == nil {
		usc.logger.Error(errs.ErrReloadAccountFBAdsetList)
		errStr = append(errStr, errs.ErrReloadAccountFBAdsetList.Error())
		return errStr
	}

	userId, err := utils.GetUserId(ctx)
	if err != nil {
		usc.logger.Error(err)
		errStr = append(errStr, err.Error())
		return errStr
	}

	for _, ad := range ads {
		if err := usc.processCreateUpdateAd(ctx, &ad, userId); err != nil {
			usc.logger.Error(err)
			errStr = append(errStr, err.Error())
		}
	}

	return nil
}

/**
 * reload ads
 */
func (usc *apiAdUsc) ReloadWithAdsetUsc(ctx context.Context, adsetID string) []string {
	_, _, _, _, err := utils.GetInfoUser(ctx)
	if err != nil {
		return nil
	}

	var errStr []string
	ads, err := usc.fbService.Ads.ListOfAdset(adsetID).Do(ctx)
	if err != nil {
		usc.logger.Error(err)
		errStr = append(errStr, err.Error())
		return errStr
	}

	if ads == nil {
		usc.logger.Error(errs.ErrReloadAccountFBAdsetList)
		errStr = append(errStr, errs.ErrReloadAccountFBAdsetList.Error())
		return errStr
	}

	userId, err := utils.GetUserId(ctx)
	if err != nil {
		usc.logger.Error(err)
		errStr = append(errStr, err.Error())
		return errStr
	}

	for _, ad := range ads {
		if err := usc.processCreateUpdateAd(ctx, &ad, userId); err != nil {
			usc.logger.Error(err)
			errStr = append(errStr, err.Error())
		}
	}

	return errStr
}

/**
 * Reload
 */
func (usc *apiAdUsc) ReloadAdDetailUsc(ctx context.Context, adID string, userId primitive.ObjectID) error {
	_, _, _, _, err := utils.GetInfoUser(ctx)
	if err != nil {
		return err
	}

	ad, err := usc.fbService.Ads.Get(ctx, adID)
	if err != nil {
		usc.logger.Error(err)
		return err
	}

	if ad == nil {
		usc.logger.Error(errs.ErrReloadAdIdFBNotExist)
		return errs.ErrReloadAdIdFBNotExist
	}

	jsonData, err := json.MarshalIndent(ad, "", "  ")
	if err != nil {
		fmt.Println("Error marshaling struct:", err)
	} else {
		fmt.Println("\n-------- AđFromFB -------> \n", string(jsonData))
	}

	if err := usc.processCreateUpdateAd(ctx, ad, userId); err != nil {
		usc.logger.Error(err)
		return err
	}

	return nil
}

// process create update reload
func (usc *apiAdUsc) processCreateUpdateAd(ctx context.Context, ad *v20.Ad, userId primitive.ObjectID) error {
	filter := bson.M{"adset_id": ad.AdsetID, "ad_id": ad.ID, "account_id": ad.AccountID}

	adsUpSert := mapping.MapperAdFBUpsert(ad, userId)

	if err := usc.repo.UpsertAdRepo(ctx, filter, adsUpSert); err != nil {
		return err
	}

	return nil
}

/**
 * Create ad
 */
func (usc *apiAdUsc) CreateAdUsc(ctx context.Context, payload *requests.CreateAdReq) (*entity.AdEntity, error) {
	// _, _, _, _, err := utils.GetInfoUser(ctx)
	// if err != nil {
	// 	return nil, err
	// }

	user, err := utils.GetInfoUserBasic(ctx)
	if err != nil && user.RoleName != conf.SysConf.RoleAdmin {
		return nil, err
	}

	adEntity, adFb := mapping.MapperCreateAdRequestToAds(payload)

	adID, err := usc.fbService.Ads.Create(ctx, *adFb)
	if err != nil {
		usc.logger.Error(err)
		return nil, err
	}

	fmt.Printf("AD ID CREATE: %v", adID)
	adEntity.AdID = adID
	if err := usc.repo.InsertAdRepo(ctx, adEntity); err != nil {
		usc.logger.Error(err)
		return nil, err
	}

	// reload
	go func() {
		if err := usc.ReloadAdDetailUsc(ctx, adID, payload.UserId); err != nil {
			usc.logger.Error(err)
		}
	}()

	return adEntity, nil
}

/**
 * Get One Ad
 */
func (usc *apiAdUsc) FindOneAdUsc(ctx context.Context, adId string) (*entity.AdEntity, error) {
	user, err := utils.GetInfoUserBasic(ctx)
	if err != nil && user.RoleName != conf.SysConf.RoleAdmin {
		return nil, err
	}
	filter := utils.GetFilterFBAds(ctx, user)

	filter["ad_id"] = adId

	adEntity, err := usc.repo.FindOneAdRepo(ctx, filter)
	if err != nil {
		if errors.Is(err, core.ErrNotFound) {
			// Attempt to reload ad details
			if reloadErr := usc.ReloadAdDetailUsc(ctx, adId, adEntity.UpdatedBy); reloadErr != nil {
				return nil, reloadErr // Return error from ReloadAdDetailUsc
			}
			// Retry fetching the ad after reloading
			adEntity, err = usc.repo.FindOneAdRepo(ctx, filter)
			if err != nil {
				return nil, err // Return the error from retry
			}
		} else {
			return nil, err // Return the error if it's not ErrNotFound
		}
	}

	return adEntity, nil // Return the ad entity if successfully found
}

func (usc *apiAdUsc) FindOneAdDetailUsc(ctx context.Context, adId string) (*entity.AdDetailReviewEntity, error) {
	user, err := utils.GetInfoUserBasic(ctx)
	if err != nil && user.RoleName != conf.SysConf.RoleAdmin {
		return nil, err
	}

	pipeline := pipelines.GetDetailAds(adId, user)

	adEntity, err := usc.repo.AggregateAdRepo(ctx, pipeline)

	if err != nil && !errors.Is(err, core.ErrNotFound) {
		return nil, err
	}

	return adEntity, nil
}

/**
 * Update Ad
 */
func (usc *apiAdUsc) UpdateAdUsc(ctx context.Context, payload *requests.UpdateAdReq) (*entity.AdEntity, error) {
	user, err := utils.GetInfoUserBasic(ctx)
	if err != nil && user.RoleName != conf.SysConf.RoleAdmin {
		return nil, err
	}

	fmt.Println("aolalsdakdnasd  -----> ")
	filter := utils.GetFilterFBAds(ctx, user)
	filter["ad_id"] = payload.AdID

	_, err = usc.FindOneAdUsc(ctx, payload.AdID)
	if err != nil {
		usc.logger.Error(err)
		return nil, err
	}

	adEntity, adFb := mapping.MapperUpdateAdRequestToAds(payload)

	err = usc.fbService.Ads.Update(ctx, *adFb)
	if err != nil {
		usc.logger.Error(err)
		return nil, err
	}

	dataUpdate := bson.M{
		"$set": bson.M{
			"name":       adEntity.Name,
			"status":     adEntity.Status,
			"updated_by": payload.UserId,
			"updated_at": time.Now(),
		},
	}
	err = usc.repo.UpdateAdNameStatusRepo(ctx, filter, dataUpdate)

	if err != nil {
		usc.logger.Error(err)
		return nil, err
	}
	// if err := usc.ReloadAdDetailUsc(ctx, adEntity.AdID, payload.UserId); err != nil {
	// 	usc.logger.Error(err)
	// 	return nil, err
	// }

	// go func() {
	// 	if err := usc.ReloadAdDetailUsc(ctx, adEntity.AdID, payload.UserId); err != nil {
	// 		usc.logger.Error(err)
	// 	}
	// }()

	return adEntity, nil
}

/**
 * get list ads from mongo
 */
func (usc *apiAdUsc) ListAdsUsc(ctx context.Context, payload *requests.ListAdTableReq) (*responses.DataTableAdsRes, error) {
	if payload == nil {
		return nil, errs.ErrAdDataTableEmpty
	}

	limit := payload.Length
	skip := payload.Start

	// Create filter (optional, add search or other conditions here)
	statuses := []string{fbenums.FB_STATUS_ARCHIVED, fbenums.FB_STATUS_DELETED}

	user, err := utils.GetInfoUserBasic(ctx)
	if err != nil {
		return nil, err
	}
	filter := utils.GetFilterFBAds(ctx, user)

	filter["status"] = bson.M{"$ne": statuses}
	// filter["account_id"] = utils.GetAdAccount(ctx)
	// filter["created_by"] = userId

	if payload.SearchValue != nil && *payload.SearchValue != "" {
		filter["name"] = bson.M{"$regex": *payload.SearchValue, "$options": "i"}
	}

	if len(payload.Filter.AdsetIds) > 0 {
		filter["adset_id"] = bson.M{"$in": payload.Filter.AdsetIds}
	} else if payload.Filter.AdsetId != "" {
		filter["adset_id"] = payload.Filter.AdsetId
	} else if len(payload.Filter.CampaignIds) > 0 {
		filter["campaign_id"] = bson.M{"$in": payload.Filter.CampaignIds}
	} else if payload.Filter.CampaignId != "" {
		filter["campaign_id"] = payload.Filter.CampaignId
	}

	if payload.Filter.Status != "" {
		filter["status"] = payload.Filter.Status
	}

	if payload.Filter.Name != "" {
		filter["name"] = bson.M{
			"$regex":   payload.Filter.Name,
			"$options": "i",
		}
	}
	//end filter

	allowDiskUse := fbenums.ALLOW_DISK_USE_TRUE
	opts := &options.FindOptions{
		AllowDiskUse: &allowDiskUse,
		Skip:         &skip,
		Limit:        &limit,
	}

	// opts.SetSort(bson.D{
	// 	// primitive.E{Key: "status", Value: 1},
	// 	primitive.E{Key: "created_at", Value: -1},
	// })

	sortOrder := payload.SortOrder
	opts.SetSort(bson.M{payload.SortField: sortOrder})

	ads, err := usc.repo.FindAdRepo(ctx, filter, opts)
	if err != nil {
		return nil, err
	}

	// test
	var adIds []string
	for _, ad := range *ads {
		adIds = append(adIds, ad.AdID)
	}

	fbReportDetails, err := usc.fbReportDetailRepo.GetMetricsColumnForAdsFBReportDetailRepo(ctx, bson.M{"ad_id": bson.M{"$in": adIds}})

	// end test

	//total all
	total, err := usc.repo.CountAdRepo(ctx, bson.M{})
	if err != nil {
		return nil, err
	}

	// total filtered
	totalFiltered := total
	if len(filter) != 0 {
		totalFiltered, err = usc.repo.CountAdRepo(ctx, filter)
		if err != nil {
			return nil, err
		}
	}

	data := mapping.MapperAdToDataTable(ads, fbReportDetails)

	return &responses.DataTableAdsRes{
		Draw:            payload.Draw,
		Data:            data,
		RecordsTotal:    total,
		RecordsFiltered: totalFiltered,
	}, nil
}

/**
 * Api toggle status Adset
 */
func (usc *apiAdUsc) UpdateNameStatusAdUsc(ctx context.Context, payload *requests.UpdateNameStatusAdReq) error {

	filter := make(bson.M)
	// filter["account_id"] = *utils.GetAdAccount(ctx)
	filter["ad_id"] = payload.AdId

	adsetFb, adsetUpdate := mapping.MapperUpdateNameStatusAdReqToAd(payload, filter["account_id"].(string))

	err := usc.fbService.Ads.UpdateNameStatus(ctx, payload.AdId, adsetFb)
	fmt.Printf("\n There is Usc Update FB err: %v \n", err)
	if err != nil {
		usc.logger.Error(err)
		return err
	}

	go func() {
		if err := usc.repo.UpdateAdNameStatusRepo(ctx, filter, adsetUpdate); err != nil {
			usc.logger.Error(err)
		} else {
			go func() {
				if err := usc.ReloadAdDetailUsc(ctx, payload.AdId, payload.UserId); err != nil {
					usc.logger.Error(err)
				}
			}()
		}
	}()

	return nil
}

func (usc *apiAdUsc) DeleteAdUsc(ctx context.Context, payload *requests.DeleteAdReq) ([]string, error) {
	ids := payload.AdIDs
	var errs []string
	var removedIds []string

	fmt.Printf("Deleting ads %v \n", ids)

	for _, id := range ids {
		if err := usc.fbService.Ads.Delete(ctx, id); err != nil {
			errs = append(errs, err.Error())
		} else {
			fmt.Printf("Deleted adset %v \n", id)
			removedIds = append(removedIds, id)
			usc.ReloadAdDetailUsc(ctx, id, payload.UserId)
		}
		removedIds = append(removedIds, id)

	}
	usc.repo.DeleteAdRepo(ctx, bson.M{"ad_id": bson.M{"$in": removedIds}})
	return removedIds, nil
}

/**
 * Handle payload list camp datable
 */
func getFilterListTableAd(ctx context.Context, payload *requests.ListAdTableReq) (bson.M, error) {

	user, _ := utils.GetInfoUserBasic(ctx)

	filter := utils.GetFilterFBAds(ctx, user)

	if payload.SearchValue != nil && *payload.SearchValue != "" {
		filter["name"] = bson.M{"$regex": *payload.SearchValue, "$options": "i"}
	}

	if len(payload.Filter.AdsetIds) > 0 {
		filter["adset_id"] = bson.M{"$in": payload.Filter.AdsetIds}
	} else if payload.Filter.AdsetId != "" {
		filter["adset_id"] = payload.Filter.AdsetId
	} else if len(payload.Filter.CampaignIds) > 0 {
		filter["campaign_id"] = bson.M{"$in": payload.Filter.CampaignIds}
	} else if payload.Filter.CampaignId != "" {
		filter["campaign_id"] = payload.Filter.CampaignId
	}

	// if payload.Filter.Status != "" {
	// 	filter["status"] = payload.Filter.Status
	// }

	if payload.Filter.Name != "" {
		filter["name"] = bson.M{
			"$regex":   payload.Filter.Name,
			"$options": "i",
		}
	}

	// if payload.StartTime != nil && payload.EndTime != nil {
	// 	filter["updated_at"] = bson.M{"$gte": *payload.StartTime, "$lte": *payload.EndTime}
	// }

	if user.RoleName == conf.SysConf.RoleAdmin {
		filter["account_id"] = payload.AccountID
	}

	// if payload.AccountID == "" {
	// 	delete(filter, "account_id")
	// }

	if payload.ClientIDStr != "" {
		filter["client_id"] = payload.ClientID
	}

	// jsonData, _ := json.MarshalIndent(filter, "", "  ")
	// fmt.Printf("filter ------->: %v\n", string(jsonData))

	return filter, nil
}

func (usc *apiAdUsc) getMetricAdsetReport(ctx context.Context, payload *requests.ListAdTableReq, adIds []string) (*[]adserverReportE.FBReportDetailEntity, error) {
	filter := bson.M{"ad_id": bson.M{"$in": adIds}}

	if payload.StartTime != nil && payload.EndTime != nil {
		startTimeStr := (*payload.StartTime).Format("2006-01-02")
		endTimeStr := (*payload.EndTime).Format("2006-01-02")
		filter["date"] = bson.M{"$gte": startTimeStr, "$lte": endTimeStr}
	} else {
		yesterday := time.Now().AddDate(0, 0, -1)
		endOfYesterdayStr := yesterday.Format("2006-01-02")
		startOfYesterdayStr := yesterday.AddDate(0, 0, -30).Format("2006-01-02")
		filter["date"] = bson.M{"$gte": startOfYesterdayStr, "$lte": endOfYesterdayStr}
	}

	return usc.fbReportDetailRepo.GetMetricsColumnForAdsFBReportDetailRepo(ctx, filter)

}

/**
 * get list ads from mongo
 */
func (usc *apiAdUsc) ListDatatableAdsUsc(ctx context.Context, payload *requests.ListAdTableReq) (*responses.DataTableAdsRes, error) {

	filter, err := getFilterListTableAd(ctx, payload)
	if err != nil {
		return nil, err
	}

	pipeline := pipelines.PipelineListTableAds(payload, filter)
	ads, err := usc.repo.FindListTableAggregateRepo(ctx, pipeline)
	if err != nil {
		return nil, err
	}

	// test
	var adIds []string
	for _, ad := range *ads {
		adIds = append(adIds, ad.AdID)
	}

	fbReportDetails, err := usc.getMetricAdsetReport(ctx, payload, adIds)

	if err != nil {
		usc.logger.Error(err)
	}

	//total all
	total, err := usc.repo.CountAdRepo(ctx, bson.M{})
	if err != nil {
		return nil, err
	}

	// total filtered
	totalFiltered := total
	if len(filter) != 0 {
		totalFiltered, err = usc.repo.CountAdRepo(ctx, filter)
		if err != nil {
			return nil, err
		}
	}

	data := mapping.MapperAdToDataTable(ads, fbReportDetails)

	return &responses.DataTableAdsRes{
		Draw:            payload.Draw,
		Data:            data,
		RecordsTotal:    total,
		RecordsFiltered: totalFiltered,
	}, nil
}

// func (usc *apiAdUsc) ListDatatableAdsUsc(ctx context.Context, payload *requests.ListAdTableReq) (*responses.DataTableAdsRes, error) {

// 	limit := payload.Length
// 	skip := payload.Start

// 	// Create filter (optional, add search or other conditions here)
// 	user, err := utils.GetInfoUserBasic(ctx)
// 	if err != nil {
// 		return nil, err
// 	}

// 	payload.UserId = *user.UserId
// 	payload.AdAccountId = user.AdAccountId

// 	filter := utils.GetFilterFBAds(ctx, user)

// 	isAdmin := false

// 	if user.RoleName == conf.SysConf.RoleAdmin {
// 		isAdmin = true
// 	}

// 	// filter["account_id"] = utils.GetAdAccount(ctx)
// 	// filter["created_by"] = userId

// 	if payload.SearchValue != nil && *payload.SearchValue != "" {
// 		filter["name"] = bson.M{"$regex": *payload.SearchValue, "$options": "i"}
// 	}

// 	if len(payload.Filter.AdsetIds) > 0 {
// 		filter["adset_id"] = bson.M{"$in": payload.Filter.AdsetIds}
// 	} else if payload.Filter.AdsetId != "" {
// 		filter["adset_id"] = payload.Filter.AdsetId
// 	} else if len(payload.Filter.CampaignIds) > 0 {
// 		filter["campaign_id"] = bson.M{"$in": payload.Filter.CampaignIds}
// 	} else if payload.Filter.CampaignId != "" {
// 		filter["campaign_id"] = payload.Filter.CampaignId
// 	}

// 	if payload.Filter.Status != "" {
// 		filter["status"] = payload.Filter.Status
// 	}

// 	if payload.Filter.Name != "" {
// 		filter["name"] = bson.M{
// 			"$regex":   payload.Filter.Name,
// 			"$options": "i",
// 		}
// 	}
// 	//end filter

// 	allowDiskUse := fbenums.ALLOW_DISK_USE_TRUE
// 	opts := &options.FindOptions{
// 		AllowDiskUse: &allowDiskUse,
// 		Skip:         &skip,
// 		Limit:        &limit,
// 	}

// 	sortOrder := payload.SortOrder
// 	opts.SetSort(bson.M{payload.SortField: sortOrder})

// 	pipeline := pipelines.PipelineListTableAds(payload, isAdmin, user)
// 	ads, err := usc.repo.FindListTableAggregateRepo(ctx, pipeline)
// 	if err != nil {
// 		return nil, err
// 	}

// 	// test
// 	var adIds []string
// 	for _, ad := range *ads {
// 		adIds = append(adIds, ad.AdID)
// 	}

// 	fbReportDetails, err := usc.fbReportDetailRepo.GetMetricsColumnForAdsFBReportDetailRepo(ctx, bson.M{"ad_id": bson.M{"$in": adIds}})
// 	if err != nil {
// 		usc.logger.Error(err)
// 	}

// 	//total all
// 	total, err := usc.repo.CountAdRepo(ctx, bson.M{})
// 	if err != nil {
// 		return nil, err
// 	}

// 	// total filtered
// 	totalFiltered := total
// 	if len(filter) != 0 {
// 		totalFiltered, err = usc.repo.CountAdRepo(ctx, filter)
// 		if err != nil {
// 			return nil, err
// 		}
// 	}

// 	data := mapping.MapperAdToDataTable(ads, fbReportDetails)

// 	return &responses.DataTableAdsRes{
// 		Draw:            payload.Draw,
// 		Data:            data,
// 		RecordsTotal:    total,
// 		RecordsFiltered: totalFiltered,
// 	}, nil
// }
