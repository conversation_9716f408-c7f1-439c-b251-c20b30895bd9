package requests

import (
	"godsp/modules/facebook/campaign/common/errs"

	"github.com/go-playground/validator/v10"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

type DeleteCampaignReq struct {
	AccountID   string             `json:"account_id" validate:"required"`
	CampaignIDs []string           `json:"campaign_id" validate:"required,dive,numeric,gt=0"`
	UserId      primitive.ObjectID `json:"-"`
}

func (req *DeleteCampaignReq) Validate() []string {
	validate := validator.New()
	var validationErrors []string

	err := validate.Struct(req)

	if err != nil {
		for _, err := range err.(validator.ValidationErrors) {
			switch err.Field() {
			case "AccountID":
				validationErrors = append(validationErrors, errs.ErrCampaignAccountID.Error())
			case "CampaignID":
				validationErrors = append(validationErrors, errs.ErrCampaignAccountID.Error())
			}
		}
	}
	return validationErrors
}
