package subredis

import (
	"context"
	"encoding/json"
	"fmt"
	payloadtopic "godsp/modules/admin/common/payload_topic"

	"github.com/dev-networldasia/dspgos/sctx/component/watermillapp/redisstream"

	"github.com/dev-networldasia/dspgos/sctx"

	"github.com/ThreeDotsLabs/watermill/message"
)

type ApiAuthUsc interface {
	ResetPasswordSubAuthUsc(ctx context.Context, payload *payloadtopic.PayloadResetPassword) error
}
type ResetPasswordSub struct {
	usc    ApiAuthUsc
	logger sctx.Logger
}

func NewResetPasswordSub(usc ApiAuthUsc, logger sctx.Logger) *ResetPasswordSub {
	return &ResetPasswordSub{
		usc:    usc,
		logger: logger,
	}
}

func (r *ResetPasswordSub) ResetPasswordSubApi() redisstream.NoPublishHandlerFunc {
	return func(msg *message.Message) error {
		var payload payloadtopic.PayloadResetPassword
		if err := json.Unmarshal(msg.Payload, &payload); err != nil {
			msgErr := fmt.Sprintf(
				"Error ResetPasswordSub Unmarshal: %s payload %s",
				err.Error(),
				string(msg.Payload),
			)
			r.logger.Error(msgErr)
			r.usc.ResetPasswordSubAuthUsc(context.Background(), &payload)
			msg.Ack()
			return err
		}

		msg.Ack()
		return nil
	}
}
