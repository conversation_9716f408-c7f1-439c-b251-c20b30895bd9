package requests

import (
	"godsp/modules/facebook/campaign/common/errs"

	"github.com/go-playground/validator/v10"
)

type CampaignReq struct {
	AccountID string `json:"act" validate:"required,numeric,gt=0"`
}

func (req *CampaignReq) Validate() error {
	validate := validator.New()
	
	err := validate.Struct(req)
	if err != nil {
		for _, err := range err.(validator.ValidationErrors) {
			switch err.Field() {
			case "AccountID":
				return errs.ErrCampaignAccountID
			}
		}
	}

	return nil
}
