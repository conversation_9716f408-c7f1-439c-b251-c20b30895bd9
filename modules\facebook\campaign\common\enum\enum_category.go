package enum

const (
	SPECIAL_AD_CATEGORIES_NONE                        = "NONE"
	SPECIAL_AD_CATEGORIES_EMPLOYMENT                  = "EMPLOYMENT"
	SPECIAL_AD_CATEGORIES_HOUSING                     = "HOUSING"
	SPECIAL_AD_CATEGORIES_CREDIT                      = "CREDIT"
	SPECIAL_AD_CATEGORIES_ISSUES_ELECTIONS_POLITICS   = "ISSUES_ELECTIONS_POLITICS"
	SPECIAL_AD_CATEGORIES_ONLINE_GAMBLING_AND_GAMING  = "ONLINE_GAMBLING_AND_GAMING"
	SPECIAL_AD_CATEGORIES_FINANCIAL_PRODUCTS_SERVICES = "FINANCIAL_PRODUCTS_SERVICES"
)

var (
	SpecialAdCategories = map[string]string{
		SPECIAL_AD_CATEGORIES_NONE:                        SPECIAL_AD_CATEGORIES_NONE,
		SPECIAL_AD_CATEGORIES_EMPLOYMENT:                  SPECIAL_AD_CATEGORIES_EMPLOYMENT,
		SPECIAL_AD_CATEGORIES_HOUSING:                     SPECIAL_AD_CATEGORIES_HOUSING,
		SPECIAL_AD_CATEGORIES_CREDIT:                      SPECIAL_AD_CATEGORIES_CREDIT,
		SPECIAL_AD_CATEGORIES_ISSUES_ELECTIONS_POLITICS:   SPECIAL_AD_CATEGORIES_ISSUES_ELECTIONS_POLITICS,
		SPECIAL_AD_CATEGORIES_ONLINE_GAMBLING_AND_GAMING:  SPECIAL_AD_CATEGORIES_ONLINE_GAMBLING_AND_GAMING,
		SPECIAL_AD_CATEGORIES_FINANCIAL_PRODUCTS_SERVICES: SPECIAL_AD_CATEGORIES_FINANCIAL_PRODUCTS_SERVICES,
	}
)

const (
	OBJECTIVE_OUTCOME_LEADS         = "OUTCOME_LEADS"
	OBJECTIVE_OUTCOME_SALES         = "OUTCOME_SALES"
	OBJECTIVE_OUTCOME_ENGAGEMENT    = "OUTCOME_ENGAGEMENT"
	OBJECTIVE_OUTCOME_AWARENESS     = "OUTCOME_AWARENESS"
	OBJECTIVE_OUTCOME_TRAFFIC       = "OUTCOME_TRAFFIC"
	OBJECTIVE_OUTCOME_APP_PROMOTION = "OUTCOME_APP_PROMOTION"
)

var (
	Objective = map[string]string{
		"OUTCOME_AWARENESS":     OBJECTIVE_OUTCOME_AWARENESS,
		"OUTCOME_ENGAGEMENT":    OBJECTIVE_OUTCOME_ENGAGEMENT,
		"OUTCOME_LEADS":         OBJECTIVE_OUTCOME_LEADS,
		"OUTCOME_SALES":         OBJECTIVE_OUTCOME_SALES,
		"OUTCOME_TRAFFIC":       OBJECTIVE_OUTCOME_TRAFFIC,
		"OUTCOME_APP_PROMOTION": OBJECTIVE_OUTCOME_APP_PROMOTION,
	}
)

const (
	DIB_STRATEGY_LOWEST_COST_WITHOUT_CAP   = "LOWEST_COST_WITHOUT_CAP"
	DIB_STRATEGY_LOWEST_COST_WITH_BID_CAP  = "LOWEST_COST_WITH_BID_CAP"
	DIB_STRATEGY_COST_CAP                  = "COST_CAP"
	DIB_STRATEGY_LOWEST_COST_WITH_MIN_ROAS = "LOWEST_COST_WITH_MIN_ROAS"
)

var (
	BidStrategy = map[string]string{
		"LOWEST_COST_WITHOUT_CAP":   DIB_STRATEGY_LOWEST_COST_WITHOUT_CAP,   // Title -> Highest Volume
		"LOWEST_COST_WITH_BID_CAP":  DIB_STRATEGY_LOWEST_COST_WITH_BID_CAP,  // Title -> Bid cap
		"COST_CAP":                  DIB_STRATEGY_COST_CAP,                  // Title -> Cost per result goal
		"LOWEST_COST_WITH_MIN_ROAS": DIB_STRATEGY_LOWEST_COST_WITH_MIN_ROAS, // Title -> Chưa cần set
	}
)
