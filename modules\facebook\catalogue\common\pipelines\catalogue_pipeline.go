package pipeline

import (
	"go.mongodb.org/mongo-driver/bson"
)

func GetCataloguePipeline(filter bson.M) []bson.M {
	var GetCataloguePipeline = []bson.M{
		{
			"$match": filter,
		},
		{
			"$lookup": bson.M{
				"from": "fb_products",
				// "localField":   "list_product_ids",
				// "foreignField": "product_id",
				"let": bson.D{{Key: "catalogueProductIds", Value: "$list_product_ids"}},
				"pipeline": bson.A{
					bson.D{
						{Key: "$match",
							Value: bson.D{
								{Key: "$expr",
									Value: bson.D{
										{Key: "$in",
											Value: bson.A{
												"$product_id",
												"$$catalogueProductIds",
											},
										},
									},
								},
							},
						},
					},
					bson.D{{Key: "$sort", Value: bson.D{{Key: "price", Value: 1}}}},
					bson.D{{Key: "$limit", Value: 10}},
				},
				"as": "products",
			},
		},
		{
			"$project": bson.M{
				"_id": 0,
			},
		},
	}
	return GetCataloguePipeline
}
