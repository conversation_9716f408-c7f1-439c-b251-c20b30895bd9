package entity

import (
	"godsp/modules/admin/user/entity"

	"go.mongodb.org/mongo-driver/bson/primitive"
)

type PermissionEntity struct {
	ID          primitive.ObjectID `json:"_id,omitempty" bson:"_id,omitempty"`
	Method      string             `json:"method" bson:"method"`
	Path        string             `json:"path" bson:"path"`
	RouteName   string             `json:"route_name" bson:"route_name"`
	Name        string             `json:"name" bson:"name"`
	Group       string             `json:"group" bson:"group"`
	Module      string             `json:"module" bson:"module"`
	Action      string             `json:"action" bson:"action"`
	Description string             `json:"description" bson:"description"`
	Ordering    int64              `json:"ordering" bson:"ordering"`
	ParentID    primitive.ObjectID `json:"parent_id" bson:"parent_id"`
	IsApi       int                `json:"is_api" bson:"is_api"`
	IsBlock     int                `json:"is_block" bson:"is_block"`
	IsAcp       int                `json:"is_acp" bson:"is_acp"`

	RefreshID int64              `json:"refresh_id" bson:"refresh_id"`
	CreatedBy primitive.ObjectID `json:"created_by" bson:"created_by"`
	UpdatedBy primitive.ObjectID `json:"updated_by" bson:"updated_by"`

	UserCreated *entity.UserEntity `json:"user_created,omitempty" bson:"user_created,omitempty"`
	UserUpdated *entity.UserEntity `json:"user_updated,omitempty" bson:"user_updated,omitempty"`

	Childrens      *[]PermissionEntity `bson:"childrens,omitempty" json:"childrens,omitempty"`
	Parent         *PermissionEntity   `json:"parent,omitempty" bson:"parent,omitempty"`
	ModulesByGroup []string            `json:"modules_by_group" bson:"modules_by_group"`
}

func (PermissionEntity) CollectionName() string {
	return "admins_permissions"
}

type PermissionUpsert struct {
	ID        primitive.ObjectID `json:"_id,omitempty" bson:"_id,omitempty"`
	Method    string             `json:"method" bson:"method"`
	Path      string             `json:"path" bson:"path"`
	RouteName string             `json:"route_name" bson:"route_name"`
	Name string             `json:"name" bson:"name"`
	Group     *string            `json:"group" bson:"group"`
	Module    *string            `json:"module,omitempty" bson:"module,omitempty"`
	Action    string             `json:"action" bson:"action"`
	IsApi     int                `json:"is_api" bson:"is_api"`
	Ordering  int64              `json:"ordering" bson:"ordering"`
	IsBlock   int                `json:"is_block" bson:"is_block"`
	IsAcp     int                `json:"is_acp" bson:"is_acp"`
	RefreshID int64              `json:"refresh_id" bson:"refresh_id"`
	CreatedBy primitive.ObjectID `json:"created_by" bson:"created_by"`
}
