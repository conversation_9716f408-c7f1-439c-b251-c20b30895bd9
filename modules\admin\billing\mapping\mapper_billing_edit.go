package mapping

import (
	"godsp/modules/admin/billing/entity"
	"godsp/modules/admin/billing/transport/requests"
	"godsp/pkg/gos/utils"
)

func MapperEditBilling(req *requests.PayloadBillingEdition) *entity.BillingEntity {
	timeNow := utils.TimeNowLocationHCM()
	data := &entity.BillingEntity{
		UpdatedBy: req.UserID,
		UpdatedAt: timeNow,
		Status:    req.Status,
		Note:      req.Note,
		Ordering:  req.Ordering,
	}

	return data
}
