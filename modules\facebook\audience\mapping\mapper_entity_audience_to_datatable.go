package mapping

import (
	"godsp/modules/facebook/audience/entity"
	"godsp/modules/facebook/audience/transport/responses"
	"godsp/pkg/gos/goconst"
	"time"
)

func MapperAudienceToDatatable(audiences *[]entity.CustomAudienceEntity) *[]responses.AudienceDatatable {
	if audiences == nil {
		return nil
	}

	datas := []responses.AudienceDatatable{}

	for _, audience := range *audiences {
		updateTime := ""

		if !audience.TimeUpdated.IsZero() && time.Since(audience.TimeUpdated).Hours() <= 365*24 {
			updateTime = audience.TimeUpdated.Format(goconst.DD_MM_YYYY)
		}

		status := "Ready"
		if audience.ApproximateCountLowerBound == -1 || audience.ApproximateCountUpperBound == -1 {
			status = "Audience not created"
		}

		if audience.DeleteTime > 0 {
			status = "Expiring"
		}

		datas = append(datas, responses.AudienceDatatable{
			DTRowId:                    "row_" + audience.CustomAudienceID,
			AudienceID:                 audience.CustomAudienceID,
			Name:                       audience.Name,
			Description:                audience.Description,
			TimeUpdated:                updateTime,
			Subtype:                    audience.Subtype,
			Rule:                       audience.Rule,
			Type:                       audience.Type,
			ApproximateCountUpperBound: audience.ApproximateCountUpperBound,
			ApproximateCountLowerBound: audience.ApproximateCountLowerBound,
			DeleteTime:                 audience.DeleteTime,
			Status:                     status,
		})
	}

	return &datas
}
