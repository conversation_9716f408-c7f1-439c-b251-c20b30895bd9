package pipelines

import (
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

func PipelineGetUserProfile(idObject primitive.ObjectID) []bson.M {
	pipeline := []bson.M{
		{
			"$match": bson.M{
				"_id": idObject,
			},
		},
		{
			"$lookup": bson.M{
				"from":         "admin_roles",
				"localField":   "role_id",
				"foreignField": "_id",
				"as":           "role",
			},
		},
		{
			"$lookup": bson.M{
				"from":         "admin_client",
				"localField":   "client_id",
				"foreignField": "_id",
				"as":           "client",
			},
		},
		{
			"$unwind": bson.M{
				"path":                       "$role",
				"preserveNullAndEmptyArrays": true,
			},
		},
		{
			"$unwind": bson.M{
				"path":                       "$client",
				"preserveNullAndEmptyArrays": true,
			},
		},
		{
			"$addFields": bson.M{
				"role_name":   "$role.role_name",
				"client_name": "$client.name",
			},
		},
	}

	return pipeline
}
