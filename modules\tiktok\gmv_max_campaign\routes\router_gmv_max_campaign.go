package routes

import (
	"github.com/dev-networldasia/dspgos/sctx"
	"github.com/gofiber/fiber/v2"
	"github.com/gofiber/fiber/v2/middleware/session"
)

func SetupRoutesGmvMaxCampaign(app *fiber.App, serviceCtx sctx.ServiceContext, store *session.Store, midds ...fiber.Handler) {

	group := app.Group("dsp/tiktok/gmv-max-campaign")
	{
		if len(midds) > 0 {
			for _, midd := range midds {
				group.Use(midd)
			}
		}

		comPage := ComposerGmvMaxCampaignService(serviceCtx)
		group.Get("/list", comPage.ListGmvMaxCampaignHdl(store)).Name("tiktok.gmv-max-campaign.list")
		group.Get("/detail", comPage.DetailGmvMaxCampaignHdl(store)).Name("tiktok.gmv-max-campaign.detail")
		// group.Get("/edit", comPage.EditCampaignHdl(store)).Name("tiktok.campaign.edit")

	}

}
