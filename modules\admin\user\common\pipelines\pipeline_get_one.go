package pipelines

import (
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

func PipelineGetOne(idObject primitive.ObjectID) []bson.M {
	pipeline := []bson.M{
		{
			"$match": bson.M{
				"_id": idObject,
			},
		},
		{
			"$lookup": bson.M{
				"from":         "admin_roles",
				"localField":   "role_id",
				"foreignField": "_id",
				"as":           "role",
			},
		},
		{
			"$lookup": bson.M{
				"from":         "admin_users",
				"localField":   "created_by",
				"foreignField": "_id",
				"as":           "user_created",
			},
		},
		{
			"$lookup": bson.M{
				"from":         "admin_users",
				"localField":   "updated_by",
				"foreignField": "_id",
				"as":           "user_updated",
			},
		},
		{
			"$unwind": bson.M{
				"path":                       "$role",
				"preserveNullAndEmptyArrays": true,
			},
		},
		{
			"$unwind": bson.M{
				"path":                       "$user_created",
				"preserveNullAndEmptyArrays": true,
			},
		},
		{
			"$unwind": bson.M{
				"path":                       "$user_updated",
				"preserveNullAndEmptyArrays": true,
			},
		},
	}

	return pipeline
}
