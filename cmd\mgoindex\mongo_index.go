package mgoindex

import (
	"fmt"
	"godsp/cmd/mgoindex/internal"

	"github.com/dev-networldasia/dspgos/sctx/configs"

	"github.com/dev-networldasia/dspgos/sctx"
	"github.com/dev-networldasia/dspgos/sctx/component/mongodb"
	"github.com/spf13/cobra"
)

var (
	nameServer      = "mongo-index-service"
	versionSchedule = "1.0.0"
)

func newMongoIndexServiceCtx() sctx.ServiceContext {
	return sctx.NewServiceContext(
		sctx.WithName(nameServer),
		sctx.WithComponent(mongodb.NewMongoDB(configs.KeyCompMongoDB, "")),
		sctx.WithComponent(sctx.NewAppLoggerDaily(configs.KeyLoggerDaily)),
	)
}

var (
	MongoIndexCmd = &cobra.Command{
		Use:     "mgo_index",
		Short:   "mongo index run godsp social",
		Long:    `mongo index CLI Long godsp social`,
		Version: versionSchedule,
		Run: func(cmd *cobra.Command, args []string) {
			fmt.Println("--->> mongo index run <<---")

			serviceCtx := newMongoIndexServiceCtx()
			loggerSv := serviceCtx.MustGet(configs.KeyLoggerDaily).(sctx.AppLoggerDaily).GetLogger("mongo_index")
			loggerSv.Info("Mongo index is running!")

			if err := serviceCtx.Load(); err != nil {
				loggerSv.Fatal(err)
			}

			if err := internal.SetIndexMongoDatabase(serviceCtx); err != nil {
				loggerSv.Error(err)
				fmt.Println("index mongodb error: ", err)
				return
			}

			loggerSv.Info("Mongo index Done!")
			fmt.Println("--->> mongo index done <<---")
		},
	}
)
