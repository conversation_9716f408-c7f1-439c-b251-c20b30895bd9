package usecase

import (
	"context"
	"encoding/json"
	"fmt"
	"github.com/dev-networldasia/dspgos/sctx/component/watermillapp/redisstream"
	"godsp/conf"
	"godsp/modules/admin/billing/common/consts"
	"godsp/modules/admin/billing/common/errs"
	client "godsp/modules/admin/billing/common/utils"
	"godsp/modules/admin/billing/entity"
	"godsp/modules/admin/billing/mapping"
	"godsp/modules/admin/billing/transport/requests"
	"godsp/modules/admin/billing/transport/response"
	clientE "godsp/modules/admin/client/entity"
	"godsp/modules/admin/common/admconst"
	"godsp/modules/admin/common/admfunc"
	payloadtopic "godsp/modules/admin/common/payload_topic"
	userE "godsp/modules/admin/user/entity"
	adAccountE "godsp/modules/facebook/ad_account/entity"
	"godsp/modules/facebook/iface_repo"
	"godsp/pkg/gos/utils"
	"strconv"
	"strings"

	"github.com/dev-networldasia/dspgos/sctx"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo/options"
)

type ApiBillingRepo interface {
	InsertBillingRepo(
		ctx context.Context,
		billing *entity.BillingEntity,
	) error
	UpdateOneBillingRepo(
		ctx context.Context,
		filter interface{},
		billing *entity.BillingEntity,
		opts ...*options.UpdateOptions,
	) error
	UpdateOneBillingRepoWithAttr(
		ctx context.Context,
		filter interface{},
		billing *entity.BillingEntity,
		opts ...*options.UpdateOptions,
	) error
	FindBillingRepo(
		ctx context.Context,
		filter interface{},
		opts ...*options.FindOptions,
	) (*[]entity.BillingEntity, error)
	FindOneBillingRepo(
		ctx context.Context,
		filter interface{},
		opts ...*options.FindOneOptions,
	) (*entity.BillingEntity, error)
}

type AdAccountRepoApi interface {
	FindUserRepo(ctx context.Context, filter interface{}, opts ...*options.FindOptions) (*[]userE.UserEntity, error)
	FindOneAdAccountRepo(ctx context.Context, filter interface{}, opts ...*options.FindOneOptions) (*adAccountE.AdAccountEntity, error)
}

type UserRepoApi interface {
	FindUserRepo(ctx context.Context, filter interface{}, opts ...*options.FindOptions) (*[]userE.UserEntity, error)
}

type ClientRepoApi interface {
	FindClientRepo(ctx context.Context, filter interface{}, opts ...*options.FindOptions) ([]*clientE.ClientEntity, error)
	FindOneClientRepo(ctx context.Context, filter interface{}, opts ...*options.FindOneOptions) (*clientE.ClientEntity, error)
}

type apiBillingUsc struct {
	repo           ApiBillingRepo
	adAccount      iface_repo.AdAccountRepo
	client         ClientRepoApi
	user           UserRepoApi
	redisStreamPub redisstream.RedisStreamPublisher
	logger         sctx.Logger
}

func NewApiBillingUsc(
	repo ApiBillingRepo,
	adAccount iface_repo.AdAccountRepo,
	client ClientRepoApi,
	user UserRepoApi,
	redisStreamPub redisstream.RedisStreamPublisher,
	logger sctx.Logger,
) *apiBillingUsc {
	return &apiBillingUsc{
		repo:           repo,
		adAccount:      adAccount,
		client:         client,
		user:           user,
		logger:         logger,
		redisStreamPub: redisStreamPub,
	}
}

func (usc *apiBillingUsc) CreateApiBillingUsc(
	ctx context.Context,
	payload *requests.PayloadBillingCreation,
) (string, error) {
	userId, _, clientId, _, err := utils.GetInfoUser(ctx)
	if err != nil {
		return "", err
	}

	// calling api to [create new payment, generate qr code instance]
	billingEntity := mapping.MapperCreateBilling(payload, userId, clientId)
	// Marshal the payload to JSON
	jsonData, err := json.Marshal(billingEntity)
	if err != nil {
		usc.logger.Error(fmt.Errorf("CreateApiBillingUsc: failed to marshal VTB payload: %w", err))

		return "", fmt.Errorf("failed to marshal VTB payload: %w", err)
	}

	resp, err := client.MakeRequest(ctx, client.REQUEST_POST, consts.DV360_BILLING_API["CREATE_VTB"], jsonData)

	return string(resp), err
}

/**
 * Api billing list datatable
 */
func (usc *apiBillingUsc) ListDatatableApiBillingUsc(ctx context.Context, payload *requests.ListBillingApiReq) (*[]response.BillingDataTable, error) {
	auth, err := utils.GetInfoUserAuth(ctx)
	if err != nil {
		usc.logger.Error(err)

		return nil, err
	}

	billings, err := usc.getAllBilling(ctx, payload)
	if err != nil {
		usc.logger.Error(err)

		return nil, err
	}

	if len(billings) == 0 {
		return nil, nil
	}

	userIDSet := make(map[string]struct{})
	clientIDSet := make(map[string]struct{})
	for _, billing := range billings {
		if billing.ApprovedBy != "" {
			userIDSet[billing.ApprovedBy] = struct{}{}
		}

		if billing.MCreatedBy != "" {
			userIDSet[billing.MCreatedBy] = struct{}{}
		}

		if billing.UpdatedBy != "" {
			userIDSet[billing.UpdatedBy] = struct{}{}
		}

		if billing.MClientID != "" {
			clientIDSet[billing.MClientID] = struct{}{}
		}
	}

	var userIDs []primitive.ObjectID
	for id := range userIDSet {
		objID, err := primitive.ObjectIDFromHex(id)
		if err == nil {
			userIDs = append(userIDs, objID)
		}
	}

	var clientIDs []primitive.ObjectID
	for id := range clientIDSet {
		objID, err := primitive.ObjectIDFromHex(id)
		if err == nil {
			clientIDs = append(clientIDs, objID)
		}
	}

	clients := []*clientE.ClientEntity{}
	if len(clientIDs) != 0 {
		clients, err = usc.client.FindClientRepo(ctx, bson.M{"_id": bson.M{"$in": clientIDs}})
		if err != nil {
			usc.logger.Error(err)

			return nil, err
		}
	}

	users := &[]userE.UserEntity{}
	if len(userIDs) != 0 {
		users, err = usc.user.FindUserRepo(ctx, bson.M{"_id": bson.M{"$in": userIDs}})
		if err != nil {
			usc.logger.Error(err)

			return nil, err
		}
	}
	//

	roleGroup, err := admfunc.GetRoleGroup(ctx, &auth.RoleName)
	if err != nil {
		usc.logger.Error(err)

		return nil, err
	}

	billingTable := response.GetDataListBillingResponse(billings, users, clients, roleGroup)

	return billingTable, nil
}

func (usc *apiBillingUsc) getAllBilling(ctx context.Context, payload *requests.ListBillingApiReq) ([]response.BillingDataResponse, error) {
	auth, err := utils.GetInfoUserAuth(ctx)
	if err != nil {
		usc.logger.Error(err)

		return nil, err
	}

	roleGroup, err := admfunc.GetRoleGroup(ctx, &auth.RoleName)
	if err != nil {
		usc.logger.Error(err)

		return nil, err
	}

	clientId := payload.Filter.ClientId
	if clientId == "" {
		clientId = "all"
	}

	if roleGroup == admconst.ROLE_GROUP_CLIENT {
		clientId = auth.ClientId.Hex()
	}

	params := requests.PayloadListBillingApi{
		MClientId: clientId,
		Platform:  consts.PLATFORM_FB,
		Page:      payload.Page,
		//PageSize:  admconst.DEFAULT_LIMIT,
		PageSize: 100,
	}

	status := payload.Filter.Status
	if status != "" {
		params.Status = status
	}

	if payload.Filter.Search != "" {
		params.Search = payload.Filter.Search
	}

	jsonParams, er := json.Marshal(params)
	if er != nil {
		usc.logger.Error(er)

		return nil, er
	}

	billings, err := client.MakeRequest(ctx, client.REQUEST_POST, consts.DV360_BILLING_API["LIST"], jsonParams)
	if err != nil {
		usc.logger.Error(err)

		return nil, err
	}

	var resp response.DataListBillingResponse
	err = json.Unmarshal(billings, &resp)
	if err != nil {
		usc.logger.Error(err)

		return nil, err
	}

	return resp.DataResponse.Data, nil
}

func (usc *apiBillingUsc) ListDatatableApiBillingDetailUsc(ctx context.Context, payload *requests.ListBillingDetailApiReq) (*[]response.BillingDetailDataTable, error) {
	auth, err := utils.GetInfoUserAuth(ctx)
	if err != nil {
		usc.logger.Error(err)

		return nil, err
	}

	billingDetails, err := usc.getAllBillingDetail(ctx, payload)
	if err != nil {
		usc.logger.Error(err)

		return nil, err
	}

	if len(billingDetails) == 0 {
		return nil, nil
	}

	userIDSet := make(map[string]struct{})
	clientIDSet := make(map[string]struct{})
	adAccountIDSet := make(map[int]struct{})
	for _, detail := range billingDetails {
		if detail.ApprovedBy != "" {
			userIDSet[detail.ApprovedBy] = struct{}{}
		}

		if detail.CreatedBy != "" {
			userIDSet[detail.CreatedBy] = struct{}{}
		}

		if detail.UpdatedBy != "" {
			userIDSet[detail.UpdatedBy] = struct{}{}
		}

		if detail.ClientID != "" {
			clientIDSet[detail.ClientID] = struct{}{}
		}

		if detail.AdvertiserID != 0 {
			adAccountIDSet[detail.AdvertiserID] = struct{}{}
		}
	}

	var userIDs []primitive.ObjectID
	for id := range userIDSet {
		objID, err := primitive.ObjectIDFromHex(id)
		if err == nil {
			userIDs = append(userIDs, objID)
		}
	}

	var clientIDs []primitive.ObjectID
	for id := range clientIDSet {
		objID, err := primitive.ObjectIDFromHex(id)
		if err == nil {
			clientIDs = append(clientIDs, objID)
		}
	}

	var adAccountIDs []string
	for id := range adAccountIDSet {
		adAccountIDs = append(adAccountIDs, strconv.Itoa(id))
	}

	clients := []*clientE.ClientEntity{}
	if len(clientIDs) != 0 {
		clients, err = usc.client.FindClientRepo(ctx, bson.M{"_id": bson.M{"$in": clientIDs}})
		if err != nil {
			usc.logger.Error(err)

			return nil, err
		}
	}

	users := &[]userE.UserEntity{}
	if len(userIDs) != 0 {
		users, err = usc.user.FindUserRepo(ctx, bson.M{"_id": bson.M{"$in": userIDs}})
		if err != nil {
			usc.logger.Error(err)

			return nil, err
		}
	}

	adAccounts := []*adAccountE.AdAccountEntity{}
	if len(adAccountIDs) != 0 {
		adAccounts, err = usc.adAccount.FindAdAccountRepo(ctx, bson.M{"account_id": bson.M{"$in": adAccountIDs}})
		if err != nil {
			usc.logger.Error(err)

			return nil, err
		}
	}

	roleGroup, err := admfunc.GetRoleGroup(ctx, &auth.RoleName)
	if err != nil {
		usc.logger.Error(err)

		return nil, err
	}

	billingTable := response.GetDataBillingDetailResponse(billingDetails, users, adAccounts, clients, roleGroup)

	return billingTable, nil
}

func (usc *apiBillingUsc) getAllBillingDetail(ctx context.Context, payload *requests.ListBillingDetailApiReq) ([]response.BillingDetailDataResponse, error) {
	params := requests.PayloadListBillingDetailApi{
		BillNumber: payload.Filter.BillNumber,
		Page:       payload.Page,
		//PageSize:  admconst.DEFAULT_LIMIT,
		PageSize: 100,
	}

	jsonParams, er := json.Marshal(params)
	if er != nil {
		usc.logger.Error(er)

		return nil, er
	}

	billingDetails, err := client.MakeRequest(ctx, client.REQUEST_POST, consts.DV360_BILLING_DETAIL_API["LIST"], jsonParams)
	if err != nil {
		usc.logger.Error(err)

		return nil, err
	}

	var resp response.DataListBillingDetailResponse
	err = json.Unmarshal(billingDetails, &resp)
	if err != nil {
		usc.logger.Error(err)

		return nil, err
	}

	return resp.DataResponse.Data, nil
}

/**
 * Api billing update/edit
 */
func (usc *apiBillingUsc) EditApiBillingUsc(ctx context.Context, payload *requests.PayloadBillingEdition) error {
	billingEntity := mapping.MapperEditBilling(payload)
	fmt.Println(billingEntity.UpdatedAt)

	filter := bson.M{
		"_id": payload.ID,
	}
	err := usc.repo.UpdateOneBillingRepoWithAttr(ctx, filter, billingEntity)
	if err != nil {
		usc.logger.Error(err)
		return err
	}

	return nil
}

func (usc *apiBillingUsc) UpdateBillingDetailApiUsc(ctx context.Context, payload *requests.PayloadUpdateBillingDetail) error {
	auth, err := utils.GetInfoUserAuth(ctx)
	if err != nil {
		usc.logger.Error(err)

		return err
	}

	roleGroup, err := admfunc.GetRoleGroup(ctx, &auth.RoleName)
	if err != nil {
		usc.logger.Error(err)

		return err
	}

	if roleGroup == admconst.ROLE_GROUP_CLIENT {
		return errs.ErrIDNotFound
	}

	payload.Platform = "FB"
	payload.ApprovedBy = payload.UserID.Hex()
	err = usc.updateBillingDetailInfo(ctx, payload)
	if err != nil {
		usc.logger.Error(err)

		return err
	}

	if _, err := usc.handleSendBillingDetailEmail(ctx, payload); err != nil {
		return err
	}

	return nil
}

func (usc *apiBillingUsc) handleSendBillingDetailEmail(
	ctx context.Context,
	payload *requests.PayloadUpdateBillingDetail,
) (*payloadtopic.PayloadUpdateBillingDetail, error) {
	clientData, err := usc.client.FindOneClientRepo(ctx, bson.M{"_id": payload.ClientID})
	if err != nil {
		usc.logger.Error(err)

		return nil, err
	}

	AdvertiserID := strconv.FormatInt(payload.AdvertiserID, 10)
	adAccountData, err := usc.adAccount.FindOneAdAccountRepo(ctx, bson.M{"account_id": AdvertiserID})
	if err != nil {
		usc.logger.Error(err)

		return nil, err
	}

	isRejected := strings.Contains(payload.Status, "_REJECTED")
	toEmail := clientData.Email
	if !isRejected {
		toEmail = conf.EmailConf.AdminEmail
	}

	pubSubPayload := payloadtopic.PayloadUpdateBillingDetail{
		ToEmail:       toEmail,
		AdAccountInfo: map[string]interface{}{"ID": adAccountData.AccountID, "Name": adAccountData.Name},
		ClientInfo:    map[string]interface{}{"ID": clientData.ID, "Name": clientData.Name, "Email": clientData.Email},
		Amount:        payload.Amount,
		Notes:         payload.Notes,
		Status:        payload.Status,
		BillNumber:    payload.BillNumber,
		Currency:      payload.Currency,
		BillingID:     payload.BillingID,
		Retry:         consts.RETRY_MAX_COUNT,
	}

	if err := usc.redisStreamPub.Publish(payloadtopic.TOPIC_SEND_EMAIL_BILLING_DETAIL, pubSubPayload); err != nil {
		usc.logger.Error("handleSendBillingDetailEmail error: %+v \n pubSubPayload: %+v \n", err, pubSubPayload)

		return nil, err
	}

	return &pubSubPayload, nil
}

func (usc *apiBillingUsc) updateBillingDetailInfo(ctx context.Context, payload *requests.PayloadUpdateBillingDetail) error {
	jsonParams, err := json.Marshal(payload)
	if err != nil {
		usc.logger.Error(err)

		return err
	}

	_, err = client.MakeClientRequest(ctx, client.REQUEST_POST, consts.DV360_BILLING_DETAIL_API["UPDATE"], jsonParams)
	if err != nil {
		usc.logger.Error(err)

		return err
	}

	return nil
}

func (usc *apiBillingUsc) UpdateBillingApiUsc(ctx context.Context, payload *requests.PayloadUpdateBilling) error {
	auth, err := utils.GetInfoUserAuth(ctx)
	if err != nil {
		usc.logger.Error(err)

		return err
	}

	if auth.RoleName != admconst.ROLE_ADMIN {
		return errs.ErrIDNotFound
	}

	payload.Status = consts.BILLING_STATUS_COMPLETED
	payload.ApprovedBy = payload.UserID.Hex()
	err = usc.updateBillingInfo(ctx, payload)
	if err != nil {
		usc.logger.Error(err)

		return err
	}

	if _, err := usc.handleSendBillingEmail(ctx, payload); err != nil {
		return err
	}

	return nil
}

func (usc *apiBillingUsc) handleSendBillingEmail(
	ctx context.Context,
	payload *requests.PayloadUpdateBilling,
) (*payloadtopic.PayloadUpdateBilling, error) {
	clientData, err := usc.client.FindOneClientRepo(ctx, bson.M{"_id": payload.ClientID})
	if err != nil {
		usc.logger.Error(err)

		return nil, err
	}

	pubSubPayload := payloadtopic.PayloadUpdateBilling{
		ToEmail:    clientData.Email,
		ClientInfo: map[string]interface{}{"ID": clientData.ID, "Name": clientData.Name, "Email": clientData.Email},
		Notes:      payload.Notes,
		Status:     payload.Status,
		BillNumber: payload.BillNumber,
		Currency:   payload.Currency,
		BillingID:  payload.BillingID,
		Retry:      consts.RETRY_MAX_COUNT,
	}

	if err := usc.redisStreamPub.Publish(payloadtopic.TOPIC_SEND_EMAIL_BILLING, pubSubPayload); err != nil {
		usc.logger.Error("handleSendBillingEmail error: %+v \n pubSubPayload: %+v \n", err, pubSubPayload)

		return nil, err
	}

	return &pubSubPayload, nil
}

func (usc *apiBillingUsc) updateBillingInfo(ctx context.Context, payload *requests.PayloadUpdateBilling) error {
	jsonParams, err := json.Marshal(payload)
	if err != nil {
		usc.logger.Error(err)

		return err
	}

	_, err = client.MakeClientRequest(ctx, client.REQUEST_POST, consts.DV360_BILLING_API["UPDATE_STATUS"], jsonParams)
	if err != nil {
		usc.logger.Error(err)

		return err
	}

	return nil
}
