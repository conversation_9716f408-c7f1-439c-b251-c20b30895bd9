package requests

import (
	"godsp/modules/admin/common/admerrs"

	"github.com/go-playground/validator/v10"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

type PayloadAuthResetPassword struct {
	ID primitive.ObjectID `form:"id" validate:"required"`
}

func (pl *PayloadAuthResetPassword) Validate() []*string {
	validate := validator.New()
	err := validate.Struct(pl)
	var validationErrors []*string
	if err != nil {
		for _, err := range err.(validator.ValidationErrors) {
			switch err.Field() {
			case "ID":
				errMsg := admerrs.ErrIsUnvalid.Error()
				validationErrors = append(validationErrors, &errMsg)
			}
		}
		return validationErrors
	}

	return nil
}
