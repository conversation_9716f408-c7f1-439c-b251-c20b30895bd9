package requests

import (
	"godsp/modules/facebook/campaign/common/errs"

	"github.com/go-playground/validator/v10"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

type GetDetailCampaignReq struct {
	CampaignID *string `json:"campaign_id,omitempty" validate:"omitempty,numeric,gt=0"`
	AdsetID    *string `json:"adset_id,omitempty" validate:"omitempty,numeric,gt=0"`
	AdID       *string `json:"ad_id,omitempty" validate:"omitempty,numeric,gt=0"`

	UserId primitive.ObjectID `json:"-"`
}

func (req *GetDetailCampaignReq) Validate() []string {
	validate := validator.New()

	var validationErrors []string

	err := validate.Struct(req)
	if err != nil {
		for _, err := range err.(validator.ValidationErrors) {
			switch err.Field() {
			case "CampaignID":
				validationErrors = append(validationErrors, errs.ErrCampaignID.Error())
			case "AdsetID":
				validationErrors = append(validationErrors, errs.ErrAdsetID.Error())
			case "AdID":
				validationErrors = append(validationErrors, errs.ErrAdID.Error())
			}
		}
	}

	// validate SpecialAdCategories
	if req.CampaignID == nil && req.AdsetID == nil && req.AdID == nil {
		validationErrors = append(validationErrors, errs.ErrCampaignID.Error())
	}

	return validationErrors
}
