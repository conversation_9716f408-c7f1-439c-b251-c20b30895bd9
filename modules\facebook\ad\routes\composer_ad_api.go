package routes

import (
	"godsp/conf"
	userR "godsp/modules/admin/user/repository/mongo"
	"godsp/modules/facebook/ad/repository/mongo"
	"godsp/modules/facebook/ad/transport/api"
	"godsp/modules/facebook/ad/usecase"
	fbReportDetailR "godsp/modules/facebook/adserver_report/repository/mongo"
	"godsp/pkg/sctx/component/facebook/fbmarketing"

	"github.com/dev-networldasia/dspgos/sctx/component/mongodb"
	"github.com/dev-networldasia/dspgos/sctx/configs"
	"github.com/dev-networldasia/dspgos/sctx"
	"github.com/gofiber/fiber/v2"
)

type ComposerAdApi interface {
	ReloadAdApi() fiber.Handler
	CreateAdApi() fiber.Handler
	GetDetailAdApi() fiber.Handler
	UpdateAdApi() fiber.Handler
	UpdateNameStatusAdApi() fiber.Handler
	ListAdDataTableApi() fiber.Handler
	DeleteAdApi() fiber.Handler
}

func ComposerAdApiService(serviceCtx sctx.ServiceContext) ComposerAdApi {
	mongoDB := serviceCtx.MustGet(configs.KeyCompMongoDB).(mongodb.MongoComponent).GetDatabase()
	mongoAdserverReportDB := serviceCtx.MustGet(conf.KeyCompReportMongoDB).(mongodb.MongoComponent).GetDatabase()
	logger := serviceCtx.MustGet(configs.KeyLoggerDaily).(sctx.AppLoggerDaily).GetLogger("webfb")
	fbService := serviceCtx.MustGet(configs.KeyFBMarketingV20).(fbmarketing.FBMarketingServices).GetFBMarketingService()

	repo := mongo.NewAdRepo(mongoDB)
	userRepo := userR.NewUserRepo(mongoDB)

	fbReportDetailRepo := fbReportDetailR.NewFBReportDetailRepo(mongoAdserverReportDB)
	usc := usecase.NewApiAdUsc(fbService, repo, fbReportDetailRepo, userRepo, logger)
	api := api.NewAdApi(usc)

	return api
}
