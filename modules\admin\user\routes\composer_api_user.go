package routes

import (
	"godsp/modules/admin/user/repository/mongo"
	"godsp/modules/admin/user/transport/api"
	"godsp/modules/admin/user/usecase"
	"godsp/pkg/gos/auths"

	"github.com/dev-networldasia/dspgos/sctx/component/mongodb"

	"github.com/dev-networldasia/dspgos/sctx/configs"

	"github.com/dev-networldasia/dspgos/sctx"

	roleR "godsp/modules/admin/role/repository/mongo"
	pixelR "godsp/modules/facebook/ad_pixel/repository/mongo"
	catalogueR "godsp/modules/facebook/catalogue/repository/mongo"
	pageR "godsp/modules/facebook/pages/repository/mongo"

	adAccR "godsp/modules/facebook/ad_account/repository/mongo"

	"github.com/gofiber/fiber/v2"
)

type composerUserApi interface {
	GetUserByIDApi() fiber.Handler
	ListUserApi() fiber.Handler
	CreateUserApi() fiber.Handler
	UpdateUserApi() fiber.Handler
	ListUserAdminApi() fiber.Handler
	UpdateUserBsonApi() fiber.Handler
}

func ComposerUserApiServive(serviceCtx sctx.ServiceContext) composerUserApi {
	logger := serviceCtx.MustGet(configs.KeyLoggerDaily).(sctx.AppLoggerDaily).GetLogger("admins")
	mongoDB := serviceCtx.MustGet(configs.KeyCompMongoDB).(mongodb.MongoComponent).GetDatabase()

	repo := mongo.NewUserRepo(mongoDB)
	hasher := new(auths.Hasher)
	roleRepo := roleR.NewRoleRepo(mongoDB)

	adAccRepo := adAccR.NewAdAccountRepo(mongoDB)
	pageRepo := pageR.NewPageRepo(mongoDB)
	pixelRepo := pixelR.NewAdPixelRepo(mongoDB)
	catalogueRepo := catalogueR.NewCatalogueRepo(mongoDB)

	usc := usecase.NewApiUserUsc(logger, hasher, repo, roleRepo, adAccRepo, pageRepo, pixelRepo, catalogueRepo)
	api := api.NewUserApi(usc)

	return api
}
