package handler

import (
	payloadtopic "godsp/modules/admin/common/payload_topic"

	"github.com/gofiber/fiber/v2"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

type EmailUsecase interface {
	SendEmailResetPasswordUsc(payload *payloadtopic.PayloadResetPassword) error
}

type EmailHdl struct {
	usc EmailUsecase
}

func NewEmailHdl(usc EmailUsecase) *EmailHdl {
	return &EmailHdl{usc: usc}
}

func (h *EmailHdl) SendEmailResetPasswordHdl() fiber.Handler {
	return func(c *fiber.Ctx) error {
		req := payloadtopic.PayloadResetPassword{
			ToEmail:           "<EMAIL>",
			ID:                primitive.NewObjectID(),
			Salt:              "sadsa",
			OldPasswordHashed: "OldPassword",
			NewPassword:       "okla change here!",
			Retry:             0,
		}

		if err := h.usc.SendEmailResetPasswordUsc(&req); err != nil {
			return c.Status(fiber.StatusInternalServerError).JSON(fiber.Map{
				"error": "Failed to initiate sending example email: " + err.Error(),
			})
		}

		return c.Status(fiber.StatusOK).JSON(fiber.Map{
			"message": "Send Reset Password successfully",
		})
	}
}
