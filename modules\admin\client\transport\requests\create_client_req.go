package requests

import (
	"fmt"
	"godsp/conf"
	"godsp/modules/admin/client/common/errs"
	"godsp/modules/admin/common/admrules"
	"godsp/pkg/gos/utils"
	"mime/multipart"
	"strings"

	"github.com/go-playground/validator/v10"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

type PayloadClientCreation struct {
	Name    string    `form:"name" validate:"required"`
	Company string    `form:"company" validate:"required"`
	Brand   *[]string `form:"brand[]" validate:"required"`
	Email   string    `form:"email" validate:"required,email"`
	Status  string    `json:"status" validate:"required,ruleStatusUser"`

	Position string `form:"position,omitempty" `
	Address  string `form:"address,omitempty"`
	Domain   string `form:"domain,omitempty" `
	Phone    string `form:"phone,omitempty"`

	AdAccountIDs []string `form:"ad_account_ids,omitempty" validate:"omitempty,gt=0"`

	ClientUserIDsString     []string `form:"client_user_ids,omitempty"`
	ClientAdminUserIDString *string  `form:"client_admin_user_id,omitempty"`

	FileImg           *multipart.FileHeader `form:"-" json:"-"`
	UserID            primitive.ObjectID    `form:"-" json:"-"`
	ClientUserIDs     []primitive.ObjectID  `form:"-" json:"-"`
	ClientAdminUserID *primitive.ObjectID   `form:"-" json:"-"`
	Logo              string                `form:"-" json:"-"`
}

func (req *PayloadClientCreation) Validate() []*string {
	validate := validator.New()
	var validationErrors []*string
	validate.RegisterValidation("ruleStatusUser", admrules.RuleStatusCreate)

	err := validate.Struct(req)
	if err != nil {
		for _, err := range err.(validator.ValidationErrors) {
			switch err.Field() {
			case "Name":
				errMsg := errs.ErrNameValidate.Error()
				validationErrors = append(validationErrors, &errMsg)
			case "Company":
				errMsg := errs.ErrCompanyValidate.Error()
				validationErrors = append(validationErrors, &errMsg)
			case "Brand":
				errMsg := errs.ErrBrandValidate.Error()
				validationErrors = append(validationErrors, &errMsg)
			case "Email":
				errMsg := errs.ErrEmailValidate.Error()
				validationErrors = append(validationErrors, &errMsg)
			case "Status":
				errMsg := errs.ErrStatusValidate.Error()
				validationErrors = append(validationErrors, &errMsg)
			case "Phone":
				errMsg := errs.ErrPhoneValidate.Error()
				validationErrors = append(validationErrors, &errMsg)
			case "ClientUserIDsString":
				errMsg := errs.ErrClientUserIDsEmpty.Error()
				validationErrors = append(validationErrors, &errMsg)
			case "ClientAdminUserIDString":
				errMsg := errs.ErrClientAdminIDEmpty.Error()
				validationErrors = append(validationErrors, &errMsg)
			case "Position":
				errMsg := errs.ErrPositionValidate.Error()
				validationErrors = append(validationErrors, &errMsg)
			case "Address":
				errMsg := errs.ErrAddressValidate.Error()
				validationErrors = append(validationErrors, &errMsg)
			case "Domain":
				errMsg := errs.ErrDomainValidate.Error()
				validationErrors = append(validationErrors, &errMsg)
			}
		}

	}

	if req.ClientAdminUserIDString != nil {
		ClientAdminUserID, err := primitive.ObjectIDFromHex(*req.ClientAdminUserIDString)
		if err != nil {
			errMsg := errs.ErrAdminIDUserValidate.Error()
			validationErrors = append(validationErrors, &errMsg)
		}
		req.ClientAdminUserID = &ClientAdminUserID
	}

	if validationErrors == nil {
		req.Email = strings.TrimSpace(req.Email)
		req.Company = strings.TrimSpace(req.Company)
		req.Status = strings.TrimSpace(req.Status)

		if req.Brand != nil && len(*req.Brand) > 0 {
			var newBrand []string
			for _, v := range *req.Brand {
				newBrand = append(newBrand, strings.TrimSpace(v))
			}
			req.Brand = &newBrand
		}
	}

	// set hinh
	if req.FileImg != nil {
		fullName := strings.ToLower(req.Company)
		if pathName, err := utils.UploadFileImageThumb(req.FileImg, conf.UploadPathPublic, conf.PathImgClient, &fullName); err != nil {
			errStr := err.Error()
			validationErrors = append(validationErrors, &errStr)
		} else {
			req.Logo = *pathName
			fmt.Printf("pathName: %v\n", *pathName)
		}

	} else {
		req.Logo = conf.PathImgUserDefault
	}

	return validationErrors
}
