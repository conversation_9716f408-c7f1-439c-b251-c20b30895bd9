package mapping

import (
	"fmt"
	"godsp/modules/facebook/adset/entity"
	v20 "godsp/pkg/fb-marketing/marketing/v20"
	"time"

	"go.mongodb.org/mongo-driver/bson/primitive"
)

func MapperAdsetFBUpdate(adset *v20.Adset, userId primitive.ObjectID, adsetEntity *entity.AdsetEntity) {

	adsetEntity.Name = adset.Name
	adsetEntity.Status = adset.Status
	adsetEntity.AccountID = adset.AccountID
	adsetEntity.AttributionSpec = adset.AttributionSpec
	adsetEntity.BidAmount = adset.BidAmount

	adsetEntity.BidStrategy = adset.BidStrategy
	adsetEntity.BillingEvent = adset.BillingEvent
	adsetEntity.BudgetRemaining = adset.BudgetRemaining
	// adsetEntity.Campaign = adset.Campaign
	adsetEntity.CampaignID = adset.CampaignID

	adsetEntity.ConfiguredStatus = adset.ConfiguredStatus
	adsetEntity.CreatedTime = time.Time(adset.CreatedTime)
	adsetEntity.DailyBudget = adset.DailyBudget
	adsetEntity.DailyMinSpendTarget = adset.DailyMinSpendTarget
	adsetEntity.DailySpendCap = adset.DailySpendCap

	adsetEntity.DestinationType = adset.DestinationType
	adsetEntity.DeliveryEstimate = adset.DeliveryEstimate
	adsetEntity.EffectiveStatus = adset.EffectiveStatus

	adsetEntity.FrequencyControlSpecs = adset.FrequencyControlSpecs

	adsetEntity.LifetimeBudget = adset.LifetimeBudget
	adsetEntity.LifetimeMinSpendTarget = adset.LifetimeMinSpendTarget
	adsetEntity.LifeTimeSpendCap = adset.LifeTimeSpendCap
	adsetEntity.LifetimeImps = adset.LifetimeImps
	adsetEntity.OptimizationGoal = adset.OptimizationGoal

	adsetEntity.PacingType = adset.PacingType
	adsetEntity.PromotedObject = adset.PromotedObject
	adsetEntity.RecurringBudgetSemantics = adset.RecurringBudgetSemantics
	adsetEntity.StartTime = time.Time(*adset.StartTime)
	adsetEntity.Targeting = adset.Targeting

	adsetEntity.UpdatedTime = time.Time(adset.UpdatedTime)
	adsetEntity.TargetingOptimizationTypes = adset.TargetingOptimizationTypes
	adsetEntity.DSABeneficiary = adset.DSABeneficiary
	adsetEntity.DSAPayor = adset.DSAPayor
	adsetEntity.IsDynamicCreative = adset.IsDynamicCreative

	adsetEntity.UpdatedBy = userId
	adsetEntity.UpdatedAt = time.Now()

	adsetEntity.IsBudgetScheduleEnabled = adset.IsBudgetScheduleEnabled

	fmt.Printf("\n value of end Time  ------> %v\n", adset.EndTime)

	// adsetEntity.EndTime  =adset.EndTime
	if adset.EndTime == nil {
		adsetEntity.EndTime = nil
	} else {
		t := time.Time(*adset.EndTime)
		adsetEntity.EndTime = &t
	}

}
