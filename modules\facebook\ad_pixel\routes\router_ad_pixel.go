package routes

import (
	"github.com/dev-networldasia/dspgos/sctx"

	"github.com/gofiber/fiber/v2"
)

func SetupRoutesAdPixel(app *fiber.App, serviceCtx sctx.ServiceContext, midds ...fiber.Handler) {
	group := app.Group("dsp/facebook/ad-pixel")
	{
		if len(midds) > 0 {
			for _, midd := range midds {
				group.Use(midd)
			}
		}

		composerAdPixelSVC := ComposerAdPixelService(serviceCtx)
		group.Get("/list", composerAdPixelSVC.ListAdPixelHandler()).Name("fb.ad_pixel.list")
		group.Get("/add", composerAdPixelSVC.ListAdPixelHandler()).Name("fb.ad_pixel.add")

	}
	apiGroup := app.Group("dsp/facebook/api/ad-pixel")
	{
		if len(midds) > 0 {
			for _, midd := range midds {
				apiGroup.Use(midd)
			}
		}
		compAdPixelApiSVC := ComposerApiAdPixelService(serviceCtx)
		apiGroup.Patch("/reload", compAdPixelApiSVC.ReloadAdPixelApi()).Name("fb.api.ad_pixel.reload")
		apiGroup.Post("/list-datatable", compAdPixelApiSVC.ListDatatableAdPixelApi()).Name("fb.api.ad_pixel.list_datatable")
		apiGroup.Post("/list", compAdPixelApiSVC.ListAdPixelApi()).Name("fb.api.ad_pixel.list")
	}
}
