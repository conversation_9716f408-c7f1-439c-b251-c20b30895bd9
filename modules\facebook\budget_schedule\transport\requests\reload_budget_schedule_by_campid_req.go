package requests

import (
	"godsp/modules/facebook/budget_schedule/common/errs"
	"strings"

	"github.com/go-playground/validator/v10"
)

type ReloadBudgetScheduleByCampaignAdsetIdReq struct {
	CampaignId string `json:"campaign_id,omitempty" validate:"omitempty,numeric,gt=0"`
	AdsetId    string `json:"adset_id,omitempty" validate:"omitempty,numeric,gt=0"`
}

func (req *ReloadBudgetScheduleByCampaignAdsetIdReq) Validate() error {
	validate := validator.New()

	req.CampaignId = strings.TrimSpace(req.CampaignId)

	err := validate.Struct(req)

	if req.CampaignId == "" && req.AdsetId == "" {
		return errs.ErrCampaignIdAdsetBudgetSchedule
	}

	if err != nil {
		for _, err := range err.(validator.ValidationErrors) {
			switch err.Field() {
			case "CampaignId":
				return errs.ErrCampaignIdBudgetSchedule
			}
		}
	}

	return nil
}
