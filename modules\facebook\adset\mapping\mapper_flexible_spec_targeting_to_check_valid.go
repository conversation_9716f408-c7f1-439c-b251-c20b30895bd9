package mapping

import (
	"fmt"
	"godsp/modules/facebook/adset/common/enums"
	v20 "godsp/pkg/fb-marketing/marketing/v20"
)

/**
 * Mappper update adset req to adset
 */

func MappingItemIdContainer(interest v20.IDContainer, typeName string) v20.InterestCheck {
	fmt.Printf("\n -----------  InterestCheck ----------- %+v \n", v20.InterestCheck{
		ID:   interest.ID,
		Type: typeName,
	})
	return v20.InterestCheck{
		ID:   interest.ID,
		Type: typeName,
	}
}

func MappingItemIds(id int, typeName string) v20.InterestCheck {
	return v20.InterestCheck{
		ID:   fmt.Sprint(id),
		Type: typeName,
	}
}

func appendMapping[T any](spec []v20.InterestCheck, items []T, typeName string, mappingFunc func(T, string) v20.InterestCheck) []v20.InterestCheck {
	for _, item := range items {
		spec = append(spec, mappingFunc(item, typeName))
	}
	return spec
}

func RemoveDuplicateFlexibleSpecViews(list *[]v20.InterestCheck) {
	seen := make(map[string]bool)
	result := []v20.InterestCheck{}

	for _, item := range *list {
		if !seen[item.ID] {
			seen[item.ID] = true
			result = append(result, item)
		}
	}

	*list = result
}

func MapperFlexibaleSpecTargetingToCheckValid(flexibleSpecFB []v20.FlexibleSpec) ([]v20.InterestCheck, [][]v20.InterestCheck) {

	flexibleSpecRoot := [][]v20.InterestCheck{}
	flexibleSpec := []v20.InterestCheck{}

	for _, flexibleSpecItem := range flexibleSpecFB {
		mappingList := []struct {
			items       interface{}
			typeName    string
			mappingFunc interface{}
		}{
			{flexibleSpecItem.Interests, enums.INTERESTS, MappingItemIdContainer},
			{flexibleSpecItem.Behaviors, enums.BEHAVIORS, MappingItemIdContainer},
			{flexibleSpecItem.LifeEvents, enums.LIFE_EVENTS, MappingItemIdContainer},
			{flexibleSpecItem.EducationMajors, enums.EDUCATION_MAJORS, MappingItemIdContainer},
			{flexibleSpecItem.WorkEmployers, enums.WORK_EMPLOYERS, MappingItemIdContainer},
			{flexibleSpecItem.WorkPositions, enums.WORK_POSITIONS, MappingItemIdContainer},
			{flexibleSpecItem.FamilyStatuses, enums.FAMILY_STATUSES, MappingItemIdContainer},
			{flexibleSpecItem.CustomAudiences, enums.CUSTOM_AUDIENCES, MappingItemIdContainer},
			{flexibleSpecItem.EducationSchools, enums.EDUCATION_SCHOOLS, MappingItemIdContainer},
			{flexibleSpecItem.Income, enums.INCOME, MappingItemIdContainer},
			{flexibleSpecItem.Industries, enums.INDUSTRIES, MappingItemIdContainer},
			{flexibleSpecItem.UserAdclusters, enums.USER_ADCLUSTERS, MappingItemIdContainer},

			{flexibleSpecItem.EducationStatuses, enums.EDUCATION_STATUSES, MappingItemIds},
			{flexibleSpecItem.RelationshipStatuses, enums.RELATIONSHIP_STATUSES, MappingItemIds},
			{flexibleSpecItem.CollegeYears, enums.COLLEGE_YEARS, MappingItemIds},

			{flexibleSpecItem.Politics, enums.POLITICS, MappingItemIdContainer},
		}
		var interestsCheck []v20.InterestCheck
		for _, entry := range mappingList {
			switch items := entry.items.(type) {
			case []v20.IDContainer:
				interestsCheck = appendMapping(interestsCheck, items, entry.typeName, entry.mappingFunc.(func(v20.IDContainer, string) v20.InterestCheck))
				flexibleSpec = appendMapping(flexibleSpec, items, entry.typeName, entry.mappingFunc.(func(v20.IDContainer, string) v20.InterestCheck))
			case []int:
				interestsCheck = appendMapping(interestsCheck, items, entry.typeName, entry.mappingFunc.(func(int, string) v20.InterestCheck))
				flexibleSpec = appendMapping(flexibleSpec, items, entry.typeName, entry.mappingFunc.(func(int, string) v20.InterestCheck))
			}
		}
		flexibleSpecRoot = append(flexibleSpecRoot, interestsCheck)
	}
	fmt.Printf("\n -----------  flexibleSpecRoot - Mapping ----------- %+v \n", flexibleSpecRoot)

	RemoveDuplicateFlexibleSpecViews(&flexibleSpec)
	return flexibleSpec, flexibleSpecRoot
}

func MapperFlexibaleSpecTargetingToView(listFlexibleSpec []*v20.InterestResultCheckValidate, listFlexibleSpecRoot [][]v20.InterestCheck) *[][]v20.InterestResultCheckValidate {

	flexibleSpecViews := [][]v20.InterestResultCheckValidate{}

	for _, flexibleSpecItems := range listFlexibleSpecRoot {
		flexibleSpecViewItem := []v20.InterestResultCheckValidate{}
		for _, flexibleSpecRootItem := range flexibleSpecItems {
			for _, item := range listFlexibleSpec {
				if item.ID == flexibleSpecRootItem.ID {
					flexibleSpecViewItem = append(flexibleSpecViewItem, *item)
					continue
				}
			}
		}
		flexibleSpecViews = append(flexibleSpecViews, flexibleSpecViewItem)

	}
	fmt.Printf("\n -----------  fflexibleSpecViews ----------- %+v \n", flexibleSpecViews)

	return &flexibleSpecViews
}
