package requests

import (
	"fmt"
	"godsp/conf"
	"godsp/modules/admin/common/admerrs"
	"godsp/modules/admin/common/admrules"
	"godsp/modules/admin/user/common/errs"
	"godsp/modules/admin/user/transport/rules"
	"godsp/modules/facebook/common/fbrules"
	"godsp/pkg/gos/utils"
	"mime/multipart"
	"strings"

	"github.com/go-playground/validator/v10"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

type PayloadUserCreation struct {
	FirstName    string `form:"first_name" validate:"required"`
	LastName     string `form:"last_name" validate:"required"`
	RoleIDString string `form:"role_id" validate:"required"`
	AdAccountID  string `form:"ad_account_id"`
	Email        string `form:"email" validate:"required"`
	Username     string `form:"username"`
	Status       string `json:"status" validate:"required,ruleStatusUser"`
	Gender       int    `form:"gender" validate:"required"`
	Password     string `form:"password" validate:"required,rulePasswordReq"`

	ListPageIds  []string              `form:"list_page_ids,omitempty"`
	ListPixelIds []string              `form:"list_pixel_ids,omitempty"`
	FileImg      *multipart.FileHeader `form:"file_img" json:"-"`
	ClientIDStr  string                `form:"client_id,omitempty" validate:"omitempty,ruleObjectid"`
	ClientID     *primitive.ObjectID   `form:"-"`

	UserID primitive.ObjectID `form:"-" json:"-"`
	Image  string             `form:"-" json:"-"`
	RoleID primitive.ObjectID `form:"-" json:"-"`
}

func (req *PayloadUserCreation) Validate() []*string {
	validate := validator.New()
	var validationErrors []*string
	validate.RegisterValidation("ruleStatusUser", admrules.RuleStatusCreate)
	validate.RegisterValidation("rulePasswordReq", rules.RulePasswordReq)
	validate.RegisterValidation("ruleObjectid", RuleObjectID)

	err := validate.Struct(req)
	if err != nil {
		for _, err := range err.(validator.ValidationErrors) {
			switch err.Field() {
			case "LastName":
				errStatus := errs.ErrLastNameValidate.Error()
				validationErrors = append(validationErrors, &errStatus)
			case "FirstName":
				errName := errs.ErrFirstNameValidate.Error()
				validationErrors = append(validationErrors, &errName)
			case "Email":
				errName := errs.ErrEmailValidate.Error()
				validationErrors = append(validationErrors, &errName)
			case "Password":
				err := errs.ErrPasswordValidate.Error()
				validationErrors = append(validationErrors, &err)
			case "Status":
				errStatus := admerrs.ErrStatusNotFound.Error()
				validationErrors = append(validationErrors, &errStatus)
			case "Gender":
				errGender := errs.ErrGenderValidate.Error()
				validationErrors = append(validationErrors, &errGender)
			case "RoleID":
				errRoleID := errs.ErrRoleIDExistsValidate.Error()
				validationErrors = append(validationErrors, &errRoleID)
			}
		}
	}
	if req.ClientIDStr != "" {
		oid, err := primitive.ObjectIDFromHex(req.ClientIDStr)
		if err != nil {
			errClientID := errs.ErrIDUserValidate.Error()
			validationErrors = append(validationErrors, &errClientID)
		} else {
			req.ClientID = &oid // bạn cần thêm 1 trường ClientIDPtr *primitive.ObjectID để sử dụng sau
		}
	}

	if !rules.RuleNameUser(req.FirstName) || !rules.RuleNameUser(req.LastName) {
		errName := errs.ErrNameUserValidate.Error()
		validationErrors = append(validationErrors, &errName)
	}

	username := strings.TrimSpace(req.Username)
	if username != "" {
		if !rules.RuleUsernameUser(username) {
			errUsername := errs.ErrUsernameValidate.Error()
			validationErrors = append(validationErrors, &errUsername)
		}
	}

	roleID, err := primitive.ObjectIDFromHex(req.RoleIDString)
	if err != nil {
		errMsg := errs.ErrRoleIDFormat.Error()
		validationErrors = append(validationErrors, &errMsg)
	}
	req.RoleID = roleID

	if len(req.ListPageIds) > 0 {
		if !fbrules.RuleIdsNumber(req.ListPageIds) {
			errIdsValid := errs.ErrListPageIdsValidate.Error()
			validationErrors = append(validationErrors, &errIdsValid)
		}
	}
	if len(req.ListPixelIds) > 0 {
		if !fbrules.RuleIdsNumber(req.ListPixelIds) {
			errIdsValid := errs.ErrListPixelIdsValidate.Error()
			validationErrors = append(validationErrors, &errIdsValid)
		}
	}

	if validationErrors == nil {
		req.FirstName = strings.TrimSpace(req.FirstName)
		req.Email = strings.TrimSpace(req.Email)
		req.LastName = strings.TrimSpace(req.LastName)
		req.Username = strings.TrimSpace(req.Username)
		req.Status = strings.TrimSpace(req.Status)

		if req.AdAccountID != "" {
			req.AdAccountID = strings.TrimSpace(req.AdAccountID)
		}
	}

	// set hinh
	if req.FileImg != nil {
		fullName := fmt.Sprintf("%s %s", req.FirstName, req.LastName)
		if pathName, err := utils.UploadFile(req.FileImg, conf.UploadPathPublic, conf.PathImgUser, &fullName); err != nil {
			errStr := err.Error()
			validationErrors = append(validationErrors, &errStr)
		} else {
			req.Image = *pathName
		}
	} else {
		req.Image = conf.PathImgUserDefault
	}

	return validationErrors
}

func RuleObjectID(fl validator.FieldLevel) bool {
	id := fl.Field().String()
	_, err := primitive.ObjectIDFromHex(id)
	return err == nil
}
