package api

import (
	"context"
	"fmt"
	"godsp/conf"
	"godsp/modules/facebook/ad_pixel/transport/requests"
	"godsp/modules/facebook/ad_pixel/transport/response"
	"godsp/pkg/gos/utils"
	"godsp/pkg/sctx/core"
	"net/http"
	"time"

	"github.com/gofiber/fiber/v2"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

type AdPixelApiUsc interface {
	ReloadAdPixelUsc(ctx context.Context, accountId string, userId primitive.ObjectID) []string
	ListDatatableAdPixelUsc(ctx context.Context) (*response.AdPixelListResponse, error)
	// ListAdPixelUsc(ctx context.Context, payload *requests.ListAdPixelReq) ([]*entity.AdPixelEntity, error)
	ListAdPixelUsc(ctx context.Context) (*[]response.AdPixelView, error)
}

type adPixelApi struct {
	usc AdPixelApiUsc
}

func NewAdPixelApi(usc AdPixelApiUsc) *adPixelApi {
	return &adPixelApi{
		usc: usc,
	}
}

/**
 * Api reload ad Pixel
 */
func (a *adPixelApi) ReloadAdPixelApi() fiber.Handler {
	return func(c *fiber.Ctx) error {
		var payload requests.ReloadAdPixelReq

		if c.Body() != nil && len(c.Body()) > 0 {
			if err := c.BodyParser(&payload); err != nil {
				return core.ReturnErrsForApi(c, err)
			}
		}

		//validate
		validationErrors := payload.Validate()
		if len(validationErrors) > 0 {
			return core.ReturnErrsForApi(c, validationErrors)
		}

		var ctx, cancel = context.WithTimeout(context.Background(), 200*time.Second)
		defer cancel()
		fmt.Println(payload.AccountID)

		userId, err := utils.GetUserId(c.Context())
		if err != nil {
			return core.ReturnErrsForApi(c, err)
		}

		errs := a.usc.ReloadAdPixelUsc(ctx, payload.AccountID, userId)
		if errs != nil {
			return core.ReturnErrsForApi(c, errs)
		}

		return c.Status(http.StatusOK).JSON(core.ResponseData(map[string]interface{}{
			"msg": "Reload data successfully",
		}))
	}
}

/***
 *	Api list data table ad Pixel
 *
 */
func (a *adPixelApi) ListDatatableAdPixelApi() fiber.Handler {
	return func(c *fiber.Ctx) error {
		datas, err := a.usc.ListDatatableAdPixelUsc(c.Context())
		if err != nil {
			return c.Status(http.StatusOK).JSON(fiber.Map{
				"data":            "",
				"draw":            1,
				"recordsTotal":    0,
				"recordsFiltered": 0,
				"err":             err.Error(),
			})
		}

		return c.Status(http.StatusOK).JSON(fiber.Map{
			"data":            datas.AdPixelDataTables,
			"draw":            2,
			"recordsTotal":    datas.Count,
			"recordsFiltered": datas.CountFilter,
		})
	}
}

/**
 * List ad pixel api
 */
func (a *adPixelApi) ListAdPixelApi() fiber.Handler {
	return func(c *fiber.Ctx) error {

		var payload requests.GetListPixelReq
		if err := c.BodyParser(&payload); err != nil {
			return core.ReturnErrsForApi(c, err)
		}

		validationErrors := payload.Validate()
		if len(validationErrors) > 0 {
			return core.ReturnErrsForApi(c, validationErrors)
		}

		user, err := utils.GetInfoUserBasic(c.Context())
		if err != nil && user.RoleName != conf.SysConf.RoleAdmin {
			return core.ReturnErrForPermissionDenied(c)
		}

		adPixels, err := a.usc.ListAdPixelUsc(c.Context())
		if err != nil {
			return core.ReturnErrForApi(c, err.Error())
		}
		return c.Status(http.StatusOK).JSON(core.ResponseData(map[string]interface{}{
			"msg":  "Get pixels successfully",
			"data": adPixels,
		}))
		// Respond with the list of pages in JSON format
		// return c.Status(http.StatusOK).JSON(core.ResponseData(adPixels))
	}
}
