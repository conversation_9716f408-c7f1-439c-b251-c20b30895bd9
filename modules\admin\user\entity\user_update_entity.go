package entity

import (
	"godsp/pkg/gos/utils"
	"time"

	"go.mongodb.org/mongo-driver/bson/primitive"
)

type UserUpdate struct {
	ID          primitive.ObjectID `json:"_id,omitempty" bson:"_id,omitempty"`
	FirstName   string             `json:"first_name" bson:"first_name"`
	LastName    string             `json:"last_name" bson:"last_name"`
	FullName    string             `json:"full_name" bson:"full_name"`
	Username    *string            `json:"username,omitempty" bson:"username,omitempty"`
	Image       *string            `json:"image" bson:"image,omitempty"`
	Status      int                `json:"status" bson:"status"`
	RoleID      primitive.ObjectID `json:"role_id" bson:"role_id"`
	AdAccountID string             `json:"ad_account_id" bson:"ad_account_id"`
	Email       string             `json:"email" bson:"email"`
	Gender      int                `json:"gender" bson:"gender"`
	Phone       *string            `json:"phone,omitempty" bson:"phone,omitempty"`
	Birthday    *time.Time         `json:"birthday,omitempty" bson:"birthday,omitempty"`
	UpdatedBy   primitive.ObjectID `json:"updated_by" bson:"updated_by"`
	UpdatedAt   time.Time          `json:"updated_at" bson:"updated_at"`

	ListPageIds  []string            `json:"list_page_ids,omitempty" bson:"list_page_ids,omitempty"`
	ListPixelIds []string            `json:"list_pixel_ids,omitempty" bson:"list_pixel_ids,omitempty"`
	ClientID     *primitive.ObjectID `json:"client_id,omitempty" bson:"client_id"`
}

func (UserUpdate) CollectionName() string {
	return UserEntity{}.CollectionName()
}

func (u *UserUpdate) BeforeUpdate() {
	now := utils.TimeNowLocationHCM()
	if u.UpdatedAt.IsZero() {
		u.UpdatedAt = now
	}
	u.UpdatedAt = now
}
