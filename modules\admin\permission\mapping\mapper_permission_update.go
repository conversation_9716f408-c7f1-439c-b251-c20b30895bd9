package mapping

import (
	"godsp/modules/admin/permission/entity"
	"godsp/modules/admin/permission/transport/requests"
)

func MapperUpdatePermission(req *requests.PayloadPermissionUpdate) *entity.PermissionUpdate {
	return &entity.PermissionUpdate{
		ID:          req.ID,
		Description: req.Description,
		Ordering:    req.Ordering,
		ParentID:    req.ParentID,
		IsBlock:     req.Is<PERSON>,
		IsAcp:       req.IsAcp,
		UpdatedBy:   req.UserID,
	}
}
