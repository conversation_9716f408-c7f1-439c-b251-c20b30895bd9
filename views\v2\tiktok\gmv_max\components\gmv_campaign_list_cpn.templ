package components

templ GmvCampaignListCpn() {
    <div class="row mt-4 fade-in">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <div class="d-flex justify-content-between align-items-center">
                        <h5 class="card-title mb-0">Campaign list</h5>
                    </div>
                    <p class="text-muted mb-0 mt-2">Reporting includes GMV Max ads created on TikTok Ads Manager and TikTok Seller Center.</p>
                </div>
                <div class="card-body">
                    <!-- Tab Navigation -->
                    <ul class="nav nav-tabs nav-tabs-custom mb-3" id="campaignTabs" role="tablist" style="width: max-content;">
                        <li class="nav-item" role="presentation">
                            <button class="nav-link active" id="product-gmv-tab" data-bs-toggle="tab" data-bs-target="#product-gmv" type="button" role="tab" aria-controls="product-gmv" aria-selected="true">
                                Product GMV Max
                            </button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="live-gmv-tab" data-bs-toggle="tab" data-bs-target="#live-gmv" type="button" role="tab" aria-controls="live-gmv" aria-selected="false">
                                LIVE GMV Max
                            </button>
                        </li>
                    </ul>
                    <!-- Tab Content -->
                    <div class="tab-content" id="campaignTabsContent">
                        <!-- Product GMV Max Tab -->
                        <div class="tab-pane fade show active" id="product-gmv" role="tabpanel" aria-labelledby="product-gmv-tab">
                            <div class="d-flex justify-content-between align-items-center mb-3">
                                <div class="d-flex align-items-center gap-3">
                                    <div class="input-group" style="width: 300px;">
                                        <input type="text" class="form-control" placeholder="Search campaign or TikTok account ID" id="productSearchInput"/>
                                        <button class="btn btn-outline-secondary" type="button">
                                            <i class="ri-search-line"></i>
                                        </button>
                                    </div>
                                    <select class="form-select" style="width: 150px;" id="productStatusFilter">
                                        <option value="">Status</option>
                                        <option value="active">Active</option>
                                        <option value="inactive">Inactive</option>
                                        <option value="not_delivering">Not delivering</option>
                                    </select>
                                </div>
                                <div class="d-flex gap-2">
                                    <button class="btn btn-outline-secondary">
                                        <i class="ri-grid-line"></i>
                                    </button>
                                    <button class="btn btn-outline-secondary">
                                        <i class="ri-external-link-line"></i>
                                    </button>
                                    <button class="btn btn-outline-secondary">
                                        <i class="ri-file-copy-line"></i>
                                    </button>
                                </div>
                            </div>
                            <!-- Product GMV Max Table Container -->
                            <div id="product-gmv-table-container">
                                <!-- Table will be loaded here -->
                                <div class="text-center py-4">
                                    <div class="spinner-border text-primary" role="status">
                                        <span class="visually-hidden">Loading...</span>
                                    </div>
                                    <p class="mt-2 text-muted">Loading Product GMV Max campaigns...</p>
                                </div>
                            </div>
                        </div>
                        <!-- LIVE GMV Max Tab -->
                        <div class="tab-pane fade" id="live-gmv" role="tabpanel" aria-labelledby="live-gmv-tab">
                            <div class="d-flex justify-content-between align-items-center mb-3">
                                <div class="d-flex align-items-center gap-3">
                                    <div class="input-group" style="width: 300px;">
                                        <input type="text" class="form-control" placeholder="Search campaign or TikTok account ID" id="liveSearchInput"/>
                                        <button class="btn btn-outline-secondary" type="button">
                                            <i class="ri-search-line"></i>
                                        </button>
                                    </div>
                                    <select class="form-select" style="width: 150px;" id="liveStatusFilter">
                                        <option value="">Status</option>
                                        <option value="active">Active</option>
                                        <option value="inactive">Inactive</option>
                                        <option value="not_delivering">Not delivering</option>
                                    </select>
                                </div>
                                <div class="d-flex gap-2">
                                    <button class="btn btn-outline-secondary">
                                        <i class="ri-grid-line"></i>
                                    </button>
                                    <button class="btn btn-outline-secondary">
                                        <i class="ri-external-link-line"></i>
                                    </button>
                                    <button class="btn btn-outline-secondary">
                                        <i class="ri-file-copy-line"></i>
                                    </button>
                                </div>
                            </div>
                            <!-- LIVE GMV Max Table Container -->
                            <div id="live-gmv-table-container">
                                <!-- Table will be loaded here -->
                                <div class="text-center py-4">
                                    <div class="spinner-border text-primary" role="status">
                                        <span class="visually-hidden">Loading...</span>
                                    </div>
                                    <p class="mt-2 text-muted">Loading LIVE GMV Max campaigns...</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
}