package api

import (
	"godsp/conf"
	"godsp/modules/facebook/campaign/transport/requests"
	"godsp/pkg/gos/utils"
	"godsp/pkg/sctx/core"
	"net/http"

	"github.com/gofiber/fiber/v2"
)

/***
 * Approve camp api
 */

func (a *campaignApi) ApproveCampaignApi() fiber.Handler {
	return func(c *fiber.Ctx) error {

		userId, _ := utils.GetInfoUserAuth(c.Context())
		if userId.RoleName != conf.SysConf.RoleAdmin {
			return c.Status(http.StatusForbidden).JSON(core.ResponseData(map[string]interface{}{
				"msg": "Permission denied",
			}))
		}

		var payload requests.ApproveManyCampaignReq

		// Parse JSON vào struct (không chứa account_id)
		if err := c.BodyParser(&payload); err != nil {
			return core.ReturnErrsForApi(c, err)
		}

		if validationErrors := payload.Validate(); len(validationErrors) > 0 {
			return core.ReturnErrsForApi(c, validationErrors)
		}

		for _, campaign := range payload.Campaigns {
			err := a.usc.ApproveCampaignUsc(c.Context(), &campaign, *userId.UserId)
			if err != nil {
				return core.ReturnErrsForApi(c, []string{
					err.Error(),
					campaign.CampaignID,
					campaign.Name,
				})
			}
		}

		return c.Status(http.StatusOK).JSON(core.ResponseData(map[string]interface{}{
			"msg": "Campaigns approved successfully",
		}))

	}
}
