package errs

import "errors"

var (
	ErrValidateRoleID        = errors.New("id is not in correct format")
	ErrNameRoleValidate      = errors.New("name of role is not empty")
	ErrRoleNameRoleValidate  = errors.New("role name of role is not empty")
	ErrOrderingRoleValidate  = errors.New("ordering of role must be greater than 0")
	ErrData                  = errors.New("invalid form format")
	ErrIDNotFound            = errors.New("id role not found")
	ErrPermissions           = errors.New("invalid permission")
	ErrUpdateStatusRoleLogin = errors.New("you cannot update the status of this role")

	ErrRoleDelete = errors.New("no roles found for deletion")
)
