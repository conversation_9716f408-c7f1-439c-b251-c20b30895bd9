package subredis

import (
	"context"
	"encoding/json"
	"fmt"
	payloadtopic "godsp/modules/admin/common/payload_topic"

	"github.com/dev-networldasia/dspgos/sctx/component/watermillapp/redisstream"

	"github.com/dev-networldasia/dspgos/sctx"

	"github.com/ThreeDotsLabs/watermill/message"
)

type EmailSubUsc interface {
	SendEmailResetPasswordSubUsc(ctx context.Context, payload *payloadtopic.PayloadResetPassword) error
	SendEmailNotifyApprovalTopupSubUsc(ctx context.Context, payload *payloadtopic.PayloadSentEmailApprovalTopup) error
	SendEmailNotifyFullBalanceAdAccountSubUsc(ctx context.Context, payload *payloadtopic.PayloadSendEmailAdAccountFullBalance) error
	SendEmailBillingDetailSubUsc(ctx context.Context, payload *payloadtopic.PayloadUpdateBillingDetail) error
	SendEmailBillingSubUsc(ctx context.Context, payload *payloadtopic.PayloadUpdateBilling) error
}

type emailSub struct {
	usc    EmailSubUsc
	logger sctx.Logger
}

func NewEmailSub(usc EmailSubUsc, logger sctx.Logger) *emailSub {
	return &emailSub{
		usc:    usc,
		logger: logger,
	}
}

/**
 * Reset password
 */
func (e *emailSub) SendEmailResetPasswordSub() redisstream.NoPublishHandlerFunc {
	return func(msg *message.Message) error {
		var payload payloadtopic.PayloadResetPassword

		if err := json.Unmarshal(msg.Payload, &payload); err != nil {
			fmt.Println("Error Unmarshal payload: ", err.Error())
			msgErr := fmt.Sprintf(
				"TRANSPORT Error SendEmailResetPasswordSub Unmarshal: %s payload %s",
				err.Error(),
				string(msg.Payload),
			)
			e.logger.Error(msgErr)
			msg.Ack()
			return err
		}

		// jsonData, _ := json.MarshalIndent(payload, "", "  ")
		// fmt.Println("-----------> SendEmailResetPasswordSub: ", string(jsonData))

		if err := e.usc.SendEmailResetPasswordSubUsc(context.Background(), &payload); err != nil {
			msgErr := fmt.Sprintf(
				"Error SendEmailResetPasswordSubUsc: %s payload %s",
				err.Error(),
				string(msg.Payload),
			)
			e.logger.Error(msgErr)
			msg.Ack()
			return err
		}

		msg.Ack()
		return nil
	}
}

/**
 * Send email nofity topup success
 */
func (e *emailSub) SendEmailNotifyApprovalTopupSub() redisstream.NoPublishHandlerFunc {
	return func(msg *message.Message) error {
		var payload payloadtopic.PayloadSentEmailApprovalTopup

		if err := json.Unmarshal(msg.Payload, &payload); err != nil {
			fmt.Println("Error Unmarshal payload: ", err.Error())
			msgErr := fmt.Sprintf(
				"TRANSPORT Error SendEmailNotifyApprovalTopupSub Unmarshal: %s payload %s",
				err.Error(),
				string(msg.Payload),
			)
			e.logger.Error(msgErr)
			msg.Ack()
			return err
		}

		// jsonData, _ := json.MarshalIndent(payload, "", "  ")
		// fmt.Println("-----------> SendEmailResetPasswordSub: ", string(jsonData))

		if err := e.usc.SendEmailNotifyApprovalTopupSubUsc(context.Background(), &payload); err != nil {
			msgErr := fmt.Sprintf(
				"Error SendEmailNotifyApprovalTopupSubUsc: %s payload %s",
				err.Error(),
				string(msg.Payload),
			)
			e.logger.Error(msgErr)
			msg.Ack()
			return err
		}

		msg.Ack()
		return nil
	}

}

/**
 * Send email nofity topup success
 */
func (e *emailSub) SendEmailNotifyFullBalanceAdAccountSub() redisstream.NoPublishHandlerFunc {
	return func(msg *message.Message) error {
		var payload payloadtopic.PayloadSendEmailAdAccountFullBalance

		if err := json.Unmarshal(msg.Payload, &payload); err != nil {
			fmt.Println("Error Unmarshal payload: ", err.Error())
			msgErr := fmt.Sprintf(
				"TRANSPORT Error SendEmailNotifyFullBalanceAdAccountSub Unmarshal: %s payload %s",
				err.Error(),
				string(msg.Payload),
			)
			e.logger.Error(msgErr)
			msg.Ack()
			return err
		}

		jsonData, _ := json.MarshalIndent(payload, "", "  ")
		fmt.Println("-----------> SendEmailNotifyFullBalanceAdAccountSub: ", string(jsonData))

		if err := e.usc.SendEmailNotifyFullBalanceAdAccountSubUsc(context.Background(), &payload); err != nil {
			msgErr := fmt.Sprintf(
				"Error SendEmailNotifyFullBalanceAdAccountSub: %s payload %s",
				err.Error(),
				string(msg.Payload),
			)
			e.logger.Error(msgErr)
			msg.Ack()
			return err
		}

		msg.Ack()
		return nil
	}

}

func (e *emailSub) SendEmailBillingDetailSub() redisstream.NoPublishHandlerFunc {
	return func(msg *message.Message) error {
		var payload payloadtopic.PayloadUpdateBillingDetail
		if err := json.Unmarshal(msg.Payload, &payload); err != nil {
			msgErr := fmt.Sprintf(
				"SendEmailBillingDetailSub error: %s \n payload: \n %s",
				err.Error(),
				string(msg.Payload),
			)

			e.logger.Error(msgErr)
			msg.Ack()

			return err
		}

		if err := e.usc.SendEmailBillingDetailSubUsc(context.Background(), &payload); err != nil {
			msgErr := fmt.Sprintf(
				"SendEmailBillingDetailSubUsc error: %s payload %s",
				err.Error(),
				string(msg.Payload),
			)

			e.logger.Error(msgErr)
			msg.Ack()

			return err
		}

		msg.Ack()

		return nil
	}
}

func (e *emailSub) SendEmailBillingSub() redisstream.NoPublishHandlerFunc {
	return func(msg *message.Message) error {
		var payload payloadtopic.PayloadUpdateBilling
		if err := json.Unmarshal(msg.Payload, &payload); err != nil {
			msgErr := fmt.Sprintf(
				"SendEmailBillingSub error: %s \n payload: \n %s",
				err.Error(),
				string(msg.Payload),
			)

			e.logger.Error(msgErr)
			msg.Ack()

			return err
		}

		if err := e.usc.SendEmailBillingSubUsc(context.Background(), &payload); err != nil {
			msgErr := fmt.Sprintf(
				"SendEmailBillingSubUsc error: %s payload %s",
				err.Error(),
				string(msg.Payload),
			)

			e.logger.Error(msgErr)
			msg.Ack()

			return err
		}

		msg.Ack()

		return nil
	}
}
