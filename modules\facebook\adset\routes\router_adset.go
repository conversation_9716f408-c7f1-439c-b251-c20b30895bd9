package routes

import (
	"github.com/dev-networldasia/dspgos/sctx"

	"github.com/gofiber/fiber/v2"
)

func SetupRoutesAdset(app *fiber.App, serviceCtx sctx.ServiceContext, midds ...fiber.Handler) {

	group := app.Group("dsp/facebook/adsets")
	{
		if len(midds) > 0 {
			for _, midd := range midds {
				group.Use(midd)
			}
		}

		comPage := ComposerAdsetService(serviceCtx)
		group.Get("/list", comPage.ListAdsetHdl()).Name("fb.adset.list")
		group.Get("/edit", comPage.ListAdsetHdl()).Name("fb.adset.edit")
	}

	apiGroup := app.Group("dsp/facebook/api/adsets")
	{
		if len(midds) > 0 {
			for _, midd := range midds {
				apiGroup.Use(midd)
			}
		}

		compApi := NewComposerAdsetApi(serviceCtx)
		apiGroup.Post("/list-datatable", compApi.ListDatatableAdsetApi()).Name("fb.adset.api.list-datatable")
		apiGroup.Patch("/reload", compApi.ReloadAdsetApi()).Name("fb.adset.api.reload")
		apiGroup.Post("/create", compApi.CreateAdsetApi()).Name("fb.adset.api.create")
		apiGroup.Post("/update", compApi.UpdateAdsetApi()).Name("fb.adset.api.update")
		apiGroup.Put("/update", compApi.UpdateNameStatusAdsetApi()).Name("fb.adset.api.updateNameStatus")
		apiGroup.Get("show/:adsetId", compApi.ShowAdsetApi()).Name("fb.adset.api.show")
		apiGroup.Delete("/delete", compApi.DeleteAdsetApi()).Name("fb.adset.api.delete")
		//list for select
		apiGroup.Post("/list-select", compApi.ListSelectAdsetApi()).Name("fb.api.adset.list_select")
	}
}
