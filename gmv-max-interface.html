<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>GMV Max - TikTok Style Interface</title>

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.2.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Bootstrap Icons -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">

    <style>
        :root {
            /* Velzon Custom Variables */
            --vz-border-radius-sm: 0.6rem;
            --vz-border-radius-lg: 0.8rem;
            --neutral-100: #000000;
            --neutral-200: #222222;
            --neutral-300: #464749;
            --neutral-400: #666666;
            --neutral-500: #adadad;
            --neutral-600: #d6d6d6;
            --neutral-700: #ebebeb;
            --neutral-800: #f5f5f5;
            --neutral-900: #ffffff;
            --primary-50: #e8ebfa;
            --primary-100: #c4ccf2;
            --primary-200: #9bacea;
            --primary-300: #708be1;
            --primary-400: #4c70db;
            --primary-500: #1858d4;
            --primary-600: #0e4ec9;
            --primary-700: #0044bd;
            --primary-800: #003ab1;
            --primary-900: #00269e;
            --tiktok-primary-400: #00a89d;
            --tiktok-primary-500: #00988b;
            --tiktok-primary-600: #008b7e;
        }

        body {
            font-family: 'Public Sans', sans-serif;
            background-color: var(--neutral-800);
            color: var(--neutral-200);
        }

        .page-content {
            padding: 1.5rem;
        }

        .card {
            border: none;
            border-radius: var(--vz-border-radius-lg);
            box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
        }

        .card-header {
            background-color: transparent;
            border-bottom: 1px solid var(--neutral-700);
            padding: 1.5rem;
        }

        .card-body {
            padding: 1.5rem;
        }

        .btn-primary {
            background-color: var(--primary-500);
            border-color: var(--primary-500);
            border-radius: var(--vz-border-radius-sm);
        }

        .btn-primary:hover {
            background-color: var(--primary-600);
            border-color: var(--primary-600);
        }

        .btn-success {
            background-color: var(--tiktok-primary-500);
            border-color: var(--tiktok-primary-500);
            border-radius: var(--vz-border-radius-sm);
        }

        .btn-success:hover {
            background-color: var(--tiktok-primary-600);
            border-color: var(--tiktok-primary-600);
        }

        .text-muted {
            color: var(--neutral-400) !important;
        }

        .text-primary {
            color: var(--primary-500) !important;
        }

        .bg-light {
            background-color: var(--neutral-800) !important;
        }

        .border-light {
            border-color: var(--neutral-700) !important;
        }

        .recommendation-card {
            border: 1px solid var(--neutral-700);
            border-radius: var(--vz-border-radius-sm);
            transition: all 0.2s ease;
        }

        .recommendation-card:hover {
            box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.1);
        }

        .product-thumbnail {
            width: 48px;
            height: 48px;
            border-radius: var(--vz-border-radius-sm);
            background: linear-gradient(45deg, #ff6b6b, #ffa500);
            border: 1px solid var(--neutral-700);
        }

        .product-thumbnail:nth-child(2) {
            background: linear-gradient(45deg, #4ecdc4, #44a08d);
        }

        .product-thumbnail:nth-child(3) {
            background: linear-gradient(45deg, #a8e6cf, #7fcdcd);
        }

        .shop-logo {
            width: 40px;
            height: 40px;
            background-color: var(--neutral-700);
            border-radius: var(--vz-border-radius-sm);
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 600;
            color: var(--neutral-400);
        }

        .info-badge {
            width: 20px;
            height: 20px;
            background-color: var(--primary-50);
            color: var(--primary-500);
            border-radius: 50%;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            font-weight: 600;
        }

        .switch-shop-link {
            color: var(--neutral-400);
            text-decoration: none;
            font-size: 0.875rem;
            transition: color 0.2s ease;
        }

        .switch-shop-link:hover {
            color: var(--neutral-300);
        }

        .fade-in {
            animation: fadeInUp 0.5s ease forwards;
            opacity: 0;
            transform: translateY(20px);
        }

        .fade-in:nth-child(1) {
            animation-delay: 0.1s;
        }

        .fade-in:nth-child(2) {
            animation-delay: 0.2s;
        }

        .fade-in:nth-child(3) {
            animation-delay: 0.3s;
        }

        @keyframes fadeInUp {
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .close-btn {
            background: none;
            border: none;
            color: var(--neutral-400);
            font-size: 1.25rem;
            cursor: pointer;
            padding: 0.25rem;
            border-radius: var(--vz-border-radius-sm);
            transition: all 0.2s ease;
        }

        .close-btn:hover {
            color: var(--neutral-300);
            background-color: var(--neutral-700);
        }
    </style>
</head>

<body>
    <div class="main-content">
        <div class="page-content">
            <div class="container-fluid">

                <!-- Page Header -->
                <div class="row mb-4">
                    <div class="col-12">
                        <div class="d-flex justify-content-between align-items-center">
                            <h1 class="h3 mb-0 fw-bold">GMV Max</h1>
                            <a href="#" class="switch-shop-link">
                                <i class="bi bi-arrow-left-right me-2"></i>
                                Switch to another shop
                            </a>
                        </div>
                    </div>
                </div>

                <!-- Recommendations Section -->
                <div class="row mb-4 fade-in">
                    <div class="col-12">
                        <div class="card">
                            <div class="card-header">
                                <div class="d-flex justify-content-between align-items-start">
                                    <div class="d-flex align-items-center">
                                        <h5 class="card-title mb-0 me-2">Quick actions and recommendations to drive more
                                            sales</h5>
                                        <span class="info-badge">i</span>
                                    </div>
                                    <button type="button" class="close-btn" onclick="closeRecommendations()">
                                        <i class="bi bi-x"></i>
                                    </button>
                                </div>
                            </div>
                            <div class="card-body">
                                <div class="recommendation-card p-3 mb-3">
                                    <div class="row align-items-start">
                                        <div class="col-lg-8 col-md-7">
                                            <h6 class="fw-semibold mb-2">Increase sales on 3 recommended products with
                                                GMV Max ads</h6>
                                            <p class="text-muted mb-3 small">
                                                Recommended products are items from your catalog that shoppers on TikTok
                                                may be interested in purchasing,
                                                based on TikTok's top-selling product categories, trending products and
                                                most searched keywords.
                                            </p>
                                            <div class="d-flex gap-2">
                                                <div class="product-thumbnail"></div>
                                                <div class="product-thumbnail"></div>
                                                <div class="product-thumbnail"></div>
                                            </div>
                                        </div>
                                        <div class="col-lg-4 col-md-5 text-md-end mt-3 mt-md-0">
                                            <button type="button" class="btn btn-primary btn-sm" onclick="createAds()">
                                                Create ads
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Shop Section -->
                <div class="row fade-in">
                    <div class="col-12">
                        <div class="card">
                            <div class="card-body">
                                <div class="d-flex justify-content-between align-items-center">
                                    <div class="d-flex align-items-center">
                                        <div class="shop-logo me-3">M</div>
                                        <div>
                                            <h6 class="mb-1 fw-semibold">Maybi</h6>
                                            <small class="text-muted">Shop code: VNLCRYWLDC</small>
                                        </div>
                                    </div>
                                    <button type="button" class="btn btn-success" onclick="createGMVAds()">
                                        <i class="bi bi-plus-lg me-1"></i>
                                        Create GMV Max ads
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.2.3/dist/js/bootstrap.bundle.min.js"></script>

    <script>
        // Initialize page animations
        document.addEventListener('DOMContentLoaded', function () {
            // Trigger fade-in animations
            const fadeElements = document.querySelectorAll('.fade-in');
            fadeElements.forEach(element => {
                element.style.animationPlayState = 'running';
            });
        });

        function closeRecommendations() {
            const recommendationSection = document.querySelector('.row.mb-4.fade-in');
            recommendationSection.style.transition = 'all 0.3s ease';
            recommendationSection.style.opacity = '0';
            recommendationSection.style.transform = 'translateY(-20px)';

            setTimeout(() => {
                recommendationSection.style.display = 'none';
            }, 300);
        }

        function createAds() {
            // Show Bootstrap toast notification
            showToast('Creating ads for recommended products...', 'primary');

            // Here you would integrate with your backend API
            console.log('Create ads functionality triggered');

            // Simulate API call
            setTimeout(() => {
                showToast('Ads creation process started successfully!', 'success');
            }, 1500);
        }

        function createGMVAds() {
            // Show Bootstrap toast notification
            showToast('Creating GMV Max ads for Maybi shop...', 'primary');

            // Here you would integrate with your backend API
            console.log('Create GMV Max ads functionality triggered');

            // Simulate API call
            setTimeout(() => {
                showToast('GMV Max ads creation process started successfully!', 'success');
            }, 1500);
        }

        function showToast(message, type = 'primary') {
            // Create toast container if it doesn't exist
            let toastContainer = document.querySelector('.toast-container');
            if (!toastContainer) {
                toastContainer = document.createElement('div');
                toastContainer.className = 'toast-container position-fixed top-0 end-0 p-3';
                toastContainer.style.zIndex = '1055';
                document.body.appendChild(toastContainer);
            }

            // Create toast element
            const toastId = 'toast-' + Date.now();
            const toastHTML = `
                <div id="${toastId}" class="toast align-items-center text-bg-${type} border-0" role="alert" aria-live="assertive" aria-atomic="true">
                    <div class="d-flex">
                        <div class="toast-body">
                            ${message}
                        </div>
                        <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast" aria-label="Close"></button>
                    </div>
                </div>
            `;

            toastContainer.insertAdjacentHTML('beforeend', toastHTML);

            // Initialize and show toast
            const toastElement = document.getElementById(toastId);
            const toast = new bootstrap.Toast(toastElement, {
                autohide: true,
                delay: 3000
            });
            toast.show();

            // Remove toast element after it's hidden
            toastElement.addEventListener('hidden.bs.toast', () => {
                toastElement.remove();
            });
        }
    </script>
</body>

</html>