package mapping

import (
	"godsp/modules/facebook/audience/common/enums"
	v20 "godsp/pkg/fb-marketing/marketing/v20"
	"time"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

func MapperFBSaveAudienceToUpsert(audience *v20.SavesavedAudience, userId primitive.ObjectID) bson.M {
	now := time.Now()
	updateData := bson.M{
		"custom_audience_id":            audience.ID,
		"account_id":                    audience.Account.AccountID,
		"name":                          audience.Name,
		"description":                   audience.Description,
		"subtype":                       audience.Subtype,
		"approximate_count_upper_bound": audience.ApproximateCountUpperBound,
		"approximate_count_lower_bound": audience.ApproximateCountLowerBound,
		"run_status":                    audience.RunStatus,
		"targeting":                     audience.Targeting,
		"type":                          enums.TYPE_SAVE_AUDIENCES,
		"time_created":                  time.Time(audience.TimeCreated),
		"time_updated":                  time.Time(audience.TimeUpdated),
		"updated_at":                    now,
		"updated_by":                    userId,
	}

	if audience.DeleteTime > 0 {
		updateData["delete_time"] = audience.DeleteTime
	}

	createData := bson.M{
		"create_at":  now,
		"created_by": userId,
	}

	return bson.M{
		"$set":         updateData,
		"$setOnInsert": createData,
	}
}
