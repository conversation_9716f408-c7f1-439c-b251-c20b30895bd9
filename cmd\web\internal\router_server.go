package internal

import (
	"fmt"
	"godsp/conf"
	"godsp/modules/admin/auths"
	"godsp/modules/admin/billing"
	"godsp/modules/admin/client"
	rHome "godsp/modules/admin/home/<USER>"
	"godsp/modules/admin/permission"
	"godsp/modules/admin/role"
	"godsp/modules/admin/user"
	rEmail "godsp/modules/email/routes"
	rAd "godsp/modules/facebook/ad/routes"
	rAdAcount "godsp/modules/facebook/ad_account/routes"
	rCreative "godsp/modules/facebook/ad_creative/routes"
	rAdPixel "godsp/modules/facebook/ad_pixel/routes"
	rAdset "godsp/modules/facebook/adset/routes"
	rAudience "godsp/modules/facebook/audience/routes"
	rBudget "godsp/modules/facebook/budget_schedule/routes"
	rCampaign "godsp/modules/facebook/campaign/routes"
	rCatalogue "godsp/modules/facebook/catalogue/routes"
	rImg "godsp/modules/facebook/image/routes"
	rInterestBehaviorDemographic "godsp/modules/facebook/interest/routes"
	rMessageTemplate "godsp/modules/facebook/message_template/routes"
	rPage "godsp/modules/facebook/pages/routes"
	rPost "godsp/modules/facebook/posts/routes"
	rProduct "godsp/modules/facebook/product/routes"
	rProductSet "godsp/modules/facebook/product_set/routes"
	rTargeting "godsp/modules/facebook/targeting/routes"
	rVideo "godsp/modules/facebook/video/routes"

	"godsp/modules/tiktok"
	"godsp/pkg/gos/midd"

	"github.com/dev-networldasia/dspgos/sctx"

	"github.com/dev-networldasia/dspgos/sctx/component/jwtc"

	"github.com/dev-networldasia/dspgos/sctx/configs"

	"github.com/gofiber/fiber/v2"
	"github.com/gofiber/fiber/v2/middleware/compress"
	"github.com/gofiber/fiber/v2/middleware/cors"
	"github.com/gofiber/fiber/v2/middleware/csrf"
	"github.com/gofiber/fiber/v2/middleware/logger"
	"github.com/gofiber/fiber/v2/middleware/session"
)

func RouterWebFacebook(app *fiber.App, serviceCtx sctx.ServiceContext, store *session.Store) {
	app.Use(logger.New(logger.Config{
		Format: `{"ip":${ip}, "timestamp":"${time}", "status":${status}, "latency":"${latency}", "method":"${method}", "path":"${path}"}` + "\n",
	}))
	app.Use(compress.New())
	app.Use(cors.New())

	if serviceCtx.EnvName() == configs.AppProd {
		app.Use(midd.Recovery(serviceCtx))
		app.Use(csrf.New())
	}

	jwtComp := serviceCtx.MustGet(configs.KeyCompJWT).(jwtc.JWTProvider)
	permissionMidd := midd.RequireAuth(jwtComp, serviceCtx)
	authMidd := midd.AuthMidd(jwtComp)
	app.Use([]string{"login", "api", "admins", "dsp"}, authMidd, permissionMidd)

	setupWebFacebookMarketing(app, serviceCtx, store)

	//tiktok
	tiktok.SetupRoutesTiktok(app, serviceCtx, store)

	app.Static("/static", fmt.Sprintf("./%s", conf.UploadPathPublic))
}

func setupWebFacebookMarketing(app *fiber.App, serviceCtx sctx.ServiceContext, store *session.Store) {

	// adacount routes
	rAdAcount.SetupRoutesAdAcount(app, serviceCtx)

	// ad routes
	rAd.SetupRoutesAd(app, serviceCtx)

	//ad pixel
	rAdPixel.SetupRoutesAdPixel(app, serviceCtx)

	// Campaign routes
	rCampaign.SetupRouterCampaign(app, serviceCtx)

	//adset routes
	rAdset.SetupRoutesAdset(app, serviceCtx)

	// ad creative
	rCreative.SetupRouterAdCreative(app, serviceCtx)

	//page routes
	rPage.SetupRoutesPages(app, serviceCtx)

	rTargeting.SetupRouterTargeting(app, serviceCtx)

	// interest behavior demographic
	rInterestBehaviorDemographic.SetupRoutesInterestBehaviorDemographic(app, serviceCtx)

	//budget schedule
	rBudget.SetupRouterBudgetSchedule(app, serviceCtx)

	//post
	rPost.SetupRoutesPosts(app, serviceCtx)

	//images
	rImg.SetupRoutesImages(app, serviceCtx)

	//video
	rVideo.SetupRoutesVideos(app, serviceCtx)

	// Message template
	rMessageTemplate.SetupRoutesMessageTemplates(app, serviceCtx)

	// Catalogue
	rCatalogue.SetupRoutesCatalogue(app, serviceCtx)

	// Product
	rProduct.SetupRoutesProduct(app, serviceCtx)

	// Product set
	rProductSet.SetupRoutesProductSet(app, serviceCtx)

	//audience
	rAudience.SetupRoutesAudience(app, serviceCtx)

	//admin
	role.SetupRoutesRole(app, serviceCtx)
	user.SetupRoutesUser(app, serviceCtx, store)
	auths.SetupRoutesAuth(app, serviceCtx, store)
	permission.SetupRoutesPermission(app, serviceCtx, store)
	client.SetupRoutesClients(app, serviceCtx, store)

	//billing
	billing.SetupRoutesBilling(app, serviceCtx)

	// Pages
	rHome.SetupRoutesHomePages(app, serviceCtx)

	//Email
	rEmail.SetupRoutesEmail(app, serviceCtx)

}
