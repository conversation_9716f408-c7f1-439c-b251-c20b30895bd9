package pubsub

import (
	"fmt"
	"godsp/cmd/pubsub/internal"

	// "github.com/dev-networldasia/dspgos/sctx"
	"time"

	"github.com/dev-networldasia/dspgos/sctx/configs"

	"github.com/dev-networldasia/dspgos/sctx/component/watermillapp/redisstream"

	"github.com/dev-networldasia/dspgos/sctx"
	"github.com/dev-networldasia/dspgos/sctx/component/discord"
	"github.com/dev-networldasia/dspgos/sctx/component/mongodb"
	"github.com/spf13/cobra"
)

var (
	serviceNameRedis = "redis pubsub-watermillapp"
	versionRedis     = "1.0.0"
)

func newRedisServiceCtx() sctx.ServiceContext {
	return sctx.NewServiceContext(
		sctx.WithName(serviceNameRedis),
		sctx.WithComponent(mongodb.NewMongoDB(configs.KeyCompMongoDB, "")),
		sctx.WithComponent(sctx.NewAppLoggerDaily(configs.KeyLoggerDaily)),
		sctx.WithComponent(redisstream.NewRedisStreamPub(configs.KeyRedisStreamPub)),
		sctx.WithComponent(redisstream.NewRedisStreamSub(configs.KeyRedisStreamSub)),
		// sctx.WithComponent(telegrams.NewTelegramClient(configs.KeyTelegramSMS)),
		sctx.WithComponent(discord.NewDiscordClient(configs.KeyDiscordSMS)),
	)
}

var (
	RedisStreamCmd = &cobra.Command{
		Use:     "redisstream_pubsub",
		Short:   "redis stream pubsub",
		Long:    `redis stream pubsub CLI Long`,
		Version: versionRedis,
		Run: func(cmd *cobra.Command, args []string) {
			fmt.Println("--->> ***redisstream pubsub run**** <<---")
			serviceCtx := newRedisServiceCtx()
			loggerSv := serviceCtx.MustGet(configs.KeyLoggerDaily).(sctx.AppLoggerDaily).GetLogger("pubsubredis")

			if err := serviceCtx.Load(); err != nil {
				loggerSv.Fatal(err)
			}

			subscriber := serviceCtx.MustGet(configs.KeyRedisStreamSub).(redisstream.RedisStreamSubcriber)
			emailSub := internal.SetEmailHandlerConsumer(serviceCtx)
			subscriber.AddNoPublisherHandler(emailSub)

			msg := fmt.Sprintf("start service pubsub redis stream time = %s \n", time.Now().Format("2006-01-02 15:04:05"))
			loggerSv.Infof(msg)

			// telegramSvc := serviceCtx.MustGet(configs.KeyTelegramSMS).(telegrams.SendMessageTelegramSVC)
			// telegramSvc.SendMessageTelegramDev(msg)
			discordSvc := serviceCtx.MustGet(configs.KeyDiscordSMS).(discord.SendMessageDiscordSVC)
			discordSvc.SendMessageDev(msg)

			var forever chan struct{}
			<-forever

		},
	}
)
