package mapping

import (
	"fmt"
	adserverReportE "godsp/modules/facebook/adserver_report/entity"
	"godsp/modules/facebook/adset/entity"
	"godsp/modules/facebook/adset/transport/responses"
)

// ReportCampaignItem is a flat struct combining FacebookAdfbReportDetails and CampaignEntity fields

func MapperAdsetsToDatatable(adsets *[]entity.AdsetEntity, fbReportDetails *[]adserverReportE.FBReportDetailEntity) *[]responses.AdsetsDatatable {

	if adsets == nil {
		return nil
	}

	fbReportDetailsData := make(map[string]adserverReportE.FBReportDetailEntity)
	if fbReportDetails != nil {
		for _, fbReportDetail := range *fbReportDetails {
			fbReportDetailsData[fbReportDetail.AdSetID] = fbReportDetail
		}
	}

	var datas []responses.AdsetsDatatable
	for _, adset := range *adsets {
		record := responses.AdsetsDatatable{
			DTRowId:                    fmt.Sprintf("row_%s", adset.IDAdset),
			AdSetID:                    adset.IDAdset,
			Name:                       adset.Name,
			Status:                     adset.Status,
			AttributionSpec:            adset.AttributionSpec,
			BidAmount:                  adset.BidAmount,
			BidStrategy:                adset.BidStrategy,
			BillingEvent:               adset.BillingEvent,
			BudgetRemaining:            adset.BudgetRemaining,
			ConfiguredStatus:           adset.ConfiguredStatus,
			DailyBudget:                adset.DailyBudget,
			DailyMinSpendTarget:        adset.DailyMinSpendTarget,
			DailySpendCap:              adset.DailySpendCap,
			DestinationType:            adset.DestinationType,
			DeliveryEstimate:           adset.DeliveryEstimate,
			EffectiveStatus:            adset.EffectiveStatus,
			EndTime:                    adset.EndTime,
			FrequencyControlSpecs:      adset.FrequencyControlSpecs,
			LifetimeBudget:             adset.LifetimeBudget,
			LifetimeMinSpendTarget:     adset.LifetimeMinSpendTarget,
			LifeTimeSpendCap:           adset.LifeTimeSpendCap,
			LifetimeImps:               adset.LifetimeImps,
			OptimizationGoal:           adset.OptimizationGoal,
			PacingType:                 adset.PacingType,
			PromotedObject:             adset.PromotedObject,
			RecurringBudgetSemantics:   adset.RecurringBudgetSemantics,
			StartTime:                  adset.StartTime,
			Targeting:                  adset.Targeting,
			TargetingOptimizationTypes: adset.TargetingOptimizationTypes,
			DSABeneficiary:             adset.DSABeneficiary,
			DSAPayor:                   adset.DSAPayor,
			IsDynamicCreative:          adset.IsDynamicCreative,
			IsBudgetScheduleEnabled:    adset.IsBudgetScheduleEnabled,
			UpdatedAt:                  adset.UpdatedAt,
			CreatedAt:                  adset.CreatedAt,
			Campaign:                   adset.Campaign,
		}

		if adsetMetrics, exists := fbReportDetailsData[adset.IDAdset]; exists {
			record.CampaignID = adsetMetrics.CampaignID
			record.CampaignName = adsetMetrics.CampaignName
			record.AccountID = adsetMetrics.AccountID
			record.AccountName = adsetMetrics.AccountName
			record.AccountCurrency = adsetMetrics.AccountCurrency
			// ID                 primitive.ObjectID `json:"_id,omitempty" bson:"_id,omitempty"`
			record.AdID = adsetMetrics.AdID

			record.AdSetName = adsetMetrics.AdSetName

			// ========== Mentric Prority ==========
			record.Impressions = adsetMetrics.Impressions
			record.Reach = adsetMetrics.Reach
			record.VideoView = adsetMetrics.VideoView
			record.PostReaction = adsetMetrics.PostReaction
			record.PostComment = adsetMetrics.PostComment
			record.PostShare = adsetMetrics.PostShare
			record.Clicks = adsetMetrics.Clicks
			record.LinkClick = adsetMetrics.LinkClick
			record.PostEngagement = adsetMetrics.PostEngagement
			record.PostSave = adsetMetrics.PostSave
			record.PageLike = adsetMetrics.PageLike

			// ========== End Mentric Prority ==========
			// record.Objective = adsetMetrics.Objective
			record.BuyingType = adsetMetrics.BuyingType
			record.AttributionSetting = adsetMetrics.AttributionSetting
			record.DateStart = adsetMetrics.DateStart
			record.DateStop = adsetMetrics.DateStop
			record.Date = adsetMetrics.Date
			// ========== Demographics ==========
			record.Age = adsetMetrics.Age
			record.Gender = adsetMetrics.Gender
			record.Country = adsetMetrics.Country
			record.Placement = adsetMetrics.Placement
			record.DevicePlatform = adsetMetrics.DevicePlatform
			record.PublisherPlatform = adsetMetrics.PublisherPlatform
			record.ImpressionDevice = adsetMetrics.ImpressionDevice

			// ========== Performance Metrics ==========
			record.Clicks = adsetMetrics.Clicks
			record.Impressions = adsetMetrics.Impressions
			record.Reach = adsetMetrics.Reach
			record.Frequency = adsetMetrics.Frequency
			record.Spend = adsetMetrics.Spend
			record.SocialSpend = adsetMetrics.SocialSpend
			record.CPM = adsetMetrics.CPM
			record.CTR = adsetMetrics.CTR
			record.CPC = adsetMetrics.CPC
			record.CPP = adsetMetrics.CPP
			record.WishBid = adsetMetrics.WishBid
			record.OptimizationGoal = adsetMetrics.OptimizationGoal
			// ========== Engagement ==========
			record.InlineLinkClicks = adsetMetrics.InlineLinkClicks
			record.InlineLinkClickCtr = adsetMetrics.InlineLinkClickCtr
			record.InlinePostEngagement = adsetMetrics.InlinePostEngagement
			record.UniqueClicks = adsetMetrics.UniqueClicks
			record.UniqueCTR = adsetMetrics.UniqueCTR
			record.UniqueOutboundClicks = adsetMetrics.UniqueOutboundClicks
			record.WebsiteCTR = adsetMetrics.WebsiteCTR
			record.ActionValues = adsetMetrics.ActionValues
			record.Conversions = adsetMetrics.Conversions
			record.ConversionValues = adsetMetrics.ConversionValues
			record.CostPerActionType = adsetMetrics.CostPerActionType
			record.CostPerConversion = adsetMetrics.CostPerConversion
			record.CostPerInlineLinkClick = adsetMetrics.CostPerInlineLinkClick
			record.CostPerInlinePostEngage = adsetMetrics.CostPerInlinePostEngage

			// ========== Video Metrics ==========
			record.CostPerThruplay = adsetMetrics.CostPerThruplay
			record.CostPer15SecVideoView = adsetMetrics.CostPer15SecVideoView
			record.VideoAvgTimeWatchedActions = adsetMetrics.VideoAvgTimeWatchedActions
			record.VideoPlayActions = adsetMetrics.VideoPlayActions
			record.Video30SecWatched = adsetMetrics.Video30SecWatched
			record.VideoTimeWatched = adsetMetrics.VideoTimeWatched
			record.VideoRetention15s = adsetMetrics.VideoRetention15s
			record.VideoRetention60s = adsetMetrics.VideoRetention60s
			record.VideoRetentionGraph = adsetMetrics.VideoRetentionGraph
			record.VideoPlayCurveActions = adsetMetrics.VideoPlayCurveActions
			record.VideoP25Watched = adsetMetrics.VideoP25Watched
			record.VideoP50Watched = adsetMetrics.VideoP50Watched
			record.VideoP75Watched = adsetMetrics.VideoP75Watched
			record.VideoP95Watched = adsetMetrics.VideoP95Watched
			record.VideoP100Watched = adsetMetrics.VideoP100Watched

			var actions []adserverReportE.ActionStats
			for _, action := range adsetMetrics.Actions {
				actions = append(actions, adserverReportE.ActionStats{
					ActionType: action.ActionType,
					Value:      action.Value,
				})
			}
			record.Actions = &actions
		}

		datas = append(datas, record)
	}
	return &datas
}
