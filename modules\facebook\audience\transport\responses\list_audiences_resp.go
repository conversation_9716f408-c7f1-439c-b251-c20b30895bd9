package responses

import (
	v20 "godsp/pkg/fb-marketing/marketing/v20"
)

type ListAudiencesResp struct {
	Draw            int                   `json:"draw"`
	Data            *[]AudiencesDatatable `json:"data"`
	RecordsTotal    int64                 `json:"recordsTotal"`
	RecordsFiltered int64                 `json:"recordsFiltered"`
	Msg             string                `json:"msg,omitempty"`
}

type AudiencesDatatable struct {
	DTRowId          string          `json:"DT_RowId"`
	CustomAudienceID string          `json:"custom_audience_id"`
	AccountID        string          `json:"account_id"`
	Name             string          `json:"name"`
	Adaccounts       *v20.Adaccounts `json:"adaccounts"`

	ApproximateCountLowerBound int `json:"approximate_count_lower_bound"`
	ApproximateCountUpperBound int `json:"approximate_count_upper_bound"`

	CustomerFileSource string                        `json:"customer_file_source"`
	DataSource         *v20.CustomAudienceDataSource `json:"data_source"`
	DeliveryStatus     *v20.CustomAudienceStatus     `json:"delivery_status"`
	Type               string                        `json:"type"`
	Subtype            string                        `json:"subtype"`
	LookalikeSpec      *v20.LookalikeSpec            `json:"lookalike_spec,omitempty" bson:"lookalike_spec,omitempty"`
	Targeting          *v20.Targeting                `json:"targeting,omitempty" bson:"targeting"`
	RunStatus          string                        `json:"run_status"`
}
