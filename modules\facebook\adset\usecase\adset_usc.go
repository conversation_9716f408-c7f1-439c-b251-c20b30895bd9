package usecase

import (
	"context"
	clientE "godsp/modules/admin/client/entity"
	userEt "godsp/modules/admin/user/entity"
	userRp "godsp/modules/admin/user/repository/mongo"
	adAccRes "godsp/modules/facebook/ad_account/transport/response"

	"godsp/modules/facebook/adset/entity"
	"godsp/modules/facebook/common/fbenums"
	"godsp/modules/facebook/iface_repo"

	"github.com/dev-networldasia/dspgos/sctx"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo/options"
)

type AdsetRepo interface {
	InsertAdsetRepo(ctx context.Context, adset *entity.AdsetEntity) error
	UpsertAdsetRepo(ctx context.Context, filter bson.M, update bson.M) error
	UpdateAdsetRepo(ctx context.Context, filter interface{}, adset *entity.AdsetEntity, opts ...*options.UpdateOptions) error
	FindOneAdsetRepo(ctx context.Context, filter interface{}, opts ...*options.FindOneOptions) (*entity.AdsetEntity, error)
	FindAdsetRepo(ctx context.Context, filter interface{}, opts ...*options.FindOptions) (*[]entity.AdsetEntity, error)
	CountAdsetRepo(ctx context.Context, filter interface{}) (int64, error)
	AggregateAdsetRepo(ctx context.Context, pipeline []bson.M, opts ...*options.AggregateOptions) (*entity.AdsetDetailViewEntity, error)
	UpdateAdsetNameStatusRepo(ctx context.Context, filter interface{}, data bson.M) error
	DeleteAdsetRepo(ctx context.Context, filter interface{}) error
	FindListTableAggregateRepo(ctx context.Context, pipeline []bson.M, opts ...*options.AggregateOptions) (*[]entity.AdsetEntity, error)
}

type ClientRepo interface {
	FindClientRepo(ctx context.Context, filter interface{}, opts ...*options.FindOptions) ([]*clientE.ClientEntity, error)
}
type ApiUserRepo interface {
	FindOneUserRepo(ctx context.Context, filter interface{}, opts ...*options.FindOneOptions) (*userEt.UserEntity, error)
	GetRoleOfUser(ctx context.Context, userId primitive.ObjectID, opts ...*options.AggregateOptions) (*userRp.RoleInfo, error)
}

type adsetUsc struct {
	repo       ApiAdsetRepo
	logger     sctx.Logger
	adAcc      iface_repo.AdAccountRepo
	clientRepo ClientRepo
}

func NewAdsetUsc(repo ApiAdsetRepo, logger sctx.Logger, adAcc iface_repo.AdAccountRepo, clientRepo ClientRepo) *adsetUsc {
	return &adsetUsc{
		repo:       repo,
		logger:     logger,
		clientRepo: clientRepo,
		adAcc:      adAcc,
	}
}

/**
 * list ad account
 */
func (usc *adsetUsc) ListAdAccountCampaignUsc(ctx context.Context) ([]*adAccRes.AdAccountEditClient, error) {
	allowDiskUse := fbenums.ALLOW_DISK_USE_TRUE
	opts := &options.FindOptions{
		AllowDiskUse: &allowDiskUse,
		Sort:         bson.D{{Key: "name", Value: -1}},
		Projection:   bson.M{"id": 1, "name": 1, "account_id": 1},
	}
	return usc.adAcc.FindAdAccountEditClientRepo(ctx, bson.M{}, opts)
}

/**
 * List client
 */
func (usc *adsetUsc) ListClientCampaignUsc(ctx context.Context) ([]*clientE.ClientEntity, error) {
	return usc.clientRepo.FindClientRepo(ctx, bson.M{})
}
