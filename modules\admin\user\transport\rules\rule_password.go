package rules

import (
	"errors"
	"fmt"
	"regexp"
	"strings"

	"github.com/go-playground/validator/v10"
)

/**
 * Check Rule password
 */
func RulePasswordReq(fl validator.FieldLevel) bool {
	pw := fl.Field().String()
	pw = strings.TrimSpace(pw)

	isMediumStrong := validateMediumStrongPassword(pw)
	isVeryStrong := validateVeryStrongPassword(pw)
	return (isMediumStrong || isVeryStrong)
}

func validateMediumStrongPassword(password string) bool {
	// Medium strength password: at least 8 characters with at least one lowercase letter, one uppercase letter, and one digit, no whitespace
	pattern := "^[0-9a-zA-Z]{8,32}$"
	regExp, err := regexp.Compile(pattern)
	if err != nil {
		fmt.Println(err)
		return false
	}

	return regExp.MatchString(password)
}

func validateVeryStrongPassword(password string) bool {
	// Very strong password: at least 12 characters with at least one lowercase letter, one uppercase letter, one digit, and one special character, no whitespace
	pattern := "^[a-z0-9A-Z!@#$%^&*()-_+={}//?]{12,32}$"
	regExp, err := regexp.Compile(pattern)
	if err != nil {
		fmt.Println(err)
		return false
	}
	return regExp.MatchString(password)
}

func ValidatePassword(password string) error {
	if len(password) < 8 {
		return errors.New("password should be of 8 characters long")
	}
	done, err := regexp.MatchString("([a-z])+", password)
	if err != nil {
		return err
	}
	if !done {
		return errors.New("password should contain atleast one lower case character")
	}
	done, err = regexp.MatchString("([A-Z])+", password)
	if err != nil {
		return err
	}
	if !done {
		return errors.New("password should contain atleast one upper case character")
	}
	done, err = regexp.MatchString("([0-9])+", password)
	if err != nil {
		return err
	}
	if !done {
		return errors.New("password should contain atleast one digit")
	}

	done, err = regexp.MatchString("([!@#$%^&*.?-])+", password)
	if err != nil {
		return err
	}
	if !done {
		return errors.New("password should contain atleast one special character")
	}
	return nil
}
