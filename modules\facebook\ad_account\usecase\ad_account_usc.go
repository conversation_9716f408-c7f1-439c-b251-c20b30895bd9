package usecase

import (
	"context"
	"fmt"
	"godsp/conf"
	v20 "godsp/pkg/fb-marketing/marketing/v20"

	"github.com/dev-networldasia/dspgos/sctx"
)

type fbAdAccountUsc struct {
	fbService *v20.Service
	logger    sctx.Logger
}

func NewFBMarketingApi(fbService *v20.Service, logger sctx.Logger) *fbAdAccountUsc {
	return &fbAdAccountUsc{
		fbService: fbService,
		logger:    logger,
	}
}

/**
 * Reload Ad Account Facebook V20
 */
func (usc *fbAdAccountUsc) ReloadAdAccountUsc(ctx context.Context) error {
	adaccounts, err := usc.fbService.AdAccounts.List(ctx, conf.FBConf.BusinessID)
	if err != nil {
		return err
	}

	for _, adc := range adaccounts {
		fmt.Printf("--->> %+v /n ", adc)

	}

	return nil
}
