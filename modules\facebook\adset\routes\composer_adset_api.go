package routes

import (
	"godsp/conf"
	userR "godsp/modules/admin/user/repository/mongo"
	fbReportDetailR "godsp/modules/facebook/adserver_report/repository/mongo"
	"godsp/modules/facebook/adset/repository/mongo"
	"godsp/modules/facebook/adset/transport/api"
	"godsp/modules/facebook/adset/usecase"
	"godsp/pkg/sctx/component/facebook/fbmarketing"

	"github.com/dev-networldasia/dspgos/sctx/component/mongodb"

	"github.com/dev-networldasia/dspgos/sctx/configs"

	"github.com/dev-networldasia/dspgos/sctx"

	"github.com/gofiber/fiber/v2"
)

type ComposerAdsetApi interface {
	ReloadAdsetApi() fiber.Handler
	CreateAdsetApi() fiber.Handler
	UpdateAdsetApi() fiber.Handler
	UpdateNameStatusAdsetApi() fiber.Handler
	ShowAdsetApi() fiber.Handler
	ListDatatableAdsetApi() fiber.Handler
	ListSelectAdsetApi() fiber.Handler
	DeleteAdsetApi() fiber.Handler
}

func NewComposerAdsetApi(serviceCtx sctx.ServiceContext) ComposerAdsetApi {
	mongoDB := serviceCtx.MustGet(configs.KeyCompMongoDB).(mongodb.MongoComponent).GetDatabase()
	mongoAdserverReportDB := serviceCtx.MustGet(conf.KeyCompReportMongoDB).(mongodb.MongoComponent).GetDatabase()
	logger := serviceCtx.MustGet(configs.KeyLoggerDaily).(sctx.AppLoggerDaily).GetLogger("webfb")
	fbService := serviceCtx.MustGet(configs.KeyFBMarketingV20).(fbmarketing.FBMarketingServices).GetFBMarketingService()

	// Init api
	repo := mongo.NewAdsetRepo(mongoDB)
	userRepo := userR.NewUserRepo(mongoDB)

	fbReportDetailRepo := fbReportDetailR.NewFBReportDetailRepo(mongoAdserverReportDB)
	usc := usecase.NewApiAdsetUsc(fbService, repo, fbReportDetailRepo, userRepo, logger)
	api := api.NewAdsetApi(usc)

	return api
}
