package routes

import (
	userMongo "godsp/modules/admin/user/repository/mongo"
	"godsp/modules/email/common"
	"godsp/modules/email/service"
	"godsp/modules/email/transport/api"
	"godsp/modules/email/usecase"

	"github.com/dev-networldasia/dspgos/sctx"
	"github.com/dev-networldasia/dspgos/sctx/component/mongodb"
	"github.com/dev-networldasia/dspgos/sctx/configs"
	"github.com/gofiber/fiber/v2"
)

// ComposerEmailApi defines the interface for email API handlers.
type ComposerEmailApi interface {
	SendEmailExampleApi() fiber.Handler
	SendEmailLoginExampleApi() fiber.Handler
}

func ComposerEmailApiService(serviceCtx sctx.ServiceContext) ComposerEmailApi {
	logger := serviceCtx.MustGet(configs.KeyLoggerDaily).(sctx.AppLoggerDaily).GetLogger("email_api")
	mongoDB := serviceCtx.MustGet(configs.KeyCompMongoDB).(mongodb.MongoComponent).GetDatabase()

	userRepo := userMongo.NewUserRepo(mongoDB)
	config := common.LoadEmailConfig()
	srv := service.NewEmailService(config)
	emailUsc := usecase.NewEmailApiUsecase(srv, logger, userRepo)

	hdl := api.NewEmailApi(emailUsc)
	return hdl
}
