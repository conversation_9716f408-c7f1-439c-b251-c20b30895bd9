package requests

import (
	"godsp/modules/facebook/ad/common/errs"

	"github.com/go-playground/validator/v10"
)

type ReloadAdReq struct {
	AccountID string `json:"account_id,omitempty"`
	AdsetID   string `json:"adset_id,omitempty"`
	AdID      string `json:"ad_id,omitempty"`
}

func (req *ReloadAdReq) Validate() []string {
	validate := validator.New()
	var validationErrors []string

	err := validate.Struct(req)
	if err != nil {
		for _, err := range err.(validator.ValidationErrors) {
			switch err.Field() {
			case "AccountID":
				validationErrors = append(validationErrors, errs.ErrReloadAdsAccountId.Error())
			case "AdsetID":
				validationErrors = append(validationErrors, errs.ErrReloadAdsetID.Error())
			case "AdID":
				validationErrors = append(validationErrors, errs.ErrReloadAdsId.Error())
			}
		}
	}

	return validationErrors
}
