package requests

import (
	clientErrs "godsp/modules/admin/client/common/errs"
	"godsp/modules/facebook/ad/common/errs"
	"godsp/modules/facebook/common/fbenums"
	"godsp/modules/facebook/common/fbrules"
	v20 "godsp/pkg/fb-marketing/marketing/v20"

	"godsp/pkg/gos/utils"
	"strings"
	"time"

	"github.com/go-playground/validator/v10"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

type CreateAdReq struct {
	// AccountID     string                      `json:"account_id" validate:"required,numeric,gt=0"`
	AccountID     string                      `json:"account_id" validate:"required"`
	AdsetId       string                      `json:"adset_id" validate:"required,numeric,gt=0"`
	Name          string                      `json:"name" validate:"required"`
	Status        string                      `json:"status"  validate:"required,ruleStatus"`
	Creative      *CreateCreativeReq          `json:"creative" validate:"required"`
	TrackingSpecs []v20.ConversionActionQuery `json:"tracking_specs" validate:"omitempty"`

	AdScheduleStartTime *time.Time         `json:"ad_schedule_start_time,omitempty"`
	AdScheduleEndTime   *time.Time         `json:"ad_schedule_end_time,omitempty"`
	UserId              primitive.ObjectID `json:"-"`

	ClientIDStr string             `json:"client_id" form:"client_id"`
	ClientID    primitive.ObjectID `json:"-" form:"-"`
}

type CreateCreativeReq struct {
	CreativeID string `json:"creative_id" validate:"required"`
}

func (req *CreateAdReq) Validate() []string {
	validate := validator.New()

	validate.RegisterValidation("ruleStatus", fbrules.RuleFBStatus)
	var validationErrors []string
	req.Name = strings.TrimSpace(req.Name)

	err := validate.Struct(req)
	if err != nil {
		for _, err := range err.(validator.ValidationErrors) {
			switch err.Field() {
			case "AccountID":
				validationErrors = append(validationErrors, errs.ErrAdAccountID.Error())
			case "AdsetId":
				validationErrors = append(validationErrors, errs.ErrAdAdsetId.Error())
			case "Name":
				validationErrors = append(validationErrors, errs.ErrAdName.Error())
			case "Status":
				validationErrors = append(validationErrors, errs.ErrAdStatus.Error())
			case "TrackingSpecs":
				validationErrors = append(validationErrors, errs.ErrAdTrackingSpecs.Error())
			}
		}
	}

	if req.ClientIDStr == "" {
		validationErrors = append(validationErrors, clientErrs.ErrClientIdEmpty.Error())
	}

	if req.ClientIDStr != "" && !utils.ValidateObjectID(req.ClientIDStr) {
		validationErrors = append(validationErrors, clientErrs.ErrIDClientValidate.Error())
	}

	if req.ClientIDStr != "" {
		clientID, _ := primitive.ObjectIDFromHex(req.ClientIDStr)
		req.ClientID = clientID
	}
	// set value when validate ok
	if validationErrors == nil {
		req.Status = fbenums.StatusFB[req.Status]
	}

	return validationErrors
}
