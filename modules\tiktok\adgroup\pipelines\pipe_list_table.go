package pipelines

import (
	"godsp/modules/tiktok/adgroup/common/enums"
	"godsp/modules/tiktok/adgroup/transport/requests"

	"go.mongodb.org/mongo-driver/bson"
)

func PipelineListDatatableAdgroup(req *requests.ListTableAdgroupReq, filter bson.M) []bson.M {
	pipeline := make([]bson.M, 0)

	// Tách riêng client_id và list_user_ids nếu có
	clientID, hasClientID := filter["client_id"]
	listUserIDs, hasListUserIDs := filter["list_user_ids"]

	// Tạo bản sao của filter để không bị mất dữ liệu khi delete
	baseFilter := bson.M{
		"status": bson.M{"$nin": []string{enums.AdGroupSecondaryStatus[enums.DELETED],enums.AdGroupSecondaryStatus[enums.CANCELLED]}},
	}
	for k, v := range filter {
		if k != "client_id" && k != "list_user_ids" {
			baseFilter[k] = v
		}
	}

	// Match theo filter chung
	if len(baseFilter) > 0 {
		pipeline = append(pipeline, bson.M{"$match": baseFilter})
	}

	// Check client_id và list_user_ids add into advertisers
	if hasClientID {
		lookupMatch := bson.M{
			"$expr": bson.M{
				"$eq": bson.A{"$advertiser_id", "$$adg_advertiser_id"},
			},
			"client_ids": clientID,
		}
		// Chỉ thêm điều kiện list_user_ids nếu có
		if hasListUserIDs {
			lookupMatch["list_user_ids"] = listUserIDs
		}

		pipeline = append(pipeline,
			bson.M{
				"$lookup": bson.M{
					"from":     "tiktok_advertisers",
					"let":      bson.M{"adg_advertiser_id": "$advertiser_id"},
					"pipeline": bson.A{bson.M{"$match": lookupMatch}},
					"as":       "matched_advertiser",
				},
			},
			bson.M{
				"$match": bson.M{
					"matched_advertiser.0": bson.M{"$exists": true},
				},
			},
		)
	}

	// Order by
	sortField := req.SortField
	sortOrder := -1
	if req.SortOrder == 1 {
		sortOrder = 1
	}
	if sortField == "" {
		pipeline = append(pipeline, bson.M{
			"$sort": bson.M{
				"create_time": -1,
				"modify_time": -1,
			},
		})
	} else {
		pipeline = append(pipeline, bson.M{
			"$sort": bson.M{sortField: sortOrder},
		})
	}

	// Pagination
	if req.Start > 0 {
		pipeline = append(pipeline, bson.M{"$skip": req.Start})
	}
	if req.Length > 0 {
		pipeline = append(pipeline, bson.M{"$limit": req.Length})
	}

	return pipeline
}
