package requests

import (
	"godsp/modules/admin/user/common/errs"
	"time"

	"github.com/go-playground/validator/v10"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

type PayloadBsonUserUpdate struct {
	ID          primitive.ObjectID  `form:"id" validate:"required"`
	ClientIDStr string              `form:"client_id,omitempty" validate:"omitempty,ruleObjectid"`
	ClientID    *primitive.ObjectID `form:"-"`

	UserID    primitive.ObjectID `form:"-" json:"-"`
	UpdatedBy primitive.ObjectID `form:"-" bson:"updated_by"`
	UpdatedAt time.Time          `form:"-" bson:"updated_at"`
}

func (req *PayloadBsonUserUpdate) Validate() []*string {
	validate := validator.New()

	validate.RegisterValidation("ruleObjectid", RuleObjectID)

	var validationErrors []*string
	err := validate.Struct(req)
	if err != nil {
		for _, err := range err.(validator.ValidationErrors) {
			switch err.Field() {
			case "ID":
				err := errs.ErrIDUserValidate.Error()
				return append(validationErrors, &err)
			case "ClientIDStr":
				errID := errs.ErrClientExistsValidate.Error()
				return append(validationErrors, &errID)
			}
		}
	}

	if req.ClientIDStr != "" {
		oid, err := primitive.ObjectIDFromHex(req.ClientIDStr)
		if err != nil {
			errClientID := errs.ErrClientExistsValidate.Error()
			validationErrors = append(validationErrors, &errClientID)
		} else {
			req.ClientID = &oid // bạn cần thêm 1 trường ClientIDPtr *primitive.ObjectID để sử dụng sau
		}
	} else {
		req.ClientID = nil
	}

	return validationErrors
}
