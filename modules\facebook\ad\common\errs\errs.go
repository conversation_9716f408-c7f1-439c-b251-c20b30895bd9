package errs

import "errors"

var (
	ErrFBAdList             = errors.New("error list campaign facebook")
	ErrReloadAdIdFBNotExist = errors.New("facebook ad id not exist")

	//validate
	ErrAdAccountID  = errors.New("validate ad account id required")
	ErrAdCampaignID = errors.New("validate ad campaign id required")
	ErrAdAdsetId    = errors.New("validate adset id ad required")
	ErrAdId         = errors.New("validate ad id required")
	ErrAudienceID   = errors.New("validate audience id required")

	ErrAdName          = errors.New("validate name ad required")
	ErrAdDailyBudget   = errors.New("validate daily budget ad must be greater than 0")
	ErrAdBidAmount     = errors.New("validate bid amount  ad must be greater than 0")
	ErrAdBillingEvent  = errors.New("validate name billing event required")
	ErrAdStatus        = errors.New("validate status ad is not in the correct format")
	ErrAdTrackingSpecs = errors.New("validate tracking_specs is not in the correct format")
	ErrAdStartTime     = errors.New("validate start time ad is not in the correct format")

	ErrFBAdUpdateInvalidate = errors.New("invalidate ad update")

	ErrReloadAccountFBAdsetList       = errors.New("facebook with accout id not found")
	ErrReloadAdsetFBNotExist          = errors.New("facebook with adset id not found")
	ErrReloadAdsAccountIdAdsetIdAdsId = errors.New("validate either account id or adset id or ads id must be provided")
	ErrReloadAdsAccountId             = errors.New("validate adset account id must be a number greater than 0")
	ErrReloadAdsetID                  = errors.New("validate adset id must be a number greater than 0")
	ErrReloadAdsId                    = errors.New("validate ads id must be a number greater than 0")

	ErrReloadAdsFbNotFound = errors.New("facebook ads not found")

	//reload
	ErrReloadAdAccountId     = errors.New("validate ad account id must be a number greater than 0")
	ErrReloadAdId            = errors.New("validate ad id must be a number greater than 0")
	ErrReloadAdAccountIdAdId = errors.New("validate either account id or ad id must be provided")

	//update
	ErrAdID = errors.New("validate ad id required")

	// list api
	ErrAdDataTableEmpty = errors.New("payload datatable not empty")
)
