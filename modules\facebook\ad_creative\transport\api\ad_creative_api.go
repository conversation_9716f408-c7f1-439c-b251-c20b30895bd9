package api

import (
	"context"
	"godsp/modules/facebook/ad_creative/entity"
	"godsp/modules/facebook/ad_creative/transport/requests"
	"godsp/pkg/gos/utils"
	"godsp/pkg/sctx/core"
	"net/http"

	"github.com/gofiber/fiber/v2"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

type ApiAdCreativeUsc interface {
	ReloadDetailAdCreativeUsc(ctx context.Context, creativeID string, userId primitive.ObjectID, clientId primitive.ObjectID) error
	ReloadListOfAdAccountCreativeUsc(ctx context.Context, adAccount string, userId primitive.ObjectID, clientId primitive.ObjectID) []string

	DetailAdCreativeUsc(ctx context.Context, payload *requests.DetailAdCreativeReq) (*entity.AdCreativeEntity, error)
	CreateAdCreativeUsc(ctx context.Context, payload *requests.CreateAdCreativeReq) (*string, error)
	GenerateAdCreativePreviewUsc(ctx context.Context, payload *requests.PreviewAdCreativeReq) (string, error)
	GenerateInstagramAdCreativePreviewUsc(ctx context.Context, payload *requests.PreviewAdCreativeReq) (string, error)

	DeleteAdCreativeUsc(ctx context.Context, payload *requests.DeleteAdCreativeReq) error
}

type adCreativeApi struct {
	usc ApiAdCreativeUsc
}

func NewAdCreativeApi(usc ApiAdCreativeUsc) *adCreativeApi {
	return &adCreativeApi{
		usc: usc,
	}
}

/**
 * Reload creatve
 */
func (a *adCreativeApi) ReloadAdCreativeApi() fiber.Handler {
	return func(c *fiber.Ctx) error {
		var payload requests.ReloadAdCreativeReq
		if err := c.BodyParser(&payload); err != nil {
			return core.ReturnErrsForApi(c, err)
		}

		//validate
		validationErrors := payload.Validate()
		if len(validationErrors) > 0 {
			return core.ReturnErrsForApi(c, validationErrors)
		}

		userId, _, clientId, _, err := utils.GetInfoUser(c.Context())
		if err != nil {
			return core.ReturnErrsForApi(c, err)
		}

		if payload.CreativeID != "" {
			if err := a.usc.ReloadDetailAdCreativeUsc(c.Context(), payload.CreativeID, userId, clientId); err != nil {
				return core.ReturnErrForApi(c, err.Error())
			}
		} else {
			if errs := a.usc.ReloadListOfAdAccountCreativeUsc(c.Context(), payload.AccountID, userId, clientId); errs != nil {
				return core.ReturnErrsForApi(c, errs)
			}
		}

		return c.Status(http.StatusOK).JSON(core.ResponseData(map[string]interface{}{
			"msg": "Reload Ad Creative successfully",
		}))
	}
}

/**
 * Detail creative
 */
func (a *adCreativeApi) DetailAdCreativeApi() fiber.Handler {
	return func(c *fiber.Ctx) error {
		var payload requests.DetailAdCreativeReq
		if err := c.BodyParser(&payload); err != nil {
			return core.ReturnErrsForApi(c, err)
		}

		if err := payload.Validate(); err != nil {
			return core.ReturnErrsForApi(c, err)
		}

		creative, err := a.usc.DetailAdCreativeUsc(c.Context(), &payload)

		if err != nil {
			return core.ReturnErrsForApi(c, err)
		}

		return c.Status(http.StatusOK).JSON(core.ResponseData(map[string]interface{}{
			"msg":  "detail Ad Creative successfully",
			"data": creative,
		}))

	}
}

/**
 * Reload creatve
 */
func (a *adCreativeApi) CreateAdCreativeApi() fiber.Handler {
	return func(c *fiber.Ctx) error {
		var payload requests.CreateAdCreativeReq

		if err := c.BodyParser(&payload); err != nil {
			return core.ReturnErrsForApi(c, err)
		}

		userId, _, clientId, adAccountId, err := utils.GetInfoUser(c.Context())
		if err != nil {
			return core.ReturnErrsForApi(c, err)
		} else {
			payload.UserID = userId
			payload.AdAccountID = adAccountId
			payload.ClientID = clientId
		}

		if err := payload.Validate(); err != nil {
			return core.ReturnErrsForApi(c, err)
		}

		creativeId, err := a.usc.CreateAdCreativeUsc(c.Context(), &payload)
		if err != nil {
			return core.ReturnErrForApi(c, err.Error())
		}

		return c.Status(http.StatusOK).JSON(core.ResponseData(map[string]interface{}{
			"msg":         "Create Ad Creative successfully",
			"creative_id": *creativeId,
		}))
	}
}

/***
 * Delete Creative ad
 */
func (a *adCreativeApi) DeleteAdCreativeApi() fiber.Handler {
	return func(c *fiber.Ctx) error {
		var payload requests.DeleteAdCreativeReq
		if err := c.BodyParser(&payload); err != nil {
			return core.ReturnErrsForApi(c, err)
		}

		if err := payload.Validate(); err != nil {
			return core.ReturnErrsForApi(c, err)
		}

		if err := a.usc.DeleteAdCreativeUsc(c.Context(), &payload); err != nil {
			return core.ReturnErrForApi(c, err.Error())
		}

		return c.Status(http.StatusOK).JSON(core.ResponseData(map[string]interface{}{
			"msg": "Delete Ad Creative successfully",
		}))
	}
}

/**
 * Generate Ad Creative Preview
 */
func (a *adCreativeApi) GenerateAdCreativePreviewApi() fiber.Handler {
	return func(c *fiber.Ctx) error {
		var payload requests.PreviewAdCreativeReq
		if err := c.BodyParser(&payload); err != nil {
			return core.ReturnErrsForApi(c, err)
		}

		// jsonData, err := json.MarshalIndent(payload, "", "  ")
		// if err != nil {
		// 	fmt.Println("Error marshaling struct:", err)
		// } else {
		// 	fmt.Println("\n-------- payload preview -------> \n", string(jsonData))
		// }

		if err := payload.Validate(); err != nil {
			return core.ReturnErrsForApi(c, err)
		}

		previewURL, err := a.usc.GenerateAdCreativePreviewUsc(c.Context(), &payload)
		if err != nil {
			return core.ReturnErrsForApi(c, err)
		}

		return c.Status(http.StatusOK).JSON(core.ResponseData(map[string]interface{}{
			"msg":  "Generate Ad Creative Preview successfully",
			"data": previewURL,
		}))

	}
}

/**
 * Generate Instagram Ad Creative Preview
 */
func (a *adCreativeApi) GenerateInstagramAdCreativePreviewApi() fiber.Handler {
	return func(c *fiber.Ctx) error {
		var payload requests.PreviewAdCreativeReq
		if err := c.BodyParser(&payload); err != nil {
			return core.ReturnErrsForApi(c, err)
		}

		if err := payload.Validate(); err != nil {
			return core.ReturnErrsForApi(c, err)
		}

		previewURL, err := a.usc.GenerateInstagramAdCreativePreviewUsc(c.Context(), &payload)
		if err != nil {
			return core.ReturnErrsForApi(c, err)
		}

		return c.Status(http.StatusOK).JSON(core.ResponseData(map[string]interface{}{
			"msg":  "Generate Ad Creative Preview successfully",
			"data": previewURL,
		}))

	}
}
