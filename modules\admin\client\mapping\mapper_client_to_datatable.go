package mapping

import (
	"fmt"
	"godsp/conf"
	"godsp/modules/admin/client/entity"
	"godsp/modules/admin/client/transport/responses"
	"godsp/pkg/gos/goconst"
	"godsp/pkg/gos/utils"
)

/***
 * Mapper user entity to datatable
 */
func MapperClientToDatatable(clients *[]entity.ClientEntity) *[]responses.ClientDataTable {
	if clients == nil {
		return nil
	}

	var datas []responses.ClientDataTable
	for _, user := range *clients {
		img := fmt.Sprintf("/static/%s", conf.PathImgUserDefault)
		if user.Logo != "" {
			img = fmt.Sprintf("/static/%s", user.Logo)
		}
		userDatatable := responses.ClientDataTable{
			ID:        user.ID,
			Name:      user.Name,
			Logo:      img,
			Company:   user.Company,
			Phone:     user.Phone,
			Brand:     user.Brand,
			Email:     user.Email,
			Domain:    user.Domain,
			Position:  user.Position,
			Address:   user.Address,
			Status:    user.Status,
			CreatedAt: utils.FormatTimeToString(user.CreatedAt, goconst.YYYY_MM_DD_HH_MM),
			UpdatedAt: utils.FormatTimeToString(user.UpdatedAt, goconst.YYYY_MM_DD_HH_MM),
		}

		datas = append(datas, userDatatable)
	}

	return &datas
}
