package mapping

import (
	"godsp/modules/facebook/ad/entity"
	"godsp/modules/facebook/ad/transport/requests"
	"godsp/modules/facebook/common/fbenums"
	v20 "godsp/pkg/fb-marketing/marketing/v20"
	"time"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

func MapperUpdateAdRequestToAds(payload *requests.UpdateAdReq) (*entity.AdEntity, *v20.Ad) {
	adFB := v20.Ad{
		ID:            payload.AdID,
		AccountID:     payload.AccountID,
		Name:          payload.Name,
		Status:        payload.Status,
		AdsetID:       payload.AdsetId,
		TrackingSpecs: payload.TrackingSpecs,
	}
	if payload.Creative != nil {

	}

	now := time.Now()
	adEntity := entity.AdEntity{
		ID:            primitive.NewObjectID(),
		AdID:          payload.AdID,
		AccountID:     payload.AccountID,
		AdsetID:       payload.AdsetId,
		Name:          payload.Name,
		TrackingSpecs: payload.TrackingSpecs,
		Status:        payload.Status,
		CreatedBy:     payload.UserId,
		CreatedAt:     now,
		UpdatedAt:     now,
		UpdatedBy:     payload.UserId,
	}

	if payload.Creative != nil && payload.Creative.CreativeID != "" {
		adFB.Creative = &v20.AdCreative{
			CreativeID: payload.Creative.CreativeID,
		}
		adEntity.CreativeID = payload.Creative.CreativeID
	}

	return &adEntity, &adFB
}

func MapperUpdateNameStatusAdReqToAd(payload *requests.UpdateNameStatusAdReq, accountId string) (bson.M, bson.M) {

	adFB := make(bson.M)
	dataUpdate := make(bson.M)

	if payload.Name != "" {
		adFB["name"] = payload.Name
		dataUpdate["$set"] = bson.M{"name": payload.Name}
	}

	if payload.Status != "" {
		adFB["status"] = fbenums.StatusFB[payload.Status]
		if len(dataUpdate) == 0 {
			dataUpdate["$set"] = bson.M{"status": fbenums.StatusFB[payload.Status]}
		} else {
			dataUpdate["$set"].(bson.M)["status"] = fbenums.StatusFB[payload.Status]
		}

	}

	return adFB, dataUpdate
}
