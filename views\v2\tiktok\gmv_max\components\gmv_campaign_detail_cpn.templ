package components

templ GmvCampaignDetailCpn() {
<div class="row mt-4 fade-in">
	<div class="col-12">
		<div class="card">
			<div class="card-header">
				<div class="d-flex justify-content-between align-items-center">
					<h5 class="card-title mb-0">Overview</h5>
					<div class="d-flex align-items-center gap-2">
						<span class="text-muted">(UTC+07:00) Indochina time</span>
						<div class="d-flex align-items-center gap-2">
							<input type="date" class="form-control form-control-sm" value="2025-06-26"
								style="width: auto;">
							<span class="text-muted">-</span>
							<input type="date" class="form-control form-control-sm" value="2025-07-03"
								style="width: auto;">
						</div>
						<button class="btn btn-outline-secondary btn-sm">
							<i class="ri-calendar-line"></i>
						</button>
						<button class="btn btn-outline-secondary btn-sm">
							<i class="ri-bar-chart-line"></i> Data views
						</button>
					</div>
				</div>
			</div>
		</div>
	</div>
</div>
}