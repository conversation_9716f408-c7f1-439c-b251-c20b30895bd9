package mapping

import (
	"godsp/modules/admin/permission/entity"
	"godsp/modules/admin/permission/transport/responses"
)

/***
 * Mapper permission entity to datatable
 */
func MapperPermissionToDatatable(permissions *[]entity.PermissionEntity) *[]responses.PermissionDataTable {
	if permissions == nil {
		return nil
	}

	var datas []responses.PermissionDataTable
	for _, permission := range *permissions {
		datatable := responses.PermissionDataTable{
			ID:          permission.ID,
			Method:      permission.Method,
			Path:        permission.Path,
			RouteName:   permission.RouteName,
			Group:       permission.Group,
			Module:      permission.Module,
			Description: permission.Description,
			Ordering:    permission.Ordering,
			IsBlock:     permission.IsBlock,
			IsAcp:       permission.IsAcp,
		}

		if permission.Parent != nil {
			datatable.ParentName = permission.Parent.RouteName
		}

		if permission.UserCreated != nil {
			datatable.UserCreated = permission.UserCreated.FullName
		}

		if permission.UserUpdated != nil {
			datatable.UserUpdated = permission.UserUpdated.FullName
		}

		if !permission.ParentID.IsZero() {
			datatable.ParentID = permission.ParentID
		}

		datas = append(datas, datatable)
	}

	return &datas
}
