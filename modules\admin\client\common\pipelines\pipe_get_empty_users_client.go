package pipelines

import (
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

func PipelineUserOfClient(clientId primitive.ObjectID) []bson.M {
	pipeline := []bson.M{
		{
			"$lookup": bson.M{
				"from":         "admin_roles",
				"localField":   "role_id",
				"foreignField": "_id",
				"as":           "role",
			},
		},
		{
			"$unwind": bson.M{
				"path":                       "$role",
				"includeArrayIndex":          "string",
				"preserveNullAndEmptyArrays": true,
			},
		},
		{
			"$match": bson.M{
				"client_id": clientId,
			},
		},
		{
			"$sort": bson.M{
				"created_at": -1,
			},
		},
		{
			"$project": bson.M{
				"_id":       1,
				"name":      1,
				"full_name": 1,
				"email":     1,
				"image":     1,
				"status":    1,
				"client_id": 1,
				"role_id":   1,
				"role": bson.M{
					"role_name": "$role.role_name",
				},
			},
		},
	}

	// var prettyDocs []bson.M
	// for _, doc := range pipeline {
	// 	bsonDoc, err := bson.Marshal(doc)
	// 	if err != nil {
	// 		panic(err)
	// 	}

	// 	var prettyDoc bson.M
	// 	err = bson.Unmarshal(bsonDoc, &prettyDoc)
	// 	if err != nil {
	// 		panic(err)
	// 	}

	// 	prettyDocs = append(prettyDocs, prettyDoc)
	// }

	// prettyJSON, err := json.MarshalIndent(prettyDocs, "", "  ")
	// if err != nil {
	// 	panic(err)
	// }

	// fmt.Println("\n \n \n xxxx-------helo------>Debug pipeline filter: %s \n \n \n ", string(prettyJSON))

	return pipeline
}
