package requests

import (
	"godsp/modules/facebook/adset/common/enums"
	"godsp/modules/facebook/adset/common/rules"
	"godsp/modules/facebook/common/fbenums"
	"godsp/modules/facebook/common/fbrules"
	"godsp/pkg/fb-marketing/fb"
	v20 "godsp/pkg/fb-marketing/marketing/v20"
	"godsp/pkg/gos/utils"
	"strings"
	"time"

	"github.com/go-playground/validator/v10"
	"go.mongodb.org/mongo-driver/bson/primitive"

	clientErrs "godsp/modules/admin/client/common/errs"
	"godsp/modules/facebook/adset/common/errs"
)

type CreateAdsetReq struct {
	AccountID        string `json:"account_id" validate:"required"`
	CampaignID       string `json:"campaign_id" validate:"required"`
	Name             string `json:"name" validate:"required"`
	OptimizationGoal string `json:"optimization_goal" validate:"ruleOptimizationGoal"`
	// 	DestinationType  string              `json:"destination_type" validate:"ruleDestinationType"` // TODO validate it
	DestinationType       string                     `json:"destination_type"`
	PromotedObject        *v20.PromotedObject        `json:"promoted_object" bson:"promoted_object"`
	BillingEvent          string                     `json:"billing_event"`
	BidStrategy           string                     `json:"bid_strategy"`
	Status                string                     `json:"status" validate:"required,ruleStatus"`
	StartTime             fb.Time                    `json:"start_time" validate:"required,gt_now"`
	FrequencyControlSpecs []v20.FrequencyControlSpec `json:"frequency_control_specs,omitempty"`
	EndTime               *fb.Time                   `json:"end_time,omitempty"`
	Targeting             *v20.Targeting             `json:"targeting" bson:"targeting"`

	DailyBudget    *float64 `json:"daily_budget,omitempty" validate:"omitempty,gte=0"`
	LifetimeBudget *float64 `json:"lifetime_budget,omitempty" validate:"omitempty,gte=0"`
	BidAmount      *uint64  `json:"bid_amount,omitempty" validate:"omitempty,gte=0"`

	LifetimeMinSpendTarget *uint64 `json:"lifetime_min_spend_target,omitempty" validate:"omitempty,gte=0"`
	LifeTimeSpendCap       *uint64 `json:"lifetime_spend_cap,omitempty" validate:"omitempty,gte=0"`
	LifetimeImps           *uint64 `json:"lifetime_imps,omitempty" validate:"omitempty,gte=0"`

	DailyMinSpendTarget *uint64 `json:"daily_min_spend_target,omitempty" validate:"omitempty,gte=0"`
	DailySpendCap       *uint64 `json:"daily_spend_cap,omitempty" validate:"omitempty,gte=0"`

	IsBudgetScheduleEnabled bool                    `json:"is_budget_schedule_enabled,omitempty"`
	BudgetScheduleSpecs     *[]v20.HighDemandPeriod `json:"budget_schedule_specs,omitempty" validate:"omitempty"`

	UserId primitive.ObjectID `json:"-"`

	ClientIDStr string             `json:"client_id" form:"client_id"`
	ClientID    primitive.ObjectID `json:"-" form:"-"`
}

func (req *CreateAdsetReq) Validate() []string {
	validate := validator.New()

	validate.RegisterValidation("ruleStatus", fbrules.RuleFBStatus)
	validate.RegisterValidation("ruleOptimizationGoal", rules.RuleOptimizationGoal)
	validate.RegisterValidation("ruleDestinationType", rules.RuleDestinationType)
	validate.RegisterValidation("ruleBidStrategy", rules.RuleBidStrategy)

	validate.RegisterValidation("gt_now", gtNow)
	validate.RegisterValidation("gt_time_start", gtTimeStart)

	if req.EndTime != nil {
		rule := map[string]string{
			"EndTime": `validate:"gt_now,gt_time_start`,
		}
		validate.RegisterStructValidationMapRules(rule, gtNow)
		validate.RegisterStructValidationMapRules(rule, gtTimeStart)
	}

	var validationErrors []string
	req.Name = strings.TrimSpace(req.Name)

	err := validate.Struct(req)
	if err != nil {
		for _, err := range err.(validator.ValidationErrors) {
			switch err.Field() {
			case "AccountID":
				validationErrors = append(validationErrors, errs.ErrAdsetAccountID.Error())
			case "CampaignID":
				validationErrors = append(validationErrors, errs.ErrAdsetCampaignID.Error())
			case "Name":
				validationErrors = append(validationErrors, errs.ErrAdsetName.Error())
			case "OptimizationGoal":
				validationErrors = append(validationErrors, errs.ErrAdsetOptimizationGoal.Error())
			case "DestinationType":
				validationErrors = append(validationErrors, errs.ErrAdsetDestinationType.Error())
			case "PromotedObject":
				validationErrors = append(validationErrors, errs.ErrAdsetPromotedObject.Error())
			case "DailyBudget":
				validationErrors = append(validationErrors, errs.ErrAdsetDailyBudget.Error())
			case "LifetimeBudget":
				validationErrors = append(validationErrors, errs.ErrAdsetDailyBudget.Error())
			case "LifetimeMinSpendTarget":
				validationErrors = append(validationErrors, errs.ErrAdsetLifetimeMinSpendTarget.Error())
			case "LifeTimeSpendCap":
				validationErrors = append(validationErrors, errs.ErrAdsetLifeTimeSpendCap.Error())
			case "BidAmount":
				validationErrors = append(validationErrors, errs.ErrAdsetBidAmount.Error())
			case "BillingEvent":
				validationErrors = append(validationErrors, errs.ErrAdsetBillingEvent.Error())
			case "BidStrategy":
				validationErrors = append(validationErrors, errs.ErrAdsetBidStrategy.Error())
			case "Status":
				validationErrors = append(validationErrors, errs.ErrAdsetStatus.Error())
			case "StartTime":
				validationErrors = append(validationErrors, errs.ErrAdsetStartTime.Error())
			case "EndTime":
				validationErrors = append(validationErrors, errs.ErrAdsetEndTime.Error())
			case "Targeting":
				validationErrors = append(validationErrors, errs.ErrAdsetTargeting.Error())
			}
		}
	}

	if req.ClientIDStr == "" {
		validationErrors = append(validationErrors, clientErrs.ErrClientIdEmpty.Error())
	}

	if req.ClientIDStr != "" && !utils.ValidateObjectID(req.ClientIDStr) {
		validationErrors = append(validationErrors, clientErrs.ErrIDClientValidate.Error())
	}

	if req.ClientIDStr != "" {
		clientID, _ := primitive.ObjectIDFromHex(req.ClientIDStr)
		req.ClientID = clientID
	}

	if validationErrors == nil {
		req.Status = fbenums.StatusFB[req.Status]
		req.OptimizationGoal = enums.OptimizationGoal[req.OptimizationGoal]

		if req.DestinationType != "" {
			req.DestinationType = enums.DestinationType[req.DestinationType]
		}
	}

	return validationErrors

}

func gtNow(fl validator.FieldLevel) bool {
	t, ok := fl.Field().Interface().(fb.Time)
	if !ok {
		return false
	}
	return time.Time(t).After(time.Now())
}

func gtTimeStart(fl validator.FieldLevel) bool {
	req, ok := fl.Top().Interface().(*CreateAdsetReq)
	if !ok {
		return false
	}
	if req.EndTime == nil {
		return true
	}
	return time.Time(*req.EndTime).After(time.Time(req.StartTime))
}
