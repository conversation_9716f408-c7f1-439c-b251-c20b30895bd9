package pipelines

import (
	"godsp/conf"
	"godsp/modules/facebook/common/fbenums"
	"godsp/modules/facebook/iface"

	"go.mongodb.org/mongo-driver/bson"
)

func GetDetailAds(adId string, user iface.UserInfoFb) []bson.M {
	filter := []bson.M{
		{
			"ad_id": adId,
		},
		{
			"status": bson.M{
				"$nin": []string{fbenums.FB_STATUS_ARCHIVED, fbenums.FB_STATUS_DELETED},
			},
		},
	}
	if user.RoleName != conf.SysConf.RoleAdmin {
		// filter = append(filter, bson.M{
		// 	"account_id": user.AdAccountId,
		// })
		if user.RoleName == conf.SysConf.RoleClientAdmin || user.RoleName == conf.SysConf.RoleClientAdminViewer {
			filter = append(filter, bson.M{
				"client_id": user.ClientId,
			})
		} else {
			filter = append(filter, bson.M{
				"list_user_ids": user.UserId,
				"client_id":     user.ClientId,
			})
		}
	}
	// if role != "ADMIN" {
	// 	or = bson.A{
	// 		bson.M{"ad_id": adId},
	// 		bson.M{"list_user_ids": userId},
	// 	}
	// }
	return []bson.M{
		{
			"$match": bson.M{
				"$and": filter,
			},
		},
		{
			"$lookup": bson.M{
				"from":         "fb_campaigns",
				"localField":   "campaign_id",
				"foreignField": "campaign_id",
				"as":           "campaign_info",
			},
		},
		{
			"$addFields": bson.M{
				"campaign": bson.M{
					"$arrayElemAt": bson.A{
						"$campaign_info",
						0,
					},
				},
			},
		},
		{
			"$lookup": bson.M{
				"from":         "fb_adsets",
				"localField":   "adset_id",
				"foreignField": "adset_id",
				"as":           "adset_info",
			},
		},
		{
			"$addFields": bson.M{
				"ad_set": bson.M{
					"$arrayElemAt": bson.A{
						"$adset_info",
						0,
					},
				},
			},
		},
	}
}
