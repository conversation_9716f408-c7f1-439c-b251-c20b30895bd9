
db.fb_campaigns.aggregate([
    // 1. Lookup từ fb_adsets để lấy adsets liên kết với campaign
    {
        $lookup: {
            from: "fb_adsets",
            localField: "campaign_id",
            foreignField: "campaign_id",
            as: "adsets",
        },
    },
    // 2. Lookup từ fb_ads để lấy ads liên kết với adsets
    {
        $lookup: {
            from: "fb_ads",
            localField: "adsets.adset_id",
            foreignField: "adset_id",
            as: "ads",
        },
    },
    // 3. Lọc theo giá trị đầu vào (campaign_id, adset_id, hoặc ad_id)
    {
        $match: {
            $or: [
                //         { "campaign_id": "120209892499530180" },  // Thay bằng campaign_id nếu có
                //         { "adsets.adset_id": "120209892499550180" },        // Thay bằng adset_id nếu có
                { "ads.ad_id": "120210152533740180" }, // Thay bằng ad_id nếu có
            ],
        },
    },
    // 4. <PERSON><PERSON><PERSON> cấu trúc adsets để thêm danh sách ads vào từng adset
    {
        $addFields: {
            adsets: {
                $map: {
                    input: "$adsets",
                    as: "adset",
                    in: {
                        adset_id: "$$adset.adset_id",
                        name: "$$adset.name",
                        ads: {
                            $filter: {
                                input: "$ads",
                                as: "ad",
                                cond: {
                                    $eq: ["$$ad.adset_id", "$$adset.adset_id"], // Lọc theo adset_id
                                },
                            },
                        },
                    },
                },
            },
        },
    },
    // 5. Nhóm ads theo adset_id
    {
        $addFields: {
            adsets: {
                $map: {
                    input: "$adsets",
                    as: "adset",
                    in: {
                        adset_id: "$$adset.adset_id",
                        name: "$$adset.name",
                        ads: {
                            $map: {
                                input: "$$adset.ads",
                                as: "ad",
                                in: {
                                    ad_id: "$$ad.ad_id",
                                    name: "$$ad.name",
                                },
                            },
                        },
                    },
                },
            },
        },
    },
    // 6. Xóa trường dư ads ở cấp cao nhất
    {
        $unset: "ads",
    },
    // 7. Chỉ loại bỏ _id và giữ lại toàn bộ các trường khác
    {
        $project: {
            _id: 0,
        },
    },
]);
