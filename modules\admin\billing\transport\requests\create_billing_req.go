package requests

import (
	"strings"

	"github.com/go-playground/validator/v10"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

type PayloadBillingCreation struct {
	Description string   `json:"description,omitempty" validate:"required"`
	Amount      []int64  `json:"amount" validate:"required"`
	AdAccount   []string `json:"account_name" validate:"required"`
	Currency    string   `json:"currency" validate:"required,oneof=USD VND"`
	Balance     int64    `json:"balance,omitempty"`
	Status      int      `json:"status,omitempty"`
	Type        int      `json:"type,omitempty"`
	Ordering    int64    `json:"ordering,omitempty"`

	UserID primitive.ObjectID `json:"-"`
}

func (req *PayloadBillingCreation) Validate() []*string {
	validate := validator.New()
	var validationErrors []*string
	err := validate.Struct(req)
	if err != nil {
		for _, err := range err.(validator.ValidationErrors) {
			tag := err.Tag()
			switch tag {
			case "required":
				err := "field " + err.Field() + " is required"
				validationErrors = append(validationErrors, &err)
			case "oneof":
				err := "field " + err.Field() + " is invalid"
				validationErrors = append(validationErrors, &err)
			case "gt":
				err := "field " + err.Field() + " must be greater than 0"
				validationErrors = append(validationErrors, &err)
			default:
				err := "field " + err.Field() + " is invalid"
				validationErrors = append(validationErrors, &err)
			}
		}
	}
	if validationErrors == nil {
		req.Description = strings.TrimSpace(req.Description)
	}
	return validationErrors
}
