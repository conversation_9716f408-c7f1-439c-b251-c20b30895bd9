import { SUB_TYPE_AUDIENCE_NAME } from "/static/js/pages/audiences/constant.js";
import Alert from "/static/js/components/alert.js";
import { ACT } from "/static/js/constants/common-constant.js";
import { AudienceTypeLookalike } from "/static/js/tiktok/constants/audience.js";

const noResultHTML = `<div class="no-results">
        <img src="/static/images/no-data.png" alt="No Results" style="max-width: 250px; margin-bottom: 10px;">
        <p>No Data</p>
        </div>`;

const AUDIENCE_API_ENDPOINTS = {
    GET_LIST_AUDIENCES: `${window.location.origin}/dsp/tiktok/api/audience/list`,
}
const ID_ADGOUP_FORM = "#adgroupForm";
const mapContentId = {
    include: {
        all: "content_all_audience",
        custom: "content_custom_audience",
        lookalike: "content_lookalike_audience",
    },
    exclude: {
        all: "content_exclude_all_audience",
        custom: "content_exclude_custom_audience",
        lookalike: "content_exclude_lookalike_audience",
    },
}

export default (() => {
    async function handleGetAudiences(value = "") {
        try {
            const advertiserID = $("#advertiserChoices").val() || null;

            const response = await axios.post(AUDIENCE_API_ENDPOINTS.GET_LIST_AUDIENCES, {
                advertiser_id: advertiserID,
                search_value: value,
            });

            return mappingData(response.data.data.data || []);
        } catch (error) {
            Alert.error(error?.response?.data?.message ?? "Please try again!");
            return [];
        }
    };


    /**
     * Mapping Data Request to UI Audience
     */
    const mappingData = (data) => {
        const dataReturn = {
            all: [],
            custom: [],
            lookalike: [],
        };
        data.forEach((item) => {
            dataReturn.all.push(item);
            if (item.type === AudienceTypeLookalike) {
                dataReturn.lookalike.push(item);
            } else {
                dataReturn.custom.push(item);
            }
        });

        return dataReturn;
    };

    /**
     * Custom Include Audience
     */
    const renderUISourceAudienceSelect = () => {
        const $input = $("#selectedAudiences");
        const inputPath = "#selectedAudiences";
        const $popover = $("#searchSourceAudiencePopover");
        const resultsSelectorPath = "#audience_results_selected";
        const $tabLookalikeAudience = $("#content_lookalike_audience");
        const $tabCustomAudience = $("#content_custom_audience");
        const $tabAllAudience = $("#content_all_audience");

        // Show popover
        $input.on("focus", () => $popover.show());

        // Hide popover if click outside
        $(document).on("click", (e) => {
            if (!$(e.target).closest($input).length && !$(e.target).closest($popover).length) {
                $popover.hide();
            }
        });

        // Clear only input value (search keyword)
        $(document).on("click", "#btn-clear-include-audience-search", () => {
            $input.val("");
            handleSearch(); // Optional: clear result also
        });

        function getSelectedItems() {
            return JSON.parse($input.attr("data-value") || "{}");
        }

        function setSelectedItems(selected) {
            $input.attr("data-value", JSON.stringify(selected));
        }

        function isSelected(item) {
            const selected = getSelectedItems();
            return selected[item.type]?.some((i) => i.id === item.id);
        }

        function syncCheckboxAndDisable(id, isChecked) {
            $(`#searchSourceAudiencePopover .list-group-item[data-id="${id}"] input[type='checkbox']`).prop("checked", isChecked);
            const $excludeItem = $(`#excludeSearchSourceAudiencePopover .list-group-item[data-id="${id}"]`);
            $excludeItem.toggleClass("disabled", isChecked);
            $excludeItem.find("input[type='checkbox']").prop("disabled", isChecked);
        }

        function addSelected(newItem) {
            const selected = getSelectedItems();
            if (!selected[newItem.type]) selected[newItem.type] = [];
            if (!isSelected(newItem)) {
                selected[newItem.type].push(newItem);
                setSelectedItems(selected);
            }
        }

        function removeSelectedItem({ id, type }) {
            const selected = getSelectedItems();
            if (!selected[type]) return;

            selected[type] = selected[type].filter(item => item.id !== id);
            if (selected[type].length === 0) delete selected[type];
            setSelectedItems(selected);

            const total = Object.values(selected).reduce((sum, list) => sum + list.length, 0);
            if (total === 0) $(resultsSelectorPath).hide();
        }

        async function handleSearch() {
            const query = $input.val();
            const data = await handleGetAudiences(query);
            renderAudiences(data, mapContentId.include);
            syncSelectedAudienceCheckboxStates();
        }

        $input.on("input", handleSearch);
        $input.on("focus", () => {
            if (!$tabAllAudience.html() && !$tabLookalikeAudience.html() && !$tabCustomAudience.html()) {
                handleSearch();
            }
        });

        // Handle audience item click
        $(`${ID_ADGOUP_FORM} #searchSourceAudiencePopover`).on("click", ".list-group-item", function () {
            const $this = $(this);
            const audience = {
                id: $this.attr("data-id"),
                name: $this.attr("data-name"),
                type: $this.attr("data-type"),
            };

            const $checkbox = $this.find("input[type='checkbox']");
            const isChecked = $checkbox.prop("checked");

            if (isChecked) {
                addSelected(audience);
            } else {
                removeSelectedItem(audience);
            }

            syncSelectedAudienceCheckboxStates();
            renderAudienceSelectedItems(resultsSelectorPath, inputPath);
        });

        // Handle remove selected
        $(`${ID_ADGOUP_FORM} ${resultsSelectorPath}`).on("click", ".btn-remove-item", function () {
            const $item = $(this).closest(".selected-item");
            const audience = {
                id: $item.attr("data-id"),
                type: $item.attr("data-type"),
            };

            $item.remove();
            removeSelectedItem(audience);
            syncCheckboxAndDisable(audience.id, false);
            renderAudienceSelectedItems(resultsSelectorPath, inputPath);
        });
    };



    /**
     * Custom Exclude Audience
     */
    const renderUISourceExcludeAudienceSelect = () => {
        const $input = $("#excludeSelectedAudiences");
        const inputPath = "#excludeSelectedAudiences";
        const $popover = $("#excludeSearchSourceAudiencePopover");
        const resultsSelectorPath = "#audience_results_exclude_selected";
        const $tabLookalikeAudience = $("#content_exclude_lookalike_audience");
        const $tabCustomAudience = $("#content_exclude_custom_audience");
        const $tabAllAudience = $("#content_exclude_all_audience");

        // Show popover
        $input.on("focus", () => $popover.show());

        // Hide popover if click outside
        $(document).on("click", (e) => {
            if (!$(e.target).closest($input).length && !$(e.target).closest($popover).length) {
                $popover.hide();
            }
        });

        // Clear search input
        $(document).on("click", "#btn-clear-exclude-audience-search", () => {
            $input.val("");
            handleSearch();
        });

        function getSelectedItems() {
            return JSON.parse($input.attr("data-value") || "{}");
        }

        function setSelectedItems(selected) {
            $input.attr("data-value", JSON.stringify(selected));
        }

        function isSelected(item) {
            const selected = getSelectedItems();
            return selected[item.type]?.some((i) => i.id === item.id);
        }

        function syncCheckboxAndDisable(id, isChecked) {
            $(`#excludeSearchSourceAudiencePopover .list-group-item[data-id="${id}"] input[type='checkbox']`).prop("checked", isChecked);
            const $includeItem = $(`#searchSourceAudiencePopover .list-group-item[data-id="${id}"]`);
            $includeItem.toggleClass("disabled", isChecked);
            $includeItem.find("input[type='checkbox']").prop("disabled", isChecked);
        }

        function addSelected(newItem) {
            const selected = getSelectedItems();
            if (!selected[newItem.type]) selected[newItem.type] = [];
            if (!isSelected(newItem)) {
                selected[newItem.type].push(newItem);
                setSelectedItems(selected);
            }
        }

        function removeSelectedItem({ id, type }) {
            const selected = getSelectedItems();
            if (!selected[type]) return;

            selected[type] = selected[type].filter(item => item.id !== id);
            if (selected[type].length === 0) delete selected[type];
            setSelectedItems(selected);

            const total = Object.values(selected).reduce((sum, list) => sum + list.length, 0);
            if (total === 0) $(resultsSelectorPath).hide();
        }

        async function handleSearch() {
            const query = $input.val();
            const data = await handleGetAudiences(query);
            renderAudiences(data, mapContentId.exclude);
            syncSelectedAudienceCheckboxStates();
        }

        $input.on("input", handleSearch);
        $input.on("focus", () => {
            if (!$tabAllAudience.html() && !$tabLookalikeAudience.html() && !$tabCustomAudience.html()) {
                handleSearch();
            }
        });

        // Handle item click
        $(`#excludeSearchSourceAudiencePopover`).on("click", ".list-group-item", function () {
            const $this = $(this);
            const audience = {
                id: $this.attr("data-id"),
                name: $this.attr("data-name"),
                type: $this.attr("data-type"),
            };

            const $checkbox = $this.find("input[type='checkbox']");
            const isChecked = $checkbox.prop("checked");

            if (isChecked) {
                addSelected(audience);
            } else {
                removeSelectedItem(audience);
            }

            syncSelectedAudienceCheckboxStates();
            renderAudienceSelectedItems(resultsSelectorPath, inputPath);
        });

        // Handle remove selected
        $(`${ID_ADGOUP_FORM} ${resultsSelectorPath}`).on("click", ".btn-remove-item", function () {
            const $item = $(this).closest(".selected-item");
            const audience = {
                id: $item.attr("data-id"),
                type: $item.attr("data-type"),
            };

            $item.remove();
            removeSelectedItem(audience);
            syncCheckboxAndDisable(audience.id, false);
            renderAudienceSelectedItems(resultsSelectorPath, inputPath);
        });
    };


    function renderAudiences(data, renderMap) {
        const renderList = (audiences, containerId) => {
            const container = document.getElementById(containerId);
            if (!container) return;

            container.innerHTML = "";

            if (!audiences || audiences.length === 0) {
                container.innerHTML = noResultHTML;
                return;
            }

            audiences.forEach((aud) => {
                const li = document.createElement("li");
                li.className = "list-group-item d-flex gap-2 flex-column cursor-pointer align-items-start";
                li.setAttribute("data-id", aud.audience_id);
                li.setAttribute("data-name", aud.name);
                li.setAttribute("data-type", aud.type);

                li.innerHTML = `
                    <div class="d-flex align-items-start">
                        <input type="checkbox" class="form-check-input mt-1 me-2 audience-checkbox">
                        <div>
                            <div class="fw-semibold">${aud.name}</div>
                            <div class="text-muted small">
                                Size: ${aud.cover_num?.toLocaleString() ?? 0} |
                                Date created: ${aud.create_time}
                            </div>
                        </div>
                    </div>
                `;

                container.appendChild(li);
            });
        };

        Object.entries(renderMap).forEach(([group, containerId]) => {
            const list = data[group] || [];
            renderList(list, containerId);
        });
    }

    const renderAudienceSelectedItems = (containerSelector, inputSelector) => {
        const selectedRaw = $(inputSelector).attr("data-value") || "{}";
        const selected = JSON.parse(selectedRaw);
        const $container = $(containerSelector);
        $container.empty();

        const groupMap = {
            custom: {
                label: "Custom Audience",
                items: [],
            },
            lookalike: {
                label: "Lookalike Audience",
                items: [],
            },
        };

        Object.entries(selected).forEach(([type, items]) => {
            if (!Array.isArray(items)) return;

            items.forEach((item) => {
                const groupType = item.type === AudienceTypeLookalike ? "lookalike" : "custom";
                groupMap[groupType].items.push(item);
            });
        });

        Object.values(groupMap).forEach((group) => {
            if (group.items.length === 0) return;

            const $group = $('<div>', { class: 'group-item mb-2' });
            $group.append($('<div>', {
                class: 'header text-secondary mb-1',
                text: `${group.items.length} ${group.label}`,
            }));

            group.items.forEach((item) => {
                const $item = $('<div>', {
                    class: 'selected-item d-flex justify-content-between align-items-center py-1 px-2 border rounded mb-1',
                    'data-id': item.id,
                    'data-name': item.name,
                    'data-type': item.type,
                });

                $item.append($('<div>').text(item.name));
                $item.append($('<i>', {
                    class: 'ri-close-circle-fill btn-remove-item text-danger',
                    style: 'cursor: pointer;',
                }));

                $group.append($item);
            });

            $container.append($group);
        });

        $container.toggle($container.children().length > 0);
    };

    function syncSelectedAudienceCheckboxStates() {
        const includeSelected = JSON.parse($("#selectedAudiences").attr("data-value") || "{}");
        const excludeSelected = JSON.parse($("#excludeSelectedAudiences").attr("data-value") || "{}");

        Object.values(includeSelected).flat().forEach(item => {
            const $excludeItem = $(`#excludeSearchSourceAudiencePopover .list-group-item[data-id="${item.id}"]`);
            $excludeItem.addClass("disabled");
            $excludeItem.find("input[type='checkbox']").prop("disabled", true);
        });

        Object.values(excludeSelected).flat().forEach(item => {
            const $includeItem = $(`#searchSourceAudiencePopover .list-group-item[data-id="${item.id}"]`);
            $includeItem.addClass("disabled");
            $includeItem.find("input[type='checkbox']").prop("disabled", true);
        });

        Object.values(includeSelected).flat().forEach(item => {
            const $includeItem = $(`#searchSourceAudiencePopover .list-group-item[data-id="${item.id}"]`);
            $includeItem.find("input[type='checkbox']").prop("checked", true);
        });

        Object.values(excludeSelected).flat().forEach(item => {
            const $excludeItem = $(`#excludeSearchSourceAudiencePopover .list-group-item[data-id="${item.id}"]`);
            $excludeItem.find("input[type='checkbox']").prop("checked", true);
        });
    }


    async function renderAudiencesTab() {
        const data = await handleGetAudiences();

        renderAudiences(data, mapContentId.include);
        renderAudiences(data, mapContentId.exclude);
    }

    async function init() {
        renderAudiencesTab();

        $(document).on("keydown", "#audienceTargeting input[type='text'].form-control", function (e) {
            if (e.key === "Enter") {
                e.preventDefault();
            }
        });

        renderUISourceAudienceSelect();
        renderUISourceExcludeAudienceSelect();
    }

    return {
        init,
    };
})();
