package role

import (
	"context"
	rPermission "godsp/modules/admin/permission/repository/mongo"
	rUser "godsp/modules/admin/user/repository/mongo"

	"godsp/modules/admin/role/repository/mongo"

	"godsp/modules/admin/role/transport/api"
	"godsp/modules/admin/role/transport/handlers"
	"godsp/modules/admin/role/usecase"

	"github.com/dev-networldasia/dspgos/sctx/component/mongodb"

	"github.com/dev-networldasia/dspgos/sctx/configs"

	"github.com/dev-networldasia/dspgos/sctx"

	"github.com/gofiber/fiber/v2"
)

// index mongo for role
type composerIndexMongoRole interface {
	CreateRoleIndex(ctx context.Context) error
}

func SetupIndexMongoRole(serviceCtx sctx.ServiceContext) composerIndexMongoRole {
	mongoDB := serviceCtx.MustGet(configs.KeyCompMongoDB).(mongodb.MongoComponent).GetDatabase()
	repo := mongo.NewRoleRepo(mongoDB)
	return repo
}

type composerRole interface {
	ListRoleHdl() fiber.Handler
	PermissionRoleHdl() fiber.Handler
}

type composerRoleApi interface {
	CreateRoleApi() fiber.Handler
	ListRoleApi() fiber.Handler
	EditRoleApi() fiber.Handler
	ShowPermissionRoleApi() fiber.Handler
	UpdatePermissionRoleApi() fiber.Handler
	DeletesRoleApi() fiber.Handler
}

func composerRoleServive(serviceCtx sctx.ServiceContext) composerRole {
	logger := serviceCtx.MustGet(configs.KeyLoggerDaily).(sctx.AppLoggerDaily).GetLogger("admins")
	mongoDB := serviceCtx.MustGet(configs.KeyCompMongoDB).(mongodb.MongoComponent).GetDatabase()

	repo := mongo.NewRoleRepo(mongoDB)
	permissionRepo := rPermission.NewPermissionRepo(mongoDB)
	usc := usecase.NewRoleUsc(repo, logger, permissionRepo)

	hdl := handlers.NewRoleHdl(usc)
	return hdl
}

func composerRoleApiServive(serviceCtx sctx.ServiceContext) composerRoleApi {
	logger := serviceCtx.MustGet(configs.KeyLoggerDaily).(sctx.AppLoggerDaily).GetLogger("admins")
	mongoDB := serviceCtx.MustGet(configs.KeyCompMongoDB).(mongodb.MongoComponent).GetDatabase()

	repo := mongo.NewRoleRepo(mongoDB)
	permissionRepo := rPermission.NewPermissionRepo(mongoDB)
	userRepo := rUser.NewUserRepo(mongoDB)
	usc := usecase.NewApiRoleUsc(repo, permissionRepo, userRepo, logger)
	api := api.NewRoleApi(usc, logger)

	return api
}

func SetupRoutesRole(app *fiber.App, serviceCtx sctx.ServiceContext, midds ...fiber.Handler) {
	group := app.Group("admins/roles")
	{
		if len(midds) > 0 {
			for _, midd := range midds {
				group.Use(midd)
			}
		}

		comp := composerRoleServive(serviceCtx)
		group.Get("/list", comp.ListRoleHdl()).Name("admins.role.list")
		group.Get("permission/:roleId", comp.PermissionRoleHdl()).Name("admins.role.permission")

	}

	apiGroup := app.Group("api/admins/roles")
	{
		if len(midds) > 0 {
			for _, midd := range midds {
				apiGroup.Use(midd)
			}
		}
		compApi := composerRoleApiServive(serviceCtx)
		apiGroup.Post("/create", compApi.CreateRoleApi()).Name("admins.api.role.create_api")
		apiGroup.Post("/list-datatable", compApi.ListRoleApi()).Name("admins.api.role.list_datatable")
		apiGroup.Put("/edit", compApi.EditRoleApi()).Name("admins.api.role.edit")
		apiGroup.Post("/show-permissions", compApi.ShowPermissionRoleApi()).Name("admins.api.role.show_permissions")
		apiGroup.Post("/update-permission", compApi.UpdatePermissionRoleApi()).Name("admins.api.role.update_permissions")
		apiGroup.Delete("/delete", compApi.DeletesRoleApi()).Name("admins.api.role.delete")
	}
}
