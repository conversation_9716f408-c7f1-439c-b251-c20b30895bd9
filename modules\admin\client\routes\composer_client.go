package routes

import (
	"godsp/modules/admin/client/repository/mongo"
	"godsp/modules/admin/client/transport/handlers"
	"godsp/modules/admin/client/usecase"
	userMongo "godsp/modules/admin/user/repository/mongo"
	adAccR "godsp/modules/facebook/ad_account/repository/mongo"
	pixelR "godsp/modules/facebook/ad_pixel/repository/mongo"
	catalogueR "godsp/modules/facebook/catalogue/repository/mongo"
	pageR "godsp/modules/facebook/pages/repository/mongo"
	tikAdverMongo "godsp/modules/tiktok/advertiser/repository/mongo"
	tikPresetColMongo "godsp/modules/tiktok/custom_column_table/repository/mongo"

	"github.com/dev-networldasia/dspgos/sctx/component/mongodb"

	"github.com/dev-networldasia/dspgos/sctx/configs"

	"github.com/dev-networldasia/dspgos/sctx"

	"github.com/gofiber/fiber/v2"
	"github.com/gofiber/fiber/v2/middleware/session"
)

type ComposerClientHdl interface {
	// ListWebsiteHdl() fiber.Handler
	ListClientHdl() fiber.Handler
	EditClientHdl(store *session.Store) fiber.Handler
}

func ComposerClientHdlService(serviceCtx sctx.ServiceContext) ComposerClientHdl {
	mongoDB := serviceCtx.MustGet(configs.KeyCompMongoDB).(mongodb.MongoComponent).GetDatabase()
	logger := serviceCtx.MustGet(configs.KeyLoggerDaily).(sctx.AppLoggerDaily).GetLogger("webfb")

	repo := mongo.NewClientRepo(mongoDB)
	userRepo := userMongo.NewUserRepo(mongoDB)

	pageRepo := pageR.NewPageRepo(mongoDB)
	pixelRepo := pixelR.NewAdPixelRepo(mongoDB)
	adAccRepo := adAccR.NewAdAccountRepo(mongoDB)
	catalogueRepo := catalogueR.NewCatalogueRepo(mongoDB)
	tikAdverRepo := tikAdverMongo.NewAdvertiserRepo(mongoDB)
	tikPresetColTableRepo := tikPresetColMongo.NewTikTokPresetColumnTableRepo(mongoDB)

	usc := usecase.NewClientUsc(logger, repo, userRepo, adAccRepo, pageRepo, pixelRepo, catalogueRepo, tikAdverRepo, tikPresetColTableRepo)
	hdl := handlers.NewClientHdl(usc)

	return hdl
}
