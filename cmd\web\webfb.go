package web

import (
	"fmt"
	"godsp/cmd/web/internal"
	"godsp/conf"
	"godsp/pkg/gos/templates"
	"godsp/pkg/sctx/component/facebook/fbmarketing"

	"github.com/dev-networldasia/dspgos/sctx"
	"github.com/dev-networldasia/dspgos/sctx/component/discord"
	"github.com/dev-networldasia/dspgos/sctx/component/fiberapp"
	"github.com/dev-networldasia/dspgos/sctx/component/jwtc"
	"github.com/dev-networldasia/dspgos/sctx/component/mongodb"
	"github.com/dev-networldasia/dspgos/sctx/component/redisc"
	"github.com/dev-networldasia/dspgos/sctx/component/watermillapp/redisstream"
	"github.com/dev-networldasia/dspgos/sctx/configs"
	"github.com/gofiber/fiber/v2/middleware/session"
	"github.com/gofiber/template/html/v2"
	"github.com/spf13/cobra"
)

var (
	webFBNameServer = "webfb-service"
	versionSchedule = "1.0.0"
)

func newWebFBServiceCtx() sctx.ServiceContext {
	return sctx.NewServiceContext(
		sctx.WithName(webFBNameServer),
		sctx.WithComponent(fiberapp.NewFiber(configs.KeyCompFIBER)),
		sctx.WithComponent(mongodb.NewMongoDB(configs.KeyCompMongoDB, "")),
		sctx.WithComponent(mongodb.NewMongoDB(conf.KeyCompReportMongoDB, "report")),
		sctx.WithComponent(sctx.NewAppLoggerDaily(configs.KeyLoggerDaily)),
		sctx.WithComponent(redisstream.NewRedisStreamPub(configs.KeyRedisStreamPub)),
		// sctx.WithComponent(telegrams.NewTelegramClient(configs.KeyTelegramSMS)),
		sctx.WithComponent(discord.NewDiscordClient(configs.KeyDiscordSMS)),
		sctx.WithComponent(fbmarketing.NewFBMarketing(configs.KeyFBMarketingV20)),
		sctx.WithComponent(jwtc.NewJWT(configs.KeyCompJWT)),
		sctx.WithComponent(redisc.NewRedisc(configs.KeyCompRedis)),
	)
}

var (
	WebFBCmd = &cobra.Command{
		Use:     "webfb",
		Short:   "web facebook run godsp social",
		Long:    `web facebook CLI Long godsp social`,
		Version: versionSchedule,
		Run: func(cmd *cobra.Command, args []string) {
			fmt.Println("--->> server run <<---")

			serviceCtx := newWebFBServiceCtx()
			loggerSv := serviceCtx.MustGet(configs.KeyLoggerDaily).(sctx.AppLoggerDaily).GetLogger("webfb")
			loggerSv.Info("Web Facebook is running!")

			if err := serviceCtx.Load(); err != nil {
				loggerSv.Fatal(err)
			}

			fiberComp := serviceCtx.MustGet(configs.KeyCompFIBER).(fiberapp.FiberComponent)

			// set template views
			engine := html.New("./views", ".html")
			engine.AddFuncMap(templates.FuncMap())
			engine.Reload(true)

			fiberComp.SetEngineConfig(engine)

			router := fiberComp.GetApp()
			internal.RouterWebFacebook(router, serviceCtx, session.New())

			if err := router.Listen(fmt.Sprintf(":%d", fiberComp.GetPort())); err != nil {
				loggerSv.Fatal(err)
			}
		},
	}
)
