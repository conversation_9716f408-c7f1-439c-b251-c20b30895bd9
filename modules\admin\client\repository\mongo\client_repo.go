package mongo

import (
	"context"
	"errors"
	"godsp/modules/admin/client/entity"
	"godsp/modules/facebook/common/fberrs"
	"godsp/pkg/sctx/core"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
)

type clientRepo struct {
	DB         *mongo.Database
	Collection *mongo.Collection
}

func NewClientRepo(DB *mongo.Database) *clientRepo {
	return &clientRepo{
		DB:         DB,
		Collection: DB.Collection(entity.ClientEntity{}.CollectionName()),
	}
}

func (r *clientRepo) GetMongoDb() *mongo.Database {
	return r.DB
}

/***
 * insert user
 */
func (r *clientRepo) InsertUserRepo(ctx context.Context, user *entity.ClientCreationEntity) (*primitive.ObjectID, error) {
	user.BeforeSave()
	result, err := r.Collection.InsertOne(ctx, user)
	if err != nil {
		if mongo.IsDuplicateKeyError(err) {
			return nil, core.ErrDuplicate
		}
		return nil, err
	}
	userId := result.InsertedID.(primitive.ObjectID)
	return &userId, nil
}

func (r *clientRepo) FindClientsWithPipelineRepo(ctx context.Context, pipeline []bson.M, opts ...*options.AggregateOptions) (*[]entity.ClientEntity, error) {
	var clients []entity.ClientEntity

	cursor, err := r.Collection.Aggregate(ctx, pipeline, opts...)
	if err != nil {
		return nil, err
	}
	defer cursor.Close(ctx)

	if cursor == nil {
		return nil, fberrs.ErrNilCursorValue
	}

	if err = cursor.All(ctx, &clients); err != nil {
		return nil, err
	}

	return &clients, nil
}

/**
 * FindOne Client
 */
func (r *clientRepo) FindOneClientRepo(ctx context.Context, filter interface{}, opts ...*options.FindOneOptions) (*entity.ClientEntity, error) {
	var entity entity.ClientEntity
	err := r.Collection.FindOne(ctx, filter, opts...).Decode(&entity)
	if errors.Is(err, mongo.ErrNoDocuments) {
		return nil, core.ErrNotFound
	}

	return &entity, nil
}

/**
 * FindOne Client
 */
func (r *clientRepo) FindClientRepo(ctx context.Context, filter interface{}, opts ...*options.FindOptions) ([]*entity.ClientEntity, error) {

	var clients []*entity.ClientEntity

	cursor, err := r.Collection.Find(ctx, filter, opts...)
	if err != nil {
		return nil, err
	}
	defer cursor.Close(ctx)

	if cursor == nil {
		return nil, fberrs.ErrNilCursorValue
	}
	err = cursor.All(ctx, &clients)
	if err != nil {
		return nil, err
	}

	return clients, nil
}

// /**
//   - Find one user with pipeline
//     */
func (r *clientRepo) FindOneClientWithPipelineRepo(ctx context.Context, pipeline []bson.M, opts ...*options.AggregateOptions) (*entity.ClientEntity, error) {
	cursor, err := r.Collection.Aggregate(ctx, pipeline, opts...)
	if err != nil {
		return nil, err
	}
	defer cursor.Close(ctx)

	if cursor == nil {
		return nil, fberrs.ErrNilCursorValue
	}

	if cursor.Next(ctx) {
		var userEntity entity.ClientEntity
		if err := cursor.Decode(&userEntity); err != nil {
			return nil, err
		}

		return &userEntity, nil
	}

	return nil, core.ErrNotFound
}

// /***
//   - update one filter interface{}, update interface{}, opts ...*options.UpdateOptions
//     */
func (r *clientRepo) UpdateOneClientRepo(ctx context.Context, filter interface{}, client *entity.ClientUpdateEntity, opts ...*options.UpdateOptions) error {
	update := bson.M{
		"$set": *client,
	}

	_, err := r.Collection.UpdateOne(ctx, filter, update)
	if err != nil {
		if mongo.IsDuplicateKeyError(err) {
			return core.ErrNotFound
		}
		return err
	}

	return nil
}

/***
 * Delete One user
 */
func (r *clientRepo) DeleteUserRepo(ctx context.Context, filter interface{}, update bson.M) error {

	_, err := r.Collection.UpdateOne(ctx, filter, update)
	if err != nil {
		return err
	}
	return nil
}
