package requests

import (
	"godsp/modules/facebook/adset/common/errs"

	"github.com/go-playground/validator/v10"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

type ListSelectAdsetReq struct {
	CampaignID string             `json:"campaign_id" validate:"required,numeric,gt=0"`
	UserId     primitive.ObjectID `json:"-"`
}

func (req *ListSelectAdsetReq) Validate() error {
	validate := validator.New()

	err := validate.Struct(req)
	if err != nil {
		for _, err := range err.(validator.ValidationErrors) {
			switch err.Field() {
			case "CampaignID":
				return errs.ErrAdsetCampaignID
			}
		}
	}

	return nil
}
