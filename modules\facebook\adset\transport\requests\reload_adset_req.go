package requests

import (
	"godsp/modules/facebook/adset/common/errs"

	"github.com/go-playground/validator/v10"
)

type ReloadAdsetReq struct {
	AdsetID    string `json:"adset_id,omitempty"`
	AccountID  string `json:"account_id,omitempty"`
	CampaignID string `json:"campaign_id,omitempty"`
}

func (req *ReloadAdsetReq) Validate() []string {
	validate := validator.New()
	var validationErrors []string

	err := validate.Struct(req)
	if err != nil {
		for _, err := range err.(validator.ValidationErrors) {
			switch err.Field() {
			case "AccountID":
				validationErrors = append(validationErrors, errs.ErrReloadAdsetAccountId.Error())
			case "CampaignID":
				validationErrors = append(validationErrors, errs.ErrReloadAdsetCampaignId.Error())
			case "AdsetID":
				validationErrors = append(validationErrors, errs.ErrReloadAdsetAdsetId.Error())
			}
		}
	}

	return validationErrors
}
