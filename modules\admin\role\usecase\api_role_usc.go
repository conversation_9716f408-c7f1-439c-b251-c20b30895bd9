package usecase

import (
	"context"
	"errors"
	"godsp/modules/admin/common/admconst"
	ePermission "godsp/modules/admin/permission/entity"
	"godsp/modules/admin/user/common/errs"
	eUser "godsp/modules/admin/user/entity"

	"godsp/modules/admin/role/common/pipelines"
	"godsp/modules/admin/role/entity"
	"godsp/modules/admin/role/mapping"
	"godsp/modules/admin/role/transport/requests"
	"godsp/modules/admin/role/transport/response"
	"godsp/modules/facebook/common/fbenums"
	"godsp/pkg/sctx/core"

	"github.com/dev-networldasia/dspgos/sctx"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo/options"
)

type ApiRoleRepo interface {
	InsertRoleRepo(ctx context.Context, role *entity.RoleCreation) error
	UpdateOneRoleRepo(ctx context.Context, filter interface{}, role *entity.RoleUpdate, opts ...*options.UpdateOptions) error
	FindRoleRepo(ctx context.Context, filter interface{}, opts ...*options.FindOptions) (*[]entity.RoleEntity, error)
	FindOneRoleRepo(ctx context.Context, filter interface{}, opts ...*options.FindOneOptions) (*entity.RoleEntity, error)
	FindWithPipelineRoleRepo(ctx context.Context, pipeline []bson.M, opts ...*options.AggregateOptions) (*[]entity.RoleEntity, error)
	UpdateOnePermissionRoleRepo(ctx context.Context, filter interface{}, role *entity.PermissionRoleUpdate, opts ...*options.UpdateOptions) error

	DeletesRoleApi(ctx context.Context, filter interface{}) error
}

type ApiPermissionRepo interface {
	FindWithPipelinePermissionRepo(ctx context.Context, pipeline []bson.M, opts ...*options.AggregateOptions) (*[]ePermission.PermissionEntity, error)
}

type ApiUserRepo interface {
	FindOneUserRepo(ctx context.Context, filter interface{}, opts ...*options.FindOneOptions) (*eUser.UserEntity, error)
}

type apiRoleUsc struct {
	repo           ApiRoleRepo
	permissionRepo ApiPermissionRepo
	userRepo       ApiUserRepo
	logger         sctx.Logger
}

func NewApiRoleUsc(repo ApiRoleRepo, permissionRepo ApiPermissionRepo, userRepo ApiUserRepo, logger sctx.Logger) *apiRoleUsc {
	return &apiRoleUsc{
		repo:           repo,
		permissionRepo: permissionRepo,
		userRepo:       userRepo,
		logger:         logger,
	}
}

func (usc *apiRoleUsc) CreateApiRoleUsc(ctx context.Context, payload *requests.PayloadRoleCreation) error {
	roleEntity, err := mapping.MapperCreateRole(payload)
	if err != nil {
		usc.logger.Error(err)
		return err
	}

	err = usc.repo.InsertRoleRepo(ctx, roleEntity)
	if err != nil {
		usc.logger.Error(err)
		return err
	}

	return nil
}

/**
 * Api role list datatable
 */
func (usc *apiRoleUsc) ListDatatableApiRoleUsc(ctx context.Context) (*[]response.RoleDataTable, error) {
	filter := pipelines.PipelineListTableRoles()

	allowDiskUse := fbenums.ALLOW_DISK_USE_TRUE
	opts := &options.AggregateOptions{
		AllowDiskUse: &allowDiskUse,
	}

	roles, err := usc.repo.FindWithPipelineRoleRepo(ctx, filter, opts)
	if err != nil {
		usc.logger.Error(err)
		return nil, err
	}

	var data []response.RoleDataTable
	for _, v := range *roles {
		data = append(data, response.MapperDataTable(v))
	}

	return &data, nil
}

/**
 * Api role update/edit
 */
func (usc *apiRoleUsc) UpdateApiRoleUsc(ctx context.Context, payload *requests.PayloadRoleEdition) (*entity.RoleUpdate, error) {
	_, err := usc.repo.FindOneRoleRepo(
		ctx,
		bson.M{"_id": payload.ID},
		options.FindOne().SetProjection(bson.M{"created_at": 1}),
	)
	if err != nil {
		usc.logger.Error(err)
		return nil, err
	}

	roleUpdate := mapping.MapperUpdateRole(payload)

	filter := bson.M{
		"_id": payload.ID,
	}
	err = usc.repo.UpdateOneRoleRepo(ctx, filter, roleUpdate)
	if err != nil {
		usc.logger.Error(err)
		return nil, err
	}

	return roleUpdate, nil
}

/**
 * Api role update/edit permission
 * 1 - lấy permission tu role id
 */
func (usc *apiRoleUsc) UpdatePermissionApiRoleUsc(ctx context.Context, payload *requests.PayloadUpdatePermissionRole) error {
	filter := bson.M{
		"_id": payload.RoleID,
	}
	opts := options.FindOne().SetProjection(bson.M{"name": 1, "permissions": 1})

	role, err := usc.repo.FindOneRoleRepo(ctx, filter, opts)
	if err != nil {
		usc.logger.Error(err)
		return err
	}

	roleEntity := mapping.MapperEditPermissionRole(payload, role.Permissions)
	if err := usc.repo.UpdateOnePermissionRoleRepo(ctx, filter, roleEntity); err != nil {
		usc.logger.Error(err)
		return err
	}

	return nil
}

func (u *apiRoleUsc) FindPermissionByModuleApiRoleUsc(ctx context.Context, module string, group string) (*[]ePermission.PermissionEntity, error) {
	allowDiskUse := fbenums.ALLOW_DISK_USE_TRUE
	opts := &options.AggregateOptions{
		AllowDiskUse: &allowDiskUse,
	}

	pipeline := pipelines.PipelineGetPermissionsByModule(module, group)
	permissions, err := u.permissionRepo.FindWithPipelinePermissionRepo(ctx, pipeline, opts)
	if err != nil && !errors.Is(err, core.ErrRecordNotFound) {
		u.logger.Error(err)
		return nil, err
	}

	return permissions, nil
}

func (usc *apiRoleUsc) FindOneApiRoleUsc(ctx context.Context, id string) (*entity.RoleEntity, error) {
	idObject, err := primitive.ObjectIDFromHex(id)
	if err != nil {
		return nil, err
	}
	filter := bson.M{
		"_id":    idObject,
		"status": bson.M{"$ne": admconst.STATUS_DELETE},
	}

	data, err := usc.repo.FindOneRoleRepo(ctx, filter)
	if err != nil {
		usc.logger.Error(err)
		return nil, err
	}

	return data, nil
}

func (usc *apiRoleUsc) FindOneUserIsLoggingRoleUsc(ctx context.Context, userId primitive.ObjectID) (*eUser.UserEntity, error) {
	filter := bson.M{
		"_id": userId,
	}

	projection := options.FindOne().SetProjection(bson.M{"_id": 1, "full_name": 1, "role_id": 1})

	data, err := usc.userRepo.FindOneUserRepo(ctx, filter, projection)
	if err != nil {
		usc.logger.Error(err)
		return nil, errs.ErrIDUserValidate
	}

	return data, nil
}

/**
 * Api role deletes
 */
func (u *apiRoleUsc) DeletesRoleUsc(ctx context.Context, payload *requests.PayloadRoleDeletes) error {
	filter := bson.M{"_id": bson.M{"$in": payload.Ids}}
	if err := u.repo.DeletesRoleApi(ctx, filter); err != nil {
		return err
	}

	return nil
}
