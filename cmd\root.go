package cmd

import (
	"fmt"
	"godsp/cmd/mgoindex"
	"godsp/cmd/pubsub"
	"godsp/cmd/web"
	"os"

	"github.com/spf13/cobra"
)

var rootCmd = &cobra.Command{
	Use:     "godsp",
	Version: "1.0.0",
	Short:   "Start go dsp service",
	Long:    `Start go dsp service`,
}

func init() {
	rootCmd.AddCommand(web.WebFBCmd)
	rootCmd.AddCommand(web.OutEnvCmd)
	rootCmd.AddCommand(pubsub.RedisStreamCmd)
	rootCmd.AddCommand(mgoindex.MongoIndexCmd)
}

func Execute() {
	if err := rootCmd.Execute(); err != nil {
		fmt.Println(err)
		os.Exit(1)
	}
}
