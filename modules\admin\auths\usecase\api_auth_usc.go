package usecase

import (
	"context"
	"encoding/json"
	"fmt"
	"godsp/modules/admin/auths/common/pipelines"
	"godsp/modules/admin/auths/entity"
	"godsp/modules/admin/auths/transport/requests"
	"godsp/modules/admin/common/admconst"
	"godsp/modules/admin/common/admerrs"
	payloadtopic "godsp/modules/admin/common/payload_topic"
	userEnt "godsp/modules/admin/user/entity"

	"godsp/pkg/gos/goconst"
	"godsp/pkg/gos/utils"
	"time"

	"github.com/dev-networldasia/dspgos/sctx"
	"github.com/dev-networldasia/dspgos/sctx/component/jwtc"
	"github.com/dev-networldasia/dspgos/sctx/component/redisc"
	"github.com/dev-networldasia/dspgos/sctx/component/watermillapp/redisstream"
	"github.com/dev-networldasia/dspgos/sctx/configs"

	"github.com/google/uuid"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo/options"
)

type ApiUserRepo interface {
	UpdateOneUserByBsonMRepo(ctx context.Context, filter interface{}, data bson.M, opts ...*options.UpdateOptions) error
	FindOneUserWithPipelineRepo(ctx context.Context, pipeline []bson.M, opts ...*options.AggregateOptions) (*userEnt.UserEntity, error)
}

type Hasher interface {
	RandomStr(length int) (string, error)
	HashPassword(salt, password string) (string, error)
	CompareHashPassword(hashedPassword, salt, password string) bool
}

type apiAuthUsc struct {
	repo           ApiUserRepo
	hasher         Hasher
	jwtProvider    jwtc.JWTProvider
	redisc         redisc.RedisComponent
	logger         sctx.Logger
	redisStreamPub redisstream.RedisStreamPublisher
}

func NewApiAuthUsc(repo ApiUserRepo, hasher Hasher, jwtProvider jwtc.JWTProvider, redisc redisc.RedisComponent, logger sctx.Logger, redisStreamPub redisstream.RedisStreamPublisher) *apiAuthUsc {
	return &apiAuthUsc{
		repo:           repo,
		hasher:         hasher,
		jwtProvider:    jwtProvider,
		redisc:         redisc,
		logger:         logger,
		redisStreamPub: redisStreamPub,
	}
}

/**
 * login usc
 */
func (usc *apiAuthUsc) LoginAuthUsc(ctx context.Context, payload *requests.PayloadAuthLogin) (*entity.TokenResponse, error) {
	pipeline := pipelines.PipelineGetUserOneRole(&payload.Email, nil)

	authData, err := usc.repo.FindOneUserWithPipelineRepo(ctx, pipeline)

	if err != nil {
		return nil, admerrs.ErrLoginEmailFailed
	}

	authData.PathImg()

	//check pass
	if checkPassword := usc.hasher.CompareHashPassword(authData.Password, authData.Salt, payload.Password); !checkPassword {
		return nil, admerrs.ErrLoginFailed
	}
	// fmt.Printf("AdAccountID %v \n", authData.AdAccountID)
	subId := authData.ID.Hex()
	clientID := authData.ClientID.Hex()

	if authData.Role == nil {
		return nil, admerrs.ErrPermissionDenied
	}
	if authData.Role == nil && authData.Role.RoleName == "" {
		return nil, admerrs.ErrPermissionDenied
	}

	fmt.Printf("authData.TiktokAdvertiserID %v \n", authData.TiktokAdvertiserID)
	sub := fmt.Sprintf("%s_%s_%s_%s_%s_%s", subId, authData.Role.RoleName, authData.FirstName, authData.AdAccountID, clientID, authData.TiktokAdvertiserID)
	tid := uuid.New().String()

	accessToken, expSecs, err := usc.jwtProvider.IssueToken(ctx, tid, sub, nil)
	if err != nil {
		return nil, admerrs.ErrLoginFailed
	}

	if authData.Role.Permissions == nil && authData.Role.RoleName != goconst.ROLE_NAME_ADMIN {
		return nil, admerrs.ErrPermissionDenied
	}

	//set permission to cache redis for permission (auth middleware)
	if authData.Role.RoleName != goconst.ROLE_NAME_ADMIN {
		keyRd := admconst.KEY_CACHE_REDIS_PERMISSION_LOGIN + subId
		sessionLifetime := configs.SessionLifetime * int(time.Minute)

		permisionByte, err := json.Marshal(authData.Role.Permissions)
		if err != nil {
			return nil, admerrs.ErrPermissionDataFailed
		}
		permisionStr := string(permisionByte)
		err = usc.redisc.SetCacheRedis(ctx, &permisionStr, keyRd, sessionLifetime)
		if err != nil {
			// send msg telegram
			usc.logger.Error(fmt.Sprintf("SetCacheRedis failed: %v - %v", payload, err))
			// discordSvc := serviceCtx.MustGet(configs.KeyDiscordSMS).(discord.SendMessageDiscordSVC)
			// discordSvc.SendMessageDev(msg)
			return nil, admerrs.ErrPermissionDataFailed
		}
	}

	return &entity.TokenResponse{
		AccessToken: entity.Token{
			Token:     accessToken,
			ExpiredIn: expSecs,
		},
		User: &entity.UserResponse{
			FirstName: authData.FirstName,
			LastName:  authData.LastName,
			Image:     authData.Image,
			RoleName:  authData.Role.RoleName,
			Email:     authData.Email,
			ClientId:  clientID,
		},
	}, nil

}

/**
 * Change Password
 */
func (usc *apiAuthUsc) ChangePasswordAuthApiUsc(ctx context.Context, payload *requests.PayloadAuthChangePassword) error {
	pipeline := pipelines.PipelineGetUserOneRole(nil, &payload.ID)
	authData, err := usc.repo.FindOneUserWithPipelineRepo(ctx, pipeline)
	if err != nil {
		return admerrs.ErrLoginEmailFailed
	}

	//check pass
	if checkPassword := usc.hasher.CompareHashPassword(authData.Password, authData.Salt, payload.OldPassword); !checkPassword {
		return admerrs.ErrOldPasswordDontMatch
	}

	passHasher, err := usc.hasher.HashPassword(authData.Salt, payload.NewPassword)
	if err != nil {
		usc.logger.Error(err)
		return err
	}

	updatePass := bson.M{
		"$set": bson.M{
			"password": passHasher,
		},
	}

	err = usc.repo.UpdateOneUserByBsonMRepo(ctx, bson.M{"_id": payload.ID}, updatePass)

	if err != nil {
		usc.logger.Error(err)
		return err
	}

	return nil

}

/**
 * Change Password
 */
func (usc *apiAuthUsc) ResetPasswordAuthApiUsc(ctx context.Context, payload *requests.PayloadAuthResetPassword) error {
	pipeline := pipelines.PipelineGetUserOneRole(nil, &payload.ID)
	authData, err := usc.repo.FindOneUserWithPipelineRepo(ctx, pipeline)
	if err != nil {
		return admerrs.ErrUserIdIsNotFound
	}
	salt := authData.Salt
	newPassword := utils.GenerateRandomPassword(12) // or a default one

	payloadResetPass := payloadtopic.PayloadResetPassword{
		ToEmail:           authData.Email,
		FullName:          authData.FullName,
		ID:                authData.ID,
		Salt:              salt,
		OldPasswordHashed: authData.Password,
		NewPassword:       newPassword,
		Retry:             3,
	}

	// payloadTopup := payloadtopic.PayloadSentEmailApprovalTopup{
	// 	ToEmail:    "<EMAIL>",
	// 	ID:         primitive.NewObjectID(),
	// 	ClientName: "Henry",
	// 	Company:    "ACME Corp",
	// 	Amount:     5000,
	// 	Balance:    50000,
	// 	AdAccount:  nil,
	// 	ReceiptID:  primitive.NewObjectID(),
	// 	InvoiceID:  primitive.NewObjectID(),
	// }

	// payloadFullBalance := payloadtopic.PayloadSendEmailAdAccountFullBalance{
	// 	ToEmail:        "<EMAIL>",
	// 	ID:             primitive.NewObjectID(),
	// 	ClientName:     "Henry",
	// 	Company:        "ACME Corp",
	// 	AdAccountID:    "***********",
	// 	AdAcountName:   "Dai Kin",
	// 	CurrentBalance: "$0",
	// }

	if err := usc.redisStreamPub.Publish(payloadtopic.TOPIC_SEND_EMAIL_RESET_PASSWORD, payloadResetPass); err != nil {
		usc.logger.Error("Execute Pub send mail reset password: ", err)
		usc.logger.Error("Payload: ", payloadResetPass)
		return err
	}

	// if err := usc.redisStreamPub.Publish(payloadtopic.TOPIC_SEND_EMAIL_APPROVAL_TOPUP, payloadTopup); err != nil {
	// 	usc.logger.Error("Execute Pub send mail topup susscess: ", err)
	// 	usc.logger.Error("Payload: ", payloadTopup)
	// 	return err
	// }

	// if err := usc.redisStreamPub.Publish(payloadtopic.TOPIC_SEND_EMAIL_FULL_BALANCE_ADACCOUNT, payloadFullBalance); err != nil {
	// 	usc.logger.Error("Execute Pub send mail full balance: ", err)
	// 	usc.logger.Error("Payload: ", payloadFullBalance)
	// 	return err
	// }

	return nil

}
