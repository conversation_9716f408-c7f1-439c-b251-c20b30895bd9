package routes

import (
	clientR "godsp/modules/admin/client/repository/mongo"
	roleR "godsp/modules/admin/role/repository/mongo"
	adAccR "godsp/modules/facebook/ad_account/repository/mongo"
	pixelR "godsp/modules/facebook/ad_pixel/repository/mongo"
	catalogueR "godsp/modules/facebook/catalogue/repository/mongo"
	pageR "godsp/modules/facebook/pages/repository/mongo"

	"godsp/modules/admin/user/repository/mongo"
	"godsp/modules/admin/user/transport/handlers"
	"godsp/modules/admin/user/usecase"

	"github.com/dev-networldasia/dspgos/sctx/component/mongodb"

	"github.com/dev-networldasia/dspgos/sctx/configs"

	"github.com/dev-networldasia/dspgos/sctx"

	"github.com/gofiber/fiber/v2"
	"github.com/gofiber/fiber/v2/middleware/session"
)

type composerUserHdl interface {
	ListUserHdl(store *session.Store) fiber.Handler
	EditUserHdl(store *session.Store) fiber.Handler
	UserProfileHdl(store *session.Store) fiber.Handler
}

func ComposerUserServive(serviceCtx sctx.ServiceContext) composerUserHdl {
	logger := serviceCtx.MustGet(configs.KeyLoggerDaily).(sctx.AppLoggerDaily).GetLogger("admins")
	mongoDB := serviceCtx.MustGet(configs.KeyCompMongoDB).(mongodb.MongoComponent).GetDatabase()

	repo := mongo.NewUserRepo(mongoDB)
	roleRepo := roleR.NewRoleRepo(mongoDB)
	clientRepo := clientR.NewClientRepo(mongoDB)

	adAccRepo := adAccR.NewAdAccountRepo(mongoDB)
	pageRepo := pageR.NewPageRepo(mongoDB)
	pixelRepo := pixelR.NewAdPixelRepo(mongoDB)
	catalogueRepo := catalogueR.NewCatalogueRepo(mongoDB)

	usc := usecase.NewUserUsc(logger, repo, roleRepo, clientRepo, adAccRepo, pageRepo, pixelRepo, catalogueRepo)
	hdl := handlers.NewUserHdl(usc)

	return hdl
}
