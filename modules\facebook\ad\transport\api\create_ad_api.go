package api

import (
	"godsp/conf"
	"godsp/modules/facebook/ad/transport/requests"
	"godsp/pkg/gos/utils"
	"godsp/pkg/sctx/core"
	"net/http"

	"github.com/gofiber/fiber/v2"
)

/***
 * Create ad api
 */

func (a *adApi) CreateAdApi() fiber.Handler {
	return func(c *fiber.Ctx) error {
		var payload requests.CreateAdReq
		if err := c.BodyParser(&payload); err != nil {
			return core.ReturnErrsForApi(c, err)
		}

		//validate
		validationErrors := payload.Validate()
		if len(validationErrors) > 0 {
			return core.ReturnErrsForApi(c, validationErrors)
		}

		userInfo, err := utils.GetInfoUserAuth(c.Context())

		if userInfo.RoleName == conf.SysConf.RoleAdmin {
			if payload.AccountID == "" && payload.ClientIDStr == "" {
				return c.Status(fiber.StatusBadRequest).JSON(core.ResponseData(map[string]interface{}{
					"msg":  "Account ID or Client ID is required",
					"data": nil,
				}))
			}
		} else {
			if err != nil {
				return core.ReturnErrForPermissionDenied(c)
			}

			if payload.ClientIDStr != userInfo.ClientId.Hex() {
				return core.ReturnErrForPermissionDenied(c)
			}
		}

		ad, err := a.usc.CreateAdUsc(c.Context(), &payload)
		if err != nil {
			return core.ReturnErrsForApi(c, err)
		}

		return c.Status(http.StatusOK).JSON(core.ResponseData(map[string]interface{}{
			"msg":  "Create Ad successfully",
			"data": ad,
		}))
	}
}
