package routes

import (
	"godsp/conf"
	userR "godsp/modules/admin/user/repository/mongo"
	fbReportDetailR "godsp/modules/facebook/adserver_report/repository/mongo"
	"godsp/modules/facebook/campaign/repository/mongo"
	"godsp/modules/facebook/campaign/transport/api"
	"godsp/modules/facebook/campaign/usecase"
	"godsp/pkg/sctx/component/facebook/fbmarketing"

	"github.com/dev-networldasia/dspgos/sctx/component/mongodb"

	"github.com/dev-networldasia/dspgos/sctx/configs"

	"github.com/dev-networldasia/dspgos/sctx"

	"github.com/gofiber/fiber/v2"
)

type ComposerCampaignApi interface {
	ReloadCampaignApi() fiber.Handler
	CreateCampaignApi() fiber.Handler
	ListTableCampaignApi() fiber.Handler
	ListCampaignApi() fiber.Handler
	DeleteCampaignApi() fiber.Handler
	GetDetailCampaignApi() fiber.Handler
	GetDetailAdsetAdCampaignApi() fiber.Handler
	UpdateCampaignApi() fiber.Handler
	UpdateNameStatusCampaignApi() fiber.Handler
	ApproveCampaignApi() fiber.Handler
}

func ComposerCampaignApiService(serviceCtx sctx.ServiceContext) ComposerCampaignApi {
	mongoDB := serviceCtx.MustGet(configs.KeyCompMongoDB).(mongodb.MongoComponent).GetDatabase()
	mongoAdserverReportDB := serviceCtx.MustGet(conf.KeyCompReportMongoDB).(mongodb.MongoComponent).GetDatabase()
	logger := serviceCtx.MustGet(configs.KeyLoggerDaily).(sctx.AppLoggerDaily).GetLogger("webfb")
	fbService := serviceCtx.MustGet(configs.KeyFBMarketingV20).(fbmarketing.FBMarketingServices).GetFBMarketingService()

	// Init api
	repo := mongo.NewCampaignRepo(mongoDB)
	userRepo := userR.NewUserRepo(mongoDB)

	fbReportDetailRepo := fbReportDetailR.NewFBReportDetailRepo(mongoAdserverReportDB)
	usc := usecase.NewApiCampaignUsc(fbService, repo, fbReportDetailRepo, userRepo, logger)
	api := api.NewCampaignApi(usc)

	return api
}
