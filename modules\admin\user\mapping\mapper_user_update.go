package mapping

import (
	"godsp/modules/admin/user/common/errs"
	"godsp/modules/admin/user/entity"
	"godsp/modules/admin/user/transport/requests"
	"godsp/pkg/gos/goconst"
	"time"
)

func MapperUpdateUser(req *requests.PayloadUserUpdate) (*entity.UserUpdate, error) {
	data := &entity.UserUpdate{
		ID:           req.ID,
		FirstName:    req.FirstName,
		LastName:     req.LastName,
		FullName:     req.LastName + " " + req.FirstName,
		RoleID:       req.RoleID,
		AdAccountID:  req.AdAccountID,
		Email:        req.Email,
		Status:       req.Status,
		Gender:       req.Gender,
		UpdatedBy:    req.UserID,
		UpdatedAt:    time.Now(),
		ListPageIds:  req.ListPageIds,
		ListPixelIds: req.ListPixelIds,
		ClientID:     req.ClientID,
	}

	username := req.Username
	if username != "" {
		data.Username = &username
	}

	if req.Phone != "" {
		data.Phone = &req.Phone
	}

	if req.Birthday != "" {
		birthday, err := time.Parse(goconst.DD_MM_YYYY, req.Birthday)
		if err != nil {
			return nil, errs.ErrBirthdayValidate
		}
		data.Birthday = &birthday
	}

	if req.Image != "" {
		data.Image = &req.Image
	}

	return data, nil
}
