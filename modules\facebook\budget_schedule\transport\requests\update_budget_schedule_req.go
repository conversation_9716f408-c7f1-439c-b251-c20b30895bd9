package requests

import (
	"fmt"
	"godsp/modules/facebook/budget_schedule/common/errs"
	"godsp/modules/facebook/budget_schedule/transport/rules"
	"time"

	"github.com/go-playground/validator/v10"
)

type UpdateListScheduleReq []*UpdateBudgetScheduleReq
type UpdateBudgetScheduleReq struct {
	CampaignId         string    `json:"campaign_id,omitempty" validate:"omitempty,numeric,gt=0"`
	AdsetId            string    `json:"adset_id,omitempty" validate:"omitempty,numeric,gt=0"`
	HighDemandPeriodId string    `json:"high_demand_period_id" validate:"required,numeric,gt=0"`
	BudgetValue        int64     `json:"budget_value" validate:"required"`
	BudgetValueType    string    `json:"budget_value_type" validate:"required,oneof=ABSOLUTE MULTIPLIER"`
	TimeStart          time.Time `json:"time_start" validate:"required"`
	TimeEnd            time.Time `json:"time_end" validate:"required,gt_now,gt_time_start"`

	TimeStartInt int64 `json:"-"`
	TimeEndInt   int64 `json:"-"`
}

func (req *UpdateBudgetScheduleReq) Validate() []string {
	validate := validator.New()
	validate.RegisterValidation("gt_now", rules.GTNow)
	validate.RegisterValidation("gt_time_start", gtTimeStartUpdate)
	var validationErrors []string

	err := validate.Struct(req)
	if err != nil {
		for _, err := range err.(validator.ValidationErrors) {
			switch err.Field() {
			case "CampaignId":
				validationErrors = append(validationErrors, errs.ErrCampaignIdBudgetSchedule.Error())
			case "BudgetValue":
				validationErrors = append(validationErrors, errs.ErrValidateBudgetValue.Error())
			case "BudgetValueType":
				validationErrors = append(validationErrors, errs.ErrValidateBudgetValueType.Error())
			case "TimeStart":
				validationErrors = append(validationErrors, errs.ErrValidateTimeStart.Error())
			case "TimeEnd":
				validationErrors = append(validationErrors, errs.ErrValidateTimeEnd.Error())
			case "HighDemandPeriodId":
				validationErrors = append(validationErrors, errs.ErrHighDemandPeriodId.Error())
			}

		}
	}

	if req.CampaignId == "" && req.AdsetId == "" {
		validationErrors = append(validationErrors, errs.ErrCampaignIdAdsetBudgetSchedule.Error())
	}

	if validationErrors == nil {
		req.TimeStartInt = req.TimeStart.Unix()
		req.TimeEndInt = req.TimeEnd.Unix()
	}
	return validationErrors
}

func gtTimeStartUpdate(fl validator.FieldLevel) bool {
	req, ok := fl.Top().Interface().(*UpdateBudgetScheduleReq)
	if !ok {
		return false
	}
	// Check if TimeEnd is after TimeStart
	return req.TimeEnd.After(req.TimeStart)
}

func (req *UpdateListScheduleReq) Validate(schedulesUpdate *UpdateListScheduleReq) error {

	for id, schedule := range *schedulesUpdate {
		if id == 0 {
			continue
		}
		fmt.Printf("\n ---------------------------- Transport Validate - Is Error!-------------------------- \n")
		fmt.Printf("\n ---------------------------| % v - %v  |--------------------------- \n", schedule.TimeStart, (*schedulesUpdate)[id-1].TimeEnd)

		if !schedule.TimeStart.After((*schedulesUpdate)[id-1].TimeEnd) {
			return errs.ErrValidateIsOverlap
		}
	}

	return nil
}
