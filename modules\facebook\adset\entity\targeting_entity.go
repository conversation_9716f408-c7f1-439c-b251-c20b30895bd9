package entity

import v20 "godsp/pkg/fb-marketing/marketing/v20"

type IDContainer struct {
	ID      string `json:"id"`
	Name    string `json:"name"`
	SubType string `json:"subtype"`
}

type AdsetTargetingEntity struct {
	// inventories
	PublisherPlatforms []string `json:"publisher_platforms,omitempty"`
	// sub inventories
	FacebookPositions        []string `json:"facebook_positions,omitempty"`
	InstagramPositions       []string `json:"instagram_positions,omitempty"`
	AudienceNetworkPositions []string `json:"audience_network_positions,omitempty"`
	MessengerPositions       []string `json:"messenger_positions,omitempty"`

	AgeMin  uint64 `json:"age_min,omitempty"`
	AgeMax  uint64 `json:"age_max,omitempty"`
	Genders []int  `json:"genders,omitempty"`

	AppInstallState string `json:"app_install_state,omitempty"`

	CustomAudiences         []IDContainer      `json:"custom_audiences,omitempty"`
	ExcludedCustomAudiences []IDContainer      `json:"excluded_custom_audiences,omitempty"`
	GeoLocations            *v20.GeoLocations  `json:"geo_locations,omitempty"`
	ExcludedGeoLocations    *v20.GeoLocations  `json:"excluded_geo_locations,omitempty"`
	FlexibleSpec            []v20.FlexibleSpec `json:"flexible_spec,omitempty"`
	Exclusions              *v20.FlexibleSpec  `json:"exclusions,omitempty"`

	DevicePlatforms                []string                     `json:"device_platforms,omitempty"`
	ExcludedPublisherCategories    []string                     `json:"excluded_publisher_categories,omitempty"`
	Locales                        []int                        `json:"locales,omitempty"`
	TargetingOptimization          string                       `json:"targeting_optimization,omitempty"`
	UserDevice                     []string                     `json:"user_device,omitempty"`
	UserOs                         []string                     `json:"user_os,omitempty"`
	WirelessCarrier                []string                     `json:"wireless_carrier,omitempty"`
	TargetingRelaxationTypes       v20.TargetingRelaxationTypes `json:"targeting_relaxation_types,omitempty"`
	TargetingAutomation            v20.TargetingAutomation      `json:"targeting_automation,omitempty"`
	BrandSafetyContentFilterLevels []string                     `json:"brand_safety_content_filter_levels,omitempty"`
}
