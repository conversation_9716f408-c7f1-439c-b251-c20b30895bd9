package usecase

import (
	"bytes"
	"context"
	"fmt"
	payloadtopic "godsp/modules/admin/common/payload_topic"
	"godsp/modules/email/ifaces"
	"godsp/modules/email/service"
	"godsp/pkg/gos/utils"
	"html/template"
	"strconv"
	"strings"
	"time"

	"github.com/dev-networldasia/dspgos/sctx/component/watermillapp/redisstream"

	"github.com/dev-networldasia/dspgos/sctx"
	"github.com/dev-networldasia/dspgos/sctx/configs"

	"go.mongodb.org/mongo-driver/bson"
)

const (
	MAX_RETRY_SEND_EMAIL = 2
	DELAY_BETWEEN_RETRY  = 3 * time.Second
	TIMEOUT_PER_ATTEMPT  = 16 * time.Second
)

type DiscordClientSv interface {
	SendMessageDev(msg string) error
}

type EmailInfoSend struct {
	To      string
	Subject string
	Body    string
}

type EmailService interface {
	Send(ctx context.Context, email EmailInfoSend) error
}

type EmailSubUsecase struct {
	emailSv        *service.EmailService
	logger         sctx.Logger
	userRepo       UserRepo
	hasher         Hasher
	redisStreamPub redisstream.RedisStreamPublisher
	discordSv      DiscordClientSv
}

func NewEmailSubUsecase(srv *service.EmailService, logger sctx.Logger, userRepo UserRepo, redisStreamPub redisstream.RedisStreamPublisher, hasher Hasher, discordSv DiscordClientSv) *EmailSubUsecase {
	u := &EmailSubUsecase{
		emailSv:        srv,
		logger:         logger,
		userRepo:       userRepo,
		hasher:         hasher,
		redisStreamPub: redisStreamPub,
		discordSv:      discordSv,
	}

	return u
}

/**
 * RenderTemplatEmailFullBalanceAdAccount renders the email template for full balance notification.
 */
func RenderTemplateEmail(data interface{}, templatePath ...string) (string, error) {

	tmpl, err := template.ParseFiles(templatePath...)
	if err != nil {
		return "", fmt.Errorf("failed to parse email template %s: %v", templatePath, err)
	}

	fmt.Printf("data %+v", data)

	var body bytes.Buffer
	if err := tmpl.Execute(&body, data); err != nil {
		return "", fmt.Errorf("failed to execute email template %s: %v", templatePath, err)

	}
	return body.String(), nil
}

/**
 * Reset password Usc
 */
func (u *EmailSubUsecase) SendEmailResetPasswordSubUsc(ctx context.Context, payload *payloadtopic.PayloadResetPassword) error {

	type EmailNotifyPayload struct {
		ToEmail     string
		FullName    string
		NewPassword string
		SiteLogin   string
	}

	data := EmailNotifyPayload{
		ToEmail:     payload.ToEmail,
		FullName:    payload.FullName,
		NewPassword: payload.NewPassword,
		SiteLogin:   configs.Domain + "/login",
	}
	subject := "NetWordAsia – Password Reset Notification"

	err := SendEmailLoopRetry(ctx, u.emailSv, data,
		func(p EmailNotifyPayload) (ifaces.EmailInfoSend, error) {
			templatePath := "modules/email/templates/email_reset_password.html"
			body, err := RenderTemplateEmail(&p, templatePath)
			if err != nil {
				u.logger.Error(err)
				return ifaces.EmailInfoSend{}, err
			}
			return ifaces.EmailInfoSend{
				To:      p.ToEmail,
				Subject: subject,
				Body:    body,
			}, nil
		}, MAX_RETRY_SEND_EMAIL, TIMEOUT_PER_ATTEMPT, DELAY_BETWEEN_RETRY)

	if err != nil {
		msg := fmt.Sprintf("\n ERROR DSP-Facebook - At: %v \n %v", time.Now().Format(time.RFC3339), err)
		u.discordSv.SendMessageDev(msg)
		u.logger.Error(msg)
		return err
	}

	passHasher, err := u.hasher.HashPassword(payload.Salt, payload.NewPassword)
	if err != nil {
		u.logger.Error(err)
		return err
	}

	// fmt.Println("new passHasher: ", passHasher)

	updatePass := bson.M{
		"$set": bson.M{
			"password":   passHasher,
			"updated_at": time.Now(),
		},
	}

	err = u.userRepo.UpdateOneUserByBsonMRepo(ctx, bson.M{"_id": payload.ID}, updatePass)

	if err != nil {
		u.logger.Error(err)
		return err
	}

	return nil
}

/**
 * Send Email Approval Topup
 */
func (u *EmailSubUsecase) SendEmailNotifyApprovalTopupSubUsc(ctx context.Context, payload *payloadtopic.PayloadSentEmailApprovalTopup) error {

	type EmailNotifyPayload struct {
		ToEmail          string
		ID               string
		ClientName       string
		Company          string
		Amount           string
		Balance          string
		AdAccount        string
		ReceiptID        string
		InvoiceID        string
		SiteListCampaign string
	}

	AdAccount := ""
	if payload.AdAccount != nil {
		AdAccount = fmt.Sprintf("%s", payload.AdAccount)
	}

	data := EmailNotifyPayload{
		ToEmail:          payload.ToEmail,
		ID:               payload.ID.Hex(),
		ClientName:       payload.ClientName,
		Company:          payload.Company,
		Amount:           "$" + fmt.Sprintf("%d", payload.Amount),
		Balance:          "$" + fmt.Sprintf("%d", payload.Balance),
		AdAccount:        AdAccount,
		ReceiptID:        "receipt_" + payload.ReceiptID.Hex(),
		InvoiceID:        "invoice_" + payload.InvoiceID.Hex(),
		SiteListCampaign: configs.Domain + "/dsp/facebook/campaigns/list",
	}

	// err = u.SendEmail(payload.ToEmail, subject, body.String())
	err := SendEmailLoopRetry(ctx, u.emailSv, data,
		func(p EmailNotifyPayload) (ifaces.EmailInfoSend, error) {
			subject := "NetWordAsia – Topup Money Notification"
			templatePath := "modules/email/templates/email_approval_topup.html"
			body, err := RenderTemplateEmail(&p, templatePath)
			if err != nil {
				u.logger.Error(err)
				return ifaces.EmailInfoSend{}, err
			}
			return ifaces.EmailInfoSend{
				To:      p.ToEmail,
				Subject: subject,
				Body:    body,
			}, nil
		}, MAX_RETRY_SEND_EMAIL, TIMEOUT_PER_ATTEMPT, DELAY_BETWEEN_RETRY)

	if err != nil {
		return err
	}

	return nil
}

/**
 * Send Email Approval Topup
 */
func (u *EmailSubUsecase) SendEmailNotifyFullBalanceAdAccountSubUsc(ctx context.Context, payload *payloadtopic.PayloadSendEmailAdAccountFullBalance) error {

	type EmailNotifyPayload struct {
		ToEmail        string
		ID             string
		ClientName     string
		Company        string
		AdAccountID    string
		AdAcountName   string
		CurrentBalance string
		SiteTopup      string
	}

	data := EmailNotifyPayload{
		ToEmail:        payload.ToEmail,
		ID:             payload.ID.Hex(),
		ClientName:     payload.ClientName,
		Company:        payload.Company,
		AdAccountID:    payload.AdAccountID,
		AdAcountName:   payload.AdAcountName,
		CurrentBalance: payload.CurrentBalance,
		SiteTopup:      configs.Domain + "admins/billings/create",
	}

	// err = u.SendEmail(payload.ToEmail, subject, body.String())
	err := SendEmailLoopRetry(ctx, u.emailSv, data,
		func(p EmailNotifyPayload) (ifaces.EmailInfoSend, error) {
			subject := "NetWordAsia – Full Balance Notification"
			templatePath := "modules/email/templates/email_full_balance_adaccount.html"
			body, err := RenderTemplateEmail(&p, templatePath)
			if err != nil {
				u.logger.Error(err)
				return ifaces.EmailInfoSend{}, err
			}
			return ifaces.EmailInfoSend{
				To:      p.ToEmail,
				Subject: subject,
				Body:    body,
			}, nil
		}, MAX_RETRY_SEND_EMAIL, 10*time.Second, 3*time.Second)

	if err != nil {
		u.logger.Error(err)
		return err
	}

	return nil
}

func (u *EmailSubUsecase) SendEmailBillingDetailSubUsc(ctx context.Context, payload *payloadtopic.PayloadUpdateBillingDetail) error {
	title := "Top-Up Successful"
	description := "We are pleased to inform you that your recent top-up has been successfully processed."
	cssClass := "success"
	isRejected := strings.Contains(payload.Status, "_REJECTED")
	if isRejected {
		title = "Top-Up Rejected"
		description = "We regret to inform you that your recent top-up has been rejected."
		cssClass = "danger"
	}

	mailData := map[string]interface{}{
		"ToEmail":       payload.ToEmail,
		"Title":         title,
		"Description":   description,
		"ClientInfo":    payload.ClientInfo,
		"AdAccountInfo": payload.AdAccountInfo,
		"Amount":        utils.FormatNumberWithCurrency(strconv.FormatInt(payload.Amount, 10), payload.Currency),
		"BillNumber":    payload.BillNumber,
		"BillingID":     payload.BillingID,
		"Link":          configs.Domain + "/admins/billings/edit/" + payload.BillingID,
		"CssClass":      cssClass,
	}

	err := SendEmailLoopRetry(ctx, u.emailSv, mailData, func(mailData map[string]interface{}) (ifaces.EmailInfoSend, error) {
		body, err := RenderTemplateEmail(
			&mailData,
			"modules/email/templates/email_update_billing_detail.html",
			"modules/email/templates/layout/header_email.html",
			"modules/email/templates/layout/footer_email.html",
		)
		if err != nil {
			u.logger.Error(err)

			return ifaces.EmailInfoSend{}, err
		}

		return ifaces.EmailInfoSend{
			To:      mailData["ToEmail"].(string),
			Subject: "NetWordAsia – " + mailData["Title"].(string),
			Body:    body,
		}, nil
	}, MAX_RETRY_SEND_EMAIL, 10*time.Second, 3*time.Second)

	if err != nil {
		u.logger.Error(err)

		return err
	}

	return nil
}

func (u *EmailSubUsecase) SendEmailBillingSubUsc(ctx context.Context, payload *payloadtopic.PayloadUpdateBilling) error {
	title := "Top-Up Successful"
	description := "We are pleased to inform you that your recent top-up has been successfully processed."
	cssClass := "success"
	isRejected := strings.Contains(payload.Status, "_REJECTED")
	if isRejected {
		title = "Top-Up Rejected"
		description = "We regret to inform you that your recent top-up has been rejected."
		cssClass = "danger"
	}

	mailData := map[string]interface{}{
		"ToEmail":     payload.ToEmail,
		"Title":       title,
		"Description": description,
		"ClientInfo":  payload.ClientInfo,
		"BillNumber":  payload.BillNumber,
		"BillingID":   payload.BillingID,
		"Link":        configs.Domain + "/admins/billings/edit/" + payload.BillingID,
		"CssClass":    cssClass,
	}

	err := SendEmailLoopRetry(ctx, u.emailSv, mailData, func(mailData map[string]interface{}) (ifaces.EmailInfoSend, error) {
		body, err := RenderTemplateEmail(
			&mailData,
			"modules/email/templates/email_update_billing.html",
			"modules/email/templates/layout/header_email.html",
			"modules/email/templates/layout/footer_email.html",
		)
		if err != nil {
			u.logger.Error(err)

			return ifaces.EmailInfoSend{}, err
		}

		return ifaces.EmailInfoSend{
			To:      mailData["ToEmail"].(string),
			Subject: "NetWordAsia – " + mailData["Title"].(string),
			Body:    body,
		}, nil
	}, MAX_RETRY_SEND_EMAIL, 10*time.Second, 3*time.Second)

	if err != nil {
		u.logger.Error(err)

		return err
	}

	return nil
}

/**
 * SendEmailLoopRetry sends an email with retry logic.
 */
func SendEmailLoopRetry[T any](
	ctx context.Context,
	emailService *service.EmailService,
	payload T,
	generateEmail func(T) (ifaces.EmailInfoSend, error),
	maxRetry int,
	timeoutPerAttempt time.Duration,
	delayBetweenRetry time.Duration,
) error {
	var lastErr error
	for i := 1; i <= maxRetry; i++ { // i <= maxRetry để đảm bảo đúng số lần
		email, err := generateEmail(payload)
		if err != nil {
			return fmt.Errorf("failed to generate email: %w", err)
		}

		fmt.Printf("\n \n Time Per Attempt: %v \n \n ", timeoutPerAttempt)
		start := time.Now()
		attemptCtx, cancel := context.WithTimeout(ctx, timeoutPerAttempt)
		fmt.Printf("Actual send duration: %v\n", time.Since(start))
		err = emailService.Send(attemptCtx, email)
		cancel() // ✅ gọi trực tiếp để giải phóng context đúng lúc

		if err == nil {
			return nil // Email sent successfully
		}

		lastErr = err
		fmt.Printf("\nSend attempt %d/%d failed: %v\n", i, maxRetry, err)

		if i < maxRetry {
			time.Sleep(delayBetweenRetry)
		}
	}
	return fmt.Errorf("failed to send email after %d attempts: %w", maxRetry, lastErr)
}
