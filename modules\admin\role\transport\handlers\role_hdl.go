package handlers

import (
	"context"
	"godsp/modules/admin/common/admconst"
	ePermission "godsp/modules/admin/permission/entity"
	"godsp/modules/admin/role/common/consts"
	"godsp/modules/admin/role/entity"
	"godsp/pkg/sctx/core"

	"github.com/gofiber/fiber/v2"
)

type RoleUsc interface {
	MaxOrderingRoleUsc(ctx context.Context) (int64, error)
	FindOneRoleUsc(ctx context.Context, id string) (*entity.RoleEntity, error)
	FindGroupModulePermissionRoleUsc(ctx context.Context) (*[]ePermission.PermissionEntity, error)
}

type roleHdl struct {
	usc RoleUsc
}

func NewRoleHdl(usc RoleUsc) *roleHdl {
	return &roleHdl{
		usc: usc,
	}
}

/**
 * List role hdl
 */
func (h *roleHdl) ListRoleHdl() fiber.Handler {
	return func(c *fiber.Ctx) error {
		ordering, err := h.usc.MaxOrderingRoleUsc(c.<PERSON>text())
		if err != nil {
			return err
		}

		return c.<PERSON><PERSON>("admins/roles/index", fiber.Map{
			"defaultImg": consts.DEFAULT_IMG_ADMIN,
			"status":     admconst.StatusFullName,
			"show": map[string]interface{}{
				"status":       admconst.StatusFullName,
				"statusCreate": admconst.StatusCreateName,
				"ordering":     ordering + 1,
			},
			"authPermission": core.GetPermission(c.Context()).GetPermissions(),
		})
	}
}

func (h *roleHdl) PermissionRoleHdl() fiber.Handler {
	return func(c *fiber.Ctx) error {
		id := c.Params("roleId")
		detailRole, err := h.usc.FindOneRoleUsc(c.Context(), id)
		if err != nil {
			return err
		}

		permissionGroupModule, err := h.usc.FindGroupModulePermissionRoleUsc(c.Context())
		if err != nil {
			return err
		}

		return c.Render("admins/roles/permission", fiber.Map{
			"detail":                detailRole,
			"permissionGroupModule": permissionGroupModule,
		})
	}
}
