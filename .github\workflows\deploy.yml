name: Deploy to VPS
on:
  push:
    branches:
      - master
jobs:
  deploy:
    runs-on: self-hosted
    steps:
      - name: Checkout Code
        uses: actions/checkout@v2
      - name: SSH Deploy
        uses: appleboy/ssh-action@master
        with:
          host: ${{ secrets.PRD_HOST_93 }}
          USERNAME: ${{ secrets.PRD_USERNAME_93 }}
          PORT: ${{ secrets.PRD_PORT_93 }}
          KEY: ${{ secrets.PRD_PRIV_KEY_93 }}
          script: |
            systemctl stop godsp     
            destination_dir="/home/<USER>/godsp"              
            cd $destination_dir
            git pull
            systemctl restart godsp
          timeout: 240s
