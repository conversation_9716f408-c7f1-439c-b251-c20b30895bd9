package errs

import "errors"

var (
	ErrReloadAudienceAccountId = errors.New("validate audience account id must be a number greater than 0")

	// list api
	ErrListAudiencesEmpty        = errors.New("payload datatable not empty")
	ErrAudienceDataTableEmpty    = errors.New("payload datatable not empty")
	ErrCreateCustomAudienceEmpty = errors.New("create data must be not empty")
	ErrUpdateCustomAudienceEmpty = errors.New("update data must be not empty")
	ErrDeleteCustomAudienceEmpty = errors.New("delete datas must be not empty")
	ErrNotFoundAudienceID        = errors.New("not found audience id")
)
