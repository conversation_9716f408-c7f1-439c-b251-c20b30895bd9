package routes

import (
	clientR "godsp/modules/admin/client/repository/mongo"
	adAccR "godsp/modules/facebook/ad_account/repository/mongo"

	"godsp/modules/facebook/adset/repository/mongo"
	"godsp/modules/facebook/adset/transport/handlers"
	"godsp/modules/facebook/adset/usecase"

	"github.com/dev-networldasia/dspgos/sctx/component/mongodb"

	"github.com/dev-networldasia/dspgos/sctx/configs"

	"github.com/dev-networldasia/dspgos/sctx"

	"github.com/gofiber/fiber/v2"
)

type ComposerAdset interface {
	ListAdsetHdl() fiber.Handler
}

func ComposerAdsetService(serviceCtx sctx.ServiceContext) ComposerAdset {
	mongoDB := serviceCtx.MustGet(configs.KeyCompMongoDB).(mongodb.MongoComponent).GetDatabase()
	logger := serviceCtx.MustGet(configs.KeyLoggerDaily).(sctx.AppLoggerDaily).GetLogger("webfb")

	// Init api
	repo := mongo.NewAdsetRepo(mongoDB)
	adAccRepo := adAccR.NewAdAccountRepo(mongoDB)
	clientRepo := clientR.NewClientRepo(mongoDB)
	usc := usecase.NewAdsetUsc(repo, logger, adAccRepo, clientRepo)
	hdl := handlers.NewAdsetHdl(usc)
	return hdl
}
