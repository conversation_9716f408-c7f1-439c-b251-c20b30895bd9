package routes

import (
	"github.com/dev-networldasia/dspgos/sctx"

	"github.com/gofiber/fiber/v2"
)

func SetupRoutesAdAcount(app *fiber.App, serviceCtx sctx.ServiceContext, midds ...fiber.Handler) {
	group := app.Group("dsp/facebook/ad-account")
	{
		if len(midds) > 0 {
			for _, midd := range midds {
				group.Use(midd)
			}
		}

		composerAdAcountSVC := ComposerAdAcountService(serviceCtx)
		group.Get("/list", composerAdAcountSVC.ListAdAccountHandler()).Name("fb.adaccount.list")
		group.Get("/add", composerAdAcountSVC.ListAdAccountHandler()).Name("fb.adaccount.add")

	}
	apiGroup := app.Group("dsp/facebook/api/ad-account")
	{
		if len(midds) > 0 {
			for _, midd := range midds {
				apiGroup.Use(midd)
			}
		}
		compAdAccountApiSVC := ComposerApiAdAcountService(serviceCtx)
		apiGroup.Patch("/reload", compAdAccountApiSVC.ReloadAdAccountApi()).Name("fb.adaccount.reload")
		apiGroup.Post("/list-datatable", compAdAccountApiSVC.ListDatatableAdAccountApi()).Name("fb.adaccount.list_datatable")
	}
}
