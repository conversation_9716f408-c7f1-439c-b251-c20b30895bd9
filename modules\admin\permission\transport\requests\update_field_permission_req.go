package requests

import (
	"godsp/modules/admin/permission/common/errs"

	"github.com/go-playground/validator/v10"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

type PayloadPermissionUpdateField struct {
	ID     primitive.ObjectID `form:"id" validate:"required"`
	UserID primitive.ObjectID `form:"-" json:"-"`
}

func (req *PayloadPermissionUpdateField) Validate() error {
	validate := validator.New()

	err := validate.Struct(req)
	if err != nil {
		for _, err := range err.(validator.ValidationErrors) {
			switch err.Field() {
			case "ID":
				return errs.ErrIDPermissionValidate
			}
		}
	}

	return nil
}
