package mapping

import (
	"godsp/modules/admin/client/entity"
	"godsp/modules/admin/client/transport/responses"
)

func MapperListSelectClient(clients []*entity.ClientEntity) []*responses.ClientListSelect {
	var data []*responses.ClientListSelect
	for _, c := range clients {
		data = append(data, &responses.ClientListSelect{
			ID:      c.ID,
			Name:    c.Name,
			Logo:    c.Logo,
			Company: c.Company,
			Email:   c.Email,
			Domain:   c.Domain,
		})
	}
	return data
}
