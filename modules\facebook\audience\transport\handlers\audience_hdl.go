package handlers

import (
	"context"
	"godsp/conf"
	clientE "godsp/modules/admin/client/entity"
	adAccRes "godsp/modules/facebook/ad_account/transport/response"

	"godsp/pkg/gos/utils"
	"godsp/pkg/sctx/core"

	"github.com/gofiber/fiber/v2"
)

type AudienceUsc interface {
	ListClientAudienceUsc(ctx context.Context) ([]*clientE.ClientEntity, error)
	ListAdAccountAudienceUsc(ctx context.Context) ([]*adAccRes.AdAccountEditClient, error)
}
type audienceHdl struct {
	usc AudienceUsc
}

func NewAudienceHdl(usc AudienceUsc) *audienceHdl {
	return &audienceHdl{
		usc,
	}
}

/**
 * list audience hdl
 */
func (h *audienceHdl) ListAudienceHandler() fiber.Handler {
	return func(c *fiber.Ctx) error {
		user, err := utils.GetInfoUserBasic(c.Context())
		if err != nil && user.RoleName != conf.SysConf.RoleAdmin {
			c.Redirect("/page-permission-denied")
		}

		clients, _ := h.usc.ListClientAudienceUsc(c.Context())
		adAccounts, _ := h.usc.ListAdAccountAudienceUsc(c.Context())

		return c.Render("facebook/audiences/index", fiber.Map{
			"authPermission": core.GetPermission(c.Context()).GetPermissions(),
			"clients":        clients,
			"adAccounts":     adAccounts,
			"userInfo": map[string]interface{}{
				"roleName": user.RoleName,
				"clientId": user.ClientId,
			},
		},
		)
	}
}
