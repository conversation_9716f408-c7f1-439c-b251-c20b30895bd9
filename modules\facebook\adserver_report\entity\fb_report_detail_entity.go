package entity

// FacebookAdInsight maps all available Facebook Insights fields into a MongoDB document
type FBReportDetailEntity struct {
	// ========== Insight Metadata ==========
	CampaignID      string `json:"campaign_id,omitempty" bson:"campaign_id,omitempty"`
	CampaignName    string `json:"campaign_name,omitempty" bson:"campaign_name,omitempty"`
	AccountID       string `json:"account_id,omitempty" bson:"account_id,omitempty"`
	AccountName     string `json:"account_name,omitempty" bson:"account_name,omitempty"`
	AccountCurrency string `json:"account_currency,omitempty" bson:"account_currency,omitempty"`
	// ID                 primitive.ObjectID `json:"_id,omitempty" bson:"_id,omitempty"`
	AdID               string `json:"ad_id,omitempty" bson:"ad_id,omitempty"`
	AdName             string `json:"ad_name,omitempty" bson:"ad_name,omitempty"`
	AdSetID            string `json:"adset_id,omitempty" bson:"adset_id,omitempty"`
	AdSetName          string `json:"adset_name,omitempty" bson:"adset_name,omitempty"`
	Objective          string `json:"objective,omitempty" bson:"objective,omitempty"`
	BuyingType         string `json:"buying_type,omitempty" bson:"buying_type,omitempty"`
	AttributionSetting string `json:"attribution_setting,omitempty" bson:"attribution_setting,omitempty"`
	DateStart          string `json:"date_start,omitempty" bson:"date_start,omitempty"`
	DateStop           string `json:"date_stop,omitempty" bson:"date_stop,omitempty"`
	Date               string `bson:"date,omitempty" json:"date"`

	// ========== Mentric Prority ==========
	Impressions    int32 `json:"impressions,omitempty" bson:"impressions,omitempty"`
	Reach          int32 `json:"reach,omitempty" bson:"reach,omitempty"`
	VideoView      int32 `json:"video_view,omitempty" bson:"video_view,omitempty"` // ----> 3 Second video plays
	PostReaction   int32 `json:"post_reaction,omitempty" bson:"post_reaction,omitempty"`
	PostComment    int32 `json:"post_comment,omitempty" bson:"comment,omitempty"`
	PostShare      int32 `json:"post_share,omitempty" bson:"post,omitempty"`                       // post share
	Clicks         int32 `json:"clicks,omitempty" bson:"clicks,omitempty"`                         // click all
	LinkClick      int32 `json:"link_click,omitempty" bson:"link_click,omitempty"`
	PostEngagement int32 `json:"post_engagement,omitempty" bson:"post_engagement,omitempty"`
	PostSave       int32 `json:"post_save,omitempty" bson:"onsite_conversion.post_save,omitempty"` // post share
	PageLike       int32 `json:"page_like,omitempty" bson:"like,omitempty"`

	// ========== Demographics ==========
	Age               string `json:"age,omitempty" bson:"age,omitempty"`
	Gender            string `json:"gender,omitempty" bson:"gender,omitempty"`
	Country           string `json:"country,omitempty" bson:"country,omitempty"`
	Placement         string `json:"placement,omitempty" bson:"placement,omitempty"`
	DevicePlatform    string `json:"device_platform,omitempty" bson:"device_platform,omitempty"`
	PublisherPlatform string `json:"publisher_platform,omitempty" bson:"publisher_platform,omitempty"`
	ImpressionDevice  string `json:"impression_device,omitempty" bson:"impression_device,omitempty"`

	// ========== Performance Metrics ==========
	Frequency        float64 `json:"frequency,omitempty" bson:"frequency,omitempty"`
	Spend            float64 `json:"spend,omitempty" bson:"spend,omitempty"`
	SocialSpend      int32   `json:"social_spend,omitempty" bson:"social_spend,omitempty"`
	CPM              float64 `json:"cpm,omitempty" bson:"cpm,omitempty"`
	CTR              float64 `json:"ctr,omitempty" bson:"ctr,omitempty"`
	CPC              float64 `json:"cpc,omitempty" bson:"cpc,omitempty"`
	CPP              float64 `json:"cpp,omitempty" bson:"cpp,omitempty"`
	WishBid          float64 `json:"wish_bid,omitempty" bson:"wish_bid,omitempty"`
	OptimizationGoal string  `bson:"optimization_goal,omitempty" json:"optimization_goal"`

	// ========== Engagement ==========
	InlineLinkClicks        int32         `json:"inline_link_clicks,omitempty" bson:"inline_link_clicks,omitempty"`
	InlineLinkClickCtr      float64       `json:"inline_link_click_ctr,omitempty" bson:"inline_link_click_ctr,omitempty"`
	InlinePostEngagement    float64       `json:"inline_post_engagement,omitempty" bson:"inline_post_engagement,omitempty"`
	UniqueClicks            int32         `json:"unique_clicks,omitempty" bson:"unique_clicks,omitempty"`
	UniqueCTR               float64       `json:"unique_ctr,omitempty" bson:"unique_ctr,omitempty"`
	UniqueOutboundClicks    int32         `json:"unique_outbound_clicks,omitempty" bson:"unique_outbound_clicks,omitempty"`
	WebsiteCTR              []ActionStats `json:"website_ctr,omitempty" bson:"website_ctr,omitempty"`
	Actions                 []ActionStats `json:"actions,omitempty" bson:"actions,omitempty"`
	ActionValues            []ActionStats `json:"action_values,omitempty" bson:"action_values,omitempty"`
	Conversions             []ActionStats `json:"conversions,omitempty" bson:"conversions,omitempty"`
	ConversionValues        []ActionStats `json:"conversion_values,omitempty" bson:"conversion_values,omitempty"`
	CostPerActionType       []ActionStats `json:"cost_per_action_type,omitempty" bson:"cost_per_action_type,omitempty"`
	CostPerConversion       []ActionStats `json:"cost_per_conversion,omitempty" bson:"cost_per_conversion,omitempty"`
	CostPerInlineLinkClick  float64       `json:"cost_per_inline_link_click,omitempty" bson:"cost_per_inline_link_click,omitempty"`
	CostPerInlinePostEngage float64       `json:"cost_per_inline_post_engagement,omitempty" bson:"cost_per_inline_post_engagement,omitempty"`

	// ========== Video Metrics ==========
	CostPerThruplay            float64       `json:"cost_per_thruplay,omitempty" bson:"cost_per_thruplay,omitempty"`
	CostPer15SecVideoView      float64       `json:"cost_per_15_sec_video_view,omitempty" bson:"cost_per_15_sec_video_view,omitempty"`
	VideoAvgTimeWatchedActions float64       `json:"video_avg_time_watched_actions,omitempty" bson:"video_avg_time_watched_actions,omitempty"`
	VideoPlayActions           []ActionStats `json:"video_play_actions,omitempty" bson:"video_play_actions,omitempty"`
	Video30SecWatched          []ActionStats `json:"video_30_sec_watched_actions,omitempty" bson:"video_30_sec_watched_actions,omitempty"`
	VideoTimeWatched           []ActionStats `json:"video_time_watched_actions,omitempty" bson:"video_time_watched_actions,omitempty"`
	VideoRetention15s          []Histogram   `json:"video_play_retention_0_to_15s_actions,omitempty" bson:"video_play_retention_0_to_15s_actions,omitempty"`
	VideoRetention60s          []Histogram   `json:"video_play_retention_20_to_60s_actions,omitempty" bson:"video_play_retention_20_to_60s_actions,omitempty"`
	VideoRetentionGraph        []Histogram   `json:"video_play_retention_graph_actions,omitempty" bson:"video_play_retention_graph_actions,omitempty"`
	VideoPlayCurveActions      []Histogram   `json:"video_play_curve_actions,omitempty" bson:"video_play_curve_actions,omitempty"`
	VideoP25Watched            float64       `json:"video_p25_watched_actions,omitempty" bson:"video_p25_watched_actions,omitempty"`
	VideoP50Watched            float64       `json:"video_p50_watched_actions,omitempty" bson:"video_p50_watched_actions,omitempty"`
	VideoP75Watched            float64       `json:"video_p75_watched_actions,omitempty" bson:"video_p75_watched_actions,omitempty"`
	VideoP95Watched            float64       `json:"video_p95_watched_actions,omitempty" bson:"video_p95_watched_actions,omitempty"`
	VideoP100Watched           float64       `json:"video_p100_watched_actions,omitempty" bson:"video_p100_watched_actions,omitempty"`
}

// type FBReportDetailEntity struct {
// 	ID                      primitive.ObjectID `bson:"_id,omitempty" json:"_id"`
// 	AccountID               string             `bson:"account_id,omitempty" json:"account_id"`
// 	AccountName             string             `bson:"account_name,omitempty" json:"account_name"`
// 	AccountCurrency         string             `bson:"account_currency,omitempty" json:"account_currency"`
// 	AdID                    string             `bson:"ad_id,omitempty" json:"ad_id"`
// 	AdName                  string             `bson:"ad_name,omitempty" json:"ad_name"`
// 	AdSetID                 string             `bson:"adset_id,omitempty" json:"adset_id"`
// 	AdSetName               string             `bson:"adset_name,omitempty" json:"adset_name"`
// 	CampaignID              string             `bson:"campaign_id,omitempty" json:"campaign_id"`
// 	CampaignName            string             `bson:"campaign_name,omitempty" json:"campaign_name"`
// 	BuyingType              string             `bson:"buying_type,omitempty" json:"buying_type"`
// 	Objective               string             `bson:"objective,omitempty" json:"objective"`
// 	AttributionSetting      string             `bson:"attribution_setting,omitempty" json:"attribution_setting"`
// 	OptimizationGoal        string             `bson:"optimization_goal,omitempty" json:"optimization_goal"`
// 	Clicks                  int32              `bson:"clicks,omitempty" json:"clicks"`
// 	Impressions             int32              `bson:"impressions,omitempty" json:"impressions"`
// 	Reach                   int32              `bson:"reach,omitempty" json:"reach"`
// 	Frequency               float64            `bson:"frequency,omitempty" json:"frequency"`
// 	Spend                   float64            `bson:"spend,omitempty" json:"spend"`
// 	SocialSpend             int32              `bson:"social_spend,omitempty" json:"social_spend"`
// 	CPM                     float64            `bson:"cpm,omitempty" json:"cpm"`
// 	CTR                     float64            `bson:"ctr,omitempty" json:"ctr"`
// 	CPC                     float64            `bson:"cpc,omitempty" json:"cpc"`
// 	CPP                     float64            `bson:"cpp,omitempty" json:"cpp"`
// 	WishBid                 float64            `bson:"wish_bid,omitempty" json:"wish_bid"`
// 	CostPerInlineLinkClick  float64            `bson:"cost_per_inline_link_click,omitempty" json:"cost_per_inline_link_click"`
// 	CostPerInlinePostEngage float64            `bson:"cost_per_inline_post_engagement,omitempty" json:"cost_per_inline_post_engagement"`
// 	CostPerThruplay         float64            `bson:"cost_per_thruplay,omitempty" json:"cost_per_thruplay"`
// 	CostPer15SecVideoView   float64            `bson:"cost_per_15_sec_video_view,omitempty" json:"cost_per_15_sec_video_view"`
// 	InlineLinkClicks        int32              `bson:"inline_link_clicks,omitempty" json:"inline_link_clicks"`
// 	InlineLinkClickCtr      float64            `bson:"inline_link_click_ctr,omitempty" json:"inline_link_click_ctr"`
// 	InlinePostEngagement    int32              `bson:"inline_post_engagement,omitempty" json:"inline_post_engagement"`
// 	UniqueClicks            int32              `bson:"unique_clicks,omitempty" json:"unique_clicks"`
// 	UniqueCTR               float64            `bson:"unique_ctr,omitempty" json:"unique_ctr"`
// 	WebsiteCTR              []ActionStats      `bson:"website_ctr,omitempty" json:"website_ctr"`
// 	UniqueOutboundClicks    int32              `bson:"unique_outbound_clicks,omitempty" json:"unique_outbound_clicks"`
// 	VideoPlayActions        []ActionStats      `bson:"video_play_actions,omitempty" json:"video_play_actions"`
// 	Actions                 []ActionStats      `bson:"actions,omitempty" json:"actions"`
// 	ActionValues            []ActionStats      `bson:"action_values,omitempty" json:"action_values"`
// 	Conversions             []ActionStats      `bson:"conversions,omitempty" json:"conversions"`
// 	ConversionValues        []ActionStats      `bson:"conversion_values,omitempty" json:"conversion_values"`
// 	CostPerActionType       []ActionStats      `bson:"cost_per_action_type,omitempty" json:"cost_per_action_type"`
// 	CostPerConversion       []ActionStats      `bson:"cost_per_conversion,omitempty" json:"cost_per_conversion"`
// 	Video30SecWatched       []ActionStats      `bson:"video_30_sec_watched_actions,omitempty" json:"video_30_sec_watched_actions"`
// 	VideoTimeWatched        []ActionStats      `bson:"video_time_watched_actions,omitempty" json:"video_time_watched_actions"`
// 	VideoRetention15s       []Histogram        `bson:"video_play_retention_0_to_15s_actions,omitempty" json:"video_play_retention_0_to_15s_actions"`
// 	VideoRetention60s       []Histogram        `bson:"video_play_retention_20_to_60s_actions,omitempty" json:"video_play_retention_20_to_60s_actions"`
// 	VideoRetentionGraph     []Histogram        `bson:"video_play_retention_graph_actions,omitempty" json:"video_play_retention_graph_actions"`
// 	VideoPlayCurveActions   []Histogram        `bson:"video_play_curve_actions,omitempty" json:"video_play_curve_actions"`
// 	VideoP25Watched         float64            `bson:"video_p25_watched_actions,omitempty" json:"video_p25_watched_actions"`
// 	VideoP50Watched         float64            `bson:"video_p50_watched_actions,omitempty" json:"video_p50_watched_actions"`
// 	VideoP75Watched         float64            `bson:"video_p75_watched_actions,omitempty" json:"video_p75_watched_actions"`
// 	VideoP95Watched         float64            `bson:"video_p95_watched_actions,omitempty" json:"video_p95_watched_actions"`
// 	VideoP100Watched        float64            `bson:"video_p100_watched_actions,omitempty" json:"video_p100_watched_actions"`
// 	DateStart               string             `bson:"date_start,omitempty" json:"date_start"`
// 	DateStop                string             `bson:"date_stop,omitempty" json:"date_stop"`
// 	Age                     string             `bson:"age,omitempty" json:"age"`
// 	Gender                  string             `bson:"gender,omitempty" json:"gender"`
// 	Country                 string             `bson:"country,omitempty" json:"country"`
// 	Placement               string             `bson:"placement,omitempty" json:"placement"`
// 	DevicePlatform          string             `bson:"device_platform,omitempty" json:"device_platform"`
// 	PublisherPlatform       string             `bson:"publisher_platform,omitempty" json:"publisher_platform"`
// 	ImpressionDevice        string             `bson:"impression_device,omitempty" json:"impression_device"`

// 	// Field Update
// 	Date                       string  `bson:"date,omitempty" json:"date"`
// 	CanvasAvgViewPercent       string  `bson:"canvas_avg_view_percent,omitempty" json:"canvas_avg_view_percent"`
// 	CanvasAvgViewTime          string  `bson:"canvas_avg_view_time,omitempty" json:"canvas_avg_view_time"`
// 	CostPerOutboundClick       string  `bson:"cost_per_outbound_click,omitempty" json:"cost_per_outbound_click"`
// 	CostPerUniqueActionType    string  `bson:"cost_per_unique_action_type,omitempty" json:"cost_per_unique_action_type"`
// 	CostPerUniqueClick         string  `bson:"cost_per_unique_click,omitempty" json:"cost_per_unique_click"`
// 	CreatedTime                string  `bson:"created_time,omitempty" json:"created_time"`
// 	UpdateTime                 string  `bson:"updated_time" json:"updated_time"`
// 	InteractiveComponentTap    string  `bson:"interactive_component_tap,omitempty" json:"interactive_component_tap"`
// 	OutboundClicksCtr          string  `bson:"outbound_clicks_ctr,omitempty" json:"outbound_clicks_ctr"`
// 	RunDate                    string  `bson:"run_date,omitempty" json:"run_date"`
// 	WebsitePurchaseRoas        string  `bson:"website_purchase_roas,omitempty" json:"website_purchase_roas"`
// 	VideoAvgTimeWatchedActions float64 `json:"video_avg_time_watched_actions,omitempty" bson:"video_avg_time_watched_actions"`

// 	// UpdatedTime             string             `bson:"updated_time,omitempty" json:"updated_time"`
// 	// CreatedTime             string             `bson:"created_time,omitempty" json:"created_time"`
// }

type ActionStats struct {
	ActionType          string  `bson:"action_type,omitempty" json:"action_type"`
	Value               float64 `bson:"value,omitempty" json:"value"`
	OneDayClick         float64 `bson:"1d_click,omitempty" json:"1d_click"`
	SevenDayClick       float64 `bson:"7d_click,omitempty" json:"7d_click"`
	TwentyEightDayClick float64 `bson:"28d_click,omitempty" json:"28d_click"`
	OneDayView          float64 `bson:"1d_view,omitempty" json:"1d_view"`
	SevenDayView        float64 `bson:"7d_view,omitempty" json:"7d_view"`
	TwentyEightDayView  float64 `bson:"28d_view,omitempty" json:"28d_view"`
}

type Histogram struct {
	Key   string  `bson:"key,omitempty" json:"key"`
	Value float64 `bson:"value,omitempty" json:"value"`
}

type Action struct {
	ActionType string `bson:"action_type,omitempty" json:"action_type"`
	Value      int    `bson:"value,omitempty" json:"value"`
}

func (FBReportDetailEntity) CollectionName() string {
	return "fb_report_detail"
}
