package requests

import (
	"encoding/json"
	"godsp/modules/facebook/ad_creative/common/enums"
	"godsp/modules/facebook/ad_creative/common/errs"
	"godsp/modules/facebook/ad_creative/common/rules"
	"godsp/modules/facebook/common/fbenums"
	v20 "godsp/pkg/fb-marketing/marketing/v20"

	"github.com/go-playground/validator/v10"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

type CreateAdCreativeReq struct {
	AdAccountID string             `json:"ad_account_id" validate:"required"`
	UserID      primitive.ObjectID `json:"-"`
	ClientID    primitive.ObjectID `json:"-"`

	// PageID           string               `json:"page_id,omitempty" validate:"numeric,gt=0"`
	ActorID          string               `json:"actor_id" validate:"required"`
	Body             string               `json:"body,omitempty" validate:"omitempty"`
	Status           *string              `json:"status,omitempty" validate:"omitempty"`
	CallToActionType string               `json:"call_to_action_type,omitempty" validate:"omitempty"`
	ImageHash        string               `json:"image_hash,omitempty" validate:"omitempty"`
	Title            string               `json:"title,omitempty" validate:"omitempty"`
	ObjectStorySpec  *v20.ObjectStorySpec `json:"object_story_spec,omitempty"`
	AssetFeedSpec    map[string]any       `json:"asset_feed_spec,omitempty"`
	ObjectStoryID    *string              `json:"object_story_id,omitempty" validate:"omitempty"`
	URLTags          string               `json:"url_tags,omitempty" validate:"omitempty"`
	ProductSetID     *string              `json:"product_set_id,omitempty" validate:"omitempty"`
	TemplateURLSpec  *json.RawMessage     `json:"template_url_spec,omitempty" validate:"omitempty"`

	ApplinkTreatment     string                    `json:"applink_treatment,omitempty" validate:"omitempty"`
	ContextualMultiAds   *v20.ContextualMultiAds   `json:"contextual_multi_ads,omitempty" validate:"omitempty,structonly"`
	DegreesOfFreedomSpec *v20.DegreesOfFreedomSpec `json:"degrees_of_freedom_spec,omitempty"`
}

func (req *CreateAdCreativeReq) Validate() []string {
	validate := validator.New()

	validate.RegisterValidation("ruleEnrollStatus", rules.RuleEnrollStatus)

	var validationErrors []string
	err := validate.Struct(req)
	if err != nil {
		for _, err := range err.(validator.ValidationErrors) {
			switch err.Field() {
			case "AccountID":
				validationErrors = append(validationErrors, errs.ErrReloadAdCreativeAdAccountId.Error())
			case "ActorID":
				validationErrors = append(validationErrors, errs.ErrReloadAdCreativeActorID.Error())
			// case "PageID":
			// 	validationErrors = append(validationErrors, errs.ErrPageIdOfPost.Error())
			case "ContextualMultiAds.EnrollStatus":
				validationErrors = append(validationErrors, errs.ErrStatus.Error())
			}
		}
	}

	if validationErrors == nil && req.ContextualMultiAds != nil && req.ContextualMultiAds.EnrollStatus != "" {
		req.ContextualMultiAds.EnrollStatus = enums.EnrollStatus[req.ContextualMultiAds.EnrollStatus]
	}

	if req.Status != nil {
		if status, ok := fbenums.StatusFB[*req.Status]; !ok {
			validationErrors = append(validationErrors, errs.ErrStatus.Error())
		} else {
			req.Status = &status
		}
	}

	return validationErrors
}
