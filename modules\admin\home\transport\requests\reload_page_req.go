package requests

import (
	"godsp/modules/facebook/pages/common/errs"

	"github.com/go-playground/validator/v10"
)

type ReloadPageReq struct {
	PageId string `json:"page_id,omitempty" validate:"omitempty,numeric"`
}

func (req *ReloadPageReq) Validate() error {
	validate := validator.New()
	err := validate.Struct(req)
	if err != nil {
		for _, validationErr := range err.(validator.ValidationErrors) {
			switch validationErr.Field() {
			case "PageId":
				if validationErr.Tag() == "numeric" {
					return errs.ErrPageIdNotNumber
				}
			}
		}
	}

	return nil
}
