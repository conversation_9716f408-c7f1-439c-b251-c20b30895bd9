package mapping

import (
	"fmt"
	"godsp/modules/facebook/audience/entity"
	"godsp/modules/facebook/audience/transport/responses"
)

/***
 * Mapper entity to datatable
 */
func MapperEntityToDatatable(audiences *[]entity.CustomAudienceEntity) *[]responses.AudiencesDatatable {
	if audiences == nil {
		return nil
	}

	var datas []responses.AudiencesDatatable
	for _, audience := range *audiences {
		datas = append(datas, responses.AudiencesDatatable{
			DTRowId:                    fmt.Sprintf("row_%s", audience.CustomAudienceID),
			CustomAudienceID:           audience.CustomAudienceID,
			AccountID:                  audience.AccountID,
			Name:                       audience.Name,
			Adaccounts:                 MapAdaccountsToV20(audience.Adaccounts),
			ApproximateCountUpperBound: audience.ApproximateCountUpperBound,
			ApproximateCountLowerBound: audience.ApproximateCountLowerBound,
			CustomerFileSource:         audience.CustomerFileSource,
			DataSource:                 MapEntityToV20DataSource(audience.DataSource),
			DeliveryStatus:             MapEntityToV20Status(audience.DeliveryStatus),
			Type:                       audience.Type,
			Subtype:                    audience.Subtype,
			LookalikeSpec:              MapEntityToV20LookalikeSpec(audience.LookalikeSpec),
			Targeting:                  MapTargetingToV20(audience.Targeting),
			RunStatus:                  audience.RunStatus,
		})
	}

	return &datas
}
