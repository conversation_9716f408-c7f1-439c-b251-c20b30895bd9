package requests

import (
	"github.com/go-playground/validator/v10"
	"godsp/modules/admin/billing/transport/rules"
	"godsp/modules/admin/common/admconst"
	"godsp/modules/admin/common/admfunc"
)

type PayloadListBillingApi struct {
	MClientId string `json:"m_client_id"`
	Platform  string `json:"platform"`
	Status    string `json:"status,omitempty"`
	Search    string `json:"search,omitempty"`
	Page      int    `json:"page,omitempty"`
	PageSize  int    `json:"page_size,omitempty"`
}

type ListBillingApiReq struct {
	Draw      int     `json:"draw" form:"draw"`
	Length    int     `json:"length" form:"length"`
	Start     int     `json:"start" form:"start"`
	Page      int     `json:"-" form:"-"`
	Order     []Order `json:"order"`
	SortField string  `json:"-" form:"-"`
	SortOrder int     `json:"-" form:"-"`
	Filter    Filter  `json:"filter" form:"filter"`
}

type Filter struct {
	Search   string `json:"search" form:"search"`
	Status   string `json:"status" form:"status" validate:"ruleBillingStatus"`
	ClientId string `json:"client_id" form:"client_id"`
}

type Order struct {
	Column int    `json:"column" form:"column"`
	Dir    string `json:"dir" form:"dir"`
	Name   string `json:"name" form:"name"`
}

func (req *ListBillingApiReq) Validate() error {
	validate := validator.New()
	validate.RegisterValidation("ruleBillingStatus", rules.RuleBillingStatus)
	err := validate.Struct(req)
	if err != nil {
		return err
	}

	req.SortOrder = 1
	if req.Order == nil && len(req.Order) == 0 {
		req.SortField = admconst.DEFAULT_SORT_COLUMN
	} else {
		orderColumn := req.Order[0].Column
		if sortField, ok := admconst.DefaultAvailableSortFields[orderColumn]; !ok {
			req.SortField = admconst.DEFAULT_SORT_COLUMN
		} else {
			req.SortField = sortField
		}

		if sortOrder := req.Order[0].Dir; sortOrder == admconst.DEFAULT_ORDER_DESCENDING {
			req.SortOrder = -1
		}

		if req.SortField == admconst.DefaultAvailableSortFields[1] {
			req.SortOrder = -1
		}
	}

	req.Page = admfunc.GetPageNumberFromDatatableRequest(req.Start, req.Length)
	//startTime, err := time.Parse(time.RFC3339, req.Time[0])
	//if err != nil {
	//	return nil
	//}
	//req.StartTime = &startTime
	//
	//endTime, err := time.Parse(time.RFC3339, req.Time[1])
	//if err != nil {
	//	req.StartTime = nil
	//	return nil
	//}
	//
	//req.EndTime = &endTime

	return nil
}
