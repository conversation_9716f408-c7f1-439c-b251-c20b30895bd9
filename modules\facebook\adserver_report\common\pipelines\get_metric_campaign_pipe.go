package pipelines

import (
	"godsp/modules/facebook/adserver_report/entity"
	"reflect"
	"strings"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
)

func GetMetricCampaignPipe(filter interface{}) mongo.Pipeline {
	project, fields := BuildProjectFromStruct()
	fieldsSum := []string{"clicks", "cpc", "cpm", "ctr", "impressions", "reach", "social_spend", "spend",
		"video_p25_watched_actions", "video_p50_watched_actions", "video_p75_watched_actions", "video_p95_watched_actions", "video_p100_watched_actions",
		"cost_per_15_sec_video_view", "cost_per_thruplay", "video_avg_time_watched_actions", "inline_link_clicks", "inline_link_click_ctr",
		"cost_per_inline_post_engagement", "frequency", "cost_per_inline_link_click",
	}
	fieldsFirst := ExculudeKeys(ExculudeKeys(fields, fieldsSum), []string{"actions"})

	groupStage1 := bson.M{
		"_id": bson.M{
			"action_type": "$actions.action_type",
			"campaign_id": "$campaign_id",
		},
		"totalValue": bson.D{{Key: "$sum", Value: "$actions.value"}},
		"origin_id":  bson.D{{Key: "$first", Value: "$_id"}},
	}

	// $First
	// for _, key := range fieldsFirst {
	// 	groupStage1[key] = bson.D{{Key: "$first", Value: "$" + key}}
	// }

	for _, key := range fieldsFirst {
		groupStage1[key] = bson.D{{
			Key: "$first",
			Value: bson.D{{
				Key: "$cond",
				Value: bson.A{
					bson.D{{Key: "$gt", Value: bson.A{"$" + key, 0}}},
					"$" + key,
					nil,
				},
			}},
		}}
	}

	// $Sum
	for _, key := range fieldsSum {
		groupStage1[key] = bson.D{{
			Key: "$sum",
			Value: bson.D{{
				Key: "$cond",
				Value: bson.A{
					bson.D{{Key: "$gt", Value: bson.A{"$" + key, 0}}},
					"$" + key,
					nil,
				},
			}},
		}}
	}

	// for _, key := range fieldsSum {
	// 	groupStage1[key] = bson.D{{Key: "$sum", Value: "$" + key}}
	// }

	//State 2
	groupStage2 := bson.M{
		"_id": "$campaign_id",
		"actions": bson.M{
			"$push": bson.M{
				"action_type": "$_id.action_type",
				"value":       "$totalValue",
			},
		},
		"origin_id": bson.D{{Key: "$first", Value: "$origin_id"}},
	}

	// $First
	for _, key := range fieldsFirst {
		groupStage2[key] = bson.D{{Key: "$first", Value: "$" + key}}
	}

	// $Max
	for _, key := range fieldsSum {
		groupStage2[key] = bson.D{{Key: "$max", Value: "$" + key}}
	}

	// for _, key := range fieldsSum {
	// 	groupStage2[key] = bson.D{{
	// 		Key: "$max",
	// 		Value: bson.D{{
	// 			Key: "$cond",
	// 			Value: bson.A{
	// 				bson.D{{Key: "$gt", Value: bson.A{"$" + key, 0}}},
	// 				"$" + key,
	// 				0,
	// 			},
	// 		}},
	// 	}}
	// }

	pipeline := mongo.Pipeline{
		{{Key: "$match", Value: filter}},
		{{Key: "$unwind", Value: "$actions"}},
		{{Key: "$group", Value: groupStage1}},
		{{Key: "$group", Value: groupStage2}},
		{{Key: "$project", Value: project}},
	}

	return pipeline
}

func GetMetricAdsetPipe(filter interface{}) mongo.Pipeline {
	project, fields := BuildProjectFromStruct()
	fieldsSum := []string{"clicks", "cpc", "cpm", "ctr", "impressions", "reach", "social_spend", "spend",
		"video_p25_watched_actions", "video_p50_watched_actions", "video_p75_watched_actions", "video_p95_watched_actions", "video_p100_watched_actions",
		"cost_per_15_sec_video_view", "cost_per_thruplay", "video_avg_time_watched_actions", "inline_link_clicks", "inline_link_click_ctr",
		"cost_per_inline_post_engagement", "frequency", "cost_per_inline_link_click",
	}
	fieldsFirst := ExculudeKeys(ExculudeKeys(fields, fieldsSum), []string{"actions"})

	groupStage1 := bson.M{
		"_id": bson.M{
			"action_type": "$actions.action_type",
			"adset_id":    "$adset_id",
		},
		"totalValue": bson.D{{Key: "$sum", Value: "$actions.value"}},
		"origin_id":  bson.D{{Key: "$first", Value: "$_id"}},
	}

	// $First
	// for _, key := range fieldsFirst {
	// 	groupStage1[key] = bson.D{{Key: "$first", Value: "$" + key}}
	// }
	for _, key := range fieldsFirst {
		groupStage1[key] = bson.D{{
			Key: "$first",
			Value: bson.D{{
				Key: "$cond",
				Value: bson.A{
					bson.D{{Key: "$gt", Value: bson.A{"$" + key, 0}}},
					"$" + key,
					nil,
				},
			}},
		}}
	}

	// $Sum
	// for _, key := range fieldsSum {
	// 	groupStage1[key] = bson.D{{Key: "$sum", Value: "$" + key}}
	// }
	for _, key := range fieldsSum {
		groupStage1[key] = bson.D{{
			Key: "$sum",
			Value: bson.D{{
				Key: "$cond",
				Value: bson.A{
					bson.D{{Key: "$gt", Value: bson.A{"$" + key, 0}}},
					"$" + key,
					nil,
				},
			}},
		}}
	}

	//State 2
	groupStage2 := bson.M{
		"_id": "$adset_id",
		"actions": bson.M{
			"$push": bson.M{
				"action_type": "$_id.action_type",
				"value":       "$totalValue",
			},
		},
		"origin_id": bson.D{{Key: "$first", Value: "$origin_id"}},
	}

	// $First
	for _, key := range fieldsFirst {
		groupStage2[key] = bson.D{{Key: "$first", Value: "$" + key}}
	}

	// $Sum
	for _, key := range fieldsSum {
		groupStage2[key] = bson.D{{Key: "$max", Value: "$" + key}}
	}

	pipeline := mongo.Pipeline{
		{{Key: "$match", Value: filter}},
		{{Key: "$unwind", Value: "$actions"}},
		{{Key: "$group", Value: groupStage1}},
		{{Key: "$group", Value: groupStage2}},
		{{Key: "$project", Value: project}},
	}

	return pipeline
}

func GetMetricAdPipe(filter interface{}) mongo.Pipeline {
	project, fields := BuildProjectFromStruct()
	fieldsSum := []string{}
	fieldUnwind := []string{"video_p25_watched_actions", "video_p50_watched_actions", "video_p75_watched_actions", "video_p95_watched_actions", "video_p100_watched_actions",
		"cost_per_15_sec_video_view", "video_avg_time_watched_actions", "cost_per_thruplay",
	}
	fieldsFirst := ExculudeKeys(ExculudeKeys(ExculudeKeys(fields, fieldsSum), []string{"actions"}), fieldUnwind)

	groupStage1 := bson.M{
		"_id": bson.M{
			"action_type": "$actions.action_type",
			"ad_id":       "$ad_id",
		},
		"totalValue": bson.D{{Key: "$sum", Value: "$actions.value"}},
		"origin_id":  bson.D{{Key: "$first", Value: "$_id"}},
	}

	// $First
	// for _, key := range fieldsFirst {
	// 	groupStage1[key] = bson.D{{Key: "$first", Value: "$" + key}}
	// }

	for _, key := range fieldsFirst {
		groupStage1[key] = bson.D{{
			Key: "$first",
			Value: bson.D{{
				Key: "$cond",
				Value: bson.A{
					bson.D{{Key: "$gt", Value: bson.A{"$" + key, 0}}},
					"$" + key,
					nil,
				},
			}},
		}}
	}

	// $Unwind
	for _, key := range fieldUnwind {
		groupStage1[key] = bson.D{{
			Key: "$max",
			Value: bson.D{{
				Key: "$cond",
				Value: bson.A{
					bson.D{{Key: "$gt", Value: bson.A{"$" + key + ".value", 0}}},
					"$" + key + ".value",
					nil,
				},
			}},
		}}
	}

	// $Sum
	// for _, key := range fieldsSum {
	// 	groupStage1[key] = bson.D{{Key: "$sum", Value: "$" + key}}
	// }

	for _, key := range fieldsSum {
		groupStage1[key] = bson.D{{
			Key: "$sum",
			Value: bson.D{{
				Key: "$cond",
				Value: bson.A{
					bson.D{{Key: "$gt", Value: bson.A{"$" + key, 0}}},
					"$" + key,
					nil,
				},
			}},
		}}
	}

	//State 2
	groupStage2 := bson.M{
		"_id": "$ad_id",
		"actions": bson.M{
			"$push": bson.M{
				"action_type": "$_id.action_type",
				"value":       "$totalValue",
			},
		},
		"origin_id": bson.D{{Key: "$first", Value: "$origin_id"}},
	}

	// $First
	for _, key := range fieldsFirst {
		groupStage2[key] = bson.D{{Key: "$first", Value: "$" + key}}
	}

	// $Unwind
	for _, key := range fieldUnwind {
		groupStage2[key] = bson.D{{Key: "$max", Value: "$" + key}}
	}

	// $Sum
	for _, key := range fieldsSum {
		groupStage2[key] = bson.D{{Key: "$max", Value: "$" + key}}
	}

	pipeline := mongo.Pipeline{
		{{Key: "$match", Value: filter}},
		{{Key: "$unwind", Value: "$actions"}},
		{{Key: "$unwind", Value: "$video_p25_watched_actions"}},
		{{Key: "$unwind", Value: "$video_p50_watched_actions"}},
		{{Key: "$unwind", Value: "$video_p75_watched_actions"}},
		{{Key: "$unwind", Value: "$video_p95_watched_actions"}},
		{{Key: "$unwind", Value: "$video_p100_watched_actions"}},
		{{Key: "$unwind", Value: "$cost_per_15_sec_video_view"}},
		{{Key: "$unwind", Value: "$video_avg_time_watched_actions"}},
		{{Key: "$unwind", Value: "$cost_per_thruplay"}},
		{{Key: "$group", Value: groupStage1}},
		{{Key: "$group", Value: groupStage2}},
		{{Key: "$project", Value: project}},
	}

	return pipeline
}

func BuildProjectFromStruct() (bson.M, []string) {
	t := reflect.TypeOf(entity.FBReportDetailEntity{})
	project := bson.M{}
	fields := []string{}

	for i := 0; i < t.NumField(); i++ {
		field := t.Field(i)

		// Bỏ qua field không export
		if field.PkgPath != "" {
			continue
		}

		tag := field.Tag.Get("bson")
		if tag != "" && tag != "-" {
			// Tách phần tag ra khỏi ",omitempty" nếu có
			key := tag
			if commaIdx := strings.Index(tag, ","); commaIdx != -1 {
				key = tag[:commaIdx]
			}
			project[key] = 1
			fields = append(fields, key)
		}
	}

	// Thay thế _id thành origin_id
	delete(project, "_id")
	project["_id"] = "$origin_id"

	return project, fields
}

func ExculudeKeys(a, b []string) []string {
	exclude := make(map[string]struct{}, len(b))
	for _, key := range b {
		exclude[key] = struct{}{}
	}
	var results []string
	for _, key := range a {
		if _, found := exclude[key]; !found {
			results = append(results, key)
		}
	}
	return results
}
