package entity

import (
	"encoding/json"
	adsetCamp "godsp/modules/facebook/campaign/entity"
	v20 "godsp/pkg/fb-marketing/marketing/v20"
	"time"

	"go.mongodb.org/mongo-driver/bson/primitive"
)

type AdsetEntity struct {
	ID primitive.ObjectID `json:"_id,omitempty" bson:"_id,omitempty"`

	AccountID  string `json:"account_id,omitempty" bson:"account_id"`
	CampaignID string `json:"campaign_id,omitempty" bson:"campaign_id"`
	IDAdset    string `json:"adset_id,omitempty" bson:"adset_id"`
	Name       string `json:"name,omitempty" bson:"name"`
	Status     string `json:"status,omitempty" bson:"status"`

	AttributionSpec json.RawMessage `json:"attribution_spec,omitempty" bson:"attribution_spec"`
	BidAmount       uint64          `json:"bid_amount,omitempty" bson:"bid_amount"`
	BidStrategy     string          `json:"bid_strategy,omitempty" bson:"bid_strategy"`
	BillingEvent    string          `json:"billing_event,omitempty" bson:"billing_event"`
	BudgetRemaining float64         `json:"budget_remaining,omitempty,string" bson:"budget_remaining"`

	ConfiguredStatus           string                     `json:"configured_status,omitempty" bson:"configured_status"`
	CreatedTime                time.Time                  `json:"created_time,omitempty" bson:"created_time"`
	DailyBudget                float64                    `json:"daily_budget,omitempty,string" bson:"daily_budget"`
	DailyMinSpendTarget        uint64                     `json:"daily_min_spend_target,omitempty,string" bson:"daily_min_spend_target"`
	DailySpendCap              uint64                     `json:"daily_spend_cap,omitempty,string" bson:"daily_spend_cap"`
	DestinationType            string                     `json:"destination_type,omitempty" bson:"destination_type"`
	DeliveryEstimate           *v20.DeliveryEstimate      `json:"delivery_estimate,omitempty" bson:"delivery_estimate"`
	EffectiveStatus            string                     `json:"effective_status,omitempty" bson:"effective_status"`
	EndTime                    *time.Time                 `json:"end_time,omitempty" bson:"end_time"`
	FrequencyControlSpecs      []v20.FrequencyControlSpec `json:"frequency_control_specs,omitempty" bson:"frequency_control_specs"`
	LifetimeBudget             float64                    `json:"lifetime_budget,omitempty,string" bson:"lifetime_budget"`
	LifetimeMinSpendTarget     uint64                     `json:"lifetime_min_spend_target,omitempty,string" bson:"lifetime_min_spend_target"`
	LifeTimeSpendCap           uint64                     `json:"lifetime_spend_cap,omitempty,string" bson:"lifetime_spend_cap"`
	LifetimeImps               uint64                     `json:"lifetime_imps,omitempty" bson:"lifetime_imps"`
	OptimizationGoal           string                     `json:"optimization_goal,omitempty" bson:"optimization_goal"`
	PacingType                 []string                   `json:"pacing_type,omitempty" bson:"pacing_type"`
	PromotedObject             *v20.PromotedObject        `json:"promoted_object,omitempty" bson:"promoted_object"`
	RecurringBudgetSemantics   bool                       `json:"recurring_budget_semantics,omitempty" bson:"recurring_budget_semantics"`
	StartTime                  time.Time                  `json:"start_time,omitempty" bson:"start_time"`
	Targeting                  *v20.Targeting             `json:"targeting,omitempty" bson:"targeting"`
	UpdatedTime                time.Time                  `json:"updated_time,omitempty" bson:"updated_time"`
	TargetingOptimizationTypes map[string]int32           `json:"targeting_optimization_types,omitempty" bson:"targeting_optimization_types"`
	DSABeneficiary             string                     `json:"dsa_beneficiary,omitempty" bson:"dsa_beneficiary"`
	DSAPayor                   string                     `json:"dsa_payor,omitempty" bson:"dsa_payor"`
	IsDynamicCreative          bool                       `json:"is_dynamic_creative,omitempty" bson:"is_dynamic_creative"`
	IsBudgetScheduleEnabled    bool                       `json:"is_budget_schedule_enabled" bson:"is_budget_schedule_enabled"`

	Campaign *adsetCamp.CampaignEntity `json:"campaign,omitempty" bson:"campaign"`

	CreatedBy primitive.ObjectID `json:"created_by" bson:"created_by"`
	CreatedAt time.Time          `json:"created_at" bson:"created_at"`
	UpdatedBy primitive.ObjectID `json:"updated_by" bson:"updated_by"`
	UpdatedAt time.Time          `json:"updated_at" bson:"updated_at"`

	ListUserIDs []primitive.ObjectID `json:"list_user_ids,omitempty" bson:"list_user_ids"`
	ClientID    primitive.ObjectID   `bson:"client_id" json:"client_id,omitempty"`
	// Update lastest
}

func (AdsetEntity) CollectionName() string {
	return "fb_adsets"
}
