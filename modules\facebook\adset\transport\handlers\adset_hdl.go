package handlers

import (
	"context"
	clientE "godsp/modules/admin/client/entity"
	adAccRes "godsp/modules/facebook/ad_account/transport/response"
	"godsp/pkg/gos/utils"
	"godsp/pkg/sctx/core"

	"github.com/gofiber/fiber/v2"
)

type AdsetUsc interface {
	ListAdAccountCampaignUsc(ctx context.Context) ([]*adAccRes.AdAccountEditClient, error)
	ListClientCampaignUsc(ctx context.Context) ([]*clientE.ClientEntity, error)
}
type adsetHdl struct {
	usc AdsetUsc
}

func NewAdsetHdl(usc AdsetUsc) *adsetHdl {
	return &adsetHdl{
		usc,
	}
}
func (h *adsetHdl) ListAdsetHdl() fiber.Handler {
	return func(c *fiber.Ctx) error {

		adAccounts, _ := h.usc.ListAdAccountCampaignUsc(c.Context())
		clients, _ := h.usc.ListClientCampaignUsc(c.Context())

		userInfo, err := utils.GetInfoUserAuth(c.Context())
		if err != nil {
			return core.ReturnErrForPermissionDenied(c)
		}
		return c.Render("facebook/camp-adset-ad/index", fiber.Map{
			"authPermission": core.GetPermission(c.Context()).GetPermissions(),
			"userInfo":       userInfo,
			"Numbers":        generateNumbers(),
			"adAccounts":     adAccounts,
			"clients":        clients,
		})

	}
}

// func (h *adsetHdl) ListAdsetHdl() fiber.Handler {
// 	return func(c *fiber.Ctx) error {

// 		userInfo, err := utils.GetInfoUserAuth(c.Context())
// 		if err != nil {
// 			// return core.ReturnErrForPermissionDenied(c)
// 			c.Redirect("/page-permission-denied")
// 		}

// 		return c.Render("facebook/camp-adset-ad/index", fiber.Map{
// 			"authPermission": core.GetPermission(c.Context()).GetPermissions(),
// 			"userInfo":       userInfo,
// 			"Numbers":        generateNumbers()})

// 	}
// }

func generateNumbers() []int {
	var numbers []int
	for i := 18; i <= 65; i++ {
		numbers = append(numbers, i)
	}

	return numbers
}
