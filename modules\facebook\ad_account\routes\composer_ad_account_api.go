package routes

import (
	"godsp/modules/facebook/ad_account/repository/mongo"
	"godsp/modules/facebook/ad_account/transport/api"
	"godsp/modules/facebook/ad_account/usecase"
	"godsp/pkg/sctx/component/facebook/fbmarketing"

	"github.com/dev-networldasia/dspgos/sctx/component/mongodb"

	"github.com/dev-networldasia/dspgos/sctx/configs"

	"github.com/dev-networldasia/dspgos/sctx"

	"github.com/gofiber/fiber/v2"
)

type ComposerAdAccountApi interface {
	ReloadAdAccountApi() fiber.Handler
	ListDatatableAdAccountApi() fiber.Handler
}

func ComposerApiAdAcountService(serviceCtx sctx.ServiceContext) ComposerAdAccountApi {
	logger := serviceCtx.MustGet(configs.KeyLoggerDaily).(sctx.AppLoggerDaily).GetLogger("webfb")
	mongoDB := serviceCtx.MustGet(configs.KeyCompMongoDB).(mongodb.MongoComponent).GetDatabase()
	repo := mongo.NewAdAccountRepo(mongoDB)

	fbService := serviceCtx.MustGet(configs.KeyFBMarketingV20).(fbmarketing.FBMarketingServices).GetFBMarketingService()

	usc := usecase.NewFBMarketingUsc(fbService, repo, logger)
	api := api.NewAdAccadAccountApi(usc)

	return api
}
