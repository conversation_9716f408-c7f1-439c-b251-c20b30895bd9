package enums

const (
	TYPE_CUSTOM_AUDIENCES    = "custom_audience"
	TYPE_SAVE_AUDIENCES      = "save_audience"
	TYPE_LOOKALIKE_AUDIENCES = "lookalike"
)
const (
	SUBTYPE_CUSTOM  = "CUSTOM"
	SUBTYPE_WEBSITE = "WEBSITE"
)

const (
	CUSTOM                        = "custom"
	PRIMARY                       = "primary"
	WEBSITE                       = "website"
	APP                           = "app"
	OFFLINE_CONVERSION            = "offline_conversion"
	CLAIM                         = "claim"
	MANAGED                       = "managed"
	PARTNER                       = "partner"
	VIDEO                         = "video"
	LOOKALIKE                     = "lookalike"
	ENGAGEMENT                    = "engagement"
	BAG_OF_ACCOUNTS               = "bag_of_accounts"
	STUDY_RULE_AUDIENCE           = "study_rule_audience"
	FOX                           = "fox"
	MEASUREMENT                   = "measurement"
	REGULATED_CATEGORIES_AUDIENCE = "regulated_categories_audience"
	BIDDING                       = "bidding"
	EXCLUSION                     = "exclusion"
	MESSENGER_SUBSCRIBER_LIST     = "messenger_subscriber_list"
	IG_BUSINESS                   = "ig_business"
)

var (
	TypeAudience = map[string]string{
		TYPE_CUSTOM_AUDIENCES:    "Custom Audience",
		TYPE_SAVE_AUDIENCES:      "Save Audience",
		TYPE_LOOKALIKE_AUDIENCES: "Lookalike audience",
	}

	SubtypeAudience = map[string]string{
		CUSTOM:                        "Custom Audience",
		PRIMARY:                       "Primary Audience",
		WEBSITE:                       "Website Audience",
		APP:                           "App Audience",
		OFFLINE_CONVERSION:            "Offline Conversion Audience",
		CLAIM:                         "Claim Audience",
		MANAGED:                       "Managed Audience",
		PARTNER:                       "Partner Audience",
		VIDEO:                         "Video Audience",
		LOOKALIKE:                     "Lookalike Audience",
		ENGAGEMENT:                    "Engagement Audience",
		BAG_OF_ACCOUNTS:               "Bag of Accounts Audience",
		STUDY_RULE_AUDIENCE:           "Study Rule Audience",
		FOX:                           "Fox Audience",
		MEASUREMENT:                   "Measurement Audience",
		REGULATED_CATEGORIES_AUDIENCE: "Regulated Categories Audience",
		BIDDING:                       "Bidding Audience",
		EXCLUSION:                     "Exclusion Audience",
		MESSENGER_SUBSCRIBER_LIST:     "Messenger Subscriber List Audience",
		IG_BUSINESS:                   "Instagram Audience",
	}
)