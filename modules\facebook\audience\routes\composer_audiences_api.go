package routes

import (
	clientR "godsp/modules/admin/client/repository/mongo"
	userR "godsp/modules/admin/user/repository/mongo"
	adAccR "godsp/modules/facebook/ad_account/repository/mongo"

	"godsp/modules/facebook/audience/transport/api"
	"godsp/modules/facebook/audience/usecase"
	"godsp/pkg/sctx/component/facebook/fbmarketing"

	"github.com/dev-networldasia/dspgos/sctx/component/mongodb"

	"github.com/dev-networldasia/dspgos/sctx/configs"

	"github.com/dev-networldasia/dspgos/sctx"

	"godsp/modules/facebook/audience/repository/mongo"

	"github.com/gofiber/fiber/v2"
)

type ComposerAudienceApi interface {
	ReloadAudienceApi() fiber.Handler
	ListAudiencesApi() fiber.Handler
	ListTableAudienceApi() fiber.Handler
	CreateCustomAudienceApi() fiber.Handler
	UpdateCustomAudienceApi() fiber.Handler
	DeleteCustomAudienceApi() fiber.Handler
	GetDetailAudienceApi() fiber.Handler
}

func ComposerAudienceApiService(serviceCtx sctx.ServiceContext) ComposerAudienceApi {
	mongoDB := serviceCtx.MustGet(configs.KeyCompMongoDB).(mongodb.MongoComponent).GetDatabase()
	logger := serviceCtx.MustGet(configs.KeyLoggerDaily).(sctx.AppLoggerDaily).GetLogger("webfb")
	fbService := serviceCtx.MustGet(configs.KeyFBMarketingV20).(fbmarketing.FBMarketingServices).GetFBMarketingService()

	// Init api
	repo := mongo.NewAudienceRepo(mongoDB)
	userRepo := userR.NewUserRepo(mongoDB)
	adAccRepo := adAccR.NewAdAccountRepo(mongoDB)
	clientRepo := clientR.NewClientRepo(mongoDB)
	usc := usecase.NewApiAudienceUsc(fbService, repo, userRepo, clientRepo, adAccRepo, logger)
	api := api.NewAudienceApi(usc)

	return api
}
