package handlers

import (
	"time"

	"github.com/dev-networldasia/dspgos/sctx/configs"

	"github.com/gofiber/fiber/v2"
)

type authHdl struct {
}

func NewAuthHdl() *authHdl {
	return &authHdl{}
}

/**
 * Methob: GET
 * Login handler
 */
func (h *authHdl) LoginHdl() fiber.Handler {
	return func(c *fiber.Ctx) error {
		return c.Render("admins/auths/login", fiber.Map{}, "")
	}
}

/**
 * Method: POST
 * Login auth
 * URI: /auth/logout
 */

func (a *authHdl) LogoutHdl() fiber.Handler {
	return func(c *fiber.Ctx) error {
		isPrd := configs.AppEnv == configs.AppProd
		cookie := fiber.Cookie{
			Name:     "brx_token",
			Value:    "",
			Expires:  time.Now().Add(-time.Minute),
			HTTPOnly: true,
			Secure:   isPrd,
			SameSite: "Lax",
		}
		c.<PERSON>(&cookie)

		return c.Redirect("/login")
	}
}

func (h *authHdl) PageForbiddenHdl() fiber.Handler {
	return func(c *fiber.Ctx) error {
		return c.Render("admins/auths/pages/forbidden", fiber.Map{})
	}
}
func (h *authHdl) PageNotFoundHdl() fiber.Handler {
	return func(c *fiber.Ctx) error {
		return c.Render("admins/auths/pages/not-found", fiber.Map{})
	}
}
func (h *authHdl) PagePermissionDeniedHdl() fiber.Handler {
	return func(c *fiber.Ctx) error {
		return c.Render("admins/auths/pages/permission-denied", fiber.Map{})
	}
}
