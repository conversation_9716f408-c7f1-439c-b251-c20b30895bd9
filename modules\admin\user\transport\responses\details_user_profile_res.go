package responses

import (
	"godsp/conf"

	"go.mongodb.org/mongo-driver/bson/primitive"
)

type DetailsUserProfile struct {
	ID         primitive.ObjectID `json:"_id,omitempty" bson:"_id,omitempty"`
	Email      string             `json:"email" bson:"email"`
	RoleName   string             `json:"role_name" bson:"role_name"`
	ClientName string             `json:"client_name" bson:"client_name"`
	Image      string             `json:"image" bson:"image"`
	FullName   string             `json:"full_name" bson:"full_name"`
	Phone      string             `json:"phone,omitempty" bson:"phone,omitempty"`
	Birthday   string             `json:"birthday,omitempty" bson:"birthday,omitempty"`
	Username   string             `json:"username,omitempty" bson:"username,omitempty"`
	Status     int                `json:"status" bson:"status"`
	Gender     int                `json:"gender" bson:"gender"`
}

func (u *DetailsUserProfile) PathImg() {
	if u.Image == "" {
		u.Image = "/static/" + conf.PathImgUserDefault
	}

	u.Image = "/static/" + u.Image
}
