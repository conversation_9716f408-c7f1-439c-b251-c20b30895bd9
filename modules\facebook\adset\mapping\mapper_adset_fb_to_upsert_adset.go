package mapping

import (
	"fmt"
	v20 "godsp/pkg/fb-marketing/marketing/v20"
	"time"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

func MapperAdsetFBToUpsertAdset(adset *v20.Adset, userId primitive.ObjectID) bson.M {
	now := time.Now()
	adsetUpsert := bson.M{
		"campaign_id":            adset.CampaignID,
		"account_id":             adset.AccountID,
		"name":                   adset.Name,
		"status":                 adset.Status,
		"attribution_spec":       adset.AttributionSpec,
		"bid_amount":             adset.BidAmount,
		"bid_strategy":           adset.BidStrategy,
		"billing_event":          adset.BillingEvent,
		"budget_remaining":       adset.BudgetRemaining,
		"configured_status":      adset.ConfiguredStatus,
		"daily_budget":           adset.DailyBudget,
		"daily_min_spend_target": adset.DailyMinSpendTarget,
		"daily_spend_cap":        adset.DailySpendCap,
		"destination_type":       adset.DestinationType,
		"delivery_estimate":      adset.DeliveryEstimate,
		"effective_status":       adset.EffectiveStatus,
		// "end_time": adset.EndTime,
		"frequency_control_specs":      adset.FrequencyControlSpecs,
		"lifetime_budget":              adset.LifetimeBudget,
		"lifetime_min_spend_target":    adset.LifetimeMinSpendTarget,
		"lifetime_spend_cap":           adset.LifeTimeSpendCap,
		"lifetime_imps":                adset.LifetimeImps,
		"optimization_goal":            adset.OptimizationGoal,
		"pacing_type":                  adset.PacingType,
		"promoted_object":              adset.PromotedObject,
		"recurring_budget_semantics":   adset.RecurringBudgetSemantics,
		"created_time":                 time.Time(adset.CreatedTime),
		"start_time":                   time.Time(*adset.StartTime),
		"updated_time":                 time.Time(adset.UpdatedTime),
		"targeting":                    adset.Targeting,
		"targeting_optimization_types": adset.TargetingOptimizationTypes,
		"dsa_beneficiary":              adset.DSABeneficiary,
		"dsa_payor":                    adset.DSAPayor,
		"is_dynamic_creative":          adset.IsDynamicCreative,
		"is_budget_schedule_enabled":   adset.IsBudgetScheduleEnabled,
		// "created_by":                   userId,
		// "created_at":                   now,
		// "updated_at":                   now,
		"updated_by": userId,
	}

	if adset.EndTime != nil {
		endTime := time.Time(*adset.EndTime)
		adsetUpsert["end_time"] = &endTime
	}

	adsetSetOnInsert := bson.M{
		"created_by": userId,
		"created_at": now,
	}

	fmt.Printf("\n -----------  adsetSetOnInsert ----------- %+v \n", adsetSetOnInsert)

	return bson.M{
		"$set":         adsetUpsert,
		"$setOnInsert": adsetSetOnInsert,
		"$addToSet":    bson.M{"list_user_ids": userId},
		// "$setOnInsert": adsetSetOnInsert,
	}
}
