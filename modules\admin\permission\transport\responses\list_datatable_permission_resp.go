package responses

import "go.mongodb.org/mongo-driver/bson/primitive"

type PermissionDataTable struct {
	ID          primitive.ObjectID `json:"_id"`
	Method      string             `json:"method"`
	Path        string             `json:"path"`
	RouteName   string             `json:"route_name"`
	Group       string             `json:"group"`
	Module      string             `json:"module"`
	Description string             `json:"description"`
	Ordering    int64              `json:"ordering"`
	ParentID    primitive.ObjectID `json:"parent_id"`
	IsBlock     int                `json:"is_block"`
	IsAcp       int                `json:"is_acp"`
	CreatedAt   string             `json:"created_at"`
	UpdatedAt   string             `json:"updated_at"`
	UserCreated string             `json:"created_by"`
	UserUpdated string             `json:"updated_by"`
	ParentName  string             `json:"parent_name"`
}
