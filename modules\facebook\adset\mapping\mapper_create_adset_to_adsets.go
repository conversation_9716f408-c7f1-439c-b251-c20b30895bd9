package mapping

import (
	"godsp/modules/facebook/adset/entity"
	"godsp/modules/facebook/adset/transport/requests"
	v20 "godsp/pkg/fb-marketing/marketing/v20"
	"time"

	"go.mongodb.org/mongo-driver/bson/primitive"
)

func MapperCreateAdsetReqToAdset(req *requests.CreateAdsetReq, clientId *primitive.ObjectID) (*entity.AdsetEntity, *v20.Adset) {

	adsetFB := v20.Adset{
		AccountID:               req.AccountID,
		CampaignID:              req.CampaignID,
		Name:                    req.Name,
		Status:                  req.Status,
		BillingEvent:            req.BillingEvent,
		BidStrategy:             req.BidStrategy,
		PromotedObject:          req.PromotedObject,
		OptimizationGoal:        req.OptimizationGoal,
		DestinationType:         req.DestinationType,
		StartTime:               &req.StartTime,
		EndTime:                 req.EndTime,
		Targeting:               req.Targeting,
		BudgetScheduleSpecs:     req.BudgetScheduleSpecs,
		IsBudgetScheduleEnabled: req.IsBudgetScheduleEnabled,
		FrequencyControlSpecs:   req.FrequencyControlSpecs,
	}

	now := time.Now()
	adsetEntity := entity.AdsetEntity{
		ID:         primitive.NewObjectID(),
		AccountID:  req.AccountID,
		CampaignID: req.CampaignID,
		Name:       req.Name,
		Status:     req.Status,
		// DailyBudget:             req.DailyBudget,
		// BidAmount:               *req.BidAmount,
		BillingEvent:            req.BillingEvent,
		BidStrategy:             req.BidStrategy,
		OptimizationGoal:        req.OptimizationGoal,
		DestinationType:         req.DestinationType,
		PromotedObject:          req.PromotedObject,
		FrequencyControlSpecs:   req.FrequencyControlSpecs,
		StartTime:               time.Time(req.StartTime),
		Targeting:               req.Targeting,
		CreatedBy:               req.UserId,
		CreatedAt:               now,
		UpdatedBy:               req.UserId,
		UpdatedAt:               now,
		IsBudgetScheduleEnabled: req.IsBudgetScheduleEnabled,
		ListUserIDs:             []primitive.ObjectID{req.UserId},
		ClientID:                *clientId,
	}

	if req.DailyBudget != nil {
		adsetEntity.DailyBudget = *req.DailyBudget
		adsetFB.DailyBudget = *req.DailyBudget
	}

	if req.LifetimeBudget != nil {
		adsetEntity.LifetimeBudget = *req.LifetimeBudget
		adsetFB.LifetimeBudget = *req.LifetimeBudget
	}

	if req.BidAmount != nil {
		adsetEntity.BidAmount = *req.BidAmount
		adsetFB.BidAmount = *req.BidAmount
	}

	// Daily
	if req.DailyBudget != nil {
		adsetEntity.DailyBudget = *req.DailyBudget
		adsetFB.DailyBudget = *req.DailyBudget
	}
	if req.DailyMinSpendTarget != nil {
		adsetEntity.DailyMinSpendTarget = *req.DailyMinSpendTarget
		adsetFB.DailyMinSpendTarget = *req.DailyMinSpendTarget
	}
	if req.DailySpendCap != nil {
		adsetEntity.DailySpendCap = *req.DailySpendCap
		adsetFB.DailySpendCap = *req.DailySpendCap
	}

	// Life time
	if req.LifeTimeSpendCap != nil {
		adsetEntity.LifeTimeSpendCap = *req.LifeTimeSpendCap
		adsetFB.LifeTimeSpendCap = *req.LifeTimeSpendCap
	}
	if req.LifetimeMinSpendTarget != nil {
		adsetEntity.LifeTimeSpendCap = *req.LifetimeMinSpendTarget
		adsetFB.LifeTimeSpendCap = *req.LifetimeMinSpendTarget
	}
	if req.LifetimeImps != nil {
		adsetEntity.LifeTimeSpendCap = *req.LifetimeImps
		adsetFB.LifeTimeSpendCap = *req.LifetimeImps
	}

	if req.EndTime != nil {
		t := time.Time(*req.EndTime) // Dereference và chuyển đổi
		adsetEntity.EndTime = &t
	}

	return &adsetEntity, &adsetFB

}
