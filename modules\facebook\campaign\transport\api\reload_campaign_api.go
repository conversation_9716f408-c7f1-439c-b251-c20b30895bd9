package api

import (
	"godsp/modules/facebook/campaign/transport/requests"
	"godsp/pkg/gos/utils"
	"godsp/pkg/sctx/core"
	"net/http"

	"github.com/gofiber/fiber/v2"
)

/***
 * Api reload Campaign
 */
func (a *campaignApi) ReloadCampaignApi() fiber.Handler {
	return func(c *fiber.Ctx) error {
		var payload requests.ReloadCampaignReq

		if c.Body() != nil && len(c.Body()) > 0 {
			if err := c.BodyParser(&payload); err != nil {
				return core.ReturnErrsForApi(c, err)
			}
		}

		validationErrors := payload.Validate()
		if len(validationErrors) > 0 {
			return core.ReturnErrsForApi(c, validationErrors)
		}

		userId, err := utils.GetUserId(c.Context())
		if err != nil {
			return core.ReturnErrsForApi(c, err)
		}
		payload.UserId = userId

		roleName, err := a.usc.GetRoleByUserId(c.Context(), payload.UserId)
		if *roleName != "ADMIN" || err != nil {
			return core.ErrForbidden
		}

		if payload.CampaignID != "" {
			errs := a.usc.ReloadCampaignDetail(c.Context(), payload.CampaignID, payload.UserId)
			if errs != nil {
				return core.ReturnErrsForApi(c, errs)
			}
		}

		if payload.AccountID != "" {
			if errs := a.usc.ReloadCampaignUsc(c.Context(), payload.AccountID, payload.UserId); errs != nil {
				return core.ReturnErrsForApi(c, errs)
			}
		}

		

		// check admin thi load all

		return c.Status(http.StatusOK).JSON(core.ResponseData(map[string]interface{}{
			"msg": "Reload Campaign successfully",
		}))
	}
}
