package routes

import (
	clientR "godsp/modules/admin/client/repository/mongo"
	userR "godsp/modules/admin/user/repository/mongo"
	adAccR "godsp/modules/facebook/ad_account/repository/mongo"
	"godsp/pkg/sctx/component/facebook/fbmarketing"

	"godsp/modules/facebook/audience/repository/mongo"
	"godsp/modules/facebook/audience/transport/handlers"
	"godsp/modules/facebook/audience/usecase"

	"github.com/dev-networldasia/dspgos/sctx"
	"github.com/dev-networldasia/dspgos/sctx/component/mongodb"
	"github.com/dev-networldasia/dspgos/sctx/configs"

	"github.com/gofiber/fiber/v2"
)

type ComposerAudience interface {
	ListAudienceHandler() fiber.Handler
}

func ComposerAudienceService(serviceCtx sctx.ServiceContext) ComposerAudience {
	mongoDB := serviceCtx.MustGet(configs.KeyCompMongoDB).(mongodb.MongoComponent).GetDatabase()
	logger := serviceCtx.MustGet(configs.KeyLoggerDaily).(sctx.AppLoggerDaily).GetLogger("webfb")
	fbService := serviceCtx.MustGet(configs.KeyFBMarketingV20).(fbmarketing.FBMarketingServices).GetFBMarketingService()

	// Init api
	repo := mongo.NewAudienceRepo(mongoDB)
	userRepo := userR.NewUserRepo(mongoDB)
	adAccRepo := adAccR.NewAdAccountRepo(mongoDB)
	clientRepo := clientR.NewClientRepo(mongoDB)
	usc := usecase.NewApiAudienceUsc(fbService, repo, userRepo, clientRepo, adAccRepo, logger)

	hdl := handlers.NewAudienceHdl(usc)
	return hdl
}
