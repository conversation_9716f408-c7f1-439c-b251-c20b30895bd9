package requests

import (
	"godsp/modules/facebook/audience/common/errs"

	"github.com/go-playground/validator/v10"
)

type ReloadAudienceReq struct {
	AccountID string `json:"account_id,omitempty"`
}

func (req *ReloadAudienceReq) Validate() []string {
	validate := validator.New()
	var validationErrors []string

	err := validate.Struct(req)
	if err != nil {
		for _, err := range err.(validator.ValidationErrors) {
			switch err.Field() {
			case "AccountID":
				validationErrors = append(validationErrors, errs.ErrReloadAudienceAccountId.Error())
			}
		}
	}

	return validationErrors
}
