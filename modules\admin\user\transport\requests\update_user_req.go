package requests

import (
	"fmt"
	"godsp/conf"
	"godsp/modules/admin/common/admerrs"
	"godsp/modules/admin/common/admrules"
	"godsp/modules/admin/user/common/errs"
	"godsp/modules/admin/user/transport/rules"
	"godsp/modules/facebook/common/fbrules"
	"godsp/pkg/gos/utils"
	"mime/multipart"
	"strings"

	"github.com/go-playground/validator/v10"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

type PayloadUserUpdate struct {
	ID          primitive.ObjectID `form:"id" validate:"required"`
	FirstName   string             `form:"first_name" validate:"required"`
	LastName    string             `form:"last_name" validate:"required"`
	RoleID      primitive.ObjectID `form:"role_id" json:"role_id" validate:"required"`
	AdAccountID string             `form:"ad_account_id"`
	Email       string             `form:"email" validate:"required"`
	OldEmail    string             `form:"old_email" validate:"required"`
	Username    string             `form:"username"`
	Status      int                `form:"status" validate:"required,ruleStatusUser"`
	Gender      int                `form:"gender" validate:"required"`
	Phone       string             `form:"phone_number" validate:"rulePhoneUser"`
	Birthday    string             `form:"birthday"`
	RoleName    string             `form:"role_name" validate:"required"`

	ListPageIds  []string            `form:"list_page_ids,omitempty"`
	ListPixelIds []string            `form:"list_pixel_ids,omitempty"`
	ClientIDStr  string              `form:"client_id,omitempty" validate:"omitempty,ruleObjectid"`
	ClientID     *primitive.ObjectID `form:"-"`

	UserID  primitive.ObjectID    `form:"-" json:"-"`
	FileImg *multipart.FileHeader `form:"-" json:"-"`
	Image   string                `form:"-" json:"-"`
}

func (req *PayloadUserUpdate) Validate() []*string {
	validate := validator.New()
	validate.RegisterValidation("ruleStatusUser", admrules.RuleStatusUpdate)
	validate.RegisterValidation("rulePhoneUser", rules.RulePhoneUser)
	validate.RegisterValidation("ruleObjectid", RuleObjectID)

	var validationErrors []*string
	err := validate.Struct(req)
	if err != nil {
		for _, err := range err.(validator.ValidationErrors) {
			switch err.Field() {
			case "ID":
				err := errs.ErrIDUserValidate.Error()
				return append(validationErrors, &err)
			case "RoleID", "RoleName":
				errRoleID := errs.ErrRoleIDExistsValidate.Error()
				return append(validationErrors, &errRoleID)
			case "LastName":
				errStatus := errs.ErrLastNameValidate.Error()
				validationErrors = append(validationErrors, &errStatus)
			case "FirstName":
				errName := errs.ErrFirstNameValidate.Error()
				validationErrors = append(validationErrors, &errName)
			case "Email", "OldEmail":
				errName := errs.ErrEmailValidate.Error()
				validationErrors = append(validationErrors, &errName)
			case "Status":
				errStatus := admerrs.ErrStatusNotFound.Error()
				validationErrors = append(validationErrors, &errStatus)
			case "Gender":
				errGender := errs.ErrGenderValidate.Error()
				validationErrors = append(validationErrors, &errGender)
			case "Phone":
				errMsg := errs.ErrPhoneValidate.Error()
				validationErrors = append(validationErrors, &errMsg)
			}
		}
	}

	if req.ClientIDStr != "" {
		oid, err := primitive.ObjectIDFromHex(req.ClientIDStr)
		if err != nil {
			errClientID := errs.ErrIDUserValidate.Error()
			validationErrors = append(validationErrors, &errClientID)
		} else {
			req.ClientID = &oid // bạn cần thêm 1 trường ClientIDPtr *primitive.ObjectID để sử dụng sau
		}
	} else {
		req.ClientID = nil
	}

	if !rules.RuleNameUser(req.FirstName) || !rules.RuleNameUser(req.LastName) {
		errName := errs.ErrNameUserValidate.Error()
		validationErrors = append(validationErrors, &errName)
	}

	username := strings.TrimSpace(req.Username)
	if username != "" {
		if !rules.RuleUsernameUser(username) {
			errUsername := errs.ErrUsernameValidate.Error()
			validationErrors = append(validationErrors, &errUsername)
		}
	}

	if len(req.ListPageIds) > 0 {
		if !fbrules.RuleIdsNumber(req.ListPageIds) {
			errIdsValid := errs.ErrListPageIdsValidate.Error()
			validationErrors = append(validationErrors, &errIdsValid)
		}
	}
	if len(req.ListPixelIds) > 0 {
		if !fbrules.RuleIdsNumber(req.ListPixelIds) {
			errIdsValid := errs.ErrListPixelIdsValidate.Error()
			validationErrors = append(validationErrors, &errIdsValid)
		}
	}

	if validationErrors == nil {
		req.FirstName = strings.TrimSpace(req.FirstName)
		req.Email = strings.TrimSpace(req.Email)
		req.LastName = strings.TrimSpace(req.LastName)
		req.Username = strings.TrimSpace(req.Username)
		req.Phone = strings.TrimSpace(req.Phone)
		req.Birthday = strings.TrimSpace(req.Birthday)
	}

	if req.FileImg != nil {
		fullName := fmt.Sprintf("%s %s", req.FirstName, req.LastName)
		if pathName, err := utils.UploadFile(req.FileImg, conf.UploadPathPublic, conf.PathImgUser, &fullName); err != nil {
			errStr := err.Error()
			validationErrors = append(validationErrors, &errStr)
		} else {
			req.Image = *pathName
		}
	}

	return validationErrors
}
