package requests

import (
	"github.com/go-playground/validator/v10"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

type PayloadPermissionUpdate struct {
	ID          primitive.ObjectID `form:"id" validate:"required"`
	Description string             `form:"description"`
	ParentID    primitive.ObjectID `form:"parent_id"`
	Ordering    int64              `form:"ordering" validate:"required,gt=0"`
	IsBlock     int                `form:"is_block" validate:"required,gt=0"`
	IsAcp       int                `form:"is_acp" validate:"required,gt=0"`
	UserID      primitive.ObjectID `form:"-" json:"-"`
}

func (req *PayloadPermissionUpdate) Validate() []*string {
	validate := validator.New()
	var validationErrors []*string
	err := validate.Struct(req)
	if err != nil {
		for _, err := range err.(validator.ValidationErrors) {
			switch err.Field() {
			// case "ID":
			// 	return errs.ErrIDPermissionValidate
			// case "ParentID":
			// 	err := aerrs.ErrParentId.Error()
			// 	validationErrors = append(validationErrors, &err)
			// case "Ordering":
			// 	err := aerrs.ErrOrdering.Error()
			// 	validationErrors = append(validationErrors, &err)
			// case "IsBlock":
			// 	err := aerrs.ErrIsBlock.Error()
			// 	validationErrors = append(validationErrors, &err)
			// case "IsAcp":
			// 	err := aerrs.ErrIsAcp.Error()
			// 	validationErrors = append(validationErrors, &err)
			}
		}
	}

	// if req.ParentID != "" {
	// 	if !gorules.RuleFakeId(req.ParentID) {
	// 		err := aerrs.ErrParentId.Error()
	// 		return append(validationErrors, &err)
	// 	}
	// 	uidParent, err := core.FromBase58(req.ParentID)
	// 	if err != nil {
	// 		err := aerrs.ErrParentIdNotValid.Error()
	// 		return append(validationErrors, &err)
	// 	}
	// 	req.ParentID = strconv.FormatInt(int64(uidParent.GetLocalID()), 10)
	// }

	// if validationErrors == nil {
	// 	req.ID = strconv.FormatInt(int64(uid.GetLocalID()), 10)
	// 	req.Description = strings.TrimSpace(req.Description)
	// }
	return validationErrors
}
