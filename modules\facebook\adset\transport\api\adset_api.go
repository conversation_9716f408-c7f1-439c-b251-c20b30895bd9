package api

import (
	"context"
	"fmt"
	"godsp/conf"
	"godsp/modules/facebook/adset/entity"
	"godsp/modules/facebook/adset/transport/requests"
	"godsp/modules/facebook/adset/transport/responses"
	"godsp/pkg/gos/utils"
	"godsp/pkg/sctx/core"
	"net/http"

	"github.com/gofiber/fiber/v2"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

type AdsetUsc interface {
	ReloadAdsetDetailUsc(ctx context.Context, adsetId string, userId primitive.ObjectID) error
	ReloadAdsetUsc(ctx context.Context, adAccountId string, userId primitive.ObjectID) []string
	ReloadWithCampaignIdAdsetUsc(ctx context.Context, campaign_id string, userId primitive.ObjectID) []string
	CreateAdsetUsc(ctx context.Context, payload *requests.CreateAdsetReq) (*entity.AdsetEntity, error)
	UpdateAdsetUsc(ctx context.Context, payload *requests.UpdateAdsetReq) error
	UpdateNameStatusAdsetUsc(ctx context.Context, payload *requests.UpdateNameStatusAdsetReq) error
	DetailAdsetUsc(ctx context.Context, adsetId string, userId primitive.ObjectID) (*responses.AdsetDetailView, error)
	ListDatatableAdsetUsc(ctx context.Context, payload *requests.ListAdsetReq) (*responses.ListAdsetsResp, error)
	ListSelectAdsetUsc(ctx context.Context, payload *requests.ListSelectAdsetReq) (*[]entity.AdsetEntity, error)
	DeleteAdsetUsc(ctx context.Context, payload *requests.DeleteAdsetReq) ([]string, error)
	GetRoleByUserId(ctx context.Context, userId primitive.ObjectID) (*string, error)
}

type adsetApi struct {
	usc AdsetUsc
}

func NewAdsetApi(usc AdsetUsc) *adsetApi {
	return &adsetApi{
		usc: usc,
	}
}

/***
 * Api reload Adset
 *
 */

func (a *adsetApi) ReloadAdsetApi() fiber.Handler {
	return func(c *fiber.Ctx) error {
		var payload requests.ReloadAdsetReq
		if c.Body() != nil && len(c.Body()) > 0 {
			if err := c.BodyParser(&payload); err != nil {
				return core.ReturnErrsForApi(c, err)
			}
		}
		//validate
		validationErrors := payload.Validate()
		if len(validationErrors) > 0 {
			return core.ReturnErrsForApi(c, validationErrors)
		}

		userId, err := utils.GetUserId(c.Context())
		if err != nil {
			fmt.Println("Error:", err)
			return core.ReturnErrsForApi(c, err)
		}

		roleName, err := a.usc.GetRoleByUserId(c.Context(), userId)
		if *roleName != "ADMIN" || err != nil {
			return core.ErrForbidden
		}

		if payload.AdsetID != "" {
			if err := a.usc.ReloadAdsetDetailUsc(c.Context(), payload.AdsetID, userId); err != nil {
				return core.ReturnErrForApi(c, err.Error())
			}
		} else if payload.CampaignID != "" {
			//camp
			errs := a.usc.ReloadWithCampaignIdAdsetUsc(c.Context(), payload.CampaignID, userId)
			if errs != nil {
				return core.ReturnErrsForApi(c, errs)
			}
		} else {
			// account id
			// if act := utils.GetAdAccount(c.Context()); act != nil {
			// 	payload.AccountID = *act
			// 	errs := a.usc.ReloadAdsetUsc(c.Context(), payload.AccountID, userId)
			// 	if errs != nil {
			// 		return core.ReturnErrsForApi(c, errs)
			// 	}
			// }
			errs := a.usc.ReloadAdsetUsc(c.Context(), payload.AccountID, userId)
			if errs != nil {
				return core.ReturnErrsForApi(c, errs)
			}
		}

		return c.Status(http.StatusOK).JSON(core.ResponseData(map[string]interface{}{
			"msg": "Reload Adset successfully",
		}))
	}
}

/**
 * Api Create adset
 */
func (a *adsetApi) CreateAdsetApi() fiber.Handler {
	return func(c *fiber.Ctx) error {
		var payload requests.CreateAdsetReq
		if err := c.BodyParser(&payload); err != nil {
			return core.ReturnErrsForApi(c, err)
		}

		//validate
		validationErrors := payload.Validate()
		if len(validationErrors) > 0 {
			return core.ReturnErrsForApi(c, validationErrors)
		}

		userInfo, err := utils.GetInfoUserAuth(c.Context())

		if userInfo.RoleName == conf.SysConf.RoleAdmin {
			if payload.AccountID == "" && payload.ClientIDStr == "" {
				return c.Status(fiber.StatusBadRequest).JSON(core.ResponseData(map[string]interface{}{
					"msg":  "Account ID or Client ID is required",
					"data": nil,
				}))
			}
		} else {
			if err != nil {
				return core.ReturnErrForPermissionDenied(c)
			}

			if payload.ClientIDStr != userInfo.ClientId.Hex() {
				return core.ReturnErrForPermissionDenied(c)
			}
		}

		payload.UserId = *userInfo.UserId

		adset, err := a.usc.CreateAdsetUsc(c.Context(), &payload)

		if err != nil {
			return core.ReturnErrsForApi(c, err)
		}

		return c.Status(http.StatusOK).JSON(core.ResponseData(map[string]interface{}{
			"msg":  "Create Adset successfully",
			"data": adset,
		}))
	}
}

/**
 * Api update adset
 */
func (a *adsetApi) UpdateAdsetApi() fiber.Handler {
	return func(c *fiber.Ctx) error {
		var payload requests.UpdateAdsetReq
		if err := c.BodyParser(&payload); err != nil {
			return core.ReturnErrsForApi(c, err)
		}

		//validate
		validationErrors := payload.Validate()
		if len(validationErrors) > 0 {
			return core.ReturnErrsForApi(c, validationErrors)
		}

		userInfo, err := utils.GetInfoUserAuth(c.Context())

		if userInfo.RoleName == conf.SysConf.RoleAdmin {
			if payload.AccountID == "" && payload.ClientIDStr == "" {
				return c.Status(fiber.StatusBadRequest).JSON(core.ResponseData(map[string]interface{}{
					"msg":  "Account ID or Client ID is required",
					"data": nil,
				}))
			}
		} else {
			if err != nil {
				return core.ReturnErrForPermissionDenied(c)
			}

			if payload.ClientIDStr != userInfo.ClientId.Hex() {
				return core.ReturnErrForPermissionDenied(c)
			}
		}
		payload.UserId = *userInfo.UserId
		if err := a.usc.UpdateAdsetUsc(c.Context(), &payload); err != nil {
			return core.ReturnErrForApi(c, err.Error())
		}

		return c.Status(http.StatusOK).JSON(core.ResponseData(map[string]interface{}{
			"msg": "Update Adset successfully",
		}))
	}
}

/**
 * Api show adset
 */
func (a *adsetApi) ShowAdsetApi() fiber.Handler {
	return func(c *fiber.Ctx) error {
		adsetId := c.Params("adsetId")
		userId, err := utils.GetUserId(c.Context())
		if err != nil {
			fmt.Println("Error:", err)
			return core.ReturnErrsForApi(c, err)
		}

		adset, err := a.usc.DetailAdsetUsc(c.Context(), adsetId, userId)
		if err != nil {
			return core.ReturnErrsForApi(c, err)
		}

		return c.Status(http.StatusOK).JSON(core.ResponseData(map[string]interface{}{
			"data": adset,
		}))
	}
}

/**
 * List data adset api
 */
func (a *adsetApi) ListDatatableAdsetApi() fiber.Handler {
	return func(c *fiber.Ctx) error {
		resData := responses.ListAdsetsResp{}
		var payload requests.ListAdsetReq

		if err := c.BodyParser(&payload); err != nil {
			resData.Msg = err.Error()
			return c.Status(fiber.StatusBadRequest).JSON(core.ResponseData(map[string]interface{}{
				"msg":  err.Error(),
				"data": nil,
			}))
		}

		// userId, err := utils.GetUserId(c.Context())
		// if err != nil {
		// 	return core.ReturnErrForApi(c, core.ErrForbidden.ErrorField)
		// }

		userInfo, _ := utils.GetInfoUserAuth(c.Context())
		if userInfo.RoleName == conf.SysConf.RoleAdmin {
			// fmt.Println("admin", payload.AccountID)
			if payload.AccountID == "" && payload.ClientIDStr == "" {
				return c.Status(fiber.StatusBadRequest).JSON(core.ResponseData(map[string]interface{}{
					"msg":  "Account ID or Client ID is required",
					"data": nil,
				}))
			}
		}

		payload.UserId = *userInfo.UserId
		resData.Draw = payload.Draw
		if err := payload.Validate(); err != nil {
			resData.Msg = err.Error()
			return c.JSON(resData)
		}

		adsets, err := a.usc.ListDatatableAdsetUsc(c.Context(), &payload)
		if err != nil {
			resData.Msg = err.Error()
			return c.JSON(resData)
		}

		return c.JSON(adsets)
	}
}

/**
 * List adset api for select
 */
func (a *adsetApi) ListSelectAdsetApi() fiber.Handler {
	return func(c *fiber.Ctx) error {
		var payload requests.ListSelectAdsetReq

		if err := c.BodyParser(&payload); err != nil {
			return core.ReturnErrForApi(c, err.Error())
		}

		userId, err := utils.GetUserId(c.Context())
		if err != nil {
			return core.ReturnErrForApi(c, core.ErrForbidden.ErrorField)
		}
		payload.UserId = userId

		adSets, err := a.usc.ListSelectAdsetUsc(c.Context(), &payload)
		if err != nil {
			return core.ReturnErrForApi(c, err.Error())
		}

		// Respond with the list of pages in JSON format
		return c.Status(http.StatusOK).JSON(core.ResponseData(adSets))
	}
}

/**
 * Togle active or edit name adset
 */

func (a *adsetApi) UpdateNameStatusAdsetApi() fiber.Handler {
	return func(c *fiber.Ctx) error {
		var payload requests.UpdateNameStatusAdsetReq
		if err := c.BodyParser(&payload); err != nil {
			return core.ReturnErrsForApi(c, err)
		}

		//validate
		validationErrors := payload.Validate()
		if len(validationErrors) > 0 {
			return core.ReturnErrsForApi(c, validationErrors)
		}

		if payload.Name == "" && payload.Status == "" {
			return core.ErrBadRequest
		}

		userId, errUser := utils.GetUserId(c.Context())
		if errUser != nil {
			return core.ReturnErrsForApi(c, errUser)
		}

		payload.UserId = userId

		err := a.usc.UpdateNameStatusAdsetUsc(c.Context(), &payload)
		if err != nil {
			fmt.Println("Error:", err)
			return core.ReturnErrsForApi(c, err)
		}

		return c.Status(http.StatusOK).JSON(core.ResponseData(map[string]interface{}{
			"msg": "Update Campaign successfully",
		}))
	}
}

func (a *adsetApi) DeleteAdsetApi() fiber.Handler {
	return func(c *fiber.Ctx) error {
		var payload requests.DeleteAdsetReq

		if err := c.BodyParser(&payload); err != nil {
			return core.ReturnErrsForApi(c, err)
		}

		//validate
		validationErrors := payload.Validate()

		if len(validationErrors) > 0 {
			return core.ReturnErrsForApi(c, validationErrors)
		}

		userId, err := utils.GetUserId(c.Context())
		if err != nil {
			return core.ReturnErrsForApi(c, err)
		}

		payload.UserId = userId

		_, err = a.usc.DeleteAdsetUsc(c.Context(), &payload)

		if err != nil {
			return core.ReturnErrsForApi(c, err)
		}

		return c.Status(http.StatusOK).JSON(core.ResponseData(map[string]interface{}{
			"msg":  "Delete successfully",
			"data": payload.AdsetIDs,
		}))
	}
}
