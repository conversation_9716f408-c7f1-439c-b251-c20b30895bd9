package response

import (
	"godsp/modules/facebook/ad_pixel/entity"
	"godsp/pkg/gos/goconst"

	"go.mongodb.org/mongo-driver/bson/primitive"
)

type AdPixelListResponse struct {
	AdPixelDataTables []*AdPixelDataTable
	Count             int64
	CountFilter       int64
}

type AdPixelDataTable struct {
	ID           primitive.ObjectID `json:"_id"`
	Name         string             `json:"name" bson:"name"`
	Currency     string             `json:"currency" bson:"currency"`
	TimeZoneName string             `json:"timezone_name" bson:"timezone_name"`
	UpdatedAt    string             `json:"updated_at" bson:"updated_at"`
}

func MapperDatatable(data *entity.AdPixelEntity) *AdPixelDataTable {
	updateAt := data.UpdatedAt.Format(goconst.YYYY_MM_DD_HH_MM_SS)
	return &AdPixelDataTable{
		ID:        data.ID,
		Name:      data.Name,
		UpdatedAt: updateAt,
	}
}
