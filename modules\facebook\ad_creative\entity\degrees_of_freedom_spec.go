package entity

type DegreesOfFreedomSpec struct {
	CreativeFeaturesSpec CreativeFeaturesSpec `json:"creative_features_spec" bson:"creative_features_spec"`
}
type StandardEnhancements struct {
	EnrollStatus string `json:"enroll_status" bson:"enroll_status"`
}
type TextOptimizations struct {
	EnrollStatus   string         `json:"enroll_status,omitempty" bson:"enroll_status,omitempty"`
	Customizations Customizations `json:"customizations,omitempty" bson:"customizations,omitempty"`
}

type Customizations struct {
	TextExtraction TextExtraction `json:"text_extraction,omitempty" bson:"text_extraction.omitempty"`
}

type TextExtraction struct {
	EnrollStatus string `json:"enroll_status,omitempty" bson:"enroll_status,omitempty"`
}
type CreativeFeaturesSpec struct {
	StandardEnhancements StandardEnhancements `json:"standard_enhancements" bson:"standard_enhancements"`
	TextOptimizations    *TextOptimizations   `json:"text_optimizations,omitempty" bson:"text_optimizations,omitempty"`
}
