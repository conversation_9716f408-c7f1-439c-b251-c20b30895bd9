package entity

import (
	"godsp/pkg/gos/utils"
	"time"

	"go.mongodb.org/mongo-driver/bson/primitive"
)

type ClientUpdateEntity struct {
	ID      primitive.ObjectID `json:"_id,omitempty" bson:"_id,omitempty"`
	Name    string             `json:"name" bson:"name" `
	Company string             `json:"company" bson:"company"`
	Brand   *[]string          `json:"brand" bson:"brand"`
	Email   string             `json:"email" bson:"email,omitempty"`
	Status  int                `json:"status" bson:"status"`

	Phone    string `json:"phone,omitempty" bson:"phone,omitempty"`
	Domain   string `json:"domain,omitempty" bson:",omitempty"`
	Logo     string `json:"logo,omitempty" bson:"logo,omitempty"`
	Position string `json:"position,omitempty" bson:"position,omitempty"`
	Address  string `json:"address,omitempty" bson:"address,omitempty"`

	AdAccountIDs  []string             `json:"ad_account_ids,omitempty" bson:"ad_account_ids,omitempty"`
	ClientUserIDs []primitive.ObjectID `json:"client_user_ids,omitempty" bson:"client_use_ids,omitempty" `

	UpdatedAt time.Time          `json:"updated_at" bson:"updated_at"`
	UpdatedBy primitive.ObjectID `json:"updated_by" bson:"updated_by"`
}

func (ClientUpdateEntity) CollectionName() string {
	return ClientEntity{}.CollectionName()
}
func (u *ClientUpdateEntity) BeforeUpdate() {
	now := utils.TimeNowLocationHCM()
	if u.UpdatedAt.IsZero() {
		u.UpdatedAt = now
	}
	u.UpdatedAt = now
}
