package requests

import (
	"godsp/modules/facebook/audience/common/consts"

	"go.mongodb.org/mongo-driver/bson/primitive"
)

type ListAudiencesReq struct {
	AccountID   string  `json:"account_id" validate:"required"`
	Draw        int     `json:"draw" form:"draw"`
	Length      int64   `json:"length" form:"length"`
	Start       int64   `json:"start" form:"start"`
	SearchValue *string `json:"search_value" form:"search_value"`
	Order       []Order `json:"order"`
	SortField   string  `json:"-" form:"-"`
	SortOrder   int     `json:"-" form:"-"`
	Filter      Filter  `json:"filter" form:"filter"`
}

type ListTableAudienceReq struct {
	Draw        int                `json:"draw" form:"draw"`
	Order       []Order            `json:"order"`
	SortField   string             `json:"sort_field"`
	SortOrder   int                `json:"sort_order"`
	Filter      Filter             `json:"filter"`
	AccountID   string             `json:"accountId,omitempty" form:"accountId"`
	ClientIDStr string             `json:"clientId,omitempty" form:"clientId"`
	ClientID    primitive.ObjectID `json:"client_id"`
	UserId      primitive.ObjectID `json:"user_id"`
}

type Filter struct {
	Search       string `json:"search"`
	Type         string `json:"type"`
	TypeSearch   string `json:"search_type"`
	Availability string `json:"availability"`
}
type Order struct {
	Column int    `json:"column" form:"column"`
	Dir    string `json:"dir" form:"dir"`
}

func (req *ListAudiencesReq) Validate() error {
	req.SortOrder = 1
	if req.Order == nil && len(req.Order) == 0 {
		req.SortField = consts.AUDIENCE_ORDER_DEFAULT
	} else {
		orderColumn := req.Order[0].Column
		if sortField, ok := consts.FieldAudienceSort[orderColumn]; !ok {
			req.SortField = consts.AUDIENCE_ORDER_DEFAULT
		} else {
			req.SortField = sortField
		}

		if sortOrder := req.Order[0].Dir; sortOrder == "desc" {
			req.SortOrder = -1
		}
	}

	return nil
}

func (req *ListTableAudienceReq) Validate() error {
	req.SortOrder = 1
	if req.Order == nil && len(req.Order) == 0 {
		req.SortField = consts.AUDIENCE_ORDER_DEFAULT
	} else {
		orderColumn := req.Order[0].Column
		if sortField, ok := consts.FieldAudienceSort[orderColumn]; !ok {
			req.SortField = consts.AUDIENCE_ORDER_DEFAULT
		} else {
			req.SortField = sortField
		}

		if sortOrder := req.Order[0].Dir; sortOrder == "desc" {
			req.SortOrder = -1
		}
	}

	if req.ClientIDStr != "" {
		clientID, _ := primitive.ObjectIDFromHex(req.ClientIDStr)
		req.ClientID = clientID
	}

	return nil
}
