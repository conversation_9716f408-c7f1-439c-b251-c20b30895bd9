package entity

import (
	"encoding/json"
	"time"

	"go.mongodb.org/mongo-driver/bson/primitive"
)

type CustomAudienceEntity struct {
	ID                         primitive.ObjectID `json:"_id,omitempty" bson:"_id,omitempty"`
	CustomAudienceID           string             `json:"custom_audience_id,omitempty" bson:"custom_audience_id,omitempty"`
	Name                       string             `json:"name,omitempty" bson:"name,omitempty"`
	AccountID                  string             `json:"account_id,omitempty" bson:"account_id,omitempty"`
	Description                string             `json:"description,omitempty" bson:"description,omitempty"`
	Subtype                    string             `json:"subtype,omitempty" bson:"subtype,omitempty"`
	ApproximateCountUpperBound int                `json:"approximate_count_upper_bound,omitempty" bson:"approximate_count_upper_bound,omitempty"`
	ApproximateCountLowerBound int                `json:"approximate_count_lower_bound,omitempty" bson:"approximate_count_lower_bound,omitempty"`

	Rule               string         `json:"rule,omitempty" bson:"rule,omitempty"`
	CustomerFileSource string         `json:"customer_file_source,omitempty" bson:"customer_file_source,omitempty"`
	Lookalikes         []string       `json:"lookalike_audience_ids,omitempty" bson:"lookalike_audience_ids,omitempty"`
	Adaccounts         *Adaccounts    `json:"adaccounts,omitempty" bson:"adaccounts,omitempty"`
	LookalikeSpec      *LookalikeSpec `json:"lookalike_spec,omitempty" bson:"lookalike_spec,omitempty"`
	OriginAudienceID   string         `json:"origin_audience_id,omitempty" bson:"origin_audience_id,omitempty"`

	Type            string                    `json:"type,omitempty" bson:"type,omitempty"`
	PixelId         string                    `json:"pixel_id,omitempty" bson:"pixel_id,omitempty"`
	DataSource      *CustomAudienceDataSource `json:"data_source,omitempty" bson:"data_source,omitempty"`
	DeliveryStatus  *CustomAudienceStatus     `json:"delivery_status,omitempty" bson:"delivery_status,omitempty"`
	RetentionDays   int                       `json:"retention_days,omitempty" bson:"retention_days,omitempty"`
	TimeCreated     time.Time                 `json:"time_created,omitempty" bson:"time_created,omitempty"`
	TimeUpdated     time.Time                 `json:"time_updated,omitempty" bson:"time_updated,omitempty"`
	DeleteTime      int                       `json:"delete_time,omitempty" bson:"delete_time,omitempty"`
	OperationStatus *CustomAudienceStatus     `json:"operation_status,omitempty" bson:"operation_status,omitempty"`
	IsValueBased    bool                      `json:"is_value_based,omitempty" bson:"is_value_based,omitempty"`

	Targeting *Targeting `json:"targeting,omitempty" bson:"targeting,omitempty"`
	RunStatus string     `json:"run_status,omitempty" bson:"run_status,omitempty"`

	CreatedBy primitive.ObjectID  `json:"created_by" bson:"created_by"`
	CreatedAt time.Time           `json:"created_at" bson:"created_at"`
	UpdatedBy primitive.ObjectID  `json:"updated_by" bson:"updated_by"`
	UpdatedAt time.Time           `json:"updated_at" bson:"updated_at"`
	ClientID  *primitive.ObjectID `json:"client_id,omitempty" bson:"client_id"`

	Status string `json:"status,omitempty" bson:"status,omitempty"`
}

func (CustomAudienceEntity) CollectionName() string {
	return "fb_audiences"
}

type Adaccounts struct {
	Data []json.Number `json:"data,omitempty"`
}

type LookalikeSpec struct {
	Country string             `json:"country,omitempty"`
	Origin  []LookalikeOrigion `json:"origin,omitempty"`
	Ratio   float64            `json:"ratio,omitempty"`
	Type    string             `json:"type,omitempty"`
}

type LookalikeOrigion struct {
	ID   string `json:"id"`
	Name string `json:"name"`
	Type string `json:"type"`
}

type CustomAudienceDataSource struct {
	CreationParams string `json:"creation_params,omitempty"`
	SubType        string `json:"sub_type,omitempty"`
	Type           string `json:"type,omitempty"`
}

type CustomAudienceStatus struct {
	Code        int    `json:"code,omitempty"`
	Description string `json:"description,omitempty"`
}

type Targeting struct {
	// inventories
	PublisherPlatforms []string `json:"publisher_platforms,omitempty"`
	// sub inventories
	FacebookPositions        []string `json:"facebook_positions,omitempty"`
	InstagramPositions       []string `json:"instagram_positions,omitempty"`
	AudienceNetworkPositions []string `json:"audience_network_positions,omitempty"`
	MessengerPositions       []string `json:"messenger_positions,omitempty"`

	AgeMin  uint64 `json:"age_min,omitempty"`
	AgeMax  uint64 `json:"age_max,omitempty"`
	Genders []int  `json:"genders,omitempty"`

	AppInstallState string `json:"app_install_state,omitempty"`

	CustomAudiences         []IDContainer  `json:"custom_audiences,omitempty"`
	ExcludedCustomAudiences []IDContainer  `json:"excluded_custom_audiences,omitempty"`
	GeoLocations            *GeoLocations  `json:"geo_locations,omitempty"`
	ExcludedGeoLocations    *GeoLocations  `json:"excluded_geo_locations,omitempty"`
	FlexibleSpec            []FlexibleSpec `json:"flexible_spec,omitempty"`
	Exclusions              *FlexibleSpec  `json:"exclusions,omitempty"`

	DevicePlatforms             []string                 `json:"device_platforms,omitempty"`
	ExcludedPublisherCategories []string                 `json:"excluded_publisher_categories,omitempty"`
	Locales                     []int                    `json:"locales,omitempty"`
	TargetingOptimization       string                   `json:"targeting_optimization,omitempty"`
	UserDevice                  []string                 `json:"user_device,omitempty"`
	UserOs                      []string                 `json:"user_os,omitempty"`
	WirelessCarrier             []string                 `json:"wireless_carrier,omitempty"`
	TargetingRelaxationTypes    TargetingRelaxationTypes `json:"targeting_relaxation_types,omitempty"`
}

type GeoLocations struct {
	Countries          []string  `json:"countries,omitempty"`
	LocationTypes      []string  `json:"location_types,omitempty"`
	Cities             []City    `json:"cities,omitempty"`
	Regions            []Region  `json:"regions,omitempty"`
	Zips               []Zip     `json:"zips,omitempty"`
	Places             []Place   `json:"places,omitempty"`
	CountryGroups      []string  `json:"country_groups,omitempty"`
	SubCities          []SubCity `json:"subcities,omitempty"`
	LocationClusterIDs []string  `json:"location_cluster_ids,omitempty"`

	// CustomLocations    []CustomLocation    `json:"custom_locations,omitempty"`
	// GeoMarkets         []GeoMarket         `json:"geo_markets,omitempty"`
	// ElectoralDistricts []ElectoralDistrict `json:"electoral_districts,omitempty"`
	// SubNeighborhoods   []SubNeighborhood   `json:"subneighborhoods,omitempty"`
	// Neighborhoods      []Neighborhood      `json:"neighborhoods,omitempty"`
	// MetroAreas         []MetroArea         `json:"metro_areas,omitempty"`
	// SmallGeoAreas      []SmallGeoArea      `json:"small_geo_areas,omitempty"`
	// MediumGeoAreas     []MediumGeoArea     `json:"medium_geo_areas,omitempty"`
	// LargeGeoAreas      []LargeGeoArea      `json:"large_geo_areas,omitempty"`
}

type IDContainer struct {
	ID   string `json:"id"`
	Name string `json:"name"`
}

type FlexibleSpec struct {
	Interests            []IDContainer `json:"interests,omitempty"`
	Behaviors            []IDContainer `json:"behaviors,omitempty"`
	LifeEvents           []IDContainer `json:"life_events,omitempty"`
	WorkEmployers        []IDContainer `json:"work_employers,omitempty"`
	FamilyStatuses       []IDContainer `json:"family_statuses,omitempty"`
	WorkPositions        []IDContainer `json:"work_positions,omitempty"`
	Politics             []IDContainer `json:"politics,omitempty"`
	EducationMajors      []IDContainer `json:"education_majors,omitempty"`
	EducationStatuses    []int         `json:"education_statuses,omitempty"`
	RelationshipStatuses []int         `json:"relationship_statuses,omitempty"`
}

type TargetingRelaxationTypes struct {
	CustomAudience int8 `json:"custom_audience"`
	Lookalike      int8 `json:"lookalike"`
}

type City struct {
	Country      string `json:"country"`
	DistanceUnit string `json:"distance_unit"`
	Key          string `json:"key"`
	Name         string `json:"name"`
	Radius       int    `json:"radius"`
	Region       string `json:"region"`
	RegionID     string `json:"region_id"`
}

// Region can be targeted.
type Region struct {
	Key     string `json:"key"`
	Name    string `json:"name"`
	Country string `json:"country"`
}

// Zip can be targeted.
type Zip struct {
	Key           string `json:"key"`
	Name          string `json:"name"`
	PrimaryCityID int    `json:"primary_city_id"`
	RegionID      int    `json:"region_id"`
	Country       string `json:"country"`
}

// DailyOutcomesCurve talk to phillip.
type DailyOutcomesCurve struct {
	Spend       float64 `json:"spend"`
	Reach       float64 `json:"reach"`
	Impressions float64 `json:"impressions"`
	Actions     float64 `json:"actions"`
}

type Place struct {
	Key           string  `json:"key"`
	Name          string  `json:"name"`
	DistanceUnit  string  `json:"distance_unit"`
	Latitude      float64 `json:"latitude"`
	Longitude     float64 `json:"longitude"`
	Radius        int     `json:"radius"`
	PrimaryCityID int     `json:"primary_city_id"`
	RegionID      int     `json:"region_id"`
	Country       string  `json:"country"`
}

type SubCity struct {
	Key      string `json:"key"`
	Country  string `json:"country"`
	Name     string `json:"name"`
	Region   string `json:"region"`
	RegionID string `json:"region_id"`
}
