package model

import "time"

type PayloadCreatePaymentVTB struct {
	AdvertiserId       []int64   `json:"advertiser_id" validate:"required,gt=0"`
	ClientId           int64     `json:"client_id"`
	PaymentAmount      []int64   `json:"payment_Amount" validate:"required,gt=0"`
	PaymentMethod      string    `json:"payment_Method,omitempty"`
	PaymentTitle       string    `json:"title,omitempty"`
	PaymentLocale      string    `json:"payment_Locale" validate:"required,ruleLocal"`
	PaymentOrderInfo   string    `json:"payment_OrderInfo" validate:"required"`
	PaymentBankCode    string    `json:"payment_BankCode" validate:"required"`
	PaymentBankCodeId  string    `json:"payment_BankCodeId" validate:"required"`
	PaymentBillNumber  string    `json:"payment_BillNumber" validate:"required"`
	PaymentReferenceId string    `json:"payment_ReferenceId" validate:"required"`
	PayLoad            string    `json:"pay_load" validate:"required"`
	PointOIMethod      string    `json:"point_oi_method" validate:"required"`
	MasterMerchant     string    `json:"master_merchant" validate:"required"`
	MerchantCC         string    `json:"merchant_cc" validate:"required"`
	Ccy                string    `json:"ccy" validate:"required"`
	CountryCode        string    `json:"country_code" validate:"required"`
	MerchantName       string    `json:"merchant_name" validate:"required"`
	MerchantCity       string    `json:"merchant_city" validate:"required"`
	PinCode            string    `json:"pin_code"`
	StoreID            string    `json:"store_id"`
	TerminalID         string    `json:"terminal_id" validate:"required"`
	CurrencyCode       string    `json:"currency_code" validate:"required,oneof=USD VND"`
	PaymentIpAddr      string    `json:"payment_IpAddr" validate:"required,ruleValidateIP"`
	CreatedBy          string    `json:"created_by" validate:"required"`
	Platform           string    `json:"platform" validate:"required,oneof=FB DV"`
	MClientId          string    `json:"m_client_id"`
	MCreatedBy         string    `json:"m_created_by"`
	ServiceFee         string    `json:"service_fee"`
	CreatedAt          time.Time `json:"-"`
}
