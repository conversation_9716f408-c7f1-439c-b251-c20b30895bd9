package mapping

import (
	"godsp/modules/admin/client/transport/responses"
	catalogueE "godsp/modules/facebook/catalogue/entity"
)

func MappingCatalogueList(catalogues []*catalogueE.CatalogueEntity) []*responses.CatalogueListEditClient {
	var cataloguesView []*responses.CatalogueListEditClient
	for _, c := range catalogues {
		if c != nil {
			cataloguesView = append(cataloguesView, &responses.CatalogueListEditClient{
				ID:              c.CatalogueID,
				Name:            c.Name,
				DefaultImageURL: c.<PERSON>mageURL,
				ProductCount:    c.ProductCount,
				ListUserIDs:     c.ListUserIDs,
				ClientIDs:       c.ClientIDs,
			})
		}
	}
	return cataloguesView
}
