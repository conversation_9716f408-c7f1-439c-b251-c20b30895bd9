package entity

import (
	"godsp/modules/admin/user/entity"
	adAccountEntity "godsp/modules/facebook/ad_account/entity"
	"time"

	"go.mongodb.org/mongo-driver/bson/primitive"
)

type BillingEntity struct {
	ID primitive.ObjectID `json:"_id,omitempty" bson:"_id,omitempty"`

	Amount      int64  `json:"amount" bson:"amount"`
	Balance     int64  `json:"balance" bson:"balance"`
	Description string `json:"description" bson:"description"`
	Note        string `json:"note,omitempty" bson:"note,omitempty"`

	Status int `json:"status" bson:"status"`
	Type   int `json:"type" bson:"type"`

	Ordering int64 `json:"ordering" bson:"ordering"`

	CreatedBy primitive.ObjectID `json:"created_by" bson:"created_by"`
	UpdatedBy primitive.ObjectID `json:"updated_by,omitempty" bson:"updated_by,omitempty"`
	CreatedAt time.Time          `json:"created_at" bson:"created_at"`
	UpdatedAt time.Time          `json:"updated_at" bson:"updated_at"`

	ReceiptID primitive.ObjectID `json:"receipt_id,omitempty" bson:"receipt_id,omitempty"`
	InvoiceID primitive.ObjectID `json:"invoice_id,omitempty" bson:"invoice_id,omitempty"`

	AdAccount          *adAccountEntity.AdAccountEntity `json:"ad_account,omitempty" bson:"ad_account,omitempty"`
	UserCreated        *entity.UserEntity               `json:"user_created,omitempty" bson:"user_created,omitempty"`
	CreatedAtFormatted string                           `json:"created_at_formatted,omitempty" bson:"-"`
}

func (BillingEntity) CollectionName() string {
	return "user_billings"
}
