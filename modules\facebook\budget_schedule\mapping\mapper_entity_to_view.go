package mapping

import (
	"godsp/modules/facebook/budget_schedule/entity"
	"godsp/modules/facebook/budget_schedule/transport/responses"
)

func MapperListEntityToView(budgets *[]entity.HighDemandPeriodEntity) *[]responses.HighDemandPeriodEntity {

	var budgetsView []responses.HighDemandPeriodEntity
	for _, bud := range *budgets {
		budgetsView = append(budgetsView, responses.HighDemandPeriodEntity{
			CampaignID:         bud.CampaignID,
			AdsetID:            bud.AdsetID,
			HighDemandPeriodID: bud.HighDemandPeriodID,
			BudgetValue:        bud.BudgetValue,
			BudgetValueType:    bud.BudgetValueType,
			RecurrenceType:     bud.RecurrenceType,
			TimeStart:          bud.TimeStart,
			TimeEnd:            bud.TimeEnd,
		})
	}
	return &budgetsView
}
