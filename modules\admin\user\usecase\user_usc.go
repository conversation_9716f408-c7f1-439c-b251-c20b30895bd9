package usecase

import (
	"context"
	clientMapping "godsp/modules/admin/client/mapping"
	clientRes "godsp/modules/admin/client/transport/responses"
	roleE "godsp/modules/admin/role/entity"

	"godsp/modules/admin/common/admconst"
	"godsp/modules/admin/user/common/errs"
	"godsp/modules/admin/user/common/pipelines"
	"godsp/modules/admin/user/entity"
	"godsp/modules/admin/user/transport/responses"
	"godsp/modules/facebook/ad_account/transport/response"
	"godsp/modules/facebook/common/fbenums"

	"github.com/dev-networldasia/dspgos/sctx"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo/options"
)

// type AdAccountRoleRepo interface {
// 	FindAdAccountRepo(ctx context.Context, filter interface{}, opts ...*options.FindOptions) ([]*response.AdAccountWithFullname, error)
// }

type userUsc struct {
	logger        sctx.Logger
	repo          UserRepo
	roleRepo      RoleRepo
	clientRepo    ClientRepo
	adAcc         AdAccountRepo
	pageRepo      PageRepo
	pixelRepo     PixelRepo
	catalogueRepo CatalogueRepo
}

func NewUserUsc(logger sctx.Logger, repo UserRepo, roleRepo RoleRepo, clientRepo ClientRepo, adAcc AdAccountRepo, pageRepo PageRepo, pixelRepo PixelRepo, catalogueRepo CatalogueRepo) *userUsc {
	return &userUsc{
		logger:        logger,
		repo:          repo,
		roleRepo:      roleRepo,
		clientRepo:    clientRepo,
		adAcc:         adAcc,
		pageRepo:      pageRepo,
		pixelRepo:     pixelRepo,
		catalogueRepo: catalogueRepo,
	}
}

/**
 * Get list roles for user
 */
func (usc *userUsc) FindRoleUserUsc(ctx context.Context, roleActive bool) (*[]roleE.RoleEntity, error) {
	allowDiskUse := fbenums.ALLOW_DISK_USE_TRUE
	opts := &options.FindOptions{
		AllowDiskUse: &allowDiskUse,
		Sort:         bson.D{{Key: "ordering", Value: -1}, {Key: "id", Value: -1}},
		Projection:   bson.M{"id": 1, "role_name": 1, "status": 1},
	}

	filter := bson.M{}
	if roleActive {
		filter["status"] = admconst.STATUS_ACTIVE
	}

	data, err := usc.roleRepo.FindRoleRepo(ctx, filter, opts)
	if err != nil {
		usc.logger.Error(err)
		return nil, err
	}

	return data, nil
}

/**
 * Get detail user by id
 */
func (usc *userUsc) FindOneDetailUserUsc(ctx context.Context, id string) (*entity.UserEntity, error) {
	objectID, err := primitive.ObjectIDFromHex(id)
	if err != nil {
		usc.logger.Error(err)
		return nil, err
	}

	allowDiskUse := fbenums.ALLOW_DISK_USE_TRUE
	opts := &options.AggregateOptions{
		AllowDiskUse: &allowDiskUse,
	}

	pipeline := pipelines.PipelineGetOne(objectID)

	data, err := usc.repo.FindOneUserWithPipelineRepo(ctx, pipeline, opts)
	if err != nil {
		usc.logger.Error(err)
		return nil, errs.ErrIDUserValidate
	}

	if data.Status == admconst.STATUS_DELETE {
		return nil, errs.ErrEditStatusDeleteUser
	}
	data.PathImg()

	return data, nil
}

/**
 * list ad account
 */
func (usc *userUsc) ListAdAccountUserUsc(ctx context.Context) ([]*response.AdAccountWithFullname, error) {
	allowDiskUse := fbenums.ALLOW_DISK_USE_TRUE
	opts := &options.FindOptions{
		AllowDiskUse: &allowDiskUse,
		Sort:         bson.D{{Key: "name", Value: -1}},
		Projection:   bson.M{"id": 1, "name": 1, "account_id": 1},
	}

	return usc.adAcc.FindAdAccountAggregateRepo(ctx, bson.M{}, opts)
}

/**
 * List Client
 */
func (usc *userUsc) ListClientUsc(ctx context.Context) ([]*clientRes.ClientListSelect, error) {
	filter := bson.M{}
	clients, err := usc.clientRepo.FindClientRepo(ctx, filter)
	if err != nil {
		usc.logger.Error(err)
		return nil, err
	}
	data := clientMapping.MapperListSelectClient(clients)
	return data, nil
}

/**
 * Get detail user by id
 */
func (usc *userUsc) FindUserProfileUsc(ctx context.Context, id string) (*responses.DetailsUserProfile, error) {
	objectID, err := primitive.ObjectIDFromHex(id)
	if err != nil {
		usc.logger.Error(err)
		return nil, err
	}

	allowDiskUse := fbenums.ALLOW_DISK_USE_TRUE
	opts := &options.AggregateOptions{
		AllowDiskUse: &allowDiskUse,
	}

	pipeline := pipelines.PipelineGetUserProfile(objectID)

	// jsonData, _ := json.MarshalIndent(pipeline, "", "  ")
	// fmt.Println("PipelineGetUserProfile", string(jsonData))

	data, err := usc.repo.FindOneUserProfileWithPipelineRepo(ctx, pipeline, opts)
	if err != nil {
		usc.logger.Error(err)
		return nil, errs.ErrIDUserValidate
	}

	if data.Status == admconst.STATUS_DELETE {
		return nil, errs.ErrEditStatusDeleteUser
	}
	data.PathImg()

	return data, nil
}
