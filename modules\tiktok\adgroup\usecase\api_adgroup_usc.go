package usecase

import (
	"context"
	"encoding/json"
	"fmt"
	tiktokReportEntity "godsp/modules/tiktok/adserver_report/entity"
	"time"

	"godsp/modules/tiktok/adgroup/common/errs"
	"godsp/modules/tiktok/adgroup/mapping"
	"godsp/modules/tiktok/adgroup/pipelines"
	"godsp/modules/tiktok/adgroup/transport/requests"
	"godsp/modules/tiktok/adgroup/transport/responses"

	"godsp/pkg/gos/utils"
	"godsp/pkg/sctx/core"

	"github.com/dev-networldasia/dspgos/sctx"
	tiktokSv "github.com/dev-networldasia/tiktokservice/marketing/v13"
	"go.mongodb.org/mongo-driver/bson"
)

type TiktokReportDetailRepo interface {
	GetReportDetailByAdgroup(ctx context.Context, filter bson.M) (*[]tiktokReportEntity.TiktokReportDetailEntity, error)
}

type apiAdgroupUsc struct {
	tiktokService *tiktokSv.Service
	repo          AdgroupRepo
	logger        sctx.Logger
	tikReportRepo TiktokReportDetailRepo
}

func NewApiAdgroupUsc(tiktokService *tiktokSv.Service, repo AdgroupRepo, logger sctx.Logger, tikReportRepo TiktokReportDetailRepo) *apiAdgroupUsc {
	return &apiAdgroupUsc{
		tiktokService: tiktokService,
		repo:          repo,
		logger:        logger,
		tikReportRepo: tikReportRepo,
	}
}

/**
 * Reload adgroups
 * */
func (usc *apiAdgroupUsc) ReloadAdgroupsUsc(ctx context.Context, payload requests.ReloadAdgroupsReq) error {

	_, err := utils.GetInfoUserAuth(ctx)
	if err != nil {
		usc.logger.Error(err)
		return err
	}

	param := tiktokSv.AdgroupParams{
		AdvertiserID: *payload.AdvertiserID,
	}

	if payload.AdgroupIDs != nil && len(*payload.AdgroupIDs) > 0 {
		param.Filtering.AdgroupIDs = payload.AdgroupIDs
	}

	if payload.CampaignIDs != nil && len(*payload.CampaignIDs) > 0 {
		param.Filtering.CampaignIDs = payload.CampaignIDs
	}

	adgroupsResp, err := usc.tiktokService.Adgroups.List(ctx, &param)
	if err != nil {
		usc.logger.Error(err)
		return err
	}

	if adgroupsResp == nil || adgroupsResp.Data.List == nil || len(adgroupsResp.Data.List) == 0 {
		usc.logger.Error(errs.ErrListAdgroupEmpty.Error())
		return errs.ErrListAdgroupEmpty
	}

	err = usc.repo.UpsertAdgroupListRepo(ctx, &adgroupsResp.Data.List)
	if err != nil {
		usc.logger.Error(err)
	}
	// jsonData, _ := json.MarshalIndent(adgroupsResp.Data.List, "", "  ")
	// fmt.Printf("adgroupsResp: %s\n", jsonData)

	return nil

}

/**
 * Handle payload list camp datable
 */
func getFilterFindListDatatableAdgroup(ctx context.Context, payload *requests.ListTableAdgroupReq, usc *apiAdgroupUsc) (bson.M, error) {
	filter := bson.M{}
	user, _ := utils.GetInfoUserTiktok(ctx)
	permission := core.GetPermission(ctx).GetPermissions()

	if payload.ClientIDStr != "" {
		filter["client_id"] = payload.ClientID
	}

	if payload.SearchValue != nil && *payload.SearchValue != "" {
		filter["adgroup_name"] = bson.M{"$regex": *payload.SearchValue, "$options": "i"}
	}

	if payload.AdvertiserID != "" {
		filter["advertiser_id"] = payload.AdvertiserID
	}

	if payload.AdgroupID != "" {
		filter["adgroup_id"] = payload.AdgroupID
	}

	if len(payload.CampaignIds) > 0 {
		filter["campaign_id"] = bson.M{"$in": payload.CampaignIds}
	}

	if user.UserId != nil && permission != nil {
		filter["list_user_ids"] = user.UserId
	}

	if user.AdvertiserId != "" {
		filter["advertiser_id"] = user.AdvertiserId
	}

	return filter, nil
}

/**
 * Get report detail by adgroup
 */
func (usc *apiAdgroupUsc) getFilterAdgroupReport(ctx context.Context, payload *requests.ListTableAdgroupReq, adgroupIds []string) bson.M {
	filter := bson.M{"adgroup_id": bson.M{"$in": adgroupIds}}

	if payload.StartTime != nil && payload.EndTime != nil {
		startTimeStr := (*payload.StartTime).Format("2006-01-02")
		endTimeStr := (*payload.EndTime).Format("2006-01-02")
		filter["date"] = bson.M{"$gte": startTimeStr, "$lte": endTimeStr}
	} else {
		yesterday := time.Now().AddDate(0, 0, -1)
		endOfYesterdayStr := yesterday.Format("2006-01-02")
		startOfYesterdayStr := yesterday.AddDate(0, 0, -30).Format("2006-01-02")
		filter["date"] = bson.M{"$gte": startOfYesterdayStr, "$lte": endOfYesterdayStr}
	}

	return filter

}

/***
 * Get Listable Adgroup
 */
func (usc *apiAdgroupUsc) ListTableAdgroups(ctx context.Context, payload *requests.ListTableAdgroupReq) (*responses.DatatableAdgroupResq, error) {
	filter, err := getFilterFindListDatatableAdgroup(ctx, payload, usc)
	if err != nil {
		return nil, err
	}

	pipeline := pipelines.PipelineListDatatableAdgroup(payload, filter)

	adgroups, err := usc.repo.FindListDatatableAdgroupsRepo(ctx, pipeline)
	if err != nil {
		return nil, err
	}

	if len(*adgroups) == 0 {
		return nil, errs.ErrListAdgroupEmpty
	}

	var adgroupIds []string
	for _, adgroup := range *adgroups {
		adgroupIds = append(adgroupIds, adgroup.AdgroupID)
	}
	reportFilter := usc.getFilterAdgroupReport(ctx, payload, adgroupIds)
	reportData, err := usc.tikReportRepo.GetReportDetailByAdgroup(ctx, reportFilter)
	if err != nil {
		usc.logger.Error(err)
		return nil, err
	}

	dataTableRes := mapping.MapperListTableAdgroup(adgroups, reportData)

	//total all
	total, err := usc.repo.CountAdgroupRepo(ctx, bson.M{})
	if err != nil {
		return nil, err
	}

	// total filtered
	totalFiltered := total
	if len(filter) != 0 {
		totalFiltered, err = usc.repo.CountAdgroupRepo(ctx, filter)
		if err != nil {
			return nil, err
		}
	}

	return &responses.DatatableAdgroupResq{
		Draw:            payload.Draw,
		Data:            dataTableRes,
		RecordsTotal:    total,
		RecordsFiltered: totalFiltered,
	}, nil

}

/**
 * Get Adgroup by ID
 */
func (usc *apiAdgroupUsc) GetAdgroupByID(ctx context.Context, payload *requests.GetDetailsAdgroupsReq) (*responses.AdGroupRes, error) {

	pipeline := pipelines.GetDetailAdgroup(payload)
	jsonData, _ := json.MarshalIndent(pipeline, "", "  ")
	fmt.Println("\n-------- pipeline -------> \n", string(jsonData))

	resp, err := usc.repo.AggregationAdgroupRepo(ctx, pipeline)
	if err != nil {
		usc.logger.Error(err)
		return nil, err
	}

	jsonData, _ = json.MarshalIndent(resp, "", "  ")
	fmt.Println("\n-------- resp -------> \n", string(jsonData))

	if resp == nil {
		return nil, errs.ErrAdgroupNotFound
	}

	return resp, nil

	// if payload.AdgroupID == "" {
	// 	return nil, errs.ErrAdgroupIsRequired
	// }

	// filter := bson.M{
	// 	"adgroup_id": payload.AdgroupID,
	// }

	// if payload.AdvertiserID != nil {
	// 	filter["advertiser_id"] = payload.AdvertiserID
	// }

	// if payload.ClientID != nil {
	// 	filter["client_id"] = payload.ClientID
	// }

	// jsonData, _ := json.MarshalIndent(filter, "", "  ")
	// fmt.Println("filter ------->: %v\n", string(jsonData))

	// adgroup, err := usc.repo.FindOneAdgroupRepo(ctx, filter)
	// if err != nil {
	// 	usc.logger.Error(err)
	// 	return nil, err
	// }

	// if adgroup == nil {
	// 	return nil, errs.ErrAdgroupNotFound
	// }

	// return adgroup, nil
}
