package pipelines

import (
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
)

func GetReportCampaignPipe(filter bson.M) mongo.Pipeline {

	groupState1 := bson.M{
		"_id":           "$campaign_id",
		"campaign_id":   bson.M{"$first": "$campaign_id"},
		"campaign_name": bson.M{"$first": "$campaign_name"},
		"impressions": bson.M{"$sum": bson.M{
			"$cond": bson.A{
				bson.M{"$gt": bson.A{"$impressions", 0}},
				"$impressions",
				0,
			},
		}},
		"reach": bson.M{"$sum": bson.M{
			"$cond": bson.A{
				bson.M{"$gt": bson.A{"$reach", 0}},
				"$reach",
				0,
			},
		}},
		"clicks": bson.M{"$sum": bson.M{
			"$cond": bson.A{
				bson.M{"$gt": bson.A{"$clicks", 0}},
				"$clicks",
				0,
			},
		}},
		"actions": bson.M{"$push": "$actions"},
	}

	projectStage1 := bson.M{
		"campaign_id":   1,
		"campaign_name": 1,
		"impressions":   1,
		"clicks":        1,
		"reach":         1,
		"flat_actions": bson.M{
			"$reduce": bson.M{
				"input":        "$actions",
				"initialValue": bson.A{},
				"in": bson.M{
					"$concatArrays": bson.A{
						"$$value",
						bson.M{"$ifNull": bson.A{"$$this", bson.A{}}},
					},
				},
			},
		},
	}

	projectStage2 := bson.M{
		"campaign_id":   1,
		"campaign_name": 1,
		"impressions":   1,
		"clicks":        1,
		"reach":         1,
		"action_summary": bson.M{
			"$arrayToObject": bson.M{
				"$map": bson.M{
					"input": bson.M{
						"$reduce": bson.M{
							"input":        "$flat_actions",
							"initialValue": bson.A{},
							"in": bson.M{
								"$cond": bson.A{
									bson.M{"$in": bson.A{
										"$$this.action_type",
										bson.M{
											"$map": bson.M{
												"input": "$$value",
												"as":    "v",
												"in":    "$$v.k",
											},
										},
									}},
									bson.M{
										"$map": bson.M{
											"input": "$$value",
											"as":    "v",
											"in": bson.M{
												"k": "$$v.k",
												"v": bson.M{
													"$cond": bson.A{
														bson.M{"$eq": bson.A{"$$v.k", "$$this.action_type"}},
														bson.M{"$add": bson.A{"$$v.v", "$$this.value"}},
														"$$v.v",
													},
												},
											},
										},
									},
									bson.M{
										"$concatArrays": bson.A{
											"$$value",
											bson.A{bson.M{
												"k": "$$this.action_type",
												"v": "$$this.value",
											}},
										},
									},
								},
							},
						},
					},
					"as": "item",
					"in": bson.M{
						"k": "$$item.k",
						"v": "$$item.v",
					},
				},
			},
		},
	}

	replaceRoot := bson.M{
		"newRoot": bson.M{
			"$mergeObjects": bson.A{
				"$$ROOT",
				"$action_summary",
			},
		},
	}

	pipeline := mongo.Pipeline{
		{{Key: "$match", Value: filter}},
		{{Key: "$group", Value: groupState1}},
		{{Key: "$project", Value: projectStage1}},
		{{Key: "$project", Value: projectStage2}},
		{{Key: "$replaceRoot", Value: replaceRoot}},
		{{Key: "$project", Value: bson.M{
			"action_summary": 0,
		}}},
	}
	return pipeline

}
