package mongo

import (
	"context"
	"errors"
	"godsp/modules/facebook/ad_pixel/entity"
	"godsp/modules/facebook/common/fberrs"
	"godsp/pkg/sctx/core"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
)

type adPixelRepo struct {
	DB         *mongo.Database
	Collection *mongo.Collection
}

func NewAdPixelRepo(DB *mongo.Database) *adPixelRepo {
	return &adPixelRepo{
		DB:         DB,
		Collection: DB.Collection(entity.AdPixelEntity{}.CollectionName()),
	}
}

/***
 * insert ad pixel
 */
func (r *adPixelRepo) InsertAdPixelRepo(ctx context.Context, adPixelEntity *entity.AdPixelEntity) error {
	_, err := r.Collection.InsertOne(ctx, adPixelEntity)
	if err != nil {
		return err
	}

	return nil
}

/***
 * update one filter interface{}, update interface{}, opts ...*options.UpdateOptions
 */
func (r *adPixelRepo) UpdateOneAdPixelRepo(ctx context.Context, filter interface{}, adPixelEntity *entity.AdPixelEntity, opts ...*options.UpdateOptions) error {
	update := bson.M{
		"$set": *adPixelEntity,
	}
	_, err := r.Collection.UpdateOne(ctx, filter, update)
	if err != nil {
		return err
	}

	return nil
}

/** Upsert Ad Pixel */
func (r *adPixelRepo) UpsertAdPixelRepo(ctx context.Context, filter bson.M, update bson.M) error {
	opts := options.Update().SetUpsert(true)
	_, err := r.Collection.UpdateOne(ctx, filter, update, opts)
	if err != nil {
		return err
	}

	return nil
}

/**
 * FindOne ad pixel
 */
func (r *adPixelRepo) FindOneAdPixelRepo(ctx context.Context, filter interface{}, opts ...*options.FindOneOptions) (*entity.AdPixelEntity, error) {
	var adPixel entity.AdPixelEntity
	err := r.Collection.FindOne(ctx, filter, opts...).Decode(&adPixel)
	if errors.Is(err, mongo.ErrNoDocuments) {
		return nil, core.ErrNotFound
	}

	return &adPixel, nil
}

func (r *adPixelRepo) FindAdPixelRepo(ctx context.Context, filter interface{}, opts ...*options.FindOptions) ([]*entity.AdPixelEntity, error) {
	var adPixels []*entity.AdPixelEntity

	cursor, err := r.Collection.Find(ctx, filter, opts...)
	if err != nil {
		return nil, err
	}
	defer cursor.Close(ctx)

	if cursor == nil {
		return nil, fberrs.ErrNilCursorValue
	}
	err = cursor.All(ctx, &adPixels)
	if err != nil {
		return nil, err
	}

	return adPixels, nil
}

/**
 * count Ad Pixel
 */
func (r *adPixelRepo) CountAdPixelRepo(ctx context.Context, filter interface{}) (int64, error) {
	return r.Collection.CountDocuments(ctx, filter)
}

/**
 * Update Bson.M Field for Pixel
 */
func (r *adPixelRepo) UpdateManyPixelRepo(ctx context.Context, filter interface{}, update interface{}) error {

	result, err := r.Collection.UpdateMany(ctx, filter, update, options.Update().SetUpsert(false))
	if err != nil {
		return err
	}

	if result.MatchedCount == 0 && result.ModifiedCount == 0 {
		return nil
	}

	return nil
}
