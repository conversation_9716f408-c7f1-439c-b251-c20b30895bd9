package mapping

import (
	"godsp/modules/facebook/ad_creative/entity"
	v20 "godsp/pkg/fb-marketing/marketing/v20"
	"time"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

func MapperAdCreativeToUpsert(creative *v20.AdCreative, userId primitive.ObjectID, clientId primitive.ObjectID) bson.M {
	// jsonData, err := json.MarshalIndent(creative, "", "  ")
	// if err != nil {
	// 	fmt.Println("Error marshaling struct:", err)
	// } else {
	// 	fmt.Println("\n-------- creative -------> \n", string(jsonData))
	// }

	now := time.Now()

	// Fields to update 38
	updateData := bson.M{
		"account_id": creative.AccountID,
		"name":       creative.Name,
		"status":     creative.Status,
		"actor_id":   creative.ActorID,

		"applink_treatment":               creative.ApplinkTreatment,
		"body":                            creative.Body,
		"branded_content_sponsor_page_id": creative.BrandedContentSponsorPageID,
		"call_to_action_type":             creative.CallToActionType,
		"effective_instagram_story_id":    creative.EffectiveInstagramStoryID,

		"effective_object_story_id": creative.EffectiveObjectStoryID,
		"image_hash":                creative.ImageHash,
		"image_url":                 creative.ImageURL,
		"instagram_actor_id":        creative.InstagramActorID,
		"instagram_permalink_url":   creative.InstagramPermalinkURL,

		"instagram_story_id":          creative.InstagramStoryID,
		"interactive_components_spec": creative.InteractiveComponentsSpec,
		"link_og_id":                  creative.LinkOgID,
		"link_url":                    creative.LinkURL,
		"messenger_sponsored_message": creative.MessengerSponsoredMessage,

		"object_id":       creative.ObjectID,
		"object_story_id": creative.ObjectStoryID,
		"object_type":     creative.ObjectType,
		"object_url":      creative.ObjectURL,
		"product_set_id":  creative.ProductSetID,

		"template_url":  creative.TemplateURL,
		"thumbnail_url": creative.ThumbnailURL,
		"title":         creative.Title,
		"url_tags":      creative.URLTags,
		"video_id":      creative.VideoID,

		"use_page_actor_override": creative.UsePageActorOverride,
		"image_crops":             creative.ImageCrops,
		"object_story_spec":       creative.ObjectStorySpec,
		"asset_feed_spec":         creative.AssetFeedSpec,
		"platform_customizations": creative.PlatformCustomizations,
		"recommender_settings":    creative.RecommenderSettings,

		"template_url_spec":       creative.TemplateURLSpec,
		"adlabels":                creative.Adlabels,
		"degrees_of_freedom_spec": creative.DegreesOfFreedomSpec,
		"contextual_multi_ads":    creative.ContextualMultiAds,

		"updated_by": userId,
		"updated_at": now,
	}

	// Fields to set only on insert
	createData := bson.M{
		"created_by": userId,
		"created_at": now,
		"client_id":  clientId,
	}

	// Return upsert document
	return bson.M{
		"$set":         updateData,
		"$setOnInsert": createData,
		"$addToSet": bson.M{
			"list_user_ids": userId,
		},
	}
}

/***
 *
 * sau nay ne tach ra de code luon mapper ko anh huont tu thu vien
 */

func mapperObjectStorySpecToEntity(objectStorySpec *v20.ObjectStorySpec) *entity.ObjectStorySpec {
	if objectStorySpec == nil {
		return nil
	}
	objectStorySpecEntity := entity.ObjectStorySpec{
		PageID:           objectStorySpec.PageID,
		InstagramActorID: objectStorySpec.InstagramActorID,
		VideoData:        mapperVideoData(objectStorySpec.VideoData),
		LinkData:         mapperAdCreativeLinkData(objectStorySpec.LinkData),
		TemplateData:     mapperAdCreativeLinkData(objectStorySpec.TemplateData),
	}

	return &objectStorySpecEntity
}

func mapperVideoData(videoData *v20.VideoData) *entity.VideoData {
	if videoData == nil {
		return nil
	}

	videoDataEntity := entity.VideoData{}
	videoDataEntity.ImageHash = videoData.ImageHash
	videoDataEntity.ImageURL = videoData.ImageURL
	videoDataEntity.LinkDescription = videoData.LinkDescription
	videoDataEntity.Message = videoData.Message
	videoDataEntity.Title = videoData.Title
	videoDataEntity.VideoID = videoData.VideoID

	if videoData.CallToAction != nil {
		callToAction := videoData.CallToAction
		adCreativeLinkDataCallToAction := entity.AdCreativeLinkDataCallToAction{
			Type: callToAction.Type,
		}

		if callToAction.Value != nil {
			adCreativeLinkDataCallToAction.Value = &entity.AdCreativeLinkDataCallToActionValue{
				AppDestination: callToAction.Value.AppDestination,
				AppLink:        callToAction.Value.AppLink,
				Application:    callToAction.Value.Application,
				EventID:        callToAction.Value.EventID,
				LeadGenFormID:  callToAction.Value.LeadGenFormID,
				Link:           callToAction.Value.Link,
				LinkCaption:    callToAction.Value.LinkCaption,
				LinkFormat:     callToAction.Value.LinkFormat,
				Page:           callToAction.Value.Page,
				ProductLink:    callToAction.Value.ProductLink,
			}
		}

		videoDataEntity.CallToAction = &adCreativeLinkDataCallToAction
	}

	return &videoDataEntity
}

func mapperAdCreativeLinkData(linkData *v20.AdCreativeLinkData) *entity.AdCreativeLinkData {
	linkDataEntity := entity.AdCreativeLinkData{
		AdditionalImageIndex: linkData.AdditionalImageIndex,
	}

	return &linkDataEntity
}

// AdCreativeLinkDataAppLinkSpec
func mapperAdCreativeLinkDataAppLinkSpec(linkData *v20.AdCreativeLinkDataAppLinkSpec) *entity.AdCreativeLinkDataAppLinkSpec {
	if linkData == nil {
		return nil
	}
	linkDataAppLinkSpec := entity.AdCreativeLinkDataAppLinkSpec{}

	if linkData.Android != nil {
		var androids []entity.AndroidAppLink
		for _, adr := range linkData.Android {
			androids = append(androids, entity.AndroidAppLink{
				AppName: adr.AppName,
				Class:   adr.Class,
				Package: adr.Package,
				URL:     adr.URL,
			})
		}
		linkDataAppLinkSpec.Android = androids
	}

	if linkData.Ios != nil {
		var ioss []entity.IosAppLink
		for _, ios := range linkData.Ios {
			ioss = append(ioss, entity.IosAppLink{
				AppName:    ios.AppName,
				AppStoreID: ios.AppStoreID,
				URL:        ios.URL,
			})
		}
		linkDataAppLinkSpec.Ios = ioss
	}

	if linkData.Ipad != nil {
		var ipads []entity.IosAppLink
		for _, ipad := range linkData.Ipad {
			ipads = append(ipads, entity.IosAppLink{
				AppName:    ipad.AppName,
				AppStoreID: ipad.AppStoreID,
				URL:        ipad.URL,
			})
		}
		linkDataAppLinkSpec.Ipad = ipads
	}

	if linkData.Iphone != nil {
		var iphones []entity.IosAppLink
		for _, iphone := range linkData.Iphone {
			iphones = append(iphones, entity.IosAppLink{
				AppName:    iphone.AppName,
				AppStoreID: iphone.AppStoreID,
				URL:        iphone.URL,
			})
		}
		linkDataAppLinkSpec.Iphone = iphones
	}

	return &linkDataAppLinkSpec
}

func mapperAdCreativeContextualMultiAds(contextualMultiAds *v20.ContextualMultiAds) *entity.ContextualMultiAds {
	contextualMultiAdsData := entity.ContextualMultiAds{
		EnrollStatus: contextualMultiAds.EnrollStatus,
	}
	return &contextualMultiAdsData
}
