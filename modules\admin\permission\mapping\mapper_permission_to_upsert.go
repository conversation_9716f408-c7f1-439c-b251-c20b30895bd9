package mapping

import (
	"godsp/modules/admin/common/admconst"
	"godsp/modules/admin/permission/common/consts"
	"godsp/pkg/gos/utils"
	"strings"
	"time"

	"github.com/gofiber/fiber/v2"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
)

func MapperRefreshPermissionUpsertWriteModel(routes []fiber.Route, userId primitive.ObjectID, refreshId int64) []mongo.WriteModel {
	var permissions []mongo.WriteModel

	updateAt, _ := time.Parse(time.RFC3339, time.Now().Format(time.RFC3339))
	var ordering int64 = 1
	for _, route := range routes {
		name := removePathParam(route)
		name = utils.FormatRouteName(name)

		routeName := route.Name

		group, module, action, isApi := processGroupModuleAction(routeName)
		path := removePathParam(route)

		filter := bson.M{
			"method": route.Method,
			"path":   path,
		}

		updateData := bson.M{
			"group":      group,
			"module":     module,
			"action":     action,
			"name":       name,
			"route_name": routeName,
			"is_api":     isApi,
			"refresh_id": refreshId,
			"updated_at": updateAt,
			"updated_by": userId,
		}

		createData := bson.M{
			"method":     route.Method,
			"path":       path,
			"is_block":   admconst.IS_BLOCK_NO,
			"is_acp":     admconst.IS_ACP_LIMIT_ACCESS,
			"ordering":   ordering,
			"created_at": updateAt,
			"created_by": userId,
		}
		model := mongo.NewUpdateOneModel().
			SetFilter(filter).
			SetUpdate(bson.M{
				"$set":         updateData,
				"$setOnInsert": createData,
			}).
			SetUpsert(true)

		permissions = append(permissions, model)

		ordering++
	}

	return permissions
}

/**
 * process get group, module, action, is api
 * rule: router.Name = group.module.action
 * rule: router.Name = group.api.module.action
 * rule: router.Name = group.module.api.action
 */

func processGroupModuleAction(str string) (group, module, action string, isApi int) {
	isApi = consts.IS_API_UNKNOWN
	if str == "" {
		return "", "", "", isApi
	}

	parts := strings.Split(str, ".")
	length := len(parts)

	if length < 3 {
		return "", "", "", isApi
	}

	// Mặc định
	isApi = consts.IS_API_FALSE
	group, module, action = parts[0], parts[1], parts[2]

	// Xử lý trường hợp có "api"
	if length == 4 {
		if parts[1] == "api" {
			module, action = parts[2], parts[3]
			isApi = consts.IS_API_TRUE
		} else if parts[2] == "api" {
			action = parts[3]
			isApi = consts.IS_API_TRUE
		}
	}

	return group, module, action, isApi
}

/**
 * /cms/option-group/edit/:id -> /cms/option-group/edit
 * Use for auth middleware when accessing routes that contain params (Ex: edit)
 */
func removePathParam(route fiber.Route) string {
	path := route.Path
	if route.Params != nil {
		index := strings.LastIndex(path, "/:"+route.Params[0])
		if index != -1 {
			path = path[:index]
		}
	}
	return path
}
