package response

import (
	"godsp/modules/admin/common/admconst"
	"godsp/modules/admin/role/entity"
	"godsp/pkg/gos/goconst"
	"godsp/pkg/gos/utils"
)

func RoleDetailUpdateDatatable(role *entity.RoleUpdate, userName string) *RoleDataTable {
	updatedAt := utils.FormatTimeToString(role.UpdatedAt, goconst.YYYY_MM_DD_HH_MM_SS)
	datatable := RoleDataTable{
		ID:          role.ID,
		Name:        role.Name,
		Status:      admconst.StatusFullName[role.Status],
		RoleName:    role.RoleName,
		Ordering:    role.Ordering,
		UpdatedAt:   updatedAt,
		UpdatedName: userName,
	}
	return &datatable
}
