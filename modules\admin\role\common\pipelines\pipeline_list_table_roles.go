package pipelines

import "go.mongodb.org/mongo-driver/bson"

func PipelineListTableRoles() []bson.M {
	pipeline := []bson.M{
		{
			"$lookup": bson.M{
				"from":         "admin_users",
				"localField":   "created_by",
				"foreignField": "_id",
				"as":           "user_created",
			},
		},
		{
			"$lookup": bson.M{
				"from":         "admin_users",
				"localField":   "updated_by",
				"foreignField": "_id",
				"as":           "user_updated",
			},
		},
		{
			"$unwind": bson.M{
				"path":                       "$user_created",
				"preserveNullAndEmptyArrays": true,
			},
		},
		{
			"$unwind": bson.M{
				"path":                       "$user_updated",
				"preserveNullAndEmptyArrays": true,
			},
		},
		{
			"$sort": bson.M{
				"ordering": -1,
			},
		},
	}

	return pipeline
}