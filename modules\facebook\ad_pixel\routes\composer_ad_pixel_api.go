package routes

import (
	userR "godsp/modules/admin/user/repository/mongo"
	"godsp/modules/facebook/ad_pixel/repository/mongo"
	"godsp/modules/facebook/ad_pixel/transport/api"
	"godsp/modules/facebook/ad_pixel/usecase"
	"godsp/pkg/sctx/component/facebook/fbmarketing"

	"github.com/dev-networldasia/dspgos/sctx/component/mongodb"

	"github.com/dev-networldasia/dspgos/sctx/configs"

	"github.com/dev-networldasia/dspgos/sctx"

	"github.com/gofiber/fiber/v2"
)

type ComposerAdPixelApi interface {
	ReloadAdPixelApi() fiber.Handler
	ListDatatableAdPixelApi() fiber.Handler
	ListAdPixelApi() fiber.Handler
}

func ComposerApiAdPixelService(serviceCtx sctx.ServiceContext) ComposerAdPixelApi {
	logger := serviceCtx.MustGet(configs.KeyLoggerDaily).(sctx.AppLoggerDaily).GetLogger("webfb")
	mongoDB := serviceCtx.MustGet(configs.KeyCompMongoDB).(mongodb.MongoComponent).GetDatabase()
	repo := mongo.NewAdPixelRepo(mongoDB)

	fbService := serviceCtx.MustGet(configs.KeyFBMarketingV20).(fbmarketing.FBMarketingServices).GetFBMarketingService()
	userRepo := userR.NewUserRepo(mongoDB)

	usc := usecase.NewFBMarketingUsc(fbService, repo, userRepo, logger)
	api := api.NewAdPixelApi(usc)

	return api
}
