package admconst

type OrderDatatable struct {
	Column int    `json:"column" form:"column"`
	Dir    string `json:"dir" form:"dir"`
	Name   string `json:"name" form:"name"`
}

const (
	STATUS_ACTIVE   = 1
	STATUS_INACTIVE = 2
	STATUS_DELETE   = 3
	STATUS_ARCHIVE  = 4

	DEFAULT_IMG_ADMIN = "/static/images/users/user-dummy-img.jpg"
	ROLE_ACTIVE       = true

	IS_ACP_FULL_ACCESS  = 1
	IS_ACP_LIMIT_ACCESS = 2
	IS_BLOCK_YES        = 1
	IS_BLOCK_NO         = 2

	PERMISSION_YES    = 1
	PERMISSION_CUSTOM = 2

	KEY_CACHE_REDIS_PERMISSION_LOGIN = "permission-auth_"

	ROLE_ADMIN               = "ADMIN"
	ROLE_CLIENT_ADMIN        = "ROLE-CLIENT-ADMIN"
	ROLE_CLIENT_ADMIN_VIEWER = "ROLE_CLIENT-ADMIN-VIEWER"
	ROLE_CLIENT_USER         = "ROLE_CLIENT_USER"

	ROLE_GROUP_ADMIN      = "ADMIN"
	ROLE_GROUP_CLIENT     = "CLIENT"
	ROLE_GROUP_ACCOUNTANT = "ACCOUNTANT"

	DEFAULT_SORT_COLUMN      = "created_at"
	DEFAULT_ORDER_ASCENDING  = "asc"
	DEFAULT_ORDER_DESCENDING = "desc"
	DEFAULT_LIMIT            = 25
)

var (
	StatusCreateName = map[int]string{
		STATUS_ACTIVE:   "active",
		STATUS_INACTIVE: "inactive",
	}
	StatusCreateNameValue = map[string]int{
		"active":   STATUS_ACTIVE,
		"inactive": STATUS_INACTIVE,
	}
	StatusFullName = map[int]string{
		STATUS_ACTIVE:   "active",
		STATUS_INACTIVE: "inactive",
		STATUS_DELETE:   "deleted",
		STATUS_ARCHIVE:  "archive",
	}
	StatusFullNameValue = map[string]int{
		"active":   STATUS_ACTIVE,
		"inactive": STATUS_INACTIVE,
		"deleted":  STATUS_DELETE,
		"archive":  STATUS_ARCHIVE,
	}
	DefaultAvailableSortFields = map[int]string{
		1: "created_at",
		2: "name",
		3: "status",
	}
)
