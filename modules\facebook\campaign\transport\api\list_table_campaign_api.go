package api

import (
	"fmt"
	"godsp/conf"
	"godsp/modules/facebook/campaign/transport/requests"
	"godsp/modules/facebook/campaign/transport/responses"
	"godsp/pkg/gos/utils"
	"godsp/pkg/sctx/core"

	"github.com/gofiber/fiber/v2"
)

/**
 * List data table campaign api
 */
func (a *campaignApi) ListTableCampaignApi() fiber.Handler {
	return func(c *fiber.Ctx) error {

		resData := responses.DatatableCampaignResq{}
		var payload requests.ListTableCampaignReq
		if err := c.BodyParser(&payload); err != nil {
			resData.Msg = err.Error()
			return c.JSON(resData)
		}

		// Validate
		resData.Draw = payload.Draw
		if err := payload.Validate(); err != nil {
			resData.Msg = err.Error()
			return c.JSON(resData)
		}

		userInfo, err := utils.GetInfoUserBasic(c.Context())
		if userInfo.RoleName == conf.SysConf.RoleAdmin {
			fmt.Println("admin", payload.AccountID)
			if payload.AccountID == "" && payload.ClientIDStr == "" {
				return c.Status(fiber.StatusBadRequest).JSON(core.ResponseData(map[string]interface{}{
					"msg":  "Account ID or Client ID is required",
					"data": nil,
				}))
			}
		} else if err != nil {
			resData.Msg = err.Error()
			return c.JSON(resData)
		}

		campDatatable, err := a.usc.ListDatatableCampaignUsc(c.Context(), &payload)
		if err != nil {
			resData.Msg = err.Error()
			return c.JSON(resData)
		}

		return c.JSON(campDatatable)
	}
}
