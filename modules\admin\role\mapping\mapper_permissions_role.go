package mapping

import (
	"godsp/modules/admin/role/common/consts"
	"godsp/modules/admin/role/entity"

	"github.com/lib/pq"
)

func MapperPermissionRole(permissions pq.StringArray, listPermission map[string]interface{}) entity.PermissionMapInt {
	if len(permissions) == 0 {
		return nil
	}
	dataPermission := make(entity.PermissionMapInt)
	for _, permission := range permissions {
		// if _, ok := listPermission[permission]; ok {
		dataPermission[permission] = consts.PERMISSION_YES
		// }
	}

	if len(dataPermission) == 0 {
		return nil
	}

	return dataPermission
}
