package components

templ GmvProductTableCpn() {
<div class="row mt-4 fade-in">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <div class="d-flex justify-content-between align-items-center">
                    <h5 class="card-title mb-0">Product and creatives reporting</h5>
                </div>
                <p class="text-muted mb-0 mt-2">
                    View creatives to see performance data for videos and images in your ads featuring each product in
                    this campaign. Video level creatives data is available after June 27, 2024.
                </p>
            </div>
            <div class="card-body">
                <!-- Tab Navigation -->
                <ul class="nav nav-tabs nav-tabs-custom nav-success mb-3" role="tablist" id="product-tabs">
                    <li class="nav-item">
                        <a class="nav-link active" data-bs-toggle="tab" href="#product-tab" role="tab"
                            aria-selected="true">
                            <i class="ri-product-hunt-line me-1 align-bottom"></i> Product 3
                        </a>
                    </li>
                </ul>

                <!-- Tab Content -->
                <div class="tab-content">
                    <!-- Product Tab -->
                    <div class="tab-pane fade show active" id="product-tab" role="tabpanel">
                        <div class="table-responsive">
                            <table class="table table-nowrap align-middle mb-0">
                                <thead class="table-light">
                                    <tr>
                                        <th scope="col">Product name</th>
                                        <th scope="col">Status</th>
                                        <th scope="col">Optimization mode</th>
                                        <th scope="col">Cost <i class="ri-arrow-up-down-line"></i></th>
                                        <th scope="col">Orders (SKU) <i class="ri-information-line"></i></th>
                                        <th scope="col">Action</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td>
                                            <div class="d-flex align-items-center">
                                                <div class="avatar-sm me-3">
                                                    <div class="avatar-title bg-light rounded">
                                                        <img src="https://via.placeholder.com/40x40/f8f9fa/6c757d?text=Product"
                                                            alt="Product" class="img-fluid">
                                                    </div>
                                                </div>
                                                <div>
                                                    <h6 class="mb-1">Áo kiểu nữ đẹp ta lụa Maybi Wo...</h6>
                                                    <small class="text-muted">ID: 1731716014238617228</small>
                                                </div>
                                            </div>
                                        </td>
                                        <td>
                                            <span class="badge bg-success-subtle text-success">Available</span>
                                        </td>
                                        <td>
                                            <div class="d-flex align-items-center">
                                                <span>Target ROI</span>
                                                <i class="ri-arrow-down-s-line ms-1"></i>
                                            </div>
                                        </td>
                                        <td>
                                            <strong>600,590 VND</strong>
                                        </td>
                                        <td>
                                            <span>-</span>
                                        </td>
                                        <td>
                                            <button type="button"
                                                class="btn btn-outline-primary btn-sm view-creatives-btn"
                                                data-product-id="1731716014238617228">
                                                View creatives
                                            </button>
                                            <div class="dropdown d-inline-block ms-1">
                                                <button class="btn btn-soft-secondary btn-sm dropdown-toggle"
                                                    type="button" data-bs-toggle="dropdown" aria-expanded="false">
                                                    <i class="ri-more-fill"></i>
                                                </button>
                                                <ul class="dropdown-menu dropdown-menu-end">
                                                    <li><a class="dropdown-item" href="#"><i
                                                                class="ri-eye-line align-bottom me-2 text-muted"></i>
                                                            View</a></li>
                                                    <li><a class="dropdown-item" href="#"><i
                                                                class="ri-edit-2-line align-bottom me-2 text-muted"></i>
                                                            Edit</a></li>
                                                    <li><a class="dropdown-item" href="#"><i
                                                                class="ri-delete-bin-fill align-bottom me-2 text-muted"></i>
                                                            Delete</a></li>
                                                </ul>
                                            </div>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td>
                                            <div class="d-flex align-items-center">
                                                <div class="avatar-sm me-3">
                                                    <div class="avatar-title bg-light rounded">
                                                        <img src="https://via.placeholder.com/40x40/f8f9fa/6c757d?text=Product"
                                                            alt="Product" class="img-fluid">
                                                    </div>
                                                </div>
                                                <div>
                                                    <h6 class="mb-1">Chân váy midi Maybi ôm tuyết m...</h6>
                                                    <small class="text-muted">ID: 1729669485021989356</small>
                                                </div>
                                            </div>
                                        </td>
                                        <td>
                                            <span class="badge bg-success-subtle text-success">Available</span>
                                        </td>
                                        <td>
                                            <span>Target ROI</span>
                                        </td>
                                        <td>
                                            <strong>16,130 VND</strong>
                                        </td>
                                        <td>
                                            <span>-</span>
                                        </td>
                                        <td>
                                            <button type="button"
                                                class="btn btn-outline-primary btn-sm view-creatives-btn"
                                                data-product-id="1729669485021989356">
                                                View creatives
                                            </button>
                                            <div class="dropdown d-inline-block ms-1">
                                                <button class="btn btn-soft-secondary btn-sm dropdown-toggle"
                                                    type="button" data-bs-toggle="dropdown" aria-expanded="false">
                                                    <i class="ri-more-fill"></i>
                                                </button>
                                                <ul class="dropdown-menu dropdown-menu-end">
                                                    <li><a class="dropdown-item" href="#"><i
                                                                class="ri-eye-line align-bottom me-2 text-muted"></i>
                                                            View</a></li>
                                                    <li><a class="dropdown-item" href="#"><i
                                                                class="ri-edit-2-line align-bottom me-2 text-muted"></i>
                                                            Edit</a></li>
                                                    <li><a class="dropdown-item" href="#"><i
                                                                class="ri-delete-bin-fill align-bottom me-2 text-muted"></i>
                                                            Delete</a></li>
                                                </ul>
                                            </div>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td>
                                            <div class="d-flex align-items-center">
                                                <div class="avatar-sm me-3">
                                                    <div class="avatar-title bg-light rounded">
                                                        <img src="https://via.placeholder.com/40x40/f8f9fa/6c757d?text=Product"
                                                            alt="Product" class="img-fluid">
                                                    </div>
                                                </div>
                                                <div>
                                                    <h6 class="mb-1">[VIỀN TRẮNG] Bộ pyjama ngắn s...</h6>
                                                    <small class="text-muted">ID: 1731110430637394412</small>
                                                </div>
                                            </div>
                                        </td>
                                        <td>
                                            <span class="badge bg-success-subtle text-success">Available</span>
                                        </td>
                                        <td>
                                            <span>Target ROI</span>
                                        </td>
                                        <td>
                                            <strong>1,584 VND</strong>
                                        </td>
                                        <td>
                                            <span>-</span>
                                        </td>
                                        <td>
                                            <button type="button"
                                                class="btn btn-outline-primary btn-sm view-creatives-btn"
                                                data-product-id="1731110430637394412">
                                                View creatives
                                            </button>
                                            <div class="dropdown d-inline-block ms-1">
                                                <button class="btn btn-soft-secondary btn-sm dropdown-toggle"
                                                    type="button" data-bs-toggle="dropdown" aria-expanded="false">
                                                    <i class="ri-more-fill"></i>
                                                </button>
                                                <ul class="dropdown-menu dropdown-menu-end">
                                                    <li><a class="dropdown-item" href="#"><i
                                                                class="ri-eye-line align-bottom me-2 text-muted"></i>
                                                            View</a></li>
                                                    <li><a class="dropdown-item" href="#"><i
                                                                class="ri-edit-2-line align-bottom me-2 text-muted"></i>
                                                            Edit</a></li>
                                                    <li><a class="dropdown-item" href="#"><i
                                                                class="ri-delete-bin-fill align-bottom me-2 text-muted"></i>
                                                            Delete</a></li>
                                                </ul>
                                            </div>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>


                </div>
            </div>
        </div>
    </div>
</div>

<script>
    $(document).ready(function () {
        // Handle View creatives button click
        $(document).on('click', '.view-creatives-btn', function () {
            const productId = $(this).data('product-id');
            const productName = $(this).closest('tr').find('h6').text();

            // Check if creatives tab already exists
            if ($('#creatives-tab').length === 0) {
                // Create creatives tab
                const creativesTabHtml = `
                    <li class="nav-item">
                        <a class="nav-link" data-bs-toggle="tab" href="#creatives-tab" role="tab" aria-selected="false" id="creatives-tab-link">
                            <i class="ri-image-line me-1 align-bottom"></i> Creatives 39
                            <button type="button" class="btn-close ms-2" aria-label="Close" id="close-creatives-tab"></button>
                        </a>
                    </li>
                `;

                // Add tab to navigation
                $('#product-tabs').append(creativesTabHtml);

                // Create creatives tab content
                const creativesContentHtml = `
                    <div class="tab-pane fade" id="creatives-tab" role="tabpanel">
                        <div class="d-flex align-items-center mb-3">
                            <button type="button" class="btn btn-outline-secondary btn-sm me-3" id="back-to-product">
                                <i class="ri-arrow-left-line me-1"></i> Back
                            </button>
                            <div class="d-flex align-items-center">
                                <div class="avatar-sm me-3">
                                    <div class="avatar-title bg-light rounded">
                                        <img src="https://via.placeholder.com/40x40/f8f9fa/6c757d?text=Product" alt="Product" class="img-fluid">
                                    </div>
                                </div>
                                <div>
                                    <h6 class="mb-1">${productName}</h6>
                                    <small class="text-muted">ID: ${productId}</small>
                                </div>
                            </div>
                            <div class="ms-auto">
                                <span class="badge bg-success-subtle text-success me-2">Available</span>
                                <span class="text-muted">Orders (SKU)</span> <strong>8</strong>
                                <span class="text-muted ms-3">Gross revenue</span> <strong>2,267,061 VND</strong>
                            </div>
                        </div>

                        <!-- Info banner -->
                        <div class="alert alert-info d-flex align-items-center" role="alert">
                            <i class="ri-information-line me-2"></i>
                            <span>To optimize your performance, add your own videos or authorize organic posts featuring this product.</span>
                            <a href="#" class="alert-link ms-1">Learn more</a>
                            <button type="button" class="btn-close ms-auto" data-bs-dismiss="alert"></button>
                        </div>

                        <!-- Filters -->
                        <div class="row mb-3">
                            <div class="col-md-4">
                                <div class="input-group">
                                    <span class="input-group-text"><i class="ri-search-line"></i></span>
                                    <input type="text" class="form-control" placeholder="Search by video title, post ID, or TikTok account">
                                </div>
                            </div>
                            <div class="col-md-2">
                                <select class="form-select">
                                    <option>Status</option>
                                    <option>Delivering</option>
                                    <option>Learning</option>
                                    <option>In queue</option>
                                </select>
                            </div>
                            <div class="col-md-2">
                                <select class="form-select">
                                    <option>Authorization type</option>
                                    <option>TikTok Shop official account</option>
                                    <option>Affiliate mass authorization</option>
                                </select>
                            </div>
                            <div class="col-md-4 text-end">
                                <button class="btn btn-outline-secondary btn-sm me-2">
                                    <i class="ri-download-line me-1"></i>
                                </button>
                                <button class="btn btn-outline-secondary btn-sm me-2">
                                    <i class="ri-external-link-line me-1"></i>
                                </button>
                                <button class="btn btn-outline-secondary btn-sm">
                                    <i class="ri-more-2-line me-1"></i>
                                </button>
                            </div>
                        </div>

                        <!-- Status tabs -->
                        <ul class="nav nav-pills mb-3">
                            <li class="nav-item">
                                <a class="nav-link active" href="#">In queue (3)</a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" href="#">Learning (1)</a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" href="#">Delivering (18)</a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" href="#">Authorization recommended (0)</a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" href="#">Boosting (0)</a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" href="#">Boosted (0)</a>
                            </li>
                        </ul>

                        <!-- Creatives table -->
                        <div class="table-responsive">
                            <table class="table table-nowrap align-middle">
                                <thead class="table-light">
                                    <tr>
                                        <th>Creative</th>
                                        <th>TikTok account</th>
                                        <th>Authorization type</th>
                                        <th>Status</th>
                                        <th>Orders (SKU)</th>
                                        <th>Creative boost</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td>
                                            <div class="d-flex align-items-center">
                                                <div class="avatar-sm me-3">
                                                    <div class="avatar-title bg-light rounded">
                                                        <i class="ri-image-line"></i>
                                                    </div>
                                                </div>
                                                <span>Product card</span>
                                            </div>
                                        </td>
                                        <td>N/A</td>
                                        <td>N/A</td>
                                        <td><span class="badge bg-success-subtle text-success">Delivering</span></td>
                                        <td>4</td>
                                        <td>-</td>
                                    </tr>
                                    <tr>
                                        <td>
                                            <div class="d-flex align-items-center">
                                                <div class="avatar-sm me-3">
                                                    <img src="https://via.placeholder.com/40x40" class="rounded" alt="Video">
                                                </div>
                                                <div>
                                                    <div>Set xinh hot hit đã về ...</div>
                                                    <small class="text-muted">Video: *************...</small>
                                                </div>
                                            </div>
                                        </td>
                                        <td>
                                            <div class="d-flex align-items-center">
                                                <div class="avatar-xs me-2">
                                                    <img src="https://via.placeholder.com/24x24" class="rounded-circle" alt="Account">
                                                </div>
                                                <span>Maybi</span>
                                            </div>
                                        </td>
                                        <td>TikTok Shop official account</td>
                                        <td><span class="badge bg-success-subtle text-success">Delivering</span></td>
                                        <td>4</td>
                                        <td>
                                            <button class="btn btn-outline-primary btn-sm me-1">Set up</button>
                                            <button class="btn btn-outline-secondary btn-sm">View data</button>
                                        </td>
                                    </tr>
                                    <!-- More creative rows... -->
                                </tbody>
                            </table>
                        </div>
                    </div>
                `;

                // Add content to tab content container
                $('.tab-content').append(creativesContentHtml);
            }

            // Switch to creatives tab
            $('#creatives-tab-link').tab('show');
        });

        // Handle close creatives tab
        $(document).on('click', '#close-creatives-tab', function (e) {
            e.preventDefault();
            e.stopPropagation();

            // Remove tab and content
            $('#creatives-tab').remove();
            $(this).closest('.nav-item').remove();

            // Switch back to product tab
            $('a[href="#product-tab"]').tab('show');
        });

        // Handle back to product button
        $(document).on('click', '#back-to-product', function () {
            $('a[href="#product-tab"]').tab('show');
        });
    });
</script>
}