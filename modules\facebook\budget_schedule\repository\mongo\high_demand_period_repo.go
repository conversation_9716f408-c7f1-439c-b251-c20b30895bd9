package mongo

import (
	"context"
	"fmt"
	"godsp/modules/facebook/budget_schedule/entity"
	"godsp/modules/facebook/common/fberrs"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
)

type highDemandPeriodRepo struct {
	DB         *mongo.Database
	Collection *mongo.Collection
}

func NewHighDemandPeriodRepo(DB *mongo.Database) *highDemandPeriodRepo {
	return &highDemandPeriodRepo{
		DB:         DB,
		Collection: DB.Collection(entity.HighDemandPeriodEntity{}.CollectionName()),
	}
}

// upser high demand period
func (r *highDemandPeriodRepo) UpsertHighDemandPeriodRepo(ctx context.Context, filter bson.M, update bson.M) error {
	// jsonData, _ := json.Marshal(update)
	// fmt.Printf("\n -----------  Budget Update ----------- %+v \n", update)

	// var prettyDocs []bson.M
	// for _, doc := range update {
	// 	bsonDoc, err := bson.Marshal(doc)
	// 	if err != nil {
	// 		panic(err)
	// 	}

	// 	var prettyDoc bson.M
	// 	err = bson.Unmarshal(bsonDoc, &prettyDoc)
	// 	if err != nil {
	// 		panic(err)
	// 	}

	// 	prettyDocs = append(prettyDocs, prettyDoc)
	// }

	// prettyJSON, err := json.MarshalIndent(prettyDocs, "", "  ")
	// if err != nil {
	// 	panic(err)
	// }

	// fmt.Println("\n \n \n xxxx-------helo------>Debug pipeline filter: %s \n \n \n ", string(prettyJSON))

	opts := options.Update().SetUpsert(true)
	_, err := r.Collection.UpdateOne(ctx, filter, update, opts)
	if err != nil {
		return err
	}

	return nil
}

func (r *highDemandPeriodRepo) FindHighDemandPeriodRepo(ctx context.Context, filter interface{}, opts ...*options.FindOptions) (*[]entity.HighDemandPeriodEntity, error) {
	var highDemand []entity.HighDemandPeriodEntity
	cursor, err := r.Collection.Find(ctx, filter, opts...)
	if err == mongo.ErrNoDocuments {
		fmt.Println("Không tìm thấy tài liệu")
		return nil, nil
	} else if err != nil {
		return nil, err
	}

	defer cursor.Close(ctx)

	if cursor == nil {
		return nil, fberrs.ErrNilCursorValue
	}
	err = cursor.All(ctx, &highDemand)
	if err != nil {
		return nil, err
	}

	return &highDemand, nil
}

func (r *highDemandPeriodRepo) FindOneHighDemandPeriodRepo(ctx context.Context, filter interface{}, opts ...*options.FindOptions) (*entity.HighDemandPeriodEntity, error) {
	var highDemand entity.HighDemandPeriodEntity
	cursor := r.Collection.FindOne(ctx, filter)

	if cursor.Err() == mongo.ErrNoDocuments {
		fmt.Println("Không tìm thấy tài liệu")
		return nil, nil
	} else if cursor.Err() != nil {
		return nil, cursor.Err()
	}

	if err := cursor.Decode(&highDemand); err != nil {
		// fmt.Printf("\n ----------- Repo err ----------- %+v \n", err)
		return nil, err
	}

	return &highDemand, nil
}

func (r *highDemandPeriodRepo) DeleteHighDemandPeriodRepo(ctx context.Context, filter interface{}, opts ...*options.DeleteOptions) error {
	// fmt.Print("DeleteHighDemandPeriodRepo ----------> %+v \n", filter)
	if _, err := r.Collection.DeleteMany(ctx, filter, opts...); err != nil {
		return err
	}
	return nil
}
func (r *highDemandPeriodRepo) DeleteByListIdHighDemandPeriodRepo(ctx context.Context, filter interface{}, opts ...*options.DeleteOptions) error {
	results, err := r.Collection.DeleteMany(ctx, filter, opts...)
	fmt.Printf("result delete %v\n", results)
	if err != nil {
		return err
	}
	return nil
}
