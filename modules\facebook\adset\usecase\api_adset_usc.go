package usecase

import (
	"context"
	"errors"
	"fmt"
	"godsp/conf"
	adserverReportE "godsp/modules/facebook/adserver_report/entity"
	"godsp/modules/facebook/adset/common/errs"
	"godsp/modules/facebook/adset/common/pipelines"
	"godsp/modules/facebook/adset/entity"
	"godsp/modules/facebook/adset/mapping"
	"godsp/modules/facebook/adset/transport/requests"
	"godsp/modules/facebook/adset/transport/responses"
	v20 "godsp/pkg/fb-marketing/marketing/v20"
	"godsp/pkg/gos/utils"
	"godsp/pkg/sctx/core"
	"time"

	"github.com/dev-networldasia/dspgos/sctx"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo/options"
)

type ApiAdsetRepo interface {
	InsertAdsetRepo(ctx context.Context, adset *entity.AdsetEntity) error
	UpsertAdsetRepo(ctx context.Context, filter bson.M, update bson.M) error
	UpdateAdsetRepo(ctx context.Context, filter interface{}, adset *entity.AdsetEntity, opts ...*options.UpdateOptions) error
	FindOneAdsetRepo(ctx context.Context, filter interface{}, opts ...*options.FindOneOptions) (*entity.AdsetEntity, error)
	FindAdsetRepo(ctx context.Context, filter interface{}, opts ...*options.FindOptions) (*[]entity.AdsetEntity, error)
	CountAdsetRepo(ctx context.Context, filter interface{}) (int64, error)
	AggregateAdsetRepo(ctx context.Context, pipeline []bson.M, opts ...*options.AggregateOptions) (*entity.AdsetDetailViewEntity, error)
	UpdateAdsetNameStatusRepo(ctx context.Context, filter interface{}, data bson.M) error
	DeleteAdsetRepo(ctx context.Context, filter interface{}) error
	FindListTableAggregateRepo(ctx context.Context, pipeline []bson.M, opts ...*options.AggregateOptions) (*[]entity.AdsetEntity, error)
}

type ApiFBReportDetailRepo interface {
	FindFBReportDetailRepo(ctx context.Context, filter bson.M, opts ...*options.FindOptions) (*[]adserverReportE.FBReportDetailEntity, error)
	GetMetricsColumnForAdsetsFBReportDetailRepo(ctx context.Context, filter bson.M) (*[]adserverReportE.FBReportDetailEntity, error)
}

type apiAdsetUsc struct {
	fbService          *v20.Service
	repo               ApiAdsetRepo
	userRepo           ApiUserRepo
	fbReportDetailRepo ApiFBReportDetailRepo
	logger             sctx.Logger
}

func NewApiAdsetUsc(fbService *v20.Service, repo ApiAdsetRepo, fbReportDetailRepo ApiFBReportDetailRepo, userRepo ApiUserRepo, logger sctx.Logger) *apiAdsetUsc {
	return &apiAdsetUsc{
		fbService:          fbService,
		repo:               repo,
		userRepo:           userRepo,
		fbReportDetailRepo: fbReportDetailRepo,
		logger:             logger,
	}
}

/**
 * Get Role By UserId
 * @return string
 */
func (usc *apiAdsetUsc) GetRoleByUserId(ctx context.Context, userId primitive.ObjectID) (*string, error) {
	role, err := usc.userRepo.GetRoleOfUser(ctx, userId)
	if err != nil {
		return nil, err
	}
	return &role.RoleName, nil
}

/**
 * Reload Ad set Facebook V20
 * - Nếu ko có tạo mới
 * - Nếu có rồi thì update
 *
 * 1 - lay danh sach adset tu api fb
 */
func (usc *apiAdsetUsc) ReloadAdsetUsc(ctx context.Context, adAccountId string, userId primitive.ObjectID) []string {
	var errStr []string
	field := v20.AdsetFields
	adsets, err := usc.fbService.Adsets.List(adAccountId, field).Do(ctx)

	// jsonData, _ := json.Marshal(adsets)
	// fmt.Printf("\n -----------  adsets ----------- %+v \n", string(jsonData))

	if err != nil || adsets == nil {
		usc.logger.Error(err)
		errStr = append(errStr, err.Error())
		return errStr
	}

	for _, adset := range adsets {
		if err := usc.processCreateUpdateAds(ctx, &adset, userId); err != nil {
			usc.logger.Error(err)
			errStr = append(errStr, err.Error())
		}
	}

	return errStr
}

/**
 * Reload with Campaign id
 *
 */
func (usc *apiAdsetUsc) ReloadWithCampaignIdAdsetUsc(ctx context.Context, campaign_id string, userId primitive.ObjectID) []string {
	var errStr []string
	adsets, err := usc.fbService.Adsets.ListOfCampaign(campaign_id, v20.AdsetFields).Do(ctx)

	if err != nil || adsets == nil {
		usc.logger.Error(err)
		errStr = append(errStr, err.Error())
		return errStr
	}

	for _, adset := range adsets {
		// jsonData, _ := json.Marshal(adset)
		// fmt.Printf("\n -----------  adsets ----------- %+v \n", string(jsonData))
		if err := usc.processCreateUpdateAds(ctx, &adset, userId); err != nil {
			usc.logger.Error(err)
			errStr = append(errStr, err.Error())
		}
	}

	return errStr
}

/***
 * reload adset id
 */
func (usc *apiAdsetUsc) ReloadAdsetDetailUsc(ctx context.Context, adsetId string, userId primitive.ObjectID) error {
	adsetFB, err := usc.fbService.Adsets.Get(ctx, adsetId)
	if err != nil {
		usc.logger.Error(err)
		return err
	}

	if err := usc.processCreateUpdateAds(ctx, adsetFB, userId); err != nil {
		usc.logger.Error(err)
		return nil
	}

	return nil
}

// process create update reload
func (usc *apiAdsetUsc) processCreateUpdateAds(ctx context.Context, adset *v20.Adset, userId primitive.ObjectID) error {
	filter := bson.M{"adset_id": adset.ID, "account_id": adset.AccountID, "campaign_id": adset.CampaignID}
	adsetUpSert := mapping.MapperAdsetFBToUpsertAdset(adset, userId)

	err := usc.repo.UpsertAdsetRepo(ctx, filter, adsetUpSert)
	if err != nil {
		return err
	}
	return nil
}

func (usc *apiAdsetUsc) DeleteAdsetUsc(ctx context.Context, payload *requests.DeleteAdsetReq) ([]string, error) {

	ids := payload.AdsetIDs
	var errs []string
	var removedIds []string

	fmt.Printf("Deleting adset %v \n", ids)
	filter := bson.M{
		"adset_id": bson.M{"$in": removedIds},
	}

	roleName, err := usc.GetRoleByUserId(ctx, payload.UserId)
	if err != nil {
		return nil, core.ErrForbidden
	}
	if *roleName != "ADMIN" {
		filter["list_user_ids"] = payload.UserId
	}

	for _, id := range ids {
		if err := usc.fbService.Adsets.Delete(ctx, id); err != nil {
			errs = append(errs, err.Error())
		} else {
			fmt.Printf("Deleted adset %v \n", id)
			removedIds = append(removedIds, id)
			usc.ReloadAdsetDetailUsc(ctx, id, payload.UserId)
		}
		removedIds = append(removedIds, id)

	}
	// usc.repo.DeleteAdsetRepo(ctx, bson.M{"adset_id": bson.M{"$in": removedIds}})
	usc.repo.DeleteAdsetRepo(ctx, filter)
	return removedIds, nil
}

/**
 * Create adset api
 */
func (usc *apiAdsetUsc) CreateAdsetUsc(ctx context.Context, payload *requests.CreateAdsetReq) (*entity.AdsetEntity, error) {
	// user, err := utils.GetInfoUserBasic(ctx)
	// if err != nil {
	// 	return nil, err
	// }

	adsetEntity, adsetFB := mapping.MapperCreateAdsetReqToAdset(payload, &payload.ClientID)
	// jsonData1, _ := json.Marshal(*payload)
	// fmt.Printf("\n -----------  adsetEntity USC ----------- %+v \n", string(jsonData1))
	// jsonData, _ := json.Marshal(adsetFB)
	// fmt.Printf("\n -----------  adset FB Request USC ----------- %+v \n", string(jsonData))
	adsetID, updateTime, err := usc.fbService.Adsets.Create(ctx, *adsetFB)
	if err != nil {
		usc.logger.Error(err)
		return nil, err
	}

	adsetEntity.IDAdset = adsetID
	adsetEntity.UpdatedTime = time.Time(updateTime)
	if err := usc.repo.InsertAdsetRepo(ctx, adsetEntity); err != nil {
		usc.logger.Error(err)
		return nil, err
	}

	go func() {
		if err := usc.ReloadAdsetDetailUsc(ctx, adsetID, payload.UserId); err != nil {
			usc.logger.Error(err)
		}
	}()

	return adsetEntity, nil
}

/***
 * Api update ad set usc
 */
func (usc *apiAdsetUsc) UpdateAdsetUsc(ctx context.Context, payload *requests.UpdateAdsetReq) error {
	adsetEntity, err := usc.GetAdsetUsc(ctx, payload.AdsetID, payload.UserId)
	if err != nil {
		usc.logger.Error(err)
		return err
	}

	adsetFB := mapping.MapperEntityToAdsetFB(adsetEntity)
	mapping.MapperUpdateAdsetReqToAdset(payload, adsetFB)

	// Check Flexspec Detail Targeting
	if len(adsetFB.Targeting.FlexibleSpec) > 0 {
		flexibleSpec, _ := mapping.MapperFlexibaleSpecTargetingToCheckValid(adsetFB.Targeting.FlexibleSpec)
		interestResults, err := usc.CheckValidateFlexibleSpecDetailTargeting(ctx, adsetFB.AccountID, flexibleSpec)
		if err != nil {
			return err
		}

		var flexibleSpecError string
		for _, inter := range interestResults {
			if !inter.Valid {
				if flexibleSpecError != "" {
					flexibleSpecError = flexibleSpecError + ", " + inter.Name
				}
			}
		}
		if flexibleSpecError != "" {
			return errors.New(flexibleSpecError)
		}
	}

	// jsonData, _ := json.Marshal(*adsetFB)
	// fmt.Printf("\n -----------  adsetFB ----------- %+v \n", string(jsonData))

	_, err = usc.fbService.Adsets.Update(ctx, *adsetFB)
	if err != nil {
		return err
	}

	go func() {
		if err := usc.ReloadAdsetDetailUsc(ctx, payload.AdsetID, payload.UserId); err != nil {
			usc.logger.Error(err)
		}
	}()

	return nil
}

/**
 * Show adset api
 */
func (usc *apiAdsetUsc) DetailAdsetUsc(ctx context.Context, adsetId string, userId primitive.ObjectID) (*responses.AdsetDetailView, error) {
	user, err := utils.GetInfoUserBasic(ctx)
	if err != nil && user.RoleName != conf.SysConf.RoleAdmin {
		return nil, err
	}

	// Lấy pipeline truy vấn dữ liệu
	pipeline := pipelines.GetDetailAds(adsetId, user)

	// Thực hiện aggregate để lấy adsetEntity
	adsetEntity, err := usc.repo.AggregateAdsetRepo(ctx, pipeline)
	if err != nil {
		if !errors.Is(err, core.ErrNotFound) {
			return nil, err
		}
		return nil, nil
	}

	// Kiểm tra nếu không có dữ liệu
	if adsetEntity == nil {
		return nil, nil
	}

	// Debug dữ liệu trả về từ aggregate
	// jsonData, _ := json.Marshal(adsetEntity)
	// fmt.Printf("\n ----------- jsonData adsetEntity ----------- %+v \n", string(jsonData))

	// Xử lý FlexibleSpec nếu có
	var adsetReview *responses.AdsetDetailView
	if len(adsetEntity.Targeting.FlexibleSpec) > 0 {
		flexibleSpecCheck, flexibleSpecRoot := mapping.MapperFlexibaleSpecTargetingToCheckValid(adsetEntity.Targeting.FlexibleSpec)

		results, err := usc.CheckValidateFlexibleSpecDetailTargeting(ctx, adsetEntity.AccountID, flexibleSpecCheck)
		if err != nil {
			return nil, err
		}

		flexibleSpecs := mapping.MapperFlexibaleSpecTargetingToView(results, flexibleSpecRoot)
		adsetReview = mapping.MapperEntityToAdsetDetailsView(adsetEntity, flexibleSpecs)
	} else {
		// jsonData, _ = json.Marshal(adsetEntity)
		// fmt.Printf("\n ----------- adsetRexxxxxxxview ----------- %+v \n", string(jsonData))
		adsetReview = mapping.MapperEntityToAdsetDetailsView(adsetEntity, nil)
		// jsonData, _ = json.Marshal(adsetReview)
		// fmt.Printf("\n ----------- jsonData adset View ----------- %+v \n", string(jsonData))
	}
	return adsetReview, nil
}

/**
 * Show adset api
 */
func (usc *apiAdsetUsc) GetAdsetUsc(ctx context.Context, adsetId string, userId primitive.ObjectID) (*entity.AdsetEntity, error) {
	user, err := utils.GetInfoUserBasic(ctx)
	if err != nil && user.RoleName != conf.SysConf.RoleAdmin {
		return nil, err
	}

	filter := utils.GetFilterFBAds(ctx, user)
	filter["adset_id"] = adsetId

	adsetEntity, err := usc.repo.FindOneAdsetRepo(ctx, filter)
	if err != nil && !errors.Is(err, core.ErrNotFound) {
		return nil, err
	}

	mapping.MapperEntityToAdsetDetails(adsetEntity)
	return adsetEntity, nil

}

/**
 * Handle payload list adset datable
 */
func getFilterListTableAdset(ctx context.Context, payload *requests.ListAdsetReq) (bson.M, error) {

	user, _ := utils.GetInfoUserBasic(ctx)

	filter := utils.GetFilterFBAds(ctx, user)

	if len(payload.CampaignIds) > 0 {
		filter["campaign_id"] = bson.M{"$in": payload.CampaignIds}
	}

	if payload.SearchValue != nil && *payload.SearchValue != "" {
		filter["name"] = bson.M{"$regex": *payload.SearchValue, "$options": "i"}
	}

	// if payload.StartTime != nil && payload.EndTime != nil {
	// 	filter["updated_time"] = bson.M{"$gte": *payload.StartTime, "$lte": *payload.EndTime}
	// }

	if user.RoleName == conf.SysConf.RoleAdmin {
		filter["account_id"] = payload.AccountID
	}

	// filter["account_id"] = payload.AccountID

	// if payload.AccountID == "" {
	// 	delete(filter, "account_id")
	// }

	if payload.ClientIDStr != "" {
		filter["client_id"] = payload.ClientID
	}

	// jsonData, _ := json.MarshalIndent(filter, "", "  ")
	// fmt.Printf("filter ------->: %v\n", string(jsonData))

	return filter, nil
}

func (usc *apiAdsetUsc) getMetricAdsetReport(ctx context.Context, payload *requests.ListAdsetReq, adsetIds []string) (*[]adserverReportE.FBReportDetailEntity, error) {
	filter := bson.M{"adset_id": bson.M{"$in": adsetIds}}

	if payload.StartTime != nil && payload.EndTime != nil {
		startTimeStr := (*payload.StartTime).Format("2006-01-02")
		endTimeStr := (*payload.EndTime).Format("2006-01-02")
		filter["date"] = bson.M{"$gte": startTimeStr, "$lte": endTimeStr}
	} else {
		yesterday := time.Now().AddDate(0, 0, -1)
		endOfYesterdayStr := yesterday.Format("2006-01-02")
		startOfYesterdayStr := yesterday.AddDate(0, 0, -30).Format("2006-01-02")
		filter["date"] = bson.M{"$gte": startOfYesterdayStr, "$lte": endOfYesterdayStr}
	}

	return usc.fbReportDetailRepo.GetMetricsColumnForAdsetsFBReportDetailRepo(ctx, filter)

}

/**
 * Api list adset
 */
func (usc *apiAdsetUsc) ListDatatableAdsetUsc(ctx context.Context, payload *requests.ListAdsetReq) (*responses.ListAdsetsResp, error) {
	// user, err := utils.GetInfoUserBasic(ctx)
	// if err != nil {
	// 	return nil, err
	// }
	// user, _ := utils.GetInfoUserBasic(ctx)
	// filter := utils.GetFilterFBAds(ctx, user)

	filter, err := getFilterListTableAdset(ctx, payload)
	if err != nil {
		return nil, err
	}

	// if payload == nil {
	// 	return nil, errs.ErrAdsetDataTableEmpty
	// }

	// limit := payload.Length
	// skip := payload.Start

	// Create filter (optional, add search or other conditions here)

	pipeline := pipelines.PipelineListTableAdset(payload, filter)
	adsets, err := usc.repo.FindListTableAggregateRepo(ctx, pipeline)

	// jsonData, _ := json.MarshalIndent(pipeline, "", "  ")
	// fmt.Printf(" PipelineListTableAdset ------->: %v\n", string(jsonData))

	if err != nil {
		return nil, err
	}

	var adsetIds []string
	for _, adset := range *adsets {
		adsetIds = append(adsetIds, adset.IDAdset)
	}

	fbReportDetails, err := usc.getMetricAdsetReport(ctx, payload, adsetIds)

	if err != nil {
		return nil, err
	}

	total, err := usc.repo.CountAdsetRepo(ctx, bson.M{})
	if err != nil {
		return nil, err
	}

	if payload.SearchValue != nil && *payload.SearchValue != "" {
		filter["name"] = bson.M{"$regex": *payload.SearchValue, "$options": "i"}
	}

	if len(payload.CampaignIds) > 0 {
		filter["campaign_id"] = bson.M{"$in": payload.CampaignIds}
	}

	if payload.StartTime != nil && payload.EndTime != nil {
		filter["updated_at"] = bson.M{"$gte": *payload.StartTime, "$lte": *payload.EndTime}
	}
	totalFiltered := total
	if len(filter) != 0 {
		totalFiltered, err = usc.repo.CountAdsetRepo(ctx, filter)
		if err != nil {
			return nil, err
		}

		total = totalFiltered
	}

	data := mapping.MapperAdsetsToDatatable(adsets, fbReportDetails)

	return &responses.ListAdsetsResp{
		Draw:            payload.Draw,
		Data:            data,
		RecordsTotal:    total,
		RecordsFiltered: totalFiltered,
	}, nil
}

// func (usc *apiAdsetUsc) ListDatatableAdsetUsc(ctx context.Context, payload *requests.ListAdsetReq) (*responses.ListAdsetsResp, error) {

// 	if payload == nil {
// 		return nil, errs.ErrAdsetDataTableEmpty
// 	}

// 	limit := payload.Length
// 	skip := payload.Start

// 	// Create filter (optional, add search or other conditions here)

// 	filter := bson.M{
// 		"status": bson.M{
// 			"$in": []string{fbenums.FB_STATUS_ACTIVE, fbenums.FB_STATUS_PAUSED},
// 		},
// 	}
// 	roleName, err := usc.GetRoleByUserId(ctx, payload.UserId)
// 	if err != nil {
// 		return nil, core.ErrForbidden
// 	}

// 	if *roleName != "ADMIN" {
// 		filter["list_user_ids"] = payload.UserId
// 	}

// 	if payload.SearchValue != nil && *payload.SearchValue != "" {
// 		filter["name"] = bson.M{"$regex": *payload.SearchValue, "$options": "i"}
// 	}

// 	if len(payload.CampaignIds) > 0 {
// 		filter["campaign_id"] = bson.M{"$in": payload.CampaignIds}
// 	}

// 	if payload.StartTime != nil && payload.EndTime != nil {
// 		filter["updated_at"] = bson.M{"$gte": *payload.StartTime, "$lte": *payload.EndTime}
// 	}

// 	allowDiskUse := fbenums.ALLOW_DISK_USE_TRUE
// 	opts := &options.FindOptions{
// 		AllowDiskUse: &allowDiskUse,
// 		Skip:         &skip,
// 		Limit:        &limit,
// 	}
// 	sortOrder := payload.SortOrder

// 	opts.SetSort(bson.M{payload.SortField: sortOrder})

// 	adsets, err := usc.repo.FindAdsetRepo(ctx, filter, opts)

// 	jsonData, _ := json.MarshalIndent(adsets, "", "  ")
// 	fmt.Printf("adsets ------->: %v\n", string(jsonData))

// 	if err != nil {
// 		return nil, err
// 	}

// 	var adsetIds []string
// 	for _, adset := range *adsets {
// 		adsetIds = append(adsetIds, adset.IDAdset)
// 	}

// 	fbReportDetails, err := usc.fbReportDetailRepo.GetMetricsColumnForAdsetsFBReportDetailRepo(ctx, bson.M{"adset_id": bson.M{"$in": adsetIds}})

// 	if err != nil {
// 		return nil, err
// 	}

// 	total, err := usc.repo.CountAdsetRepo(ctx, bson.M{})
// 	if err != nil {
// 		return nil, err
// 	}

// 	totalFiltered := total
// 	if len(filter) != 0 {
// 		totalFiltered, err = usc.repo.CountAdsetRepo(ctx, filter)
// 		if err != nil {
// 			return nil, err
// 		}

// 		total = totalFiltered
// 	}

// 	data := mapping.MapperAdsetsToDatatable(adsets, fbReportDetails)

// 	return &responses.ListAdsetsResp{
// 		Draw:            payload.Draw,
// 		Data:            data,
// 		RecordsTotal:    total,
// 		RecordsFiltered: totalFiltered,
// 	}, nil
// }

/**
 * Api list adsets for select/choices
 */
func (usc *apiAdsetUsc) ListSelectAdsetUsc(ctx context.Context, payload *requests.ListSelectAdsetReq) (*[]entity.AdsetEntity, error) {
	user, err := utils.GetInfoUserBasic(ctx)
	if err != nil {
		return nil, err
	}

	filter := utils.GetFilterFBAds(ctx, user)

	if payload == nil {
		return nil, errs.ErrAdsetCampaignID
	}

	//filter
	if payload.CampaignID != "" {
		filter["campaign_id"] = payload.CampaignID
	}

	adsets, err := usc.repo.FindAdsetRepo(ctx, filter)
	if err != nil {
		return nil, err
	}

	return adsets, nil
}

/**
 * Api toggle status Adset
 */
func (usc *apiAdsetUsc) UpdateNameStatusAdsetUsc(ctx context.Context, payload *requests.UpdateNameStatusAdsetReq) error {

	user, err := utils.GetInfoUserBasic(ctx)
	if err != nil {
		return err
	}

	filter := utils.GetFilterFBAds(ctx, user)

	filter["adset_id"] = payload.AdsetId

	adsetFb, adsetUpdate := mapping.MapperUpdateNameStatusAdsetReqToAdset(payload, filter["account_id"].(string))

	err = usc.fbService.Adsets.UpdateNameStatus(ctx, payload.AdsetId, adsetFb)
	fmt.Printf("\n There is Usc Update FB err: %v \n", err)
	if err != nil {
		usc.logger.Error(err)
		return err
	}

	go func() {
		if err := usc.repo.UpdateAdsetNameStatusRepo(ctx, filter, adsetUpdate); err != nil {
			usc.logger.Error(err)
		} else {
			go func() {
				if err := usc.ReloadAdsetDetailUsc(ctx, payload.AdsetId, payload.UserId); err != nil {
					usc.logger.Error(err)
				}
			}()
		}
	}()

	return nil

}

/**
 * Check Validate Flexibale Spec Detail Targeting
 */
func (usc *apiAdsetUsc) CheckValidateFlexibleSpecDetailTargeting(ctx context.Context, act string, interests []v20.InterestCheck) ([]*v20.InterestResultCheckValidate, error) {
	// filter := bson.M{"adset_id": payload.AdsetId}
	interestResults, err := usc.fbService.Interests.InterestCheckValid(ctx, act, interests)
	if err != nil {
		usc.logger.Error(err)
		return nil, err
	}
	return interestResults, nil
}
