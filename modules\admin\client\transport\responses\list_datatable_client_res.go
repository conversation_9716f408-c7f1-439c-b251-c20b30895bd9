package responses

import "go.mongodb.org/mongo-driver/bson/primitive"

type ClientDataTable struct {
	ID      primitive.ObjectID `json:"id"`
	Name    string             `json:"name"`
	Logo    string             `json:"logo"`
	Company string             `json:"company"`
	Brand   *[]string          `json:"brand"`
	Email   string             `json:"email"`

	Domain   string `json:"domain"`
	Phone    string `json:"phone,omitempty"`
	Position string `json:"position"`
	Address  string `json:"address"`

	Status      int    `json:"status"`
	UserCreated string `json:"created_by"`
	UserUpdated string `json:"updated_by"`
	CreatedAt   string `json:"created_at"`
	UpdatedAt   string `json:"updated_at"`
}
