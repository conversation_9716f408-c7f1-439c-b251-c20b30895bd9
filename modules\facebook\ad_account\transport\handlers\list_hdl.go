package handlers

import (
	"godsp/conf"
	"godsp/pkg/gos/utils"

	"github.com/gofiber/fiber/v2"
)

func (h *adAccountHdl) ListAdAccountHandler() fiber.Handler {
	return func(c *fiber.Ctx) error {
		user, err := utils.GetInfoUserBasic(c.Context())
		if err != nil && user.RoleName != conf.SysConf.RoleAdmin {
			c.Redirect("/page-permission-denied")
		}

		return c.Render("facebook/ad_account/index", fiber.Map{})
	}
}
