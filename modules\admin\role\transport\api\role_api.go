package api

import (
	"context"
	"godsp/modules/admin/common/admconst"
	ePermission "godsp/modules/admin/permission/entity"
	eUser "godsp/modules/admin/user/entity"

	"godsp/modules/admin/role/common/errs"
	"godsp/modules/admin/role/entity"
	"godsp/modules/admin/role/transport/requests"
	"godsp/modules/admin/role/transport/response"
	"godsp/pkg/gos/goconst"
	"godsp/pkg/gos/utils"
	"godsp/pkg/sctx/core"
	"net/http"

	"github.com/dev-networldasia/dspgos/sctx"

	"github.com/gofiber/fiber/v2"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

type RoleApiUsc interface {
	CreateApiRoleUsc(ctx context.Context, payload *requests.PayloadRoleCreation) error
	ListDatatableApiRoleUsc(ctx context.Context) (*[]response.RoleDataTable, error)
	UpdateApiRoleUsc(ctx context.Context, payload *requests.PayloadRoleEdition) (*entity.RoleUpdate, error)
	FindOneApiRoleUsc(ctx context.Context, id string) (*entity.RoleEntity, error)
	FindPermissionByModuleApiRoleUsc(ctx context.Context, module string, group string) (*[]ePermission.PermissionEntity, error)
	UpdatePermissionApiRoleUsc(ctx context.Context, payload *requests.PayloadUpdatePermissionRole) error
	FindOneUserIsLoggingRoleUsc(ctx context.Context, userId primitive.ObjectID) (*eUser.UserEntity, error)

	DeletesRoleUsc(ctx context.Context, payload *requests.PayloadRoleDeletes) error
}

type roleApi struct {
	usc    RoleApiUsc
	logger sctx.Logger
}

func NewRoleApi(usc RoleApiUsc, logger sctx.Logger) *roleApi {
	return &roleApi{
		usc:    usc,
		logger: logger,
	}
}

/**
 * Create role
 */
func (a *roleApi) CreateRoleApi() fiber.Handler {
	return func(c *fiber.Ctx) error {
		var param requests.PayloadRoleCreation
		if err := c.BodyParser(&param); err != nil {
			return core.ReturnErrsForApi(c, err)
		}

		//validate
		validationErrors := param.Validate()
		if len(validationErrors) > 0 {
			return core.ReturnErrsForApi(c, validationErrors)
		}

		userId, err := utils.GetUserIdPrimitive(c.Context())
		if err != nil {
			return core.ReturnErrForApi(c, err.Error())
		}

		param.UserID = userId

		if err := a.usc.CreateApiRoleUsc(c.Context(), &param); err != nil {
			return core.ReturnErrForApi(c, err.Error())
		}

		return c.Status(http.StatusOK).JSON(core.ResponseData(map[string]interface{}{
			"msg":      "Create role successfully!",
			"redirect": "/admins/roles/list",
		}))
	}
}

/**
 * list datatable
 */

func (a *roleApi) ListRoleApi() fiber.Handler {
	return func(ctx *fiber.Ctx) error {
		data, err := a.usc.ListDatatableApiRoleUsc(ctx.Context())
		if err != nil {
			return ctx.JSON(fiber.Map{
				"data": "",
			})
		}

		return ctx.JSON(fiber.Map{
			"data": data,
		})
	}
}

/**
 * Edit role
 */

func (a *roleApi) EditRoleApi() fiber.Handler {
	return func(c *fiber.Ctx) error {
		var param requests.PayloadRoleEdition
		err := c.BodyParser(&param)
		if err != nil {
			return core.ReturnErrForApi(c, err.Error())
		}

		//validate
		validationErrors := param.Validate()
		if len(validationErrors) > 0 {
			return core.ReturnErrsForApi(c, validationErrors)
		}

		ctx := c.Context()

		userId, err := utils.GetUserIdPrimitive(ctx)
		if err != nil {
			return core.ReturnErrForApi(c, err.Error())
		}
		param.UserID = userId

		user, err := a.usc.FindOneUserIsLoggingRoleUsc(ctx, userId)
		if err != nil {
			return core.ReturnErrForApi(c, err.Error())
		}

		var roleName string
		if user.RoleID == param.ID {
			if admconst.StatusFullNameValue[param.Status] != goconst.STATUS_ACTIVE {
				return core.ReturnErrForApi(c, errs.ErrUpdateStatusRoleLogin.Error())
			}
			roleName = param.Name
		}

		role, err := a.usc.UpdateApiRoleUsc(ctx, &param)
		if err != nil {
			return core.ReturnErrForApi(c, err.Error())
		}

		//response edit - datatable
		data := response.RoleDetailUpdateDatatable(role, user.FullName)

		return c.Status(http.StatusOK).JSON(core.ResponseData(map[string]interface{}{
			"msg":      "Edit role successfully!",
			"data":     data,
			"roleName": roleName,
		}))
	}
}

/**
 * Show list permissions by module
 * Use: update permission handler
 */

func (a *roleApi) ShowPermissionRoleApi() fiber.Handler {
	return func(c *fiber.Ctx) error {
		ctx := c.Context()

		module := c.Query("module")
		group := c.Query("group")
		idRole := c.Query("id")

		detailRole, err := a.usc.FindOneApiRoleUsc(ctx, idRole)
		if err != nil {
			return core.ReturnErrForApi(c, err.Error())
		}

		permissions, err := a.usc.FindPermissionByModuleApiRoleUsc(c.Context(), module, group)
		if err != nil {
			return core.ReturnErrForApi(c, err.Error())
		}

		html := response.PermissionsMapToHTML(permissions, detailRole.Permissions)
		return c.Status(http.StatusOK).JSON(core.ResponseData(map[string]interface{}{
			"html": html,
		}))
	}
}

func (a *roleApi) UpdatePermissionRoleApi() fiber.Handler {
	return func(c *fiber.Ctx) error {
		var param requests.PayloadUpdatePermissionRole
		err := c.BodyParser(&param)
		if err != nil {
			return core.ReturnErrForApi(c, err.Error())
		}

		userId, err := utils.GetUserIdPrimitive(c.Context())
		if err != nil {
			return core.ReturnErrForApi(c, err.Error())
		}
		param.UserID = userId

		err = a.usc.UpdatePermissionApiRoleUsc(c.Context(), &param)
		if err != nil {
			return core.ReturnErrForApi(c, err.Error())
		}

		return c.Status(http.StatusOK).JSON(core.ResponseData(map[string]interface{}{
			"msg":      "Edit permission role successfully!",
			"redirect": "/admins/roles/list",
		}))
	}
}

/**
 * deletes roles
 */
func (a *roleApi) DeletesRoleApi() fiber.Handler {
	return func(c *fiber.Ctx) error {
		var param requests.PayloadRoleDeletes
		err := c.BodyParser(&param)
		if err != nil {
			return core.ReturnErrForApi(c, err.Error())
		}

		if err := param.Validate(c.Context()); err != nil {
			return core.ReturnErrForApi(c, err.Error())
		}

		if err := a.usc.DeletesRoleUsc(c.Context(), &param); err != nil {
			return core.ReturnErrForApi(c, err.Error())
		}

		return c.Status(http.StatusOK).JSON(core.ResponseData(map[string]interface{}{
			"msg": "Delete role successfully",
		}))
	}
}
