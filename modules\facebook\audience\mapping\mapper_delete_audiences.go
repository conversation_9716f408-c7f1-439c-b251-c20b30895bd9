package mapping

import (
	"godsp/modules/facebook/audience/transport/requests"
	"godsp/modules/facebook/common/fbenums"
	"time"

	"go.mongodb.org/mongo-driver/bson"
)

func MapperDeleteAudienciesReq(req *requests.DeleteCustomAudienceReq) bson.M {
	update := bson.M{
		"$set": bson.M{
			"updated_at": time.Now(),
			"updated_by": req.UserId,
			"status":     fbenums.FB_STATUS_DELETED,
		},
	}
	return update
}
