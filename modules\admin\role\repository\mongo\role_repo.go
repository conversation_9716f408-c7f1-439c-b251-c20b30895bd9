package mongo

import (
	"context"
	"errors"
	"godsp/modules/admin/role/common/errs"
	"godsp/modules/admin/role/entity"
	"godsp/modules/facebook/common/fberrs"
	"godsp/pkg/sctx/core"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
)

type roleRepo struct {
	DB         *mongo.Database
	Collection *mongo.Collection
}

func NewRoleRepo(DB *mongo.Database) *roleRepo {
	return &roleRepo{
		DB:         DB,
		Collection: DB.Collection(entity.RoleEntity{}.CollectionName()),
	}
}

/**
 * Create index
 */
func (r *roleRepo) CreateRoleIndex(ctx context.Context) error {
	cursor, err := r.Collection.Indexes().List(ctx)
	if err != nil {
		return err
	}
	defer cursor.Close(ctx)

	for cursor.Next(ctx) {
		var index bson.M
		if err := cursor.Decode(&index); err != nil {
			return err
		}
		if indexName, ok := index["name"].(string); ok && indexName == "role_name_index" {
			return nil
		}
	}

	indexModel := mongo.IndexModel{
		// role_name asc
		Keys:    bson.M{"role_name": 1},
		Options: options.Index().SetUnique(true).SetName("role_name_index"),
	}

	_, err = r.Collection.Indexes().CreateOne(ctx, indexModel)
	return err
}

/**
 * Max ording
 */
func (r *roleRepo) MaxOrderingRepo(ctx context.Context, filter interface{}) (int64, error) {
	var result struct {
		MaxOrdering int64 `bson:"maxOrdering"`
	}

	pipeline := []bson.M{}
	if filter != nil {
		pipeline = append(pipeline, bson.M{"$match": filter})
	}

	pipeline = append(pipeline, bson.M{
		"$group": bson.M{
			"_id":         nil,
			"maxOrdering": bson.M{"$max": "$ordering"},
		},
	})

	cursor, err := r.Collection.Aggregate(ctx, pipeline)
	if err != nil {
		return 0, err
	}
	defer cursor.Close(ctx)

	if cursor.Next(ctx) {
		if err := cursor.Decode(&result); err != nil {
			return 0, err
		}
		return result.MaxOrdering, nil
	}

	return 0, nil
}

/**
 * FindOne role
 */
func (r *roleRepo) FindOneRoleRepo(ctx context.Context, filter interface{}, opts ...*options.FindOneOptions) (*entity.RoleEntity, error) {
	var roleEntity entity.RoleEntity
	err := r.Collection.FindOne(ctx, filter, opts...).Decode(&roleEntity)
	if errors.Is(err, mongo.ErrNoDocuments) {
		return nil, core.ErrNotFound
	}

	return &roleEntity, nil
}

/**
 * Find role
 */
func (r *roleRepo) FindRoleRepo(ctx context.Context, filter interface{}, opts ...*options.FindOptions) (*[]entity.RoleEntity, error) {
	var roles []entity.RoleEntity

	cursor, err := r.Collection.Find(ctx, filter, opts...)
	if err != nil {
		return nil, err
	}
	defer cursor.Close(ctx)

	if cursor == nil {
		return nil, fberrs.ErrNilCursorValue
	}
	err = cursor.All(ctx, &roles)
	if err != nil {
		return nil, err
	}

	return &roles, nil
}

/***
 * insert role
 */
func (r *roleRepo) InsertRoleRepo(ctx context.Context, role *entity.RoleCreation) error {
	role.BeforeSave()
	_, err := r.Collection.InsertOne(ctx, role)
	if err != nil {
		if mongo.IsDuplicateKeyError(err) {
			return core.ErrDuplicate
		}
		return err
	}

	return nil
}

/***
 * update one filter interface{}, update interface{}, opts ...*options.UpdateOptions
 */
func (r *roleRepo) UpdateOneRoleRepo(ctx context.Context, filter interface{}, role *entity.RoleUpdate, opts ...*options.UpdateOptions) error {
	role.BeforeUpdate()

	update := bson.M{
		"$set": *role,
	}
	_, err := r.Collection.UpdateOne(ctx, filter, update)
	if err != nil {
		if mongo.IsDuplicateKeyError(err) {
			return core.ErrNotFound
		}
		return err
	}

	return nil
}

/***
 * update one permission role filter interface{}, update interface{}, opts ...*options.UpdateOptions
 */
func (r *roleRepo) UpdateOnePermissionRoleRepo(ctx context.Context, filter interface{}, role *entity.PermissionRoleUpdate, opts ...*options.UpdateOptions) error {
	role.BeforeUpdate()

	update := bson.M{
		"$set": *role,
	}
	_, err := r.Collection.UpdateOne(ctx, filter, update)
	if err != nil {
		if mongo.IsDuplicateKeyError(err) {
			return core.ErrNotFound
		}
		return err
	}

	return nil
}

/**
 * Find roles with pipeline
 */
func (r *roleRepo) FindWithPipelineRoleRepo(ctx context.Context, pipeline []bson.M, opts ...*options.AggregateOptions) (*[]entity.RoleEntity, error) {
	var roles []entity.RoleEntity

	cursor, err := r.Collection.Aggregate(ctx, pipeline, opts...)
	if err != nil {
		return nil, err
	}
	defer cursor.Close(ctx)

	if cursor == nil {
		return nil, fberrs.ErrNilCursorValue
	}

	if err = cursor.All(ctx, &roles); err != nil {
		return nil, err
	}

	return &roles, nil
}

/**
 * deletes roles
 */
func (r *roleRepo) DeletesRoleApi(ctx context.Context, filter interface{}) error {
	result, err := r.Collection.DeleteMany(ctx, filter)
	if err != nil {
		return err
	}

	if result.DeletedCount == 0 {
		return errs.ErrRoleDelete
	}

	return nil
}
