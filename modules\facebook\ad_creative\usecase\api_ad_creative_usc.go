package usecase

import (
	"context"
	"fmt"
	"godsp/conf"
	"godsp/modules/facebook/ad_creative/common/errs"
	"godsp/modules/facebook/ad_creative/entity"
	"godsp/modules/facebook/ad_creative/mapping"
	"godsp/modules/facebook/ad_creative/transport/requests"
	v20 "godsp/pkg/fb-marketing/marketing/v20"
	"godsp/pkg/gos/utils"

	"github.com/dev-networldasia/dspgos/sctx"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo/options"
)

type ApiAdCreativeRepo interface {
	UpsertAdCreativeRepo(ctx context.Context, filter bson.M, update bson.M) error
	FindOneAdCreativeRepo(ctx context.Context, filter interface{}, opts ...*options.FindOneOptions) (*entity.AdCreativeEntity, error)
	DeleteAdCreativeRepo(ctx context.Context, filter bson.M) error
}

type apiAdCreativeUsc struct {
	fbService *v20.Service
	repo      ApiAdCreativeRepo
	logger    sctx.Logger
}

func NewApiAdCreativeUsc(fbService *v20.Service, repo ApiAdCreativeRepo, logger sctx.Logger) *apiAdCreativeUsc {
	return &apiAdCreativeUsc{
		fbService: fbService,
		repo:      repo,
		logger:    logger,
	}
}

/**
 * reload get Detail creative fb
 */
func (usc *apiAdCreativeUsc) ReloadDetailAdCreativeUsc(ctx context.Context, creativeID string, userId primitive.ObjectID, clientId primitive.ObjectID) error {
	creative, err := usc.fbService.AdCreatives.Get(ctx, creativeID)
	if err != nil {
		usc.logger.Error(err)
		return errs.ErrGetCreativeDetailFB
	}

	if creative == nil {
		return errs.ErrGetCreativeDetailFBNotFound
	}

	updateData := mapping.MapperAdCreativeToUpsert(creative, userId, clientId)

	filter := bson.M{"ad_creative_id": creative.ID}
	if err := usc.repo.UpsertAdCreativeRepo(ctx, filter, updateData); err != nil {
		usc.logger.Error(err)
		return err
	}

	return nil
}

/**
 * reload lis of Account creative fb
 */
func (usc *apiAdCreativeUsc) ReloadListOfAdAccountCreativeUsc(ctx context.Context, adAccount string, userId primitive.ObjectID, clientId primitive.ObjectID) []string {
	var errStr []string
	creativesCh := make(chan v20.AdCreative)
	go func() {
		err := usc.fbService.AdCreatives.List(adAccount).ReadList(ctx, adAccount, creativesCh)
		if err != nil {
			usc.logger.Error(err)
			errStr = append(errStr, errs.ErrReloadListCreativeFB.Error())
		}
		close(creativesCh)
	}()

	for creative := range creativesCh {
		updateData := mapping.MapperAdCreativeToUpsert(&creative, userId, clientId)
		filter := bson.M{"ad_creative_id": creative.ID}
		if err := usc.repo.UpsertAdCreativeRepo(ctx, filter, updateData); err != nil {
			usc.logger.Error(err)
			errStr = append(errStr, err.Error())
		}
	}

	return errStr
}

/**
 * detail creative
 */
func (usc *apiAdCreativeUsc) DetailAdCreativeUsc(ctx context.Context, payload *requests.DetailAdCreativeReq) (*entity.AdCreativeEntity, error) {
	creative, err := usc.fbService.AdCreatives.Get(ctx, payload.CreativeID)
	if err != nil {
		usc.logger.Error(err)
		return nil, err
	}
	if creative == nil {
		return nil, errs.ErrGetCreativeDetailFBNotFound
	}

	user, err := utils.GetInfoUserBasic(ctx)
	if err != nil && user.RoleName != conf.SysConf.RoleAdmin {
		return nil, err
	}

	filter := utils.GetFilterFBAds(ctx, user)
	filter["ad_creative_id"] = payload.CreativeID

	updateData := mapping.MapperAdCreativeToUpsert(creative, *user.UserId, *user.ClientId)

	if err := usc.repo.UpsertAdCreativeRepo(ctx, filter, updateData); err != nil {
		usc.logger.Error(err)
		return nil, err
	}

	adCreativeEntity, err := usc.repo.FindOneAdCreativeRepo(ctx, filter)
	if err != nil {
		usc.logger.Error(err)
		return nil, err
	}

	return adCreativeEntity, nil
}

/**
 * Create ad creative
 *
 * @return creative_id, error
 */
func (usc *apiAdCreativeUsc) CreateAdCreativeUsc(ctx context.Context, payload *requests.CreateAdCreativeReq) (*string, error) {
	adCreative := mapping.MapperAdCreativeReqToFB(payload)
	fmt.Printf("\n --------Mapper---------> %+v\n", adCreative)
	fmt.Printf("\n --------Object-spect---------> %+v\n", adCreative.ObjectStorySpec)
	adCreativeId, _, err := usc.fbService.AdCreatives.Create(ctx, *adCreative)
	fmt.Printf("\n --------adCreativeId---------> %+v\n", adCreativeId)
	fmt.Printf("\n --------clientId---------> %+v\n", payload.ClientID)

	if err != nil {
		fmt.Printf("\n -----------------> error %v \n", err)
		usc.logger.Error(err)
		return nil, err
	}

	go func() {
		if err := usc.ReloadDetailAdCreativeUsc(ctx, adCreativeId, payload.UserID, payload.ClientID); err != nil {
			usc.logger.Error(err)
		}
	}()

	return &adCreativeId, nil
}

/**
 * Delete ad creative
 */
func (usc *apiAdCreativeUsc) DeleteAdCreativeUsc(ctx context.Context, payload *requests.DeleteAdCreativeReq) error {
	if err := usc.fbService.AdCreatives.Delete(ctx, payload.CreativeID); err != nil {
		usc.logger.Error(err)
		return err
	}

	go func() {
		filter := bson.M{"ad_creative_id": payload.CreativeID}
		if err := usc.repo.DeleteAdCreativeRepo(ctx, filter); err != nil {
			usc.logger.Error(err)
		}
	}()

	return nil
}

/**
 * generate ad creative preview usc
 *
 * @return string, error
 */
func (usc *apiAdCreativeUsc) GenerateAdCreativePreviewUsc(ctx context.Context, payload *requests.PreviewAdCreativeReq) (string, error) {
	previewBody, err := usc.fbService.AdCreatives.GenerateAdPreview(ctx, payload.Creative, payload.AdFormat)
	if err != nil {
		usc.logger.Error(err)
		return "", errs.ErrApiPreviewFB
	}

	if previewBody == "" {
		return "", errs.ErrPreviewBodyFB
	}

	return previewBody, nil
}

/**
 * generate ad creative preview usc
 *
 * @return string, error
 */
func (usc *apiAdCreativeUsc) GenerateInstagramAdCreativePreviewUsc(ctx context.Context, payload *requests.PreviewAdCreativeReq) (string, error) {
	previewBody, err := usc.fbService.AdCreatives.GenerateInstagramAdPreview(ctx, payload.Creative, payload.AdFormat, payload.AccountID)
	if err != nil {
		usc.logger.Error(err)
		return "", errs.ErrApiPreviewFB
	}

	if previewBody == "" {
		return "", errs.ErrPreviewBodyFB
	}

	return previewBody, nil
}
