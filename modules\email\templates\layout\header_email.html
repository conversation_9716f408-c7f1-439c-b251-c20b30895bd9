{{define "header_email"}}
<!DOCTYPE html>
    <html lang="en">

    <head>
        <meta charset="UTF-8">
        <title>Topup Success</title>
        <style>
            body {
                background-color: #f4f4f7;
                font-family: 'Helvetica Neue', Helvetica, Arial, sans-serif;
                margin: 0;
                padding: 0;
                color: #333;
            }

            .container {
                max-width: 600px;
                margin: 40px auto;
                background-color: #fff;
                border-radius: 8px;
                padding: 30px;
                box-shadow: 0 0 10px rgba(0, 0, 0, 0.05);
            }

            table {
                width: 100%;
                border-collapse: collapse;
                margin-top: 15px;
            }

            th,
            td {
                padding: 12px 15px;
                border-bottom: 1px solid #ddd;
                text-align: left;
            }

            th {
                background-color: #f0f4f8;
            }

            .success-color {
                color: #07a352;
            }

            .danger-color {
                color: #cc0303;
            }

            .footer {
                margin-top: 25px;
                font-size: 12px;
                color: #999;
                text-align: center;
            }

            .highlight {
                color: #cc0303;
                font-weight: bold;
            }

            .danger-highlight {
                color: #cc0303;
                font-weight: bold;
            }

            .success-highlight {
                color: #07a352;
                font-weight: bold;
            }

            h1 {
                font-size: 22px;
                color: #2c3e50;
                margin: auto;
                text-align: center;

            }

            p {
                font-size: 16px;
                line-height: 1.6;
            }

            .password-box {
                background-color: #f1f3f5;
                border-left: 4px solid #007bff;
                padding: 15px;
                font-size: 18px;
                font-weight: bold;
                margin: 20px 0;
                word-break: break-all;
                margin: auto;

            }

            .btn {
                display: inline-block;
                padding: 12px 24px;
                background-color: #007bff;
                color: #fff;
                text-decoration: none;
                border-radius: 5px;
                font-size: 16px;
                margin-top: 20px;
                margin: auto;
            }

            .btn:hover {
                background-color: #0154ac;
            }

            .footer {
                text-align: center;
                font-size: 13px;
                color: #888;
                margin-top: 40px;
                border-top: 1px solid #e0e0e0;
                padding-top: 20px;
            }

            .footer .contact-info {
                margin-top: 10px;
                line-height: 1.6;
            }

            .margin-top {
                margin-top: 16px;
            }
        </style>
    </head>

    <body>
        <div class="container" style="border: 1px solid #3333331f;">
            <div style="text-align: center; margin-bottom: 25px; width: 100%;">
                <img style="max-width: 250px;" src="https://networldasia.com/assets/images/logo.png" alt="Logo" class="logo" />
            </div>
{{end}}