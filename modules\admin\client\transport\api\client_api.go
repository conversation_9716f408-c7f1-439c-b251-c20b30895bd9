package api

import (
	"context"
	"errors"
	"fmt"
	"godsp/modules/admin/client/common/errs"
	"godsp/modules/admin/client/entity"
	"godsp/modules/admin/client/transport/requests"
	"godsp/modules/admin/client/transport/responses"
	"godsp/pkg/gos/goconst"
	"godsp/pkg/gos/utils"
	"godsp/pkg/sctx/core"
	"net/http"
	"time"

	"github.com/gofiber/fiber/v2"
	"github.com/valyala/fasthttp"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

type ClientApiUsc interface {
	CreateApiClientUsc(ctx context.Context, payload *requests.PayloadClientCreation) error
	ListDatatablClienteApiUsc(ctx context.Context) (*[]responses.ClientDataTable, error)
	UpdateApiClientUsc(ctx context.Context, payload *requests.PayloadClientUpdating) (*entity.ClientUpdateEntity, error)
	DeletelClienteApiUsc(ctx context.Context, payload *requests.PayloadClientDelete) error
	UpsertUserToMultipleCollectionsWithTransactionApiUsc(ctx context.Context, payload requests.UpdateUserIdClientIdFacebookResource) error
	UpsertUserToMultipleCollectionsWithTransactionTikTokApiUsc(ctx context.Context, payload requests.UpdateUserIdClientIdTiktokResource) error
	// ListDatatableApiUserUsc(ctx context.Context) (*[]responses.UserDataTable, error)
	// UpdateUserUsc(ctx context.Context, data *requests.PayloadUserUpdate) (*entity.UserUpdate, error)
	// GetListUserUsc(ctx context.Context) (*[]responses.ListUser, error)
	// GetUserByIDUsc(ctx context.Context, userId primitive.ObjectID) (*responses.DetailsUser, error)
}

type clientApi struct {
	usc ClientApiUsc
}

func NewClientApi(usc ClientApiUsc) *clientApi {
	return &clientApi{
		usc: usc,
	}
}

func (a *clientApi) CreateClientApi() fiber.Handler {
	return func(c *fiber.Ctx) error {
		var param requests.PayloadClientCreation
		if err := c.BodyParser(&param); err != nil {
			return core.ReturnErrForApi(c, err.Error())
		}
		form, _ := c.MultipartForm()
		if values, ok := form.Value["brand[]"]; ok {
			param.Brand = &values
		}

		fmt.Printf("\n  Data Payload2 Request %+v : \n", param)

		//file
		file, err := c.FormFile("logo")

		if err != nil && !errors.Is(err, fasthttp.ErrMissingFile) {
			return core.ReturnErrForApi(c, err.Error())
		} else {
			param.FileImg = file
		}

		//validate
		validationErrors := param.Validate()
		if len(validationErrors) > 0 {
			return core.ReturnErrsForApi(c, validationErrors)
		}
		userId, err := utils.GetUserIdPrimitive(c.Context())
		if err != nil {
			return core.ReturnErrForApi(c, err.Error())
		}

		param.UserID = userId

		if err := a.usc.CreateApiClientUsc(c.Context(), &param); err != nil {
			return core.ReturnErrForApi(c, err.Error())
		}

		return c.Status(http.StatusOK).JSON(core.ResponseData(map[string]interface{}{
			"message":  "Create client successfully!",
			"redirect": "/admins/clients/list",
		}))
	}
}

/**
 * list datatable
 */

func (a *clientApi) ListClientsApi() fiber.Handler {
	return func(c *fiber.Ctx) error {
		data, err := a.usc.ListDatatablClienteApiUsc(c.Context())
		if err != nil {
			return c.JSON(fiber.Map{
				"msg":  "get list clients fail",
				"data": "",
			})
		}

		return c.JSON(fiber.Map{
			"msg":  "get list clients successfully",
			"data": data,
		})
	}
}

/**
 * Func update client
 * Neu roleID = inactive hoac role UNKNOWN =2> khong cho cap nhat status -> active
 */
func (a *clientApi) UpdateUserApi() fiber.Handler {
	return func(c *fiber.Ctx) error {
		var param requests.PayloadClientUpdating
		ctx := c.Context()
		if err := c.BodyParser(&param); err != nil {

			return core.ReturnErrForApi(c, err.Error())
		}

		form, _ := c.MultipartForm()
		if values, ok := form.Value["brand[]"]; ok {
			param.Brand = &values
		}

		file, err := c.FormFile("image")
		if err != nil {
			if !errors.Is(err, fasthttp.ErrMissingFile) {
				return core.ReturnErrForApi(c, err.Error())
			}
		} else {
			param.FileImg = file
		}

		//validate
		validationErrors := param.Validate()
		if len(validationErrors) > 0 {
			return core.ReturnErrsForApi(c, validationErrors)
		}

		userId, err := utils.GetUserIdPrimitive(c.Context())
		if err != nil {
			return core.ReturnErrForApi(c, err.Error())
		}

		param.UserID = userId

		dataClient, err := a.usc.UpdateApiClientUsc(ctx, &param)
		if err != nil {
			return core.ReturnErrForApi(c, err.Error())
		}

		return c.Status(http.StatusOK).JSON(core.ResponseData(map[string]interface{}{
			"msg":      "Updated client successfully!",
			"redirect": "/admins/clients/list",
			"data":     dataClient,
		}))
	}
}

/**
 * Delete One Client
 */
func (a *clientApi) DeleteOneClientApi() fiber.Handler {
	return func(c *fiber.Ctx) error {

		id := c.Params("id")

		if id == "" {
			return core.ReturnErrForApi(c, errs.ErrIdUserEmpty.Error())
		}

		clientID, err := primitive.ObjectIDFromHex(id)

		if err != nil {
			return core.ReturnErrForApi(c, errs.ErrIdUserInvalidate.Error())
		}

		userId, err := utils.GetUserIdPrimitive(c.Context())
		if err != nil {
			return core.ReturnErrForApi(c, err.Error())
		}

		var payload requests.PayloadClientDelete
		payload.ID = clientID
		payload.UpdatedBy = userId
		payload.UpdatedAt = time.Now()
		payload.Status = goconst.STATUS_DELETE

		err = a.usc.DeletelClienteApiUsc(c.Context(), &payload)

		if err != nil {
			return core.ReturnErrForApi(c, err.Error())
		}

		return c.Status(http.StatusOK).JSON(core.ResponseData(map[string]interface{}{
			"msg": "Delete user successfully!",
		}))
	}
}

/**
 * Update User Id and Client Id for AdAccount, Pixel, Page, Catalogue
 */
func (a *clientApi) UpdateUserIdClientIdFacebookResourceApi() fiber.Handler {
	return func(c *fiber.Ctx) error {
		var payload requests.UpdateUserIdClientIdFacebookResource

		ctx := c.Context()
		if err := c.BodyParser(&payload); err != nil {
			return core.ReturnErrForApi(c, err.Error())
		}

		//validate
		validationErrors := payload.Validate()
		fmt.Printf("\n Validation Errors %v \n", validationErrors)
		if len(validationErrors) > 0 {
			return core.ReturnErrsForApi(c, validationErrors)
		}

		userId, err := utils.GetUserIdPrimitive(c.Context())
		if err != nil {
			return core.ReturnErrForApi(c, err.Error())
		}

		payload.UpdatedBy = userId
		payload.UpdatedAt = time.Now()

		err = a.usc.UpsertUserToMultipleCollectionsWithTransactionApiUsc(ctx, payload)

		if err != nil {
			fmt.Printf("Error at Usc: %v ", err)
			return c.Status(http.StatusBadRequest).JSON(core.ResponseData(map[string]interface{}{
				"msg": "Update client fail!",
			}))
		}

		return c.Status(http.StatusOK).JSON(core.ResponseData(map[string]interface{}{
			"msg": "Update client successfully!",
		}))
	}
}

/**
 * Update User Id and Client Id for AdAccount, Pixel, Page, Catalogue Tiktok
 */
func (a *clientApi) UpdateUserIdClientIdTiktokResourceApi() fiber.Handler {
	return func(c *fiber.Ctx) error {
		var payload requests.UpdateUserIdClientIdTiktokResource

		ctx := c.Context()
		if err := c.BodyParser(&payload); err != nil {
			return core.ReturnErrForApi(c, err.Error())
		}

		//validate
		validationErrors := payload.Validate()
		fmt.Printf("\n Validation Errors %v \n", validationErrors)
		if len(validationErrors) > 0 {
			return core.ReturnErrsForApi(c, validationErrors)
		}

		userId, err := utils.GetUserIdPrimitive(c.Context())
		if err != nil {
			return core.ReturnErrForApi(c, err.Error())
		}

		payload.UpdatedBy = userId
		payload.UpdatedAt = time.Now()

		err = a.usc.UpsertUserToMultipleCollectionsWithTransactionTikTokApiUsc(ctx, payload)

		if err != nil {
			fmt.Printf("Error at Usc: %v ", err)
			return c.Status(http.StatusBadRequest).JSON(core.ResponseData(map[string]interface{}{
				"msg": "Update client fail!",
			}))
		}

		return c.Status(http.StatusOK).JSON(core.ResponseData(map[string]interface{}{
			"msg": "Update client successfully!",
		}))
	}
}
