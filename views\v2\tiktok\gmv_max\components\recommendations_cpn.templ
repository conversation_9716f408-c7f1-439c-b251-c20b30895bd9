package components

templ RecommendationsCpn() {
    <div class="row mb-4 fade-in">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <div class="d-flex justify-content-between align-items-start">
                        <div class="d-flex align-items-center">
                            <h5 class="card-title mb-0 me-2">
                                Quick actions and recommendations to drive more
                                sales
                            </h5>
                            <span class="info-badge">i</span>
                        </div>
                        <button type="button" class="close-btn" onclick="closeRecommendations()">
                            <i class="bi bi-x"></i>
                        </button>
                    </div>
                </div>
                <div class="card-body">
                    <div class="recommendation-card p-3 mb-3">
                        <div class="row align-items-start">
                            <div class="col-lg-8 col-md-7">
                                <h6 class="fw-semibold mb-2">
                                    Increase sales on 3 recommended products with
                                    GMV Max ads
                                </h6>
                                <p class="text-muted mb-3 small">
                                    Recommended products are items from your catalog that shoppers on TikTok
                                    may be interested in purchasing,
                                    based on TikTok's top-selling product categories, trending products and
                                    most searched keywords.
                                </p>
                                <div class="d-flex gap-2">
                                    <div class="product-thumbnail"></div>
                                    <div class="product-thumbnail"></div>
                                    <div class="product-thumbnail"></div>
                                </div>
                            </div>
                            <div class="col-lg-4 col-md-5 text-md-end mt-3 mt-md-0">
                                <button type="button" class="btn btn-primary btn-sm" onclick="createAds()">
                                    Create ads
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
}