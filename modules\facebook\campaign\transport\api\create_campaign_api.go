package api

import (
	"fmt"
	"godsp/modules/facebook/campaign/transport/requests"
	"godsp/pkg/gos/utils"
	"godsp/pkg/sctx/core"
	"net/http"

	"github.com/gofiber/fiber/v2"
)

/***
 * Create camp api
 */

func (a *campaignApi) CreateCampaignApi() fiber.Handler {
	return func(c *fiber.Ctx) error {
		var payload requests.CreateCampaignReq
		if err := c.BodyParser(&payload); err != nil {
			return core.ReturnErrsForApi(c, err)
		}

		//validate
		validationErrors := payload.Validate()
		fmt.Printf("🚀  ~ payload: %v \n", &payload)
		fmt.Println("----->1", validationErrors)
		if len(validationErrors) > 0 {
			return core.ReturnErrsForApi(c, validationErrors)
		}

		// userId, err := utils.GetUserId(c.Context())
		userId, _, clientId, adAccountId, err := utils.GetInfoUser(c.Context())

		if err != nil {
			return core.ReturnErrsForApi(c, err)
		} else {
			payload.UserId = userId
			payload.ClientID = clientId
			payload.AccountID = adAccountId
		}

		camp, err := a.usc.CreateCampaignUsc(c.Context(), &payload)
		if err != nil {
			return core.ReturnErrsForApi(c, err)
		}

		return c.Status(http.StatusOK).JSON(core.ResponseData(map[string]interface{}{
			"msg":  "Create Campaign successfully",
			"data": camp,
		}))
	}
}
