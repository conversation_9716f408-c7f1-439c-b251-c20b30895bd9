package pipelines

import (
	"godsp/modules/tiktok/adgroup/common/enums"
	"godsp/modules/tiktok/adgroup/transport/requests"

	"go.mongodb.org/mongo-driver/bson"
)

func GetDetailAdgroup(payload *requests.GetDetailsAdgroupsReq) []bson.M {
	// Base filter
	filter := []bson.M{
		{"adgroup_id": payload.AdgroupID},
		{"status": bson.M{"$nin": []string{enums.AdGroupSecondaryStatus[enums.DELETED],enums.AdGroupSecondaryStatus[enums.CANCELLED]}}},
	}

	if payload.AdvertiserID != nil {
		filter = append(filter, bson.M{"advertiser_id": *payload.AdvertiserID})
	}



	// Apply permission filters
	// if user.RoleName != conf.SysConf.RoleAdmin {
	// 	if user.RoleName == conf.SysConf.RoleClientAdmin || user.RoleName == conf.SysConf.RoleClientAdminViewer {
	// 		filter = append(filter, bson.M{"client_id": user.ClientId})
	// 	} else {
	// 		filter = append(filter, bson.M{
	// 			"client_id":     user.ClientId,
	// 			"list_user_ids": user.UserId,
	// 		})
	// 	}
	// }
	// else {
	// 	filter = append(filter, bson.M{"client_id": payload.ClientID})
	// }

	return []bson.M{
		{"$match": bson.M{"$and": filter}},
		{
			"$lookup": bson.M{
				"from":         "tiktok_campaigns",
				"localField":   "campaign_id",
				"foreignField": "campaign_id",
				"as":           "campaign",
			},
		},
		{
			"$unwind": bson.M{
				"path":                       "$campaign",
				"preserveNullAndEmptyArrays": true,
			},
		},
		// Join campaign
		{
			"$lookup": bson.M{
				"from":         "tiktok_campaigns",
				"localField":   "campaign_id",
				"foreignField": "campaign_id",
				"as":           "campaign_info",
			},
		},
		{
			"$addFields": bson.M{
				"campaign": bson.M{"$arrayElemAt": bson.A{"$campaign_info", 0}},
			},
		},

		// Campaign Structure (nested lookup)
		{
			"$lookup": bson.M{
				"from": "tiktok_ads",
				"let":  bson.M{"cid": "$campaign_id"},
				"pipeline": []bson.M{
					// Match ads in same campaign
					{"$match": bson.M{
						"$expr": bson.M{
							"$eq": bson.A{"$campaign_id", "$$cid"},
						},
					}},

					// Join adgroup to get adgroup_name
					{"$lookup": bson.M{
						"from":         "tiktok_ad_groups",
						"localField":   "adgroup_id",
						"foreignField": "adgroup_id",
						"as":           "adgroup_info",
					}},
					{"$addFields": bson.M{
						"adgroup_name": bson.M{"$arrayElemAt": bson.A{"$adgroup_info.adgroup_name", 0}},
					}},

					// Group ads by adgroup
					{"$group": bson.M{
						"_id": bson.M{
							"adgroup_id":   "$adgroup_id",
							"adgroup_name": "$adgroup_name",
						},
						"ads": bson.M{
							"$push": bson.M{
								"ad_id":   "$ad_id",
								"ad_name": "$ad_name",
							},
						},
					}},

					// Group result into `adgroups`
					{"$group": bson.M{
						"_id": nil,
						"adgroups": bson.M{"$push": bson.M{
							"adgroup_id":   "$_id.adgroup_id",
							"adgroup_name": "$_id.adgroup_name",
							"ads":          "$ads",
						}},
					}},

					// Final projection
					{"$project": bson.M{
						"campaign_id": "$$cid",
						"adgroups":    1,
					}},
				},
				"as": "campaign_structure",
			},
		},

		// Flatten campaign_structure[0]
		{
			"$addFields": bson.M{
				"campaign_structure": bson.M{
					"$arrayElemAt": bson.A{"$campaign_structure", 0},
				},
			},
		},

		// Copy campaign_name from campaign into campaign_structure
		{
			"$addFields": bson.M{
				"campaign_structure.campaign_name": "$campaign.campaign_name",
			},
		},
	}
}
