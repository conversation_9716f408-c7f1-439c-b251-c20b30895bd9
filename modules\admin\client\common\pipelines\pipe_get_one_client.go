package pipelines

import (
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

func PipelineGetOneClient(idObject primitive.ObjectID) []bson.M {
	pipeline := []bson.M{
		{
			"$match": bson.M{
				"_id": idObject,
			},
		},
		{
			"$lookup": bson.M{
				"from":         "admin_users",
				"localField":   "created_by",
				"foreignField": "_id",
				"as":           "user_created",
			},
		},
		{
			"$lookup": bson.M{
				"from":         "admin_users",
				"localField":   "updated_by",
				"foreignField": "_id",
				"as":           "user_updated",
			},
		},
		{
			"$unwind": bson.M{
				"path":                       "$user_created",
				"preserveNullAndEmptyArrays": true,
			},
		},
		{
			"$unwind": bson.M{
				"path":                       "$user_updated",
				"preserveNullAndEmptyArrays": true,
			},
		},
		{
			"$project": bson.M{
				"root": "$$ROOT",
				"user_created": bson.M{
					"_id":       "$user_created._id",
					"full_name": "$user_created.full_name",
					"email":     "$user_created.email",
				},
				"user_updated": bson.M{
					"_id":       "$user_updated._id",
					"full_name": "$user_updated.full_name",
					"email":     "$user_updated.email",
				},
			},
		},
		{
			"$replaceRoot": bson.M{
				"newRoot": bson.M{
					"$mergeObjects": []interface{}{
						"$root",
						bson.M{
							"user_created": "$user_created",
							"user_updated": "$user_updated",
						},
					},
				},
			},
		},
	}

	return pipeline
}
