package tiktok

import (
	adRoutes "godsp/modules/tiktok/ad/routes"
	adgroupRoutes "godsp/modules/tiktok/adgroup/routes"
	advertiser "godsp/modules/tiktok/advertiser"
	audienceRoutes "godsp/modules/tiktok/audience/routes"
	campRoutes "godsp/modules/tiktok/campaign/routes"
	customColTable "godsp/modules/tiktok/custom_column_table/routes"
	gmvMaxCampaignRoutes "godsp/modules/tiktok/gmv_max_campaign/routes"
	identityRoutes "godsp/modules/tiktok/identity/routes"
	postRoutes "godsp/modules/tiktok/post/routes"
	targetingRoutes "godsp/modules/tiktok/targeting/routes"

	"github.com/dev-networldasia/dspgos/sctx"

	"github.com/gofiber/fiber/v2"
	"github.com/gofiber/fiber/v2/middleware/session"
)

func SetupRoutesTiktok(app *fiber.App, serviceCtx sctx.ServiceContext, store *session.Store, midds ...fiber.Handler) {
	advertiser.SetupRoutesTiktokAdvertiser(app, serviceCtx, store, midds...)
	campRoutes.SetupRoutesCampaign(app, serviceCtx, store, midds...)
	adgroupRoutes.SetupRoutesAdGroup(app, serviceCtx, store, midds...)
	adRoutes.SetupRoutesAd(app, serviceCtx, store, midds...)
	customColTable.SetupRoutesCustomColumnTable(app, serviceCtx, store, midds...)
	targetingRoutes.SetupRouterTargeting(app, serviceCtx, midds...)
	identityRoutes.SetupRoutesIdentity(app, serviceCtx, store, midds...)
	postRoutes.SetupRoutesPost(app, serviceCtx, store, midds...)
	audienceRoutes.SetupRoutesAudience(app, serviceCtx, store, midds...)
	gmvMaxCampaignRoutes.SetupRoutesGmvMaxCampaign(app, serviceCtx, store, midds...)
}
