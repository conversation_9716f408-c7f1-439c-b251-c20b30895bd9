package api

import (
	"godsp/modules/facebook/ad/transport/requests"
	"godsp/pkg/gos/utils"
	"godsp/pkg/sctx/core"
	"net/http"

	"github.com/gofiber/fiber/v2"
)

/**
 * Api load list page
 */
func (a *adApi) DeleteAdApi() fiber.Handler {
	return func(c *fiber.Ctx) error {
		var payload requests.DeleteAdReq

		if err := c.BodyParser(&payload); err != nil {
			return core.ReturnErrsForApi(c, err)
		}

		//validate
		validationErrors := payload.Validate()

		if len(validationErrors) > 0 {
			return core.ReturnErrsForApi(c, validationErrors)
		}

		userId, err := utils.GetUserId(c.Context())
		if err != nil {
			return core.ReturnErrsForApi(c, err)
		}

		payload.UserId = userId

		_, err = a.usc.DeleteAdUsc(c.Context(), &payload)

		if err != nil {
			return core.ReturnErrsForApi(c, err)
		}

		return c.Status(http.StatusOK).JSON(core.ResponseData(map[string]interface{}{
			"msg":  "Delete successfully",
			"data": payload.AdIDs,
		}))
	}
}
