package requests

import (
	"godsp/modules/admin/common/admerrs"
	"godsp/modules/admin/user/transport/rules"
	"strings"

	"github.com/go-playground/validator/v10"
)

type PayloadAuthLogin struct {
	Email    string `form:"email" validate:"required,email"`
	Password string `form:"password" validate:"required,checkPassword"`
}

func (pl *PayloadAuthLogin) Validate() []*string {
	validate := validator.New()
	validate.RegisterValidation("checkPassword", rules.RulePasswordReq)
	err := validate.Struct(pl)
	if err != nil {
		var validationErrors []*string
		for _, err := range err.(validator.ValidationErrors) {
			switch err.Field() {
			case "Email":
				errMsg := admerrs.ErrValidateLoginEmail.Error()
				validationErrors = append(validationErrors, &errMsg)
			case "Password":
				errMsg := admerrs.ErrValidateLoginPassword.Error()
				validationErrors = append(validationErrors, &errMsg)
			}
		}
		return validationErrors
	}

	pl.Email = strings.TrimSpace(pl.Email)
	return nil
}
