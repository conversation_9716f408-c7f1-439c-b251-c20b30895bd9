package handlers

import (
	"context"
	clientRes "godsp/modules/admin/client/transport/responses"
	roleE "godsp/modules/admin/role/entity"
	"godsp/modules/admin/user/common/errs"
	"godsp/modules/admin/user/transport/responses"

	"godsp/modules/admin/common/admconst"
	"godsp/modules/admin/user/entity"
	"godsp/modules/facebook/ad_account/transport/response"

	"godsp/pkg/gos/utils"
	"godsp/pkg/sctx/core"

	"github.com/gofiber/fiber/v2"
	"github.com/gofiber/fiber/v2/middleware/session"
)

type UserUsc interface {
	FindRoleUserUsc(ctx context.Context, roleActive bool) (*[]roleE.RoleEntity, error)
	FindOneDetailUserUsc(ctx context.Context, id string) (*entity.UserEntity, error)
	ListAdAccountUserUsc(ctx context.Context) ([]*response.AdAccountWithFullname, error)
	ListClientUsc(ctx context.Context) ([]*clientRes.ClientListSelect, error)
	FindUserProfileUsc(ctx context.Context, id string) (*responses.DetailsUserProfile, error)
}

type userHdl struct {
	usc UserUsc
}

func NewUserHdl(usc UserUsc) *userHdl {
	return &userHdl{usc: usc}
}

/**
 * List user hdl
 */
func (h *userHdl) ListUserHdl(store *session.Store) fiber.Handler {
	return func(c *fiber.Ctx) error {
		roles, err := h.usc.FindRoleUserUsc(c.Context(), !admconst.ROLE_ACTIVE)
		if err != nil {
			return err
		}

		// adAccounts, err := h.usc.ListAdAccountUserUsc(c.Context())
		// if err != nil {
		// 	return err
		// }

		clients, err := h.usc.ListClientUsc(c.Context())
		if err != nil {
			return err
		}

		flashMsg := utils.GetFlashMessage(c, store, "editUser")

		return c.Render("admins/users/index", fiber.Map{
			"flashMsg": flashMsg,
			"status":   admconst.StatusFullName,
			"show": map[string]interface{}{
				"roles": roles,
				// "adAccounts":   adAccounts,
				"statusCreate": admconst.StatusCreateName,
				"defaultImg":   admconst.DEFAULT_IMG_ADMIN,
				"statusActive": admconst.STATUS_ACTIVE,
				"clients":      clients,
			},
		})
	}
}

/**
 * Edit user hdl
 */
func (h *userHdl) EditUserHdl(store *session.Store) fiber.Handler {
	return func(c *fiber.Ctx) error {
		ctx := c.Context()
		id := c.Params("id")

		detail, err := h.usc.FindOneDetailUserUsc(ctx, id)
		if err != nil {
			utils.SetFlashMessage(c, store, "editUser", err.Error())
			c.Locals("test", "test")
			return c.Redirect("/admins/users/list")
		}

		roleActive := admconst.ROLE_ACTIVE
		checkUserLoging, err := utils.GetUserIdPrimitive(c.Context())
		if err != nil {
			return core.ReturnErrForApi(c, err.Error())
		}

		roles, err := h.usc.FindRoleUserUsc(ctx, roleActive)
		if err != nil {
			return err
		}

		clients, err := h.usc.ListClientUsc(c.Context())
		if err != nil {
			return err
		}

		return c.Render("admins/users/edit", fiber.Map{
			"detail":          detail,
			"status":          admconst.StatusFullName,
			"defaultImg":      admconst.DEFAULT_IMG_ADMIN,
			"roles":           roles,
			"checkUserLoging": checkUserLoging,
			"clients":         clients,
		})
	}
}

/**
 * Edit user hdl
 */
// func (h *userHdl) UserProfileHdl(store *session.Store) fiber.Handler {
// 	return func(c *fiber.Ctx) error {
// 		// ctx := c.Context()
// 		id := c.Params("id")

// 		userInfo, err := utils.GetInfoUserAuth(c.Context())
// 		fmt.Println("GetInfoUserAuth", userInfo)
// 		if err != nil {
// 			return core.ReturnErrForPermissionDenied(c)
// 		}

// 		isErr := utils.ValidateObjectID(id)
// 		if !isErr {
// 			utils.SetFlashMessage(c, store, "editUser", errs.ErrIDUserValidate.Error())
// 			return c.Redirect("/admins")
// 		}

// 		jsonData, _ := json.MarshalIndent(userInfo, "", "  ")
// 		fmt.Println("ListClientUsc ok ok  -------->", string(jsonData))
// 		return c.Render("admins/users/profile", fiber.Map{
// 			"status":     admconst.StatusFullName,
// 			"defaultImg": admconst.DEFAULT_IMG_ADMIN,
// 			"userInfo":   userInfo,
// 		})
// 	}
// }

func (h *userHdl) UserProfileHdl(store *session.Store) fiber.Handler {
	return func(c *fiber.Ctx) error {
		ctx := c.Context()
		id := c.Params("id")

		// fmt.Println("UserProfileHdl", id)
		userInfo, err := utils.GetInfoUserAuth(c.Context())
		// fmt.Println("GetInfoUserAuth", userInfo)
		if err != nil {
			return core.ReturnErrForPermissionDenied(c)
		}

		isErr := utils.ValidateObjectID(id)
		if !isErr {
			utils.SetFlashMessage(c, store, "editUser", errs.ErrIDUserValidate.Error())
			return c.Redirect("/admins")
		}

		detail, err := h.usc.FindUserProfileUsc(ctx, id)
		// jsonData, _ := json.MarshalIndent(detail, "", "  ")
		// fmt.Println("detail -------->", string(jsonData))
		if err != nil {
			utils.SetFlashMessage(c, store, "editUser", err.Error())
			return c.Redirect("/admins")
		}

		// roleActive := admconst.ROLE_ACTIVE
		checkUserLoging, err := utils.GetUserIdPrimitive(c.Context())
		if err != nil {
			utils.SetFlashMessage(c, store, "editUser", err.Error())
			return c.Redirect("/admins")
		}
		// if err != nil {
		// 	return core.ReturnErrForApi(c, err.Error())
		// }

		// roles, err := h.usc.FindRoleUserUsc(ctx, roleActive)
		// if err != nil {
		// 	return err
		// }

		// clients, err := h.usc.ListClientUsc(c.Context())
		// if err != nil {
		// 	return err
		// }

		return c.Render("admins/users/profile", fiber.Map{
			"authPermission":  core.GetPermission(c.Context()).GetPermissions(),
			"detail":          detail,
			"status":          admconst.StatusFullName,
			"defaultImg":      admconst.DEFAULT_IMG_ADMIN,
			"checkUserLoging": checkUserLoging,
			"userInfo":        userInfo,
		})
	}
}
