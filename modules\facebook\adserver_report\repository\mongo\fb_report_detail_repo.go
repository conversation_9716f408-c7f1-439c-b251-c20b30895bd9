package mongo

import (
	"context"
	"fmt"
	"godsp/modules/facebook/adserver_report/common/pipelines"
	"godsp/modules/facebook/adserver_report/entity"
	"godsp/modules/facebook/common/fberrs"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
)

type fbReportDetailRepo struct {
	DB         *mongo.Database
	Collection *mongo.Collection
}

func NewFBReportDetailRepo(DB *mongo.Database) *fbReportDetailRepo {
	return &fbReportDetailRepo{
		DB:         DB,
		Collection: DB.Collection(entity.FBReportDetailEntity{}.CollectionName()),
	}
}

/**
 * Max ording
 */
func (r *fbReportDetailRepo) MaxOrderingRepo(ctx context.Context, filter interface{}) (int64, error) {
	var result struct {
		MaxOrdering int64 `bson:"maxOrdering"`
	}

	pipeline := []bson.M{}
	if filter != nil {
		pipeline = append(pipeline, bson.M{"$match": filter})
	}

	pipeline = append(pipeline, bson.M{
		"$group": bson.M{
			"_id":         nil,
			"maxOrdering": bson.M{"$max": "$ordering"},
		},
	})

	cursor, err := r.Collection.Aggregate(ctx, pipeline)
	if err != nil {
		return 0, err
	}
	defer cursor.Close(ctx)

	if cursor.Next(ctx) {
		if err := cursor.Decode(&result); err != nil {
			return 0, err
		}
		return result.MaxOrdering, nil
	}

	return 0, nil
}

/**
 * Find role
 */
func (r *fbReportDetailRepo) FindFBReportDetailRepo(ctx context.Context, filter bson.M, opts ...*options.FindOptions) (*[]entity.FBReportDetailEntity, error) {
	var reports []entity.FBReportDetailEntity

	cursor, err := r.Collection.Find(ctx, filter, opts...)
	if err != nil {
		return nil, err
	}

	defer cursor.Close(ctx)
	if cursor == nil {
		return nil, fberrs.ErrNilCursorValue
	}

	err = cursor.All(ctx, &reports)
	if err != nil {
		return nil, err
	}

	return &reports, nil
}

/**
 * Get metrics column for ads fb report detail repo
 */
// func (r *fbReportDetailRepo) GetMetricsColumnForAdsFBReportDetailRepo(ctx context.Context, filter bson.M) (*[]entity.FBReportDetailEntity, error) {
// 	pipeline := mongo.Pipeline{
// 		{{Key: "$unwind", Value: "$actions"}},
// 		{{Key: "$match", Value: filter}},
// 		{{Key: "$group", Value: bson.D{
// 			{Key: "_id", Value: bson.D{
// 				{Key: "ad_id", Value: "$ad_id"},
// 				{Key: "action_type", Value: "$actions.action_type"},
// 			}},
// 			{Key: "totalValue", Value: bson.D{{Key: "$sum", Value: "$actions.value"}}},
// 			{Key: "origin_id", Value: bson.D{{Key: "$first", Value: "$_id"}}},
// 			{Key: "account_id", Value: bson.D{{Key: "$first", Value: "$account_id"}}},
// 			{Key: "campaign_id", Value: bson.D{{Key: "$first", Value: "$campaign_id"}}},
// 			{Key: "adset_id", Value: bson.D{{Key: "$first", Value: "$adset_id"}}},
// 			{Key: "ad_id", Value: bson.D{{Key: "$first", Value: "$ad_id"}}},
// 			{Key: "campaign_name", Value: bson.D{{Key: "$first", Value: "$campaign_name"}}},
// 			{Key: "adset_name", Value: bson.D{{Key: "$first", Value: "$adset_name"}}},
// 			{Key: "ad_name", Value: bson.D{{Key: "$first", Value: "$ad_name"}}},
// 			{Key: "objective", Value: bson.D{{Key: "$first", Value: "$objective"}}},
// 			{Key: "buying_type", Value: bson.D{{Key: "$first", Value: "$buying_type"}}},
// 			{Key: "optimization_goal", Value: bson.D{{Key: "$first", Value: "$optimization_goal"}}},
// 			{Key: "date", Value: bson.D{{Key: "$first", Value: "$date"}}},
// 			{Key: "clicks", Value: bson.D{{Key: "$sum", Value: "$clicks"}}},
// 			{Key: "cpc", Value: bson.D{{Key: "$sum", Value: "$cpc"}}},
// 			{Key: "cpm", Value: bson.D{{Key: "$sum", Value: "$cpm"}}},
// 			{Key: "ctr", Value: bson.D{{Key: "$sum", Value: "$ctr"}}},
// 			{Key: "impressions", Value: bson.D{{Key: "$sum", Value: "$impressions"}}},
// 			{Key: "reach", Value: bson.D{{Key: "$sum", Value: "$reach"}}},
// 			{Key: "social_spend", Value: bson.D{{Key: "$sum", Value: "$social_spend"}}},
// 			{Key: "spend", Value: bson.D{{Key: "$sum", Value: "$spend"}}},
// 		}}},
// 		{{Key: "$group", Value: bson.D{
// 			{Key: "_id", Value: "$ad_id"},
// 			{Key: "origin_id", Value: bson.D{{Key: "$first", Value: "$origin_id"}}},
// 			{Key: "account_id", Value: bson.D{{Key: "$first", Value: "$account_id"}}},
// 			{Key: "campaign_id", Value: bson.D{{Key: "$first", Value: "$campaign_id"}}},
// 			{Key: "adset_id", Value: bson.D{{Key: "$first", Value: "$adset_id"}}},
// 			{Key: "ad_id", Value: bson.D{{Key: "$first", Value: "$ad_id"}}},
// 			{Key: "campaign_name", Value: bson.D{{Key: "$first", Value: "$campaign_name"}}},
// 			{Key: "adset_name", Value: bson.D{{Key: "$first", Value: "$adset_name"}}},
// 			{Key: "ad_name", Value: bson.D{{Key: "$first", Value: "$ad_name"}}},
// 			{Key: "objective", Value: bson.D{{Key: "$first", Value: "$objective"}}},
// 			{Key: "buying_type", Value: bson.D{{Key: "$first", Value: "$buying_type"}}},
// 			{Key: "optimization_goal", Value: bson.D{{Key: "$first", Value: "$optimization_goal"}}},
// 			{Key: "actions", Value: bson.D{
// 				{Key: "$push", Value: bson.D{
// 					{Key: "action_type", Value: "$_id.action_type"},
// 					{Key: "value", Value: "$totalValue"},
// 				}},
// 			}},
// 			{Key: "date", Value: bson.D{{Key: "$first", Value: "$date"}}},
// 			{Key: "clicks", Value: bson.D{{Key: "$first", Value: "$clicks"}}},
// 			{Key: "cpc", Value: bson.D{{Key: "$first", Value: "$cpc"}}},
// 			{Key: "cpm", Value: bson.D{{Key: "$first", Value: "$cpm"}}},
// 			{Key: "ctr", Value: bson.D{{Key: "$first", Value: "$ctr"}}},
// 			{Key: "impressions", Value: bson.D{{Key: "$first", Value: "$impressions"}}},
// 			{Key: "reach", Value: bson.D{{Key: "$first", Value: "$reach"}}},
// 			{Key: "social_spend", Value: bson.D{{Key: "$first", Value: "$social_spend"}}},
// 			{Key: "spend", Value: bson.D{{Key: "$first", Value: "$spend"}}},
// 		}}},
// 		{{Key: "$project", Value: bson.D{
// 			{Key: "_id", Value: "$origin_id"},
// 			{Key: "ad_id", Value: "$ad_id"},
// 			{Key: "account_id", Value: 1},
// 			{Key: "campaign_id", Value: 1},
// 			{Key: "adset_id", Value: 1},
// 			{Key: "campaign_name", Value: 1},
// 			{Key: "adset_name", Value: 1},
// 			{Key: "ad_name", Value: 1},
// 			{Key: "objective", Value: 1},
// 			{Key: "buyingType", Value: 1},
// 			{Key: "optimization_goal", Value: 1},
// 			{Key: "actions", Value: 1},
// 			{Key: "clicks", Value: 1},
// 			{Key: "cpc", Value: 1},
// 			{Key: "cpm", Value: 1},
// 			{Key: "ctr", Value: 1},
// 			{Key: "impressions", Value: 1},
// 			{Key: "reach", Value: 1},
// 			{Key: "social_spend", Value: 1},
// 			{Key: "spend", Value: 1},
// 			{Key: "date", Value: 1},
// 		}}},
// 	}

// 	allowDiskUse := true
// 	aggregateOptions := options.Aggregate().SetAllowDiskUse(allowDiskUse)
// 	cursor, err := r.Collection.Aggregate(ctx, pipeline, aggregateOptions)
// 	if err != nil {
// 		return nil, err
// 	}

// 	defer cursor.Close(ctx)
// 	if cursor == nil {
// 		return nil, fberrs.ErrNilCursorValue
// 	}

// 	var reports []entity.FBReportDetailEntity
// 	err = cursor.All(ctx, &reports)
// 	if err != nil {
// 		fmt.Println("Error reading cursor:", err)
// 		return nil, err
// 	}

// 	return &reports, nil
// }

/**
 * Get metrics column for campaigns fb report detail repo
 */
func (r *fbReportDetailRepo) GetMetricsColumnForCampaignsFBReportDetailRepo(ctx context.Context, filter bson.M) (*[]entity.FBReportDetailEntity, error) {

	pipeline := pipelines.GetReportCampaignPipe(filter)

	// var prettyDocs []bson.M
	// for _, doc := range pipeline {
	// 	bsonDoc, err := bson.Marshal(doc)
	// 	if err != nil {
	// 		panic(err)
	// 	}

	// 	var prettyDoc bson.M
	// 	err = bson.Unmarshal(bsonDoc, &prettyDoc)
	// 	if err != nil {
	// 		panic(err)
	// 	}

	// 	prettyDocs = append(prettyDocs, prettyDoc)
	// }

	// // prettyJSON, err := json.MarshalIndent(prettyDocs, "", "  ")
	// prettyJSON, err := json.Marshal(prettyDocs)
	// if err != nil {
	// 	panic(err)
	// }

	// fmt.Println("\n \n \n xxxx-------helo------>Debug pipeline filter: %s \n \n \n ", string(prettyJSON))

	allowDiskUse := true
	aggregateOptions := options.Aggregate().SetAllowDiskUse(allowDiskUse)
	cursor, err := r.Collection.Aggregate(ctx, pipeline, aggregateOptions)

	if err != nil {
		return nil, err
	}

	defer cursor.Close(ctx)
	if cursor == nil {
		return nil, fberrs.ErrNilCursorValue
	}

	// var reports []responses.FBReportDetailTableItem
	var reports []entity.FBReportDetailEntity

	err = cursor.All(ctx, &reports)
	if err != nil {
		fmt.Println("Error reading cursor:", err)
		return nil, err
	}

	// for index, report := range reports {
	// 	_, err := json.MarshalIndent(report, "", "  ")
	// 	if err != nil {
	// 		fmt.Println("Error marshalling to JSON:", err)
	// 		fmt.Printf("report campaign error: : %v", report.CampaignID)
	// 		FindNaNFields(report)
	// 	} else {
	// 		fmt.Printf("report campaign ok: : %v", report.CampaignID)
	// 	}
	// 	fmt.Printf("------> index: %v\n", index)
	// }

	return &reports, nil
}

/**
 * Get metrics column for adsets fb report detail repo
 */
func (r *fbReportDetailRepo) GetMetricsColumnForAdsetsFBReportDetailRepo(ctx context.Context, filter bson.M) (*[]entity.FBReportDetailEntity, error) {

	// Debug pipeline
	pipeline := pipelines.GetReportAdsetPipe(filter)
	// var prettyDocs []bson.M
	// for _, doc := range pipeline {
	// 	bsonDoc, err := bson.Marshal(doc)
	// 	if err != nil {
	// 		panic(err)
	// 	}

	// 	var prettyDoc bson.M
	// 	err = bson.Unmarshal(bsonDoc, &prettyDoc)
	// 	if err != nil {
	// 		panic(err)
	// 	}

	// 	prettyDocs = append(prettyDocs, prettyDoc)
	// }

	// // prettyJSON, err := json.MarshalIndent(prettyDocs, "", "  ")
	// prettyJSON, err := json.Marshal(prettyDocs)
	// if err != nil {
	// 	panic(err)
	// }

	// fmt.Println("\n \n \n xxxx------------->Debug pipeline filter: %s \n \n \n ", string(prettyJSON))

	// End Debug

	allowDiskUse := true
	aggregateOptions := options.Aggregate().SetAllowDiskUse(allowDiskUse)
	cursor, err := r.Collection.Aggregate(ctx, pipeline, aggregateOptions)
	if err != nil {
		return nil, err
	}

	defer cursor.Close(ctx)
	if cursor == nil {
		return nil, fberrs.ErrNilCursorValue
	}

	var reports []entity.FBReportDetailEntity
	err = cursor.All(ctx, &reports)
	if err != nil {
		fmt.Println("Error reading cursor:", err)
		return nil, err
	}

	return &reports, nil
}

/**
 * Get metrics column for adsets fb report detail repo
 */
func (r *fbReportDetailRepo) GetMetricsColumnForAdsFBReportDetailRepo(ctx context.Context, filter bson.M) (*[]entity.FBReportDetailEntity, error) {

	// Debug pipeline
	pipeline := pipelines.GetReportAdPipe(filter)
	// var prettyDocs []bson.M
	// for _, doc := range pipeline {
	// 	bsonDoc, err := bson.Marshal(doc)
	// 	if err != nil {
	// 		panic(err)
	// 	}

	// 	var prettyDoc bson.M
	// 	err = bson.Unmarshal(bsonDoc, &prettyDoc)
	// 	if err != nil {
	// 		panic(err)
	// 	}

	// 	prettyDocs = append(prettyDocs, prettyDoc)
	// }

	// prettyJSON, err := json.Marshal(prettyDocs)
	// if err != nil {
	// 	panic(err)
	// }

	// fmt.Println("\n \n \n xxxx------------->Debug pipeline filter: %s \n \n \n ", string(prettyJSON))

	// End Debug

	allowDiskUse := true
	aggregateOptions := options.Aggregate().SetAllowDiskUse(allowDiskUse)
	cursor, err := r.Collection.Aggregate(ctx, pipeline, aggregateOptions)
	if err != nil {
		return nil, err
	}

	defer cursor.Close(ctx)
	if cursor == nil {
		return nil, fberrs.ErrNilCursorValue
	}

	var reports []entity.FBReportDetailEntity
	err = cursor.All(ctx, &reports)
	if err != nil {
		fmt.Println("Error reading cursor:", err)
		return nil, err
	}

	// fmt.Printf("\n ----------- reports 2----------- %+v \n", reports)

	return &reports, nil
}
