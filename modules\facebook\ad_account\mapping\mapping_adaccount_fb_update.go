package mapping

import (
	"godsp/modules/facebook/ad_account/entity"
	v20 "godsp/pkg/fb-marketing/marketing/v20"
	"time"

	"go.mongodb.org/mongo-driver/bson/primitive"
)

func MapperAdAccountFBUpdate(adc *v20.AdAccount, adAccount *entity.AdAccountEntity, userId primitive.ObjectID) {
	adAccount.Name = adc.Name
	adAccount.Currency = adc.Currency
	adAccount.TimeZoneName = adc.TimeZoneName
	adAccount.UpdatedBy = userId
	adAccount.UpdatedAt = time.Now()
}
