package gmv_max

import (
	clientE "godsp/modules/admin/client/entity"
	"godsp/modules/facebook/iface"
)

type ListTableGmvMaxLayoutData struct {
	FlashMsg       string         `json:"flash_msg"`
	AuthPermission map[string]int `json:"auth_permission" `
	UserInfo       iface.UserInfoAuth
	Clients        []*clientE.ClientEntity
}

type DetailTableGmvMaxLayoutData struct {
	FlashMsg       string         `json:"flash_msg"`
	AuthPermission map[string]int `json:"auth_permission" `
	UserInfo       iface.UserInfoAuth
	Clients        []*clientE.ClientEntity
}
