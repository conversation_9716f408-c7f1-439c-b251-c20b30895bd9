package enum

const (
	VERTICAL_ADOPTABLE_PETS           = "adoptable_pets"
	VERTICAL_COMMERCE                 = "commerce"
	VERTICAL_DESTINATIONS             = "destinations"
	VERTICAL_FLIGHTS                  = "flights"
	VERTICAL_GENERIC                  = "generic"
	VERTICAL_HOME_LISTINGS            = "home_listings"
	VERTICAL_HOTELS                   = "hotels"
	VERTICAL_JOBS                     = "jobs"
	VERTICAL_LOCAL_SERVICE_BUSINESSES = "local_service_businesses"
	VERTICAL_OFFER_ITEMS              = "offer_items"
	VERTICAL_OFFLINE_COMMERCE         = "offline_commerce"
	VERTICAL_TRANSACTABLE_ITEMS       = "transactable_items"
	VERTICAL_VEHICLES                 = "vehicles"
)

var Vertical = map[string]string{
	VERTICAL_ADOPTABLE_PETS:           VERTICAL_ADOPTABLE_PETS,
	VERTICAL_COMMERCE:                 VERTICAL_COMMERCE,
	VERTICAL_DESTINATIONS:             VERTICAL_DESTINATIONS,
	VERTICAL_FLIGHTS:                  VERTICAL_FLIGHTS,
	VERTICAL_GENERIC:                  VERTICAL_GENERIC,
	VERTICAL_HOME_LISTINGS:            VERTICAL_HOME_LISTINGS,
	VERTICAL_HOTELS:                   VERTICAL_HOTELS,
	VERTICAL_JOBS:                     VERTICAL_JOBS,
	VERTICAL_LOCAL_SERVICE_BUSINESSES: VERTICAL_LOCAL_SERVICE_BUSINESSES,
	VERTICAL_OFFER_ITEMS:              VERTICAL_OFFER_ITEMS,
	VERTICAL_OFFLINE_COMMERCE:         VERTICAL_OFFLINE_COMMERCE,
	VERTICAL_TRANSACTABLE_ITEMS:       VERTICAL_TRANSACTABLE_ITEMS,
	VERTICAL_VEHICLES:                 VERTICAL_VEHICLES,
}
