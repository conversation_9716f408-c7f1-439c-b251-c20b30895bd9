package mapping

import (
	campE "godsp/modules/facebook/campaign/common/enum"
	"godsp/modules/facebook/campaign/entity"
	"godsp/modules/facebook/campaign/transport/requests"
	"godsp/modules/facebook/common/fbconst"
	"godsp/modules/facebook/common/fbenums"

	v20 "godsp/pkg/fb-marketing/marketing/v20"
	"time"

	"go.mongodb.org/mongo-driver/bson/primitive"
)

func MapperCreateCampaignRequestToCampaigns(payload *requests.CreateCampaignReq) (*entity.CampaignEntity, *v20.Campaign) {
	campFB := v20.Campaign{
		AccountID:               payload.AccountID,
		Name:                    payload.Name,
		Objective:               payload.Objective,
		Status:                  payload.Status,
		SpecialAdCategories:     payload.SpecialAdCategories,
		IsBudgetScheduleEnabled: *payload.IsBudgetScheduleEnabled,
	}
	if payload.BidStrategy != nil {
		campFB.BidStrategy = *payload.BidStrategy
	}

	if payload.DailyBudget != nil {
		if *payload.DailyBudget > 0 {
			campFB.DailyBudget = *payload.DailyBudget
		}
	}

	if payload.LifeTimeBudget != nil {
		campFB.LifeTimeBudget = *payload.LifeTimeBudget
	}

	if campFB.DailyBudget >= fbconst.DAILY_BUDGET_MIN && campFB.LifeTimeBudget >= fbconst.LIFETIME_BUDGET_MIN {
		campFB.DailyBudget = 0
	}

	if payload.PromotedObject != nil {
		campFB.PromotedObject = v20.AdPromotedObject{
			ProductCatalogID: payload.PromotedObject.ProductCatalogID,
		}
	}

	now := time.Now()
	campEntity := entity.CampaignEntity{
		ID: primitive.NewObjectID(),

		AccountID:           payload.AccountID,
		Name:                payload.Name,
		Objective:           payload.Objective,
		Status:              payload.Status,
		SpecialAdCategories: payload.SpecialAdCategories,
		ListUserIDs:         []primitive.ObjectID{payload.UserId},
		ClientID:            payload.ClientID,
		CreateddBy:          payload.UserId,
		CreatedAt:           now,
		UpdatedAt:           now,
		UpdatedBy:           payload.UserId,
	}

	if payload.IsBudgetScheduleEnabled != nil {
		campEntity.IsBudgetScheduleEnabled = *payload.IsBudgetScheduleEnabled
		campFB.IsBudgetScheduleEnabled = *payload.IsBudgetScheduleEnabled
	}

	if payload.CanUseSpendCap != nil {
		campEntity.CanUseSpendCap = *payload.CanUseSpendCap
		campFB.CanUseSpendCap = *payload.CanUseSpendCap
	}

	if payload.SpendCap != nil {
		campEntity.SpendCap = *payload.SpendCap
		campFB.SpendCap = *payload.SpendCap
	}

	if payload.BidStrategy != nil {
		campEntity.BidStrategy = *payload.BidStrategy
	}

	if payload.PromotedObject != nil {
		campEntity.PromotedObject = *payload.PromotedObject
	}

	if fbenums.FB_STATUS_ACTIVE == payload.Status {
		campEntity.Approve = campE.Review
	} else {
		campEntity.Approve = campE.None
	}
	campEntity.Status = fbenums.FB_STATUS_PAUSED
	campFB.Status = fbenums.FB_STATUS_PAUSED

	return &campEntity, &campFB
}
