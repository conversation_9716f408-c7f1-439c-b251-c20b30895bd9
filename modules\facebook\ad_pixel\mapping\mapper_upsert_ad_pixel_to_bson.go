package mapping

import (
	v20 "godsp/pkg/fb-marketing/marketing/v20"
	"time"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

func MapperUpsertAdPixelToBson(adPixelFb *v20.AdPixel, userId primitive.ObjectID) bson.M {

	updateAt, _ := time.Parse(time.RFC3339, time.Now().Format(time.RFC3339))

	updateData := bson.M{
		"name":       adPixelFb.Name,
		"updated_at": updateAt,
		"updated_by": userId,
	}

	createData := bson.M{
		"created_at": updateAt,
		"created_by": userId,
	}

	data := bson.M{
		"$set":         updateData,
		"$setOnInsert": createData,
	}

	return data
}
