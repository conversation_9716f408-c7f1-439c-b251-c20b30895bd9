package handlers

import (
	"godsp/pkg/gos/utils"
	"godsp/pkg/sctx/core"
	"godsp/views/v2/pages"

	"github.com/dev-networldasia/dspgos/gos/templates"
	"github.com/gofiber/fiber/v2"
)

type homePageHdl struct {
}

func NewPageHdl() *homePageHdl {
	return &homePageHdl{}
}

func (h *homePageHdl) HomePageHdl() fiber.Handler {
	return func(c *fiber.Ctx) error {

		userInfo, err := utils.GetInfoUserAuth(c.Context())
		if err != nil {
			// return core.ReturnErrForPermissionDenied(c)
			c.Redirect("/page-permission-denied")
		}

		return templates.Render(c, pages.HomePages(&pages.HomeLayoutData{
			AuthPermission:     core.GetPermission(c.Context()).GetPermissions(),
			UserInfo:           userInfo,
		}))
	}
}
// func (h *homePageHdl) HomePageHdl() fiber.Handler {
// 	return func(c *fiber.Ctx) error {

// 		userInfo, err := utils.GetInfoUserAuth(c.Context())
// 		if err != nil {
// 			// return core.ReturnErrForPermissionDenied(c)
// 			c.Redirect("/page-permission-denied")
// 		}
// 		fmt.Println("userInfo:", userInfo)

// 		return c.Render("pages/home/<USER>", fiber.Map{
// 			"authPermission": core.GetPermission(c.Context()).GetPermissions(),
// 			"roleAdmin":      conf.SysConf.RoleAdmin,
// 			"userInfo":       userInfo,
// 		})
// 	}
// }
