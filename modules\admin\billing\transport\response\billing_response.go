package response

import (
	"godsp/modules/admin/billing/common/consts"
	"godsp/modules/admin/client/entity"
	"godsp/modules/admin/common/admconst"
	userEntity "godsp/modules/admin/user/entity"
	"godsp/pkg/gos/goconst"
	"godsp/pkg/gos/utils"
	"time"

	"go.mongodb.org/mongo-driver/bson/primitive"
)

type BillingDataResponse struct {
	ID           string      `json:"id"`
	CreatedAt    time.Time   `json:"created_at"`
	UpdatedAt    time.Time   `json:"updated_at"`
	AdvertiserID int         `json:"advertiser_id"`
	Platform     string      `json:"platform"`
	MClientID    string      `json:"m_client_id"`
	MCreatedBy   string      `json:"m_created_by"`
	ClientID     int         `json:"client_id"`
	Title        string      `json:"title"`
	Notes        string      `json:"notes"`
	Description  string      `json:"description"`
	BillingDate  time.Time   `json:"billing_date"`
	Amount       int64       `json:"amount"`
	Balance      int64       `json:"balance"`
	Currency     string      `json:"currency"`
	BillingType  string      `json:"billing_type"`
	ServiceFee   *ServiceFee `json:"service_fee,omitempty"`
	BillNumber   string      `json:"bill_number"`
	BankCodeID   string      `json:"bank_code_id"`
	BankCode     string      `json:"bank_code"`
	MerchantName string      `json:"merchant_name"`
	Status       string      `json:"status"`
	IsIpn        int         `json:"is_ipn"`
	IsActive     int         `json:"is_active"`
	UpdatedBy    string      `json:"updated_by"`
	ApprovedBy   string      `json:"approved_by"`
	ApprovedAt   time.Time   `json:"approved_at"`
	ClientName   string      `json:"client_name"`
	ApprovedUser string      `json:"approved_user,omitempty"`
}

type DataListBillingResponse struct {
	DataResponse struct {
		Data   []BillingDataResponse `json:"data"`
		Msg    string                `json:"msg"`
		Status string                `json:"status"`
		Total  int                   `json:"total"`
	} `json:"data"`
}

type DataBillingResponse struct {
	DataResponse struct {
		Data   BillingDataResponse `json:"data"`
		Status string              `json:"status"`
	} `json:"data"`
}

type BillingDataTable struct {
	ID           string              `json:"id"`
	AdvertiserID int                 `json:"advertiser_id"`
	Description  string              `json:"description"`
	Amount       int64               `json:"amount"`
	Balance      int64               `json:"balance"`
	Status       int                 `json:"status"`
	Platform     string              `json:"platform"`
	MClientID    string              `json:"m_client_id"`
	MCreatedBy   string              `json:"m_created_by"`
	ClientID     int                 `json:"client_id"`
	Title        string              `json:"title"`
	Notes        string              `json:"notes"`
	BillingDate  time.Time           `json:"billing_date"`
	Currency     string              `json:"currency"`
	BillingType  string              `json:"billing_type"`
	ServiceFee   *ServiceFee         `json:"service_fee,omitempty"`
	BillNumber   string              `json:"bill_number"`
	BankCodeID   *string             `json:"bank_code_id,omitempty"`
	BankCode     *string             `json:"bank_code,omitempty"`
	MerchantName *string             `json:"merchant_name,omitempty"`
	IsIpn        *int                `json:"is_ipn,omitempty"`
	IsActive     int                 `json:"is_active"`
	Ordering     int64               `json:"ordering"`
	CreatedBy    primitive.ObjectID  `json:"created_by"`
	UpdatedBy    *primitive.ObjectID `json:"updated_by,omitempty"`
	ApprovedBy   *primitive.ObjectID `json:"approved_by,omitempty"`
	CreatedAt    string              `json:"created_at"`
	UpdatedAt    *string             `json:"updated_at,omitempty"`
	ApprovedAt   *string             `json:"approved_at,omitempty"`
	ClientName   string              `json:"client_name"`
	IsConfirm    string              `json:"is_confirm"`
	ApprovedUser *string             `json:"approved_user,omitempty"`
}

type ServiceFee struct {
	ManagementFee int `json:"management_fee,omitempty"`
	OtherFee      int `json:"other_fee,omitempty"`
	PlatformFee   int `json:"platform_fee,omitempty"`
	TechnicalFee  int `json:"technical_fee,omitempty"`
}

func GetDataListBillingResponse(
	data []BillingDataResponse,
	users *[]userEntity.UserEntity,
	clients []*entity.ClientEntity,
	roleGroup string,
) *[]BillingDataTable {
	userMapping := make(map[string]userEntity.UserEntity, len(*users))
	for _, user := range *users {
		userMapping[user.ID.Hex()] = user
	}

	clientMapping := make(map[string]entity.ClientEntity, len(clients))
	for _, client := range clients {
		clientMapping[client.ID.Hex()] = *client
	}

	var result = make([]BillingDataTable, len(data))
	for i, v := range data {
		result[i] = mapperDataTable(v, userMapping, clientMapping, roleGroup)
	}

	return &result
}

func GetDataBillingResponse(
	data BillingDataResponse,
	users *[]userEntity.UserEntity,
	client *entity.ClientEntity,
	roleGroup string,
) *BillingDataTable {
	userMapping := make(map[string]userEntity.UserEntity, len(*users))
	for _, user := range *users {
		userMapping[user.ID.Hex()] = user
	}

	var clientMapping = make(map[string]entity.ClientEntity, 1)
	clientMapping[client.ID.Hex()] = *client
	result := mapperDataTable(data, userMapping, clientMapping, roleGroup)

	return &result
}

func mapperDataTable(
	r BillingDataResponse,
	userMapping map[string]userEntity.UserEntity,
	clientMapping map[string]entity.ClientEntity,
	roleGroup string,
) BillingDataTable {
	createdAt := utils.FormatTimeToString(r.CreatedAt, goconst.YYYY_MM_DD_HH_MM_SS)
	updatedAt := utils.FormatTimeToString(r.UpdatedAt, goconst.YYYY_MM_DD_HH_MM_SS)
	approvedAt := utils.FormatTimeToString(r.ApprovedAt, goconst.YYYY_MM_DD_HH_MM_SS)
	data := BillingDataTable{
		ID:           r.ID,
		AdvertiserID: r.AdvertiserID,
		ClientID:     r.ClientID,
		MClientID:    r.MClientID,
		ClientName:   clientMapping[r.MClientID].Name,
		Title:        r.Title,
		Description:  r.Description,
		Amount:       r.Amount,
		Balance:      r.Balance,
		BillingType:  r.BillingType,
		IsConfirm:    getStatusWebhookConfirm(r.IsIpn),
		Platform:     r.Platform,
		Notes:        r.Notes,
		BillingDate:  r.BillingDate,
		Currency:     r.Currency,
		BillNumber:   r.BillNumber,
		MCreatedBy:   r.MCreatedBy,
		Status:       getStatusBilling(r.Status),
		CreatedAt:    createdAt,
		UpdatedAt:    &updatedAt,
		ApprovedAt:   &approvedAt,
		IsIpn:        &r.IsIpn,
	}

	if roleGroup == admconst.ROLE_GROUP_CLIENT {
		return data
	}

	approvedUser := ""
	data.ApprovedUser = &approvedUser
	if !userMapping[r.ApprovedBy].ID.IsZero() {
		approvedUser = userMapping[r.ApprovedBy].FirstName + " " + userMapping[r.ApprovedBy].LastName
		data.ApprovedUser = &approvedUser
	}

	if roleGroup == admconst.ROLE_GROUP_ACCOUNTANT {
		return data
	}

	data.ServiceFee = r.ServiceFee
	data.MerchantName = &r.MerchantName
	data.MClientID = r.MClientID
	data.BankCodeID = &r.BankCodeID
	data.BankCode = &r.BankCode
	data.IsActive = r.IsActive

	return data
}

func getStatusBilling(status string) int {
	switch status {
	case consts.BILLING_STATUS_CREATE_PAYMENT:
		return consts.BILLING_STATUS_CREATE_PAYMENT_ID
	case consts.BILLING_STATUS_CHECKING:
		return consts.BILLING_STATUS_CHECKING_ID
	case consts.BILLING_STATUS_COMPLETED:
		return consts.BILLING_STATUS_COMPLETED_ID
	case consts.BILLING_STATUS_FAILED:
		return consts.BILLING_STATUS_FAILED_ID
	}

	return consts.BILLING_STATUS_CREATE_PAYMENT_ID
}

func getStatusWebhookConfirm(status int) string {
	switch status {
	case 0:
		return "Not Yet"
	case 1:
		return "Success"
	case 2:
		return "Failed"
	}
	return "No Support"
}

func (b BillingDataTable) GetStatusLabelHtml() string {
	switch b.Status {
	case consts.BILLING_STATUS_CREATE_PAYMENT_ID:
		return `<span class="badge badge-gradient-primary">Create</span>`
	case consts.BILLING_STATUS_CHECKING_ID:
		return `<span class="badge badge-gradient-info">Checking</span>`
	case consts.BILLING_STATUS_COMPLETED_ID:
		return `<span class="badge badge-gradient-success">Completed</span>`
	case consts.BILLING_STATUS_FAILED_ID:
		return `<span class="badge badge-gradient-danger">Failed</span>`
	default:
		return `<span class="badge badge-gradient-secondary">---</span>`
	}
}

func (b BillingDataTable) GetActiveLabelHtml() string {
	switch b.IsActive {
	case 0:
		return `<span class="badge text-uppercase badge-soft-primary">Inactive</span>`
	case 1:
		return `<span class="badge text-uppercase badge-soft-success">Active</span>`
	default:
		return `<span class="badge badge-gradient-secondary">---</span>`
	}
}

func (b BillingDataTable) GetIpnLabelHtml() string {
	isIpn := *b.IsIpn

	switch isIpn {
	case 0:
		return `<span class="badge text-uppercase badge-soft-primary">Not Yet</span>`
	case 1:
		return `<span class="badge text-uppercase badge-soft-success">Success</span>`
	case 2:
		return `<span class="badge text-uppercase badge-soft-danger">Failed</span>`
	default:
		return `<span class="badge badge-gradient-secondary">---</span>`
	}
}

func (b BillingDataTable) IsCheckingStatus() bool {
	return b.Status == consts.BILLING_STATUS_CHECKING_ID
}
