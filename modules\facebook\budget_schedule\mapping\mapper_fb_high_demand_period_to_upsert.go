package mapping

import (
	"fmt"
	v20 "godsp/pkg/fb-marketing/marketing/v20"
	"time"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

func MapperHighDemandPeriodFBUpsert(period *v20.HighDemandPeriod, userId primitive.ObjectID, campaignId string) bson.M {
	now := time.Now()
	updateData := bson.M{
		"campaign_id":           campaignId,
		"high_demand_period_id": period.ID,
		"budget_value":          period.BudgetValue,
		"budget_value_type":     period.BudgetValueType,
		"recurrence_type":       period.RecurrenceType,
		"time_start":            time.Time(period.TimeStart),
		"time_end":              time.Time(period.TimeEnd),
		"updated_by":            userId,
		"updated_at":            now,
	}
	fmt.Printf("\n -----------  userIde ----------- %+v \n", userId)

	adsetSetOnInsert := bson.M{
		"created_by": userId,
		"created_at": now,
	}

	return bson.M{
		"$set":         updateData,
		"$setOnInsert": adsetSetOnInsert,
		// "$addToSet":    []bson.M{{"list_user_ids": userId}},
		"$addToSet": bson.M{"list_user_ids": userId},
	}
}

// utils.ConvertIntToTime(period.TimeStart),
// 		"time_end":              utils.ConvertIntToTime(period.TimeEnd),
