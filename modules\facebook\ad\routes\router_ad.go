package routes

import (
	"github.com/dev-networldasia/dspgos/sctx"

	"github.com/gofiber/fiber/v2"
)

func SetupRoutesAd(app *fiber.App, serviceCtx sctx.ServiceContext, midds ...fiber.Handler) {
	group := app.Group("dsp/facebook/ads")
	{
		if len(midds) > 0 {
			for _, midd := range midds {
				group.Use(midd)
			}
		}

		comPage := ComposerAdsService(serviceCtx)
		group.Get("/list", comPage.ListAdsHdl()).Name("fb.ad.list")
		group.Get("/edit", comPage.ListAdsHdl()).Name("fb.ad.edit")
	}

	apiGroup := app.Group("/dsp/facebook/api/ads")
	{
		if len(midds) > 0 {
			for _, midd := range midds {
				apiGroup.Use(midd)
			}
		}
		compApi := ComposerAdApiService(serviceCtx)
		apiGroup.Patch("/reload", compApi.ReloadAdApi()).Name("fb.api.ad.reload")
		apiGroup.Delete("/delete", compApi.DeleteAdApi()).Name("fb.api.ad.delete")
		apiGroup.Post("/create", compApi.CreateAdApi()).Name("fb.api.ad.create")
		apiGroup.Post("/list-datatable", compApi.ListAdDataTableApi()).Name("fb.api.ad.list_datatable")
		apiGroup.Post("/:ad_id", compApi.GetDetailAdApi()).Name("fb.api.ad.detail")
		apiGroup.Patch("/update", compApi.UpdateAdApi()).Name("fb.api.ad.update")
		apiGroup.Put("/update", compApi.UpdateNameStatusAdApi()).Name("fb.api.ad.updateNameStatus")
	}
}
