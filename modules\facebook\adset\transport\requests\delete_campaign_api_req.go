package requests

import (
	"godsp/modules/facebook/adset/common/errs"

	"github.com/go-playground/validator/v10"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

type DeleteAdsetReq struct {
	AdsetIDs []string           `json:"adset_ids" validate:"required,dive,numeric,gt=0"`
	UserId   primitive.ObjectID `json:"-"`
}

func (req *DeleteAdsetReq) Validate() []string {
	validate := validator.New()
	var validationErrors []string

	err := validate.Struct(req)

	if err != nil {
		for _, err := range err.(validator.ValidationErrors) {
			switch err.Field() {
			case "AdsetID":
				validationErrors = append(validationErrors, errs.ErrAdsetID.Error())
			}
		}
	}
	return validationErrors
}
