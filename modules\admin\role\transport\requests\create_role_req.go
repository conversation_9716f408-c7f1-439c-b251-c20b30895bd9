package requests

import (
	"godsp/modules/admin/common/admerrs"
	"godsp/modules/admin/common/admrules"
	"godsp/modules/admin/role/common/errs"
	"strings"

	"github.com/go-playground/validator/v10"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

type PayloadRoleCreation struct {
	Name     string             `json:"name" validate:"required"`
	RoleName string             `json:"role_name" validate:"required"`
	Status   string             `json:"status" validate:"required,ruleStatusRole"`
	Ordering int64              `json:"ordering" validate:"required,gt=0"`
	UserID   primitive.ObjectID `json:"-"`
}

func (req *PayloadRoleCreation) Validate() []*string {
	validate := validator.New()
	var validationErrors []*string
	validate.RegisterValidation("ruleStatusRole", admrules.RuleStatusCreate)
	err := validate.Struct(req)
	if err != nil {
		for _, err := range err.(validator.ValidationErrors) {
			switch err.Field() {
			case "Name":
				errName := errs.ErrNameRoleValidate.Error()
				validationErrors = append(validationErrors, &errName)
			case "RoleName":
				errMsg := errs.ErrRoleNameRoleValidate.Error()
				validationErrors = append(validationErrors, &errMsg)
			case "Status":
				errStatus := admerrs.ErrStatusNotFound.Error()
				validationErrors = append(validationErrors, &errStatus)
			case "Ordering":
				errStatus := errs.ErrOrderingRoleValidate.Error()
				validationErrors = append(validationErrors, &errStatus)
			}
		}
	}
	if validationErrors == nil {
		req.Name = strings.TrimSpace(req.Name)
		req.RoleName = strings.TrimSpace(req.RoleName)
	}
	return validationErrors
}
