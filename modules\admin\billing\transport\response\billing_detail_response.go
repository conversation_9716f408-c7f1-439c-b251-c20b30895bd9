package response

import (
	"go.mongodb.org/mongo-driver/bson/primitive"
	clientEntity "godsp/modules/admin/client/entity"
	"godsp/modules/admin/common/admconst"
	userEntity "godsp/modules/admin/user/entity"
	adAccountEntity "godsp/modules/facebook/ad_account/entity"
	"godsp/pkg/gos/goconst"
	"godsp/pkg/gos/utils"
	"strconv"
	"time"
)

type BillingDetailDataResponse struct {
	ID             int       `json:"id"`
	AdvertiserID   int       `json:"advertiser_id"`
	AdvertiserName string    `json:"advertiser_name"`
	ClientID       string    `json:"client_id"`
	ClientName     string    `json:"client_name"`
	Platform       string    `json:"platform"`
	BillingTitle   string    `json:"billing_title"`
	BillNumber     string    `json:"bill_number"`
	Notes          string    `json:"notes"`
	Amount         int64     `json:"amount"`
	Balance        int64     `json:"balance"`
	BillingDate    time.Time `json:"billing_date"`
	Status         string    `json:"status"`
	IsActive       int       `json:"is_active"`
	CreatedAt      time.Time `json:"created_at"`
	UpdatedAt      time.Time `json:"updated_at"`
	ApprovedAt     time.Time `json:"approved_at"`
	CreatedBy      string    `json:"created_by"`
	UpdatedBy      string    `json:"updated_by"`
	ApprovedBy     string    `json:"approved_by"`
}

type DataListBillingDetailResponse struct {
	DataResponse struct {
		Data   []BillingDetailDataResponse `json:"data"`
		Msg    string                      `json:"msg"`
		Status string                      `json:"status"`
		Total  int                         `json:"total"`
	} `json:"data"`
}

type BillingDetailDataTable struct {
	ID             int                 `json:"id"`
	AdvertiserID   int                 `json:"advertiser_id"`
	ClientID       string              `json:"client_id"`
	BillNumber     string              `json:"bill_number"`
	Title          string              `json:"title"`
	Amount         int64               `json:"amount"`
	Balance        int64               `json:"balance"`
	Notes          string              `json:"notes"`
	Status         string              `json:"status"`
	Platform       string              `json:"platform"`
	BillingDate    time.Time           `json:"billing_date"`
	IsActive       int                 `json:"is_active"`
	CreatedAt      string              `json:"created_at"`
	UpdatedAt      *string             `json:"updated_at,omitempty"`
	ApprovedAt     *string             `json:"approved_at,omitempty"`
	CreatedBy      primitive.ObjectID  `json:"created_by"`
	UpdatedBy      *primitive.ObjectID `json:"updated_by,omitempty"`
	ApprovedBy     *primitive.ObjectID `json:"approved_by,omitempty"`
	ApprovedUser   *string             `json:"approved_user,omitempty"`
	AdvertiserName *string             `json:"advertiser_name,omitempty"`
}

func GetDataBillingDetailResponse(
	data []BillingDetailDataResponse,
	users *[]userEntity.UserEntity,
	adAccounts []*adAccountEntity.AdAccountEntity,
	clients []*clientEntity.ClientEntity,
	roleGroup string,
) *[]BillingDetailDataTable {
	clientMapping := make(map[string]clientEntity.ClientEntity, len(clients))
	for _, client := range clients {
		clientMapping[client.ID.Hex()] = *client
	}

	adAccountMapping := make(map[string]adAccountEntity.AdAccountEntity, len(adAccounts))
	for _, adAccount := range adAccounts {
		adAccountMapping[adAccount.AccountID] = *adAccount
	}

	userMapping := make(map[string]userEntity.UserEntity, len(*users))
	for _, user := range *users {
		userMapping[user.ID.Hex()] = user
	}

	var result = make([]BillingDetailDataTable, len(data))
	for i, v := range data {
		result[i] = mapperBillingDetailDataTable(v, userMapping, adAccountMapping, clientMapping, roleGroup)
	}

	return &result
}

func mapperBillingDetailDataTable(
	r BillingDetailDataResponse,
	userMapping map[string]userEntity.UserEntity,
	adAccountMapping map[string]adAccountEntity.AdAccountEntity,
	clientMapping map[string]clientEntity.ClientEntity,
	roleGroup string,
) BillingDetailDataTable {
	createdAt := utils.FormatTimeToString(r.CreatedAt, goconst.YYYY_MM_DD_HH_MM_SS)
	updatedAt := utils.FormatTimeToString(r.UpdatedAt, goconst.YYYY_MM_DD_HH_MM_SS)
	approvedAt := utils.FormatTimeToString(r.ApprovedAt, goconst.YYYY_MM_DD_HH_MM_SS)
	advertiserName := ""
	if adAccount, ok := adAccountMapping[strconv.Itoa(r.AdvertiserID)]; ok {
		advertiserName = adAccount.Name
	}

	data := BillingDetailDataTable{
		ID:             r.ID,
		AdvertiserID:   r.AdvertiserID,
		AdvertiserName: &advertiserName,
		ClientID:       r.ClientID,
		Title:          r.BillingTitle,
		Amount:         r.Amount,
		Balance:        r.Balance,
		Platform:       r.Platform,
		Notes:          r.Notes,
		BillingDate:    r.BillingDate,
		BillNumber:     r.BillNumber,
		Status:         r.Status,
		CreatedAt:      createdAt,
		UpdatedAt:      &updatedAt,
		ApprovedAt:     &approvedAt,
	}

	if roleGroup == admconst.ROLE_GROUP_CLIENT {
		return data
	}

	approvedUser := ""
	data.ApprovedUser = &approvedUser
	if !userMapping[r.ApprovedBy].ID.IsZero() {
		approvedUser = userMapping[r.ApprovedBy].FirstName + " " + userMapping[r.ApprovedBy].LastName
		data.ApprovedUser = &approvedUser
	}

	if roleGroup == admconst.ROLE_GROUP_ACCOUNTANT {
		return data
	}

	data.IsActive = r.IsActive

	return data
}
