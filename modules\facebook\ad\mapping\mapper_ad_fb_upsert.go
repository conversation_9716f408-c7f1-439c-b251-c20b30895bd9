package mapping

import (
	"fmt"
	v20 "godsp/pkg/fb-marketing/marketing/v20"
	"time"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

func MapperAdFBUpsert(ads *v20.Ad, userId primitive.ObjectID) bson.M {
	now := time.Now()
	fmt.Printf("\n -----------  Ad get from FB ----------- %+v \n", ads.TrackingSpecs)

	updateData := bson.M{
		"account_id":             ads.AccountID,
		"campaign_id":            ads.CampaignID,
		"adset_id":               ads.AdsetID,
		"name":                   ads.Name,
		"status":                 ads.Status,
		"tracking_specs":         ads.TrackingSpecs,
		"updated_by":             userId,
		"updated_at":             now,
		"ad_schedule_start_time": (*time.Time)(ads.AdScheduleStartTime),
		"ad_schedule_end_time":   (*time.Time)(ads.AdScheduleEndTime),
	}

	if ads.Creative != nil {
		updateData["creative_id"] = ads.Creative.ID
	}

	createData := bson.M{
		"created_by": userId,
		"created_at": now,
	}

	return bson.M{
		"$set":         updateData,
		"$setOnInsert": createData,
		"$addToSet":    bson.M{"list_user_ids": userId},
	}
}
