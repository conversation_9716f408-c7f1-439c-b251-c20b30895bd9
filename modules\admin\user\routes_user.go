package user

import (
	"context"
	"godsp/modules/admin/user/repository/mongo"
	"godsp/modules/admin/user/routes"

	"github.com/dev-networldasia/dspgos/sctx"
	"github.com/dev-networldasia/dspgos/sctx/component/mongodb"
	"github.com/dev-networldasia/dspgos/sctx/configs"
	"github.com/gofiber/fiber/v2"
	"github.com/gofiber/fiber/v2/middleware/session"
)

// index mongo for user
type composerIndexMongoUser interface {
	CreateUserIndex(ctx context.Context) error
}

func SetupIndexMongoUser(serviceCtx sctx.ServiceContext) composerIndexMongoUser {
	mongoDB := serviceCtx.MustGet(configs.KeyCompMongoDB).(mongodb.MongoComponent).GetDatabase()
	repo := mongo.NewUserRepo(mongoDB)
	return repo
}

func SetupRoutesUser(app *fiber.App, serviceCtx sctx.ServiceContext, store *session.Store, midds ...fiber.Handler) {
	group := app.Group("admins/users")
	{
		if len(midds) > 0 {
			for _, midd := range midds {
				group.Use(midd)
			}
		}

		comp := routes.ComposerUserServive(serviceCtx)
		group.Get("/list", comp.ListUserHdl(store)).Name("admins.users.list")
		group.Get("/edit/:id", comp.EditUserHdl(store)).Name("admins.users.edit")
		group.Get("/profile/:id", comp.UserProfileHdl(store)).Name("admins.users.profile")

	}

	apiGroup := app.Group("api/admins/users")
	{
		if len(midds) > 0 {
			for _, midd := range midds {
				apiGroup.Use(midd)
			}
		}
		compApi := routes.ComposerUserApiServive(serviceCtx)
		apiGroup.Post("/user", compApi.GetUserByIDApi()).Name("admins.api.users.user_api")
		apiGroup.Post("/create", compApi.CreateUserApi()).Name("admins.api.users.create_api")
		apiGroup.Put("/update", compApi.UpdateUserBsonApi()).Name("admins.api.users.update_api")
		apiGroup.Put("/edit", compApi.UpdateUserApi()).Name("admins.api.users.update_api")
		apiGroup.Post("/list", compApi.ListUserAdminApi()).Name("admins.api.users.list_user_api")
		apiGroup.Post("/list-datatable", compApi.ListUserApi()).Name("admins.api.users.list-datatable")
	}
}
