package mongo

import (
	"context"
	"errors"

	"godsp/modules/admin/permission/entity"
	"godsp/modules/facebook/common/fberrs"
	"godsp/pkg/sctx/core"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
)

type permissionRepo struct {
	DB         *mongo.Database
	Collection *mongo.Collection
}

func NewPermissionRepo(DB *mongo.Database) *permissionRepo {
	return &permissionRepo{
		DB:         DB,
		Collection: DB.Collection(entity.PermissionEntity{}.CollectionName()),
	}
}

/**
 * Create index
 */
func (r *permissionRepo) CreatePermissionIndex(ctx context.Context) error {
	cursor, err := r.Collection.Indexes().List(ctx)
	if err != nil {
		return err
	}
	defer cursor.Close(ctx)

	for cursor.Next(ctx) {
		var index bson.M
		if err := cursor.Decode(&index); err != nil {
			return err
		}
		if indexName, ok := index["name"].(string); ok && indexName == "permission_methob_path_unique_index" {
			return nil
		}
	}

	// methob path asc
	indexModel := mongo.IndexModel{
		Keys: bson.D{
			{Key: "method", Value: 1},
			{Key: "path", Value: 1},
		},
		Options: options.Index().SetUnique(true).SetName("permission_methob_path_unique_index"),
	}
	_, err = r.Collection.Indexes().CreateOne(ctx, indexModel)

	return err
}

/**
 * FindOne permission
 */
func (r *permissionRepo) FindOnePermissionRepo(ctx context.Context, filter interface{}, opts ...*options.FindOneOptions) (*entity.PermissionEntity, error) {
	var roleEntity entity.PermissionEntity
	err := r.Collection.FindOne(ctx, filter, opts...).Decode(&roleEntity)
	if errors.Is(err, mongo.ErrNoDocuments) {
		return nil, core.ErrNotFound
	}

	return &roleEntity, nil
}

/**
 * Find permissions
 */
func (r *permissionRepo) FindPermissionRepo(ctx context.Context, filter interface{}, opts ...*options.FindOptions) (*[]entity.PermissionEntity, error) {
	var permissions []entity.PermissionEntity

	cursor, err := r.Collection.Find(ctx, filter, opts...)
	if err != nil {
		return nil, err
	}
	defer cursor.Close(ctx)

	if cursor == nil {
		return nil, fberrs.ErrNilCursorValue
	}
	err = cursor.All(ctx, &permissions)
	if err != nil {
		return nil, err
	}

	return &permissions, nil
}

/**
 * Find Permissions with pipeline FindWithPipelinePermissionRepo
 */
func (r *permissionRepo) FindWithPipelinePermissionRepo(ctx context.Context, pipeline []bson.M, opts ...*options.AggregateOptions) (*[]entity.PermissionEntity, error) {
	var permissions []entity.PermissionEntity

	cursor, err := r.Collection.Aggregate(ctx, pipeline, opts...)
	if err != nil {
		return nil, err
	}
	defer cursor.Close(ctx)

	if cursor == nil {
		return nil, fberrs.ErrNilCursorValue
	}

	if err = cursor.All(ctx, &permissions); err != nil {
		return nil, err
	}

	return &permissions, nil
}

/** Upsert Ad Account */
func (r *permissionRepo) UpsertPermissionRepo(ctx context.Context, filter bson.M, update bson.M) error {
	opts := options.Update().SetUpsert(true)
	_, err := r.Collection.UpdateOne(ctx, filter, update, opts)
	if err != nil {
		return err
	}

	return nil
}

func (r *permissionRepo) UpsertPermissionsRepo(ctx context.Context, permissions []mongo.WriteModel) error {
	opts := options.BulkWrite().SetOrdered(false)
	_, err := r.Collection.BulkWrite(ctx, permissions, opts)
	if err != nil {
		return err
	}

	return nil
}

func (r *permissionRepo) GetMaxRefreshID(ctx context.Context) (int64, error) {
	pipeline := []bson.M{
		{
			"$group": bson.M{
				"_id": nil,
				"maxRefreshID": bson.M{
					"$max": "refresh_id",
				},
			},
		},
	}

	cursor, err := r.Collection.Aggregate(ctx, pipeline)
	if err != nil {
		return 0, err
	}
	defer cursor.Close(ctx)

	var result []bson.M
	if err := cursor.All(ctx, &result); err != nil {
		return 0, err
	}

	if len(result) > 0 {
		if maxValue, ok := result[0]["maxRefreshID"].(int64); ok {
			return maxValue, nil
		}
	}

	return 0, nil
}

/***
 * update one filter interface{}, update interface{}, opts ...*options.UpdateOptions
 */
func (r *permissionRepo) UpdateOnePermissionRepo(ctx context.Context, filter interface{}, permission *entity.PermissionEntity, opts ...*options.UpdateOptions) error {
	update := bson.M{
		"$set": *permission,
	}
	_, err := r.Collection.UpdateOne(ctx, filter, update)
	if err != nil {
		if mongo.IsDuplicateKeyError(err) {
			return core.ErrNotFound
		}
		return err
	}

	return nil
}

/***
 * update filter interface{}, update interface{}, opts ...*options.UpdateOptions
 */
func (r *permissionRepo) UpdatePermissionRepo(ctx context.Context, filter interface{}, permission *entity.PermissionUpdate, opts ...*options.UpdateOptions) error {
	update := bson.M{
		"$set": *permission,
	}
	_, err := r.Collection.UpdateOne(ctx, filter, update)
	if err != nil {
		if mongo.IsDuplicateKeyError(err) {
			return core.ErrNotFound
		}
		return err
	}

	return nil
}

/**
 * delete many
 */
func (r *permissionRepo) DeleteManyPermissionRepo(ctx context.Context, filter interface{}, opts ...*options.DeleteOptions) (*int64, error) {
	deleteResult, err := r.Collection.DeleteMany(ctx, filter, opts...)
	if err != nil {
		return nil, err
	}

	return &deleteResult.DeletedCount, nil
}
