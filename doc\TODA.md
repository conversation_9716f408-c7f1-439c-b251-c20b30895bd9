- add [`airflow`, `apache spark`, `redis`, `mongodb`] into [application] => add preprocessed data
    => [`raw_data`(data added date by date), `process_data`(data sum(ad_id))]
- integrate api-history-date for [`dv360`]

=> next optimized
[response](/error.jpg)
- integrated `redis` for retrieve if ad_account block => add upsert function to overide the old data record saved


- re-index mongodb [`report_detail`, `fb_report_detail`, `reports`, `fb_campaigns_setting`] for unique data
    + report_detail: `["advertiser": 1,"insertion_order": 1,"creative_id": 1,"line_item": 1,"date": 1]`, is_unique==True
    + fb_report_detail: `["account_id": 1,"campaign_id": 1,"adset_id": 1,"ad_id": 1,"date": 1]`, is_unique==True
    + reports: `["query_id", "report_date]`, is_unique==True
    + fb_campaigns_setting: `["ad_account_id", "campaign_id"]`, is_unique==True

I. ADD REDIS:
    - `schedule job`(cron job): call api get report data `[dv360, fb]` `everyday`
    - `manual job`(command): `manual job` import data `[dv360, fb]`

=> add event driven architect into data processing pipeline:
    + publish event
    + subscribe event


FILTER AD ACCOUNT MANUAL:
*************** - kplus - 10624
**************** - 03 maybi - 9913
***************



** INDEX:
    report_detail: [('line_item', 1), ('date', 1)], [('creative_id', 1), ('date', 1)]
    fb_report_detail: [('adset_id', 1), ('date', 1)], [('ad_id', 1), ('date', 1)]



** GET MORE METRIC INSIGHTS REPORT FB  <= `DONE`
- `ThruPlays metric` => add `video_thruplay_watched_actions` into current `query_builder`:

`act_****************/insights?level=ad&filtering=%5B%7B'field':'ad.impressions','operator':'GREATER_THAN','value':0%7D%5D&limit=100&transport=cors&time_range=%7B'since':'2023-02-18','until':'2023-02-18'%7D&fields=account_currency,account_id,account_name,action_values,actions,ad_click_actions,ad_id,ad_impression_actions,ad_name,adset_id,adset_name,age_targeting,attribution_setting,auction_bid,auction_competitiveness,auction_max_competitor_bid,buying_type,campaign_id,campaign_name,canvas_avg_view_percent,canvas_avg_view_time,catalog_segment_actions,catalog_segment_value,catalog_segment_value_mobile_purchase_roas,catalog_segment_value_omni_purchase_roas,catalog_segment_value_website_purchase_roas,clicks,conversion_values,conversions,converted_product_quantity,converted_product_value,cost_per_15_sec_video_view,cost_per_2_sec_continuous_video_view,cost_per_action_type,cost_per_ad_click,cost_per_conversion,cost_per_dda_countby_convs,cost_per_inline_link_click,cost_per_inline_post_engagement,cost_per_one_thousand_ad_impression,cost_per_outbound_click,cost_per_thruplay,cost_per_unique_action_type,cost_per_unique_click,cpc,cpm,cpp,created_time,ctr,date_start,date_stop,dda_countby_convs,dda_results,estimated_ad_recall_rate_lower_bound,estimated_ad_recall_rate_upper_bound,estimated_ad_recallers_lower_bound,estimated_ad_recallers_upper_bound,frequency,full_view_impressions,full_view_reach,gender_targeting,impressions,inline_link_click_ctr,inline_link_clicks,inline_post_engagement,instant_experience_clicks_to_open,instant_experience_clicks_to_start,instant_experience_outbound_clicks,interactive_component_tap,labels,location,mobile_app_purchase_roas,objective,optimization_goal,outbound_clicks,outbound_clicks_ctr,place_page_name,purchase_roas,qualifying_question_qualify_answer_rate,reach,social_spend,spend,updated_time,video_30_sec_watched_actions,video_avg_time_watched_actions,video_continuous_2_sec_watched_actions,video_p100_watched_actions,video_p25_watched_actions,video_p50_watched_actions,video_p75_watched_actions,video_p95_watched_actions,video_play_actions,video_play_curve_actions,video_play_retention_0_to_15s_actions,video_play_retention_20_to_60s_actions,video_play_retention_graph_actions,video_time_watched_actions,website_ctr,website_purchase_roas,wish_bid,video_thruplay_watched_actions&access_token=EAAHCiyPr6yoBAAChBzVZAhCQI3UNWAf2jQeW8IHjpVtdspS2SXvzeBFZBjFOW2jlUQaVL10q1f26zIWOaZAi4KbYeKrwXViA4yt4AHKnkEtGpcOVUS0OrQywy9WeMhCU2rLes7WPAZC2yOIzSGmCZBxZBEpP3HMV5NHUqz600xrsjOm6YGoOza`
    ** ref: [ThruPlays metric](https://developers.facebook.com/docs/marketing-api/reference/ad-report-run/insights/)

- `Mobile app installs`: includes in `actions` parameters in `query_builder`: `actions:omni_app_install` elems in `actions` array
    ** ref: [Mobile app installs](https://developers.facebook.com/docs/marketing-api/reference/ads-action-stats/)

- `Leads`: includes in `actions` paramters in `offsite_conversion.fb_pixel_lead` elems
    ** ref: [Mobile app installs](https://developers.facebook.com/docs/marketing-api/reference/ads-action-stats/)


- `Messaging conversations started`: in `actions` params in `onsite_conversion.messaging_conversation_started_7d`
    ** ref: [Mobile app installs](https://developers.facebook.com/docs/marketing-api/reference/ads-action-stats/)

- `Link clicks`: in `actions` params in `link_click`:
    ** ref: [Mobile app installs](https://developers.facebook.com/docs/marketing-api/reference/ads-action-stats/)

- `Landing page views`: in `actions` params in `landing_page_view`:
    ** ref: [Mobile app installs](https://developers.facebook.com/docs/marketing-api/reference/ads-action-stats/)

- `Outbound clicks`: in `outbound_clicks` params in `outbound_click`:
    ** ref: [Mobile app installs](https://developers.facebook.com/docs/marketing-api/reference/ads-action-stats/)

- `Conversion` - need confirm laters
    ** ref: [Mobile app installs](https://developers.facebook.com/docs/marketing-api/reference/ads-action-stats/)

- `Post engagements`: in `actions` params in `post_engagement`:
    ** ref: [Mobile app installs](https://developers.facebook.com/docs/marketing-api/reference/ads-action-stats/)

- `Page Likes or followers`: in `actions` params in `like`:
    ** ref: [Mobile app installs](https://developers.facebook.com/docs/marketing-api/reference/ads-action-stats/)



    ******
- to get start_time of campaign, adsets, add `start_time` into query parameters:
`act_409341211262738/campaigns?fields=start_time,name,id,effective_status,budget_remaining,buying_type,created_time,daily_budget,lifetime_budget,objective,pacing_type,status,bid_strategy,adsets{start_time,name,id,ads{name,id,daily_budget,effective_status,adset_schedule,bid_amount,billing_event,budget_remaining,created_time,end_time,lifetime_budget,lifetime_imps,pacing_type,status,targeting,adcreatives{id,body,call_to_action_type,image_url,name,object_id,object_store_url,object_story_id,object_type,status,thumbnail_url,thumbnail_id,title,video_id}}}`



** clean data fb api: nested array field type: [null, NaN] <= clean this values



**NOTE**
IMPORTANT: NEED TO RECHECK API GET THRUPLAY FB
- check cron-date: get error account-id fb api get insights, retrieve run separate parameters when errors: `{"error":{"code":1,"message":"Please reduce the amount of data you're asking for, then retry your request"}}`

- re-run with account list miss in date [date, account_id] fb api get insights data


** NOTE **:
 
- ACCOUNT[16.3 Coway - CPAS SHOPEE] - ACCOUNT_ID == ****************  => không có quyền truy cập, không lấy data được từ ngày 2023-02-01
- ACCOUNT[16.1 Coway - CPAS LAZADA] - ACCOUNT_ID == *************** => không có quyền truy cập, không lấy data được từ ngày 2023-02-01
- ACCOUNT[16.2 Coway - CPAS TIKI] - ACCOUNT_ID == *************** => không có quyền truy cập, không lấy data được từ ngày 2023-02-01
- ACCOUNT[16.5 Coway - CPAS Web] - ACCOUNT_ID == *************** => không có quyền truy cập, không lấy data được từ ngày 2023-02-01
- ACCOUNT[16.4 Coway - CPAS LAZADA AUTHORISED] - ACCOUNT_ID == *************** => không có quyền truy cập, không lấy data được từ ngày 2023-02-01


https://stackoverflow.com/questions/********/python-try-except-block-re-raising-exception


TASKS: integrate [api_history_date] into GG
    GG:
        - cron_date: run with [specificed time in date && every date]
            + insert into [api_history_date](account_id, date, status=[error, success], active_ad[0,1], run_date, run_mode[cron,manual], platform=[fb,gg])
            + run get insights [yesterday] for all [ad-account]:
                => api return `success`:
                    => len(data) == 0:
                        + upsert [api_history_date](account_id, date==`today`, status==`success`, active_ad==`0`)
                    => otherwise len(data) > 0
                        + insert `mongodb`[api_history_date] if query api success
                => api return `failed`:
                    + update into [fb_report_date](account_id, date==`today`, status==`error`)
            + scan [api_history_date] to run insights api again => separate params for [account, date] with [status==error]
            + run update campaign/advertisers list into `mysql`[channels_report_advertisers, channels_report_campaigns]
            + run get campaign settings => insert `mongodb`[fb_campaign_settings]

        - run_manual: run in time range [start_date, end_date]
            => same with cron_date
        - update_insights: same with cron_date but update `insights` only




**NOTE: breakdown insights ads into [age,gender] parameters query: **
`act_***************/insights?level=ad&breakdowns=age,gender&filtering=%5B%7B'field':'ad.impressions','operator':'GREATER_THAN','value':0%7D%5D&limit=25&transport=cors&time_range=%7B'since':%20'2023-03-01',%20'until':%20'2023-03-01'%7D&fields=account_currency,account_id,ad_id,adset_id,campaign_id,account_name,action_values,actions,ad_click_actions,ad_impression_actions,ad_name,adset_name,age_targeting,attribution_setting,auction_bid,auction_competitiveness,auction_max_competitor_bid,buying_type,campaign_name,catalog_segment_value_mobile_purchase_roas,catalog_segment_value_omni_purchase_roas,catalog_segment_value_website_purchase_roas,clicks,conversion_values,conversions,converted_product_quantity,converted_product_value,cost_per_15_sec_video_view,cost_per_2_sec_continuous_video_view,cost_per_action_type,cost_per_ad_click,cost_per_conversion,cost_per_dda_countby_convs,cost_per_inline_link_click,cost_per_inline_post_engagement,cost_per_one_thousand_ad_impression,cost_per_outbound_click,cost_per_thruplay,cost_per_unique_action_type,cost_per_unique_click,cpc,cpm,cpp,created_time,ctr,date_start,date_stop,dda_countby_convs,dda_results,estimated_ad_recall_rate_lower_bound,estimated_ad_recall_rate_upper_bound,estimated_ad_recallers_lower_bound,estimated_ad_recallers_upper_bound,frequency,full_view_impressions,full_view_reach,gender_targeting,impressions,inline_link_click_ctr,inline_link_clicks,inline_post_engagement&access_token=EAAHCiyPr6yoBAAChBzVZAhCQI3UNWAf2jQeW8IHjpVtdspS2SXvzeBFZBjFOW2jlUQaVL10q1f26zIWOaZAi4KbYeKrwXViA4yt4AHKnkEtGpcOVUS0OrQywy9WeMhCU2rLes7WPAZC2yOIzSGmCZBxZBEpP3HMV5NHUqz600xrsjOm6YGoOza`
`act_***************/insights?level=ad&breakdowns=region,country&filtering=%5B%7B'field':'ad.impressions','operator':'GREATER_THAN','value':0%7D%5D&limit=100&transport=cors&time_range=%7B'since':%20'2023-03-01',%20'until':%20'2023-03-01'%7D&fields=account_currency,account_id,ad_id,adset_id,campaign_id,account_name,action_values,actions,ad_click_actions,ad_impression_actions,ad_name,adset_name,age_targeting,attribution_setting,auction_bid,auction_competitiveness,auction_max_competitor_bid,buying_type,campaign_name,catalog_segment_value_mobile_purchase_roas,catalog_segment_value_omni_purchase_roas,catalog_segment_value_website_purchase_roas,clicks,conversion_values,conversions,converted_product_quantity,converted_product_value,cost_per_15_sec_video_view,cost_per_2_sec_continuous_video_view,cost_per_action_type,cost_per_ad_click,cost_per_conversion,cost_per_dda_countby_convs,cost_per_inline_link_click,cost_per_inline_post_engagement,cost_per_one_thousand_ad_impression,cost_per_outbound_click,cost_per_thruplay,cost_per_unique_action_type,cost_per_unique_click,cpc,cpm,cpp,created_time,ctr,date_start,date_stop,dda_countby_convs,dda_results,estimated_ad_recall_rate_lower_bound,estimated_ad_recall_rate_upper_bound,estimated_ad_recallers_lower_bound,estimated_ad_recallers_upper_bound,frequency,full_view_impressions,full_view_reach,gender_targeting,impressions,inline_link_click_ctr,inline_link_clicks,inline_post_engagement&access_token=EAAHCiyPr6yoBAAChBzVZAhCQI3UNWAf2jQeW8IHjpVtdspS2SXvzeBFZBjFOW2jlUQaVL10q1f26zIWOaZAi4KbYeKrwXViA4yt4AHKnkEtGpcOVUS0OrQywy9WeMhCU2rLes7WPAZC2yOIzSGmCZBxZBEpP3HMV5NHUqz600xrsjOm6YGoOza`

=> breakdown value: => `DONE`
    breakdowns=[ad_format_asset, age, app_id, body_asset, call_to_action_asset, country, description_asset, gender, image_asset, impression_device, is_conversion_id_modeled, link_url_asset, product_id, region, skan_campaign_id, skan_conversion_id, title_asset, video_asset, dma, frequency_value, hourly_stats_aggregated_by_advertiser_time_zone, hourly_stats_aggregated_by_audience_time_zone, mmm, place_page_id, publisher_platform, platform_position, device_platform]

=> breakdown combine invalid:
{
  "error": {
    "message": "(#100) Current combination of data breakdown columns (action_type, age, device_platform, gender) is invalid ",
    "type": "OAuthException",
    "code": 100,
    "fbtrace_id": "AdNfFLUavOVIncAAwbwEnou"
  }
}


check to re run this 
act_2886************, 2022-10-25
act_2886************, 2022-11-03
act_2886************, 2022-10-27
act_2886************, 2022-10-30
act_2886************, 2022-10-26
act_2886************, 2022-10-29
act_2886************, 2022-10-23
act_***************, 2023-02-25
act_***************, 2023-02-24
act_***************, 2022-06-08
act_***************, 2023-02-26
act_***************, 2023-02-18
act_***************, 2023-03-05
act_***************, 2023-03-10
act_***************, 2023-03-12

**ALL OF FIELDS FACEBOOK ADS**
"account_currency",
"account_id",
"account_name",
"action_values",
"actions",
"ad_id",
"ad_name",
"adset_id",
"adset_name",
"buying_type",
"campaign_id",
"campaign_name",
"canvas_avg_view_percent",
"canvas_avg_view_time",
"catalog_segment_actions",
"catalog_segment_value",
"catalog_segment_value_mobile_purchase_roas",
"catalog_segment_value_omni_purchase_roas",
"clicks",
"conversions",
"converted_product_quantity",
"converted_product_value",
"cost_per_15_sec_video_view",
"cost_per_2_sec_continuous_video_view",
"cost_per_action_type",
"cost_per_conversion",
"cost_per_inline_link_click",
"cost_per_inline_post_engagement",
"cost_per_outbound_click",
"cost_per_thruplay",
"cost_per_unique_action_type",
"cost_per_unique_click",
"cpc",
"cpm",
"cpp",
"created_time",
"ctr",
"date",
"date_start",
"date_stop",
"frequency",
"impressions",
"inline_link_click_ctr",
"inline_link_clicks",
"inline_post_engagement",
"interactive_component_tap",
"mobile_app_purchase_roas",
"objective",
"optimization_goal",
"outbound_clicks",
"outbound_clicks_ctr",
"purchase_roas",
"reach",
"run_date",
"social_spend",
"spend",
"updated_time",
"video_30_sec_watched_actions",
"video_avg_time_watched_actions",
"video_continuous_2_sec_watched_actions",
"video_p100_watched_actions",
"video_p25_watched_actions",
"video_p50_watched_actions",
"video_p75_watched_actions",
"video_p95_watched_actions",
"video_play_actions",
"video_play_curve_actions",
"video_thruplay_watched_actions",
"website_ctr",
"website_purchase_roas",
"wish_bid"

report 360dv: add report audience type
# QueryTargetAudienceReport=1046809598 => `DONE`
# QueryTargetAudienceReport=1073365748 => `DONE`
QueryTargetAudienceReport=1073365458

# DV360 REPORT GG: Location report ID: 1073365748 => `DONE`
# DV360 REPORT GG: Audience (age, gender): 1073365458 => `DONE`

- custom client dv360 report fields:
    "billable_impressions",
    "clicks",
    "ctr",
    "engagement_rate",
    "engagements",
    "impressions",
    "last_clicks",
    "last_impressions",
    "rich_media_video_completions",
    "total_conversions",
    "trueview_view_rate",
    "trueview_views",


- custom client fb report fields:
        "canvas_avg_view_percent",
        "canvas_avg_view_time",
        "clicks" === "clicks",
        "cost_per_15_sec_video_view",
        "cost_per_2_sec_continuous_video_view",
        "cost_per_action_type",
        "cost_per_conversion",
        "cost_per_inline_link_click",
        "cost_per_inline_post_engagement",
        "cost_per_outbound_click",
        "cost_per_thruplay",
        "cost_per_unique_action_type",
        "cost_per_unique_click",
        "cpc" === [spend/clicks],
        "cpm" === [spend/impressions],
        "cpp" === [spend/total purchases],
        "created_time",
        "ctr" === "ctr" === [(clicks *1000)/impressions],
        "date",
        "date_start",
        "date_stop",
        "frequency" === [impressions/(unique_users saw ad)],
        "impressions" === "impressions",
        "inline_link_click_ctr",
        "inline_link_clicks",
        "inline_post_engagement",
        "interactive_component_tap",
        "objective",
        "optimization_goal",
        "outbound_clicks",
        "outbound_clicks_ctr",
        "purchase_roas",
        "reach",
        "run_date",
        "social_spend",
        "spend" === "revenue_usd",
        "updated_time",
        "video_30_sec_watched_actions",
        "video_avg_time_watched_actions",
        "video_continuous_2_sec_watched_actions",
        "video_p100_watched_actions",
        "video_p25_watched_actions",
        "video_p50_watched_actions",
        "video_p75_watched_actions",
        "video_p95_watched_actions",
        "video_play_actions",
        "video_play_curve_actions",
        "video_thruplay_watched_actions",
        "website_ctr",
        "website_purchase_roas",
        "wish_bid"

**step building api** => `DONE`
- break code into single piece of process: <= break into dag steps, prepare for integrated with `airflow`
    + extract: request from api [dv360, fb, fb_insights[age, gender, location, device insights], dv360_insights[age, gender, location, device insights], tiktok...]
    + quality check => process models object python mapping with result return from api => `check null values`, `convert data type (string numeric => [int, float,..])`
    + transform <= logic process data based on requirement
    + load => save into db[mongo, mysql]: integrated with the previous steps above (`extract <- quality_check <- transform <- load into db`)
- construct project code into modules:
    + DV360
        * api: include [campaign_date, insights[age, gender, location, device]]
        * model
        * common/utils
        * ETL:
            **extract
            **check_quality_data
            **transform
            **load
**next optimized** 
    => build dags for intergrate with airflow => running [batch/schedule] mode [service reports]
    => separate into microservices:
        + service reports
        + service create/edit ads through `display api`



**IMPORTANT:**
- fb insights breakdowns=[age,gender,device_impressions,region,country] return no rows with specificed day`[{since:2023-03-01, until:2023-03-01}]` in range `last_7d` with current day => change into `[{since:2023-03-01, until:2023-03-02}]`, `[{since:2023-03-02, until:2023-03-03}]` if range last 7 days with current date (date=`now`) => save into another collection: `last_7d` for break insights date <= need to check summary metric: `impressions`, `click`, `actions`
=> process [`manual`] mode since start date until last 7 days with `current_date` ( if `end_date == current date`) => the remaining days, calc:
    `{since = date, util: date-1}` => save into last_7days_insights_[age_gender, device, location] for aggregate && re-call later


https://syncwith.com/api/facebook-ads-api
https://resources.fivetran.com/on-demand-webinars/fivetran-databytes-how-to-set-up-a-modern-data-stack#main-content
https://fivetran.com/docs/applications/facebook-ads#missingdata
https://resources.fivetran.com/on-demand-webinars
https://fivetran.com/docs/getting-started/architecture


python fb_app.py --fb_report_type=insights_break_age_gender --insights_start_date=2023-03-02 --insights_end_date=2023-03-02 --run_ad_account=***************



=> **NEXT**: INTEGRATE `[AIRFLOW, SPARK]`
=> USE DATAFRAME TO FILTER [`IMPRESSIONS` > 0] => [LEN(DF) == 0] => [ACTIVE_ADS == 0 && STATUS == 1 && MESSAGE=DATAFRAME EMPTY, RECHECK](API_HISTORY_DATE)


**INITIAL OBSERVER TABLE `manual_data_source_campaign`**
- on create event: => [status==[0, 1], observer==0]
    + process read csv file && insert data from csv into [mongo](manual_tiktok_report_detail, manual_tiktok_report_insights_[age, gender, device, location])
    + update (status==1)[manual_data_source_campaign] for processing(--not done yet)
    + update (status==2)[manual_data_source_campaign] for if process success
    + update (status==3)[manual_data_source_campaign] for if process error
    + extract unique [advertiser, capmaign] from csv file 
        => insert into [channel_report_advertisers, channel_report_campaigns] with [data_source==manual]
        => update ads_plan_id[channel_report_campaigns] == plan_id[manual_data_source_campaign] && (status==1)[channel_report_campaigns] <= mapping campaign-report on `create` event
        => if advertiser_id(csv file exist) => extract [advertiser_id] for insert [channel_report_advertisers] with (status==1)[channel_report_advertisers] for mapping advertiser report

- create webhooks on [cms]/[api-python], listen event change gg sheets file => update [status_observer==1, status!=0](https required)
- OBSERVER EVENT CHANGE:
    + CHANGE ON EVENT_HOOKS GOOGLE SHEETS: => observer [status_observer] event => [file_name] NOT CHANGE
        append data with current data [manual_tiktok_report_detail, manual_tiktok_report_insights_[age, gender, device, location]]
            * update (status==1)[manual_data_source_campaign] for processing(--not done yet)
            * update (status==2)[manual_data_source_campaign] for if process success
            * update (status==3)[manual_data_source_campaign] for if process error
            * extract unique [advertiser, capmaign] from csv file => insert into [channel_report_advertisers, channel_report_campaigns] with [data_source==manual]
            * update [status_observer==0]
    + CHANGE ON FILE_NAME: user has update [file_name] on cms => => observer [file_name] event 
        => change completely data report campaign [status_observer] NOT CHANGE
        * delete all of data exist in [manual_tiktok_report_detail, manual_tiktok_report_insights_[age, gender, device, location]] from:
                    + [ads_advertiser_id(manual_tiktok_report_detail) => ads_plan_id => get list of [campaign_id]channels_report_dampaigns[data_source==manual, ads_plan_id==ads_plan_id]]
        * update (status==0)[channel_report_campaigns] for un-mapping campaign report with ads_plan_id[channel_report_campaigns] == plan_id[manual_data_source_campaign]
        * process as [create_event]
        * update [status_observer==0]




api create ads: [python + react]:
https://testdriven.io/blog/fastapi-react/
https://www.geeksforgeeks.org/how-to-connect-reactjs-with-flask-api/
https://developer.okta.com/blog/2018/12/20/crud-app-with-python-flask-react
https://developers.google.com/display-video/api/reference/rest/v2?hl=en
https://developers.google.com/apis-explorer?hl=vi
https://github.com/googleapis/google-api-python-client/blob/main/docs/start.md
https://googleapis.github.io/google-api-python-client/docs/
https://github.com/googleads/googleads-displayvideo-examples
https://developers.google.com/display-video/api/guides/getting-started/configure?hl=vi


https://developers.google.com/drive/api/guides/manage-changes?hl=vi
https://developers.google.com/sheets/api/quickstart/python?hl=vi


**REF**
- https://developers.google.com/doubleclick-advertisers/libraries
- https://github.com/google/dv360-automation/blob/master/dv360-automation-notebook.ipynb
- https://github.com/googleads/googleads-dfa-reporting-samples/blob/main/python/v4/create_campaign.py
- https://github.com/googleapis/google-api-python-client/tree/main/samples/dfareporting
- https://github.com/googleads/googleads-bidmanager-examples
- https://github.com/googleapis/google-api-python-client/blob/main/docs/dyn/doubleclickbidmanager_v1.reports.html
- https://www.youtube.com/watch?v=oRp-DT2kJV8
- https://github.com/googleads/googleads-dfa-reporting-samples
- https://marketingplatform.google.com/about/campaign-manager-360/
- https://developers.google.com/doubleclick-advertisers/dtv2/overview
- https://support.google.com/campaignmanager/answer/9015629?hl=en
- https://developers.google.com/doubleclick-advertisers/guides/migration
- https://github.com/googleads/googleads-displayvideo-examples/tree/main
- https://groups.google.com/g/google-doubleclick-for-advertisers-api/c/xS7eBdf3Inc/m/jEhwmTd7AQAJ


update index mongo:
- change: fb(fb_report_detail)['campaign_id', 'date'] => ['campaign_id', 'adset_id', 'ad_id', 'date']
- change: gg(report_detail)['insertion_order', 'date'] => ['insertion_order', 'line_item', 'creative_id', 'date']
- remove: gg[report_detail_date]
- add more index into [manual_tiktok] import data table



# https://stackoverflow.com/questions/49677060/pandas-count-empty-strings-in-a-column


**QUERY REACH LEVEL AD**
`act_732339467843183/insights?level=ad&filtering=[{'field':'ad.reach','operator':'GREATER_THAN','value':0},{'field':'campaign.id','operator':'IN','value':["*****************","*****************","*****************","*****************","23***************","*****************","*****************","*****************","*****************","*****************","*****************","*****************","*****************","*****************","*****************","*****************","*****************","*****************","*****************","*****************","*****************","*****************","*****************","*****************","*****************","*****************","*****************"]}]&limit=100&transport=cors&date_preset=maximum&fields=account_id,adset_id,ad_id,campaign_id,account_name,adset_name,campaign_name,frequency,reach&access_token=EAAHCiyPr6yoBAAChBzVZAhCQI3UNWAf2jQeW8IHjpVtdspS2SXvzeBFZBjFOW2jlUQaVL10q1f26zIWOaZAi4KbYeKrwXViA4yt4AHKnkEtGpcOVUS0OrQywy9WeMhCU2rLes7WPAZC2yOIzSGmCZBxZBEpP3HMV5NHUqz600xrsjOm6YGoOza`

act_732339467843183/insights?level=ad&filtering=[{'field':'ad.reach','operator':'GREATER_THAN','value':0},{'field':'campaign.id','operator':'IN','value':["*****************","*****************","*****************","*****************","23***************","*****************","*****************","*****************","*****************","*****************","*****************","*****************","*****************","*****************","*****************","*****************","*****************","*****************","*****************","*****************","*****************","*****************","*****************","*****************","*****************","*****************","*****************"]}]&limit=100&transport=cors&date_preset=maximum&fields=account_id,adset_id,ad_id,campaign_id,account_name,adset_name,campaign_name,frequency,reach&access_token=EAAHCiyPr6yoBAAChBzVZAhCQI3UNWAf2jQeW8IHjpVtdspS2SXvzeBFZBjFOW2jlUQaVL10q1f26zIWOaZAi4KbYeKrwXViA4yt4AHKnkEtGpcOVUS0OrQywy9WeMhCU2rLes7WPAZC2yOIzSGmCZBxZBEpP3HMV5NHUqz600xrsjOm6YGoOza



**Contact email google ads support**
`https://support.google.com/adspolicy/contact/contact_ads_api`


**asyncronous request report when request alot of data**
`https://stackoverflow.com/questions/********/retrieve-async-ads-insights-results-from-fb-ads-api-with-pagination`
`https://developers.facebook.com/support/bugs/***************/`


**portainer**
`https://www.portainer.io/platform-management`
`https://github.com/portainer/portainer`
`https://viblo.asia/p/su-dung-portainer-de-quan-ly-docker-apps-Eb85oGa852G`


`https://datahubproject.io/docs/developers`


=> **calc reach unique by campaign sumary**
`act_4662907423796644/insights?filtering=[{'field':'campaign.id','operator':'IN','value':["120202389965980058","120202659307240058"]}]&limit=100&transport=cors&date_preset=maximum&fields=account_id,reach&access_token=EAAHCiyPr6yoBOZCDOpvHzX9HhEhvzhLhCCZAZBvuJuLB80RE0YlY0JM66G6HfgqP7RYenb4dlZBQmYPg0hCA4zZAyI9RGXhErzSbBgmeLbpaWRCCZAin76bodgmSUwW3GZBTKzZB6ZCyQSq1sNUnDC5WpdV8QWVhuSQug3tlzaZCUDed4DDevPObwvyr3RpGMyEZBYznO2aLrSKuvTIYTAOGwZDZD`

https://spaceads.digital/blog/google-marketing-platforms-dv360-everything-you-need-to-know/


**data transfer**
`https://support.google.com/displayvideo/answer/7315192?hl=en&sjid=7851502388574138883-AP`
`https://developers.google.com/bid-manager/dtv2/overview`
`https://developers.google.com/bid-manager/dtv2/reference/file-format`
`https://developers.google.com/bid-manager/dtv2/migrating`
`https://www.googlecloudcommunity.com/gc/Data-Analytics/How-to-export-DV360-raw-data-to-BigQuery/m-p/621973`
`https://windsor.ai/connectors/google-display-video-360/`
`https://groups.google.com/g/google-doubleclick-for-advertisers-api`



**instant report && update API**
`https://groups.google.com/g/google-doubleclick-for-advertisers-api/c/IqlJWKGT6ME`
`https://support.google.com/displayvideo/answer/14190111?hl=en`
`https://groups.google.com/u/1/g/google-doubleclick-for-advertisers-api/c/xS7eBdf3Inc`



**ECOMMERCE METRICS TRACKING CUCKOO**

`catalog_segment_actions:omni_add_to_cart`: Adds to cart with shared items
`catalog_segment_actions:offsite_conversion.fb_pixel_add_to_cart`: Website adds to cart with shared items
`catalog_segment_actions:app_custom_event.fb_mobile_add_to_cart`: In-app adds to cart with shared items
`catalog_segment_actions:omni_view_content`: Content views with shared items
`catalog_segment_actions:offsite_conversion.fb_pixel_view_content`: Website content views with shared items
`catalog_segment_actions:app_custom_event.fb_mobile_content_view`: In-app content views with shared items
`catalog_segment_actions:omni_purchase`: Purchases with shared items
`catalog_segment_actions:offsite_conversion.fb_pixel_purchase`: Website purchases with shared items
`catalog_segment_actions:app_custom_event.fb_mobile_purchase`: In-app purchases with shared items
`catalog_segment_actions:offline_conversion.purchase`; Offline purchases with shared items


`catalog_segment_value:omni_add_to_cart`: Adds to cart conversion value for shared items only
`catalog_segment_value:offsite_conversion.fb_pixel_add_to_cart`: Website adds to cart conversion value for shared items only
`catalog_segment_value:app_custom_event.fb_mobile_add_to_cart`: In-app adds to cart conversion value for shared items only



`catalog_segment_value_omni_purchase_roas:omni_purchase`: Purchase ROAS for shared items
`catalog_segment_value_website_purchase_roas:offsite_conversion.fb_pixel_purchase`: Website purchase ROAS for shared items only
`catalog_segment_value_mobile_purchase_roas:app_custom_event.fb_mobile_purchase`: Mobile app purchase ROAS for shared items only

