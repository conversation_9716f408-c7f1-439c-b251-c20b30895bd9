package mapping

import (
	"godsp/modules/facebook/audience/entity"
	v20 "godsp/pkg/fb-marketing/marketing/v20"
)

// From Facebook to Entitiy

func MapV20ToEntityLookalikeOrigion(v20Origin []v20.LookalikeOrigion) []entity.LookalikeOrigion {
	result := make([]entity.LookalikeOrigion, len(v20Origin))
	for i, origin := range v20Origin {
		result[i] = entity.LookalikeOrigion{
			ID:   origin.ID,
			Name: origin.Name,
			Type: origin.Type,
		}
	}
	return result
}

func MapV20ToEntityLookalikeSpec(v20Spec *v20.LookalikeSpec) *entity.LookalikeSpec {
	if v20Spec == nil {
		return nil
	}
	return &entity.LookalikeSpec{
		Country: v20Spec.Country,
		Origin:  MapV20ToEntityLookalikeOrigion(v20Spec.Origin),
		Ratio:   v20Spec.Ratio,
		Type:    v20Spec.Type,
	}
}

func MapV20ToEntityDataSource(v20Source *v20.CustomAudienceDataSource) *entity.CustomAudienceDataSource {
	if v20Source == nil {
		return nil
	}
	return &entity.CustomAudienceDataSource{
		CreationParams: v20Source.CreationParams,
		SubType:        v20Source.SubType,
		Type:           v20Source.Type,
	}
}

func MapV20ToEntityStatus(v20Status *v20.CustomAudienceStatus) *entity.CustomAudienceStatus {
	if v20Status == nil {
		return nil
	}
	return &entity.CustomAudienceStatus{
		Code:        v20Status.Code,
		Description: v20Status.Description,
	}
}

func MapV20ToEntityAdaccounts(v20Ad *v20.Adaccounts) *entity.Adaccounts {
	if v20Ad == nil {
		return nil
	}
	return &entity.Adaccounts{
		Data: v20Ad.Data,
	}
}

func MapV20TargetingToTargeting(v20Targeting *v20.Targeting) entity.Targeting {
	return entity.Targeting{
		PublisherPlatforms:          v20Targeting.PublisherPlatforms,
		FacebookPositions:           v20Targeting.FacebookPositions,
		InstagramPositions:          v20Targeting.InstagramPositions,
		AudienceNetworkPositions:    v20Targeting.AudienceNetworkPositions,
		MessengerPositions:          v20Targeting.MessengerPositions,
		AgeMin:                      v20Targeting.AgeMin,
		AgeMax:                      v20Targeting.AgeMax,
		Genders:                     v20Targeting.Genders,
		AppInstallState:             v20Targeting.AppInstallState,
		CustomAudiences:             MapV20IDContainerList(v20Targeting.CustomAudiences),
		ExcludedCustomAudiences:     MapV20IDContainerList(v20Targeting.ExcludedCustomAudiences),
		GeoLocations:                MapV20GeoLocations(v20Targeting.GeoLocations),
		ExcludedGeoLocations:        MapV20GeoLocations(v20Targeting.ExcludedGeoLocations),
		FlexibleSpec:                MapV20FlexibleSpec(v20Targeting.FlexibleSpec),
		Exclusions:                  MapV20FlexibleSpecSingle(v20Targeting.Exclusions),
		DevicePlatforms:             v20Targeting.DevicePlatforms,
		ExcludedPublisherCategories: v20Targeting.ExcludedPublisherCategories,
		Locales:                     v20Targeting.Locales,
		TargetingOptimization:       v20Targeting.TargetingOptimization,
		UserDevice:                  v20Targeting.UserDevice,
		UserOs:                      v20Targeting.UserOs,
		WirelessCarrier:             v20Targeting.WirelessCarrier,
		TargetingRelaxationTypes:    MapV20TargetingRelaxationTypes(v20Targeting.TargetingRelaxationTypes),
	}
}

func MapV20GeoLocations(v20Geo *v20.GeoLocations) *entity.GeoLocations {
	if v20Geo == nil {
		return nil
	}
	return &entity.GeoLocations{
		Countries:          v20Geo.Countries,
		LocationTypes:      v20Geo.LocationTypes,
		Cities:             MapV20Cities(v20Geo.Cities),
		Regions:            MapV20Regions(v20Geo.Regions),
		Zips:               MapV20Zips(v20Geo.Zips),
		Places:             MapV20Places(v20Geo.Places),
		CountryGroups:      v20Geo.CountryGroups,
		SubCities:          MapV20SubCities(v20Geo.SubCities),
		LocationClusterIDs: v20Geo.LocationClusterIDs,
	}
}

func MapV20IDContainerList(v20List []v20.IDContainer) []entity.IDContainer {
	result := make([]entity.IDContainer, len(v20List))
	for i, v := range v20List {
		result[i] = entity.IDContainer{
			ID:   v.ID,
			Name: v.Name,
		}
	}
	return result
}

func MapV20FlexibleSpec(v20Specs []v20.FlexibleSpec) []entity.FlexibleSpec {
	result := make([]entity.FlexibleSpec, len(v20Specs))
	for i, spec := range v20Specs {
		result[i] = entity.FlexibleSpec{
			Interests:            MapV20IDContainerList(spec.Interests),
			Behaviors:            MapV20IDContainerList(spec.Behaviors),
			LifeEvents:           MapV20IDContainerList(spec.LifeEvents),
			WorkEmployers:        MapV20IDContainerList(spec.WorkEmployers),
			FamilyStatuses:       MapV20IDContainerList(spec.FamilyStatuses),
			WorkPositions:        MapV20IDContainerList(spec.WorkPositions),
			Politics:             MapV20IDContainerList(spec.Politics),
			EducationMajors:      MapV20IDContainerList(spec.EducationMajors),
			EducationStatuses:    spec.EducationStatuses,
			RelationshipStatuses: spec.RelationshipStatuses,
		}
	}
	return result
}

func MapV20FlexibleSpecSingle(v20Spec *v20.FlexibleSpec) *entity.FlexibleSpec {
	if v20Spec == nil {
		return nil
	}
	return &entity.FlexibleSpec{
		Interests:            MapV20IDContainerList(v20Spec.Interests),
		Behaviors:            MapV20IDContainerList(v20Spec.Behaviors),
		LifeEvents:           MapV20IDContainerList(v20Spec.LifeEvents),
		WorkEmployers:        MapV20IDContainerList(v20Spec.WorkEmployers),
		FamilyStatuses:       MapV20IDContainerList(v20Spec.FamilyStatuses),
		WorkPositions:        MapV20IDContainerList(v20Spec.WorkPositions),
		Politics:             MapV20IDContainerList(v20Spec.Politics),
		EducationMajors:      MapV20IDContainerList(v20Spec.EducationMajors),
		EducationStatuses:    v20Spec.EducationStatuses,
		RelationshipStatuses: v20Spec.RelationshipStatuses,
	}
}

func MapV20TargetingRelaxationTypes(v20Relaxation v20.TargetingRelaxationTypes) entity.TargetingRelaxationTypes {
	return entity.TargetingRelaxationTypes{
		CustomAudience: v20Relaxation.CustomAudience,
		Lookalike:      v20Relaxation.Lookalike,
	}
}

func MapV20Cities(v20Cities []v20.City) []entity.City {
	result := make([]entity.City, len(v20Cities))
	for i, city := range v20Cities {
		result[i] = entity.City{
			Country:      city.Country,
			DistanceUnit: city.DistanceUnit,
			Key:          city.Key,
			Name:         city.Name,
			Radius:       city.Radius,
			Region:       city.Region,
			RegionID:     city.RegionID,
		}
	}
	return result
}

func MapV20Regions(v20Regions []v20.Region) []entity.Region {
	result := make([]entity.Region, len(v20Regions))
	for i, region := range v20Regions {
		result[i] = entity.Region{
			Key:     region.Key,
			Name:    region.Name,
			Country: region.Country,
		}
	}
	return result
}

func MapV20Zips(v20Zips []v20.Zip) []entity.Zip {
	result := make([]entity.Zip, len(v20Zips))
	for i, zip := range v20Zips {
		result[i] = entity.Zip{
			Key:           zip.Key,
			Name:          zip.Name,
			PrimaryCityID: zip.PrimaryCityID,
			RegionID:      zip.RegionID,
			Country:       zip.Country,
		}
	}
	return result
}

func MapV20Places(v20Places []v20.Place) []entity.Place {
	result := make([]entity.Place, len(v20Places))
	for i, place := range v20Places {
		result[i] = entity.Place{
			Key:           place.Key,
			Name:          place.Name,
			DistanceUnit:  place.DistanceUnit,
			Latitude:      place.Latitude,
			Longitude:     place.Longitude,
			Radius:        place.Radius,
			PrimaryCityID: place.PrimaryCityID,
			RegionID:      place.RegionID,
			Country:       place.Country,
		}
	}
	return result
}

func MapV20SubCities(v20SubCities []v20.SubCity) []entity.SubCity {
	result := make([]entity.SubCity, len(v20SubCities))
	for i, subCity := range v20SubCities {
		result[i] = entity.SubCity{
			Key:      subCity.Key,
			Country:  subCity.Country,
			Name:     subCity.Name,
			Region:   subCity.Region,
			RegionID: subCity.RegionID,
		}
	}
	return result
}

//
//	From Facebook to Entitiy
//

func MapTargetingToV20(entityTargeting *entity.Targeting) *v20.Targeting {
	if entityTargeting == nil {
		return nil
	}
	return &v20.Targeting{
		PublisherPlatforms:          entityTargeting.PublisherPlatforms,
		FacebookPositions:           entityTargeting.FacebookPositions,
		InstagramPositions:          entityTargeting.InstagramPositions,
		AudienceNetworkPositions:    entityTargeting.AudienceNetworkPositions,
		MessengerPositions:          entityTargeting.MessengerPositions,
		AgeMin:                      entityTargeting.AgeMin,
		AgeMax:                      entityTargeting.AgeMax,
		Genders:                     entityTargeting.Genders,
		AppInstallState:             entityTargeting.AppInstallState,
		CustomAudiences:             MapIDContainerListToV20(entityTargeting.CustomAudiences),
		ExcludedCustomAudiences:     MapIDContainerListToV20(entityTargeting.ExcludedCustomAudiences),
		GeoLocations:                MapGeoLocationsToV20(entityTargeting.GeoLocations),
		ExcludedGeoLocations:        MapGeoLocationsToV20(entityTargeting.ExcludedGeoLocations),
		FlexibleSpec:                MapFlexibleSpecToV20(entityTargeting.FlexibleSpec),
		Exclusions:                  MapFlexibleSpecSingleToV20(entityTargeting.Exclusions),
		DevicePlatforms:             entityTargeting.DevicePlatforms,
		ExcludedPublisherCategories: entityTargeting.ExcludedPublisherCategories,
		Locales:                     entityTargeting.Locales,
		TargetingOptimization:       entityTargeting.TargetingOptimization,
		UserDevice:                  entityTargeting.UserDevice,
		UserOs:                      entityTargeting.UserOs,
		WirelessCarrier:             entityTargeting.WirelessCarrier,
		TargetingRelaxationTypes:    MapTargetingRelaxationTypesToV20(entityTargeting.TargetingRelaxationTypes),
	}
}

func MapGeoLocationsToV20(entityGeo *entity.GeoLocations) *v20.GeoLocations {
	if entityGeo == nil {
		return nil
	}
	return &v20.GeoLocations{
		Countries:          entityGeo.Countries,
		LocationTypes:      entityGeo.LocationTypes,
		Cities:             MapCitiesToV20(entityGeo.Cities),
		Regions:            MapRegionsToV20(entityGeo.Regions),
		Zips:               MapZipsToV20(entityGeo.Zips),
		Places:             MapPlacesToV20(entityGeo.Places),
		CountryGroups:      entityGeo.CountryGroups,
		SubCities:          MapSubCitiesToV20(entityGeo.SubCities),
		LocationClusterIDs: entityGeo.LocationClusterIDs,
	}
}

func MapIDContainerListToV20(entityList []entity.IDContainer) []v20.IDContainer {
	result := make([]v20.IDContainer, len(entityList))
	for i, v := range entityList {
		result[i] = v20.IDContainer{
			ID:   v.ID,
			Name: v.Name,
		}
	}
	return result
}

func MapFlexibleSpecToV20(entitySpecs []entity.FlexibleSpec) []v20.FlexibleSpec {
	result := make([]v20.FlexibleSpec, len(entitySpecs))
	for i, spec := range entitySpecs {
		result[i] = v20.FlexibleSpec{
			Interests:            MapIDContainerListToV20(spec.Interests),
			Behaviors:            MapIDContainerListToV20(spec.Behaviors),
			LifeEvents:           MapIDContainerListToV20(spec.LifeEvents),
			WorkEmployers:        MapIDContainerListToV20(spec.WorkEmployers),
			FamilyStatuses:       MapIDContainerListToV20(spec.FamilyStatuses),
			WorkPositions:        MapIDContainerListToV20(spec.WorkPositions),
			Politics:             MapIDContainerListToV20(spec.Politics),
			EducationMajors:      MapIDContainerListToV20(spec.EducationMajors),
			EducationStatuses:    spec.EducationStatuses,
			RelationshipStatuses: spec.RelationshipStatuses,
		}
	}
	return result
}

func MapFlexibleSpecSingleToV20(entitySpec *entity.FlexibleSpec) *v20.FlexibleSpec {
	if entitySpec == nil {
		return nil
	}
	return &v20.FlexibleSpec{
		Interests:            MapIDContainerListToV20(entitySpec.Interests),
		Behaviors:            MapIDContainerListToV20(entitySpec.Behaviors),
		LifeEvents:           MapIDContainerListToV20(entitySpec.LifeEvents),
		WorkEmployers:        MapIDContainerListToV20(entitySpec.WorkEmployers),
		FamilyStatuses:       MapIDContainerListToV20(entitySpec.FamilyStatuses),
		WorkPositions:        MapIDContainerListToV20(entitySpec.WorkPositions),
		Politics:             MapIDContainerListToV20(entitySpec.Politics),
		EducationMajors:      MapIDContainerListToV20(entitySpec.EducationMajors),
		EducationStatuses:    entitySpec.EducationStatuses,
		RelationshipStatuses: entitySpec.RelationshipStatuses,
	}
}

func MapTargetingRelaxationTypesToV20(entityRelaxation entity.TargetingRelaxationTypes) v20.TargetingRelaxationTypes {
	return v20.TargetingRelaxationTypes{
		CustomAudience: entityRelaxation.CustomAudience,
		Lookalike:      entityRelaxation.Lookalike,
	}
}

func MapCitiesToV20(entityCities []entity.City) []v20.City {
	result := make([]v20.City, len(entityCities))
	for i, city := range entityCities {
		result[i] = v20.City{
			Country:      city.Country,
			DistanceUnit: city.DistanceUnit,
			Key:          city.Key,
			Name:         city.Name,
			Radius:       city.Radius,
			Region:       city.Region,
			RegionID:     city.RegionID,
		}
	}
	return result
}

func MapRegionsToV20(entityRegions []entity.Region) []v20.Region {
	result := make([]v20.Region, len(entityRegions))
	for i, region := range entityRegions {
		result[i] = v20.Region{
			Key:     region.Key,
			Name:    region.Name,
			Country: region.Country,
		}
	}
	return result
}

func MapZipsToV20(entityZips []entity.Zip) []v20.Zip {
	result := make([]v20.Zip, len(entityZips))
	for i, zip := range entityZips {
		result[i] = v20.Zip{
			Key:           zip.Key,
			Name:          zip.Name,
			PrimaryCityID: zip.PrimaryCityID,
			RegionID:      zip.RegionID,
			Country:       zip.Country,
		}
	}
	return result
}

func MapPlacesToV20(entityPlaces []entity.Place) []v20.Place {
	result := make([]v20.Place, len(entityPlaces))
	for i, place := range entityPlaces {
		result[i] = v20.Place{
			Key:           place.Key,
			Name:          place.Name,
			DistanceUnit:  place.DistanceUnit,
			Latitude:      place.Latitude,
			Longitude:     place.Longitude,
			Radius:        place.Radius,
			PrimaryCityID: place.PrimaryCityID,
			RegionID:      place.RegionID,
			Country:       place.Country,
		}
	}
	return result
}

func MapSubCitiesToV20(entitySubCities []entity.SubCity) []v20.SubCity {
	result := make([]v20.SubCity, len(entitySubCities))
	for i, subCity := range entitySubCities {
		result[i] = v20.SubCity{
			Key:      subCity.Key,
			Country:  subCity.Country,
			Name:     subCity.Name,
			Region:   subCity.Region,
			RegionID: subCity.RegionID,
		}
	}
	return result
}

func MapAdaccountsToV20(entityAd *entity.Adaccounts) *v20.Adaccounts {
	if entityAd == nil {
		return nil
	}
	return &v20.Adaccounts{
		Data: entityAd.Data,
	}
}

func MapEntityToV20DataSource(entitySource *entity.CustomAudienceDataSource) *v20.CustomAudienceDataSource {
	if entitySource == nil {
		return nil
	}
	return &v20.CustomAudienceDataSource{
		CreationParams: entitySource.CreationParams,
		SubType:        entitySource.SubType,
		Type:           entitySource.Type,
	}
}

func MapEntityToV20Status(entityStatus *entity.CustomAudienceStatus) *v20.CustomAudienceStatus {
	if entityStatus == nil {
		return nil
	}
	return &v20.CustomAudienceStatus{
		Code:        entityStatus.Code,
		Description: entityStatus.Description,
	}
}

func MapEntityToV20LookalikeOrigion(entityOrigin []entity.LookalikeOrigion) []v20.LookalikeOrigion {
	result := make([]v20.LookalikeOrigion, len(entityOrigin))
	for i, origin := range entityOrigin {
		result[i] = v20.LookalikeOrigion{
			ID:   origin.ID,
			Name: origin.Name,
			Type: origin.Type,
		}
	}
	return result
}

func MapEntityToV20LookalikeSpec(entitySpec *entity.LookalikeSpec) *v20.LookalikeSpec {
	if entitySpec == nil {
		return nil
	}
	return &v20.LookalikeSpec{
		Country: entitySpec.Country,
		Origin:  MapEntityToV20LookalikeOrigion(entitySpec.Origin),
		Ratio:   entitySpec.Ratio,
		Type:    entitySpec.Type,
	}
}
