package api

import (
	"fmt"
	"godsp/modules/tiktok/adgroup/transport/requests"
	"godsp/pkg/gos/utils"
	"godsp/pkg/sctx/core"
	"godsp/views/v2/tiktok/adgroups/components"
	"net/http"

	"github.com/dev-networldasia/dspgos/gos/templates"
	"github.com/gofiber/fiber/v2"
)

func (a *AdgroupApi) GetAdgroupByAdgroupIDApi() fiber.Handler {
	return func(c *fiber.Ctx) error {
		var ctx = c.Context()
		var payload requests.GetDetailsAdgroupsReq
		if err := c.BodyParser(&payload); err != nil {
			return core.ReturnErrsForApi(c, err)
		}

		validateErrs := payload.Validate()
		if len(validateErrs) > 0 {
			return core.ReturnErrsForApi(c, validateErrs)
		}

		permission := core.GetPermission(ctx).GetPermissions()
		if permission != nil {
			// Always override AdvertiserID from context
			userTiktok, err := utils.GetInfoUserTiktok(ctx)
			if err != nil {
				return response(c, "Advertiser ID or Client ID is required", fiber.StatusBadRequest)
			}
			payload.AdvertiserID = &userTiktok.AdvertiserId
			payload.ClientID = userTiktok.ClientId
			payload.UserID = userTiktok.UserId
		}

		// userInfo, err := utils.GetInfoUserBasic(c.Context())
		// if userInfo.RoleName == conf.SysConf.RoleAdmin {
		// 	fmt.Println("admin", payload.AdvertiserID)
		// 	if payload.AdvertiserID != nil && payload.ClientIDStr == "" {
		// 		return c.Status(fiber.StatusBadRequest).JSON(core.ResponseData(map[string]interface{}{
		// 			"msg":  "Advertiser ID or Client ID is required",
		// 			"data": nil,
		// 		}))
		// 	}
		// } else if err != nil {
		// 	fmt.Println("Error GetInfoUserBasic:", err)
		// 	return core.ReturnErrsForApi(c, []string{err.Error()})
		// }

		result, err := a.usc.GetAdgroupByID(c.Context(), &payload)
		if err != nil {
			fmt.Println("Error GetAdgroupByID:", err)
			return core.ReturnErrsForApi(c, []string{err.Error()})
		}

		html, err := templates.RenderToString(c, components.OffcanvasEditAdgroupCpn())
		if err != nil {
			fmt.Println("Error rendering template:", err)
			return core.ReturnErrsForApi(c, []string{err.Error()})
		}

		return c.Status(http.StatusOK).JSON(core.ResponseData(map[string]interface{}{
			"msg":  "Get Adgroup successfully",
			"data": result,
			"html": html,
		}))
	}
}
