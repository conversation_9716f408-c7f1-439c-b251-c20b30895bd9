package responses

import "go.mongodb.org/mongo-driver/bson/primitive"

type ClientListSelect struct {
	ID      primitive.ObjectID `jsoen:"id" bson:"_id"`
	Name    string             `jsoen:"name" bson:"name"`
	Logo    string             `jsoen:"logo" bson:"logo"`
	Company string             `jsoen:"company" bson:"company"`
	Email   string             `jsoen:"email" bson:"email"`
	Domain  string             `jsoen:"domain" bson:"domain"`
}
