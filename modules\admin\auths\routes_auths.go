package auths

import (
	"godsp/modules/admin/auths/transport/api"
	"godsp/modules/admin/auths/transport/handlers"
	"godsp/modules/admin/auths/usecase"
	"godsp/modules/admin/user/repository/mongo"
	"godsp/pkg/gos/auths"

	"github.com/dev-networldasia/dspgos/sctx/component/redisc"

	"github.com/dev-networldasia/dspgos/sctx/component/jwtc"

	"github.com/dev-networldasia/dspgos/sctx/component/mongodb"

	"github.com/dev-networldasia/dspgos/sctx/configs"

	"github.com/dev-networldasia/dspgos/sctx/component/watermillapp/redisstream"

	"github.com/dev-networldasia/dspgos/sctx"

	"github.com/gofiber/fiber/v2"
	"github.com/gofiber/fiber/v2/middleware/session"
)

type composerAuth interface {
	LoginHdl() fiber.Handler
	LogoutHdl() fiber.Handler
	PageForbiddenHdl() fiber.Handler
	PageNotFoundHdl() fiber.Handler
	PagePermissionDeniedHdl() fiber.Handler
}

func composerAuthServive(serviceCtx sctx.ServiceContext) composerAuth {
	hdl := handlers.NewAuthHdl()
	return hdl
}

type composerAuthApi interface {
	LoginApiHdl() fiber.Handler
	ChangePassworddApi() fiber.Handler
	ResetPassworddApi() fiber.Handler
}

func composerAuthApiServive(serviceCtx sctx.ServiceContext) composerAuthApi {
	logger := serviceCtx.MustGet(configs.KeyLoggerDaily).(sctx.AppLoggerDaily).GetLogger("admins")
	mongoDB := serviceCtx.MustGet(configs.KeyCompMongoDB).(mongodb.MongoComponent).GetDatabase()
	jwtProvider := serviceCtx.MustGet(configs.KeyCompJWT).(jwtc.JWTProvider)
	redisClient := serviceCtx.MustGet(configs.KeyCompRedis).(redisc.RedisComponent)
	redisStreamPub := serviceCtx.MustGet(configs.KeyRedisStreamPub).(redisstream.RedisStreamPublisher)

	repo := mongo.NewUserRepo(mongoDB)
	hasher := new(auths.Hasher)

	usc := usecase.NewApiAuthUsc(repo, hasher, jwtProvider, redisClient, logger, redisStreamPub)
	api := api.NewAuthApi(usc)

	return api
}

func SetupRoutesAuth(app *fiber.App, serviceCtx sctx.ServiceContext, store *session.Store, midds ...fiber.Handler) {
	comp := composerAuthServive(serviceCtx)
	app.Get("/login", comp.LoginHdl()).Name("admins.auth.login")
	app.Get("/logout", comp.LogoutHdl()).Name("admins.auth.logout")
	app.Get("/page-forbidden", comp.PageForbiddenHdl()).Name("admins.auth.forbidden")
	app.Get("/page-not-found", comp.PageNotFoundHdl()).Name("admins.auth.not-found")
	app.Get("/page-permission-denied", comp.PagePermissionDeniedHdl()).Name("admins.auth.not-permission-denied")

	group := app.Group("api/auths")
	{
		if len(midds) > 0 {
			for _, midd := range midds {
				group.Use(midd)
			}
		}

		compAuthApi := composerAuthApiServive(serviceCtx)
		group.Post("/login", compAuthApi.LoginApiHdl()).Name("admins.auth.api.login")
		group.Patch("/change-password", compAuthApi.ChangePassworddApi()).Name("admins.auth.api.change_password")
		group.Post("/reset-password", compAuthApi.ResetPassworddApi()).Name("admins.auth.api.reset_password")
	}
}
