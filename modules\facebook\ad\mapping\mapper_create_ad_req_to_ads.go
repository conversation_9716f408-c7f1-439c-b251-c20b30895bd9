package mapping

import (
	"godsp/modules/facebook/ad/entity"
	"godsp/modules/facebook/ad/transport/requests"
	"godsp/pkg/fb-marketing/fb"
	v20 "godsp/pkg/fb-marketing/marketing/v20"
	"time"

	"go.mongodb.org/mongo-driver/bson/primitive"
)

func MapperCreateAdRequestToAds(payload *requests.CreateAdReq) (*entity.AdEntity, *v20.Ad) {
	adFB := v20.Ad{
		AccountID:     payload.AccountID,
		Name:          payload.Name,
		Status:        payload.Status,
		AdsetID:       payload.AdsetId,
		TrackingSpecs: payload.TrackingSpecs,
	}
	if payload.Creative != nil {

	}
	if payload.AdScheduleStartTime != nil {
		adFB.AdScheduleStartTime = (*fb.Time)(payload.AdScheduleStartTime)
	}
	if payload.AdScheduleEndTime != nil {
		adFB.AdScheduleEndTime = (*fb.Time)(payload.AdScheduleEndTime)
	}

	now := time.Now()
	adEntity := entity.AdEntity{
		ID:          primitive.NewObjectID(),
		AccountID:   payload.AccountID,
		AdsetID:     payload.AdsetId,
		Name:        payload.Name,
		Status:      payload.Status,
		CreatedBy:   payload.UserId,
		CreatedAt:   now,
		UpdatedAt:   now,
		UpdatedBy:   payload.UserId,
		ListUserIDs: []primitive.ObjectID{},
		ClientID:    payload.ClientID,
	}
	adEntity.ListUserIDs = append(adEntity.ListUserIDs, payload.UserId)

	if payload.Creative != nil && payload.Creative.CreativeID != "" {
		adFB.Creative = &v20.AdCreative{
			CreativeID: payload.Creative.CreativeID,
		}
		adEntity.CreativeID = payload.Creative.CreativeID
	}

	return &adEntity, &adFB
}
