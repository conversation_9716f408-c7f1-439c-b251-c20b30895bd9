package entity

import (
	"time"

	"go.mongodb.org/mongo-driver/bson/primitive"
)

type HighDemandPeriodEntity struct {
	ID                 primitive.ObjectID `json:"-" bson:"_id,omitempty"`
	CampaignID         string             `json:"campaign_id,omitempty" bson:"campaign_id"`
	AdsetID            string             `json:"adset_id,omitempty" bson:"adset_id"`
	HighDemandPeriodID string             `json:"high_demand_period_id" bson:"high_demand_period_id"`
	BudgetValue        uint64             `json:"budget_value,omitempty" bson:"budget_value"`
	BudgetValueType    string             `json:"budget_value_type,omitempty" bson:"budget_value_type"`
	RecurrenceType     string             `json:"recurrence_type,omitempty" bson:"recurrence_type"`
	TimeStart          time.Time          `json:"time_start,omitempty" bson:"time_start"`
	TimeEnd            time.Time          `json:"time_end,omitempty" bson:"time_end"`

	CreateddBy primitive.ObjectID `json:"created_by" bson:"created_by"`
	CreatedAt  time.Time          `json:"created_at" bson:"created_at"`
	UpdatedBy  primitive.ObjectID `json:"updated_by" bson:"updated_by"`
	UpdatedAt  time.Time          `json:"updated_at" bson:"updated_at"`

	ListUserIDs []primitive.ObjectID `json:"list_user_ids" bson:"list_user_ids"`
}

func (HighDemandPeriodEntity) CollectionName() string {
	return "fb_high_demand_periods_campaigns"
}
