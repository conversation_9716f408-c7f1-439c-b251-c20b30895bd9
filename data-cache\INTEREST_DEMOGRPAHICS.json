[{"_id": "67be7a17cb8a85c6757d8c32", "demographic_id": "6002714398172", "name": "Newlywed (1 year)", "type": "life_events", "path": ["Newlywed (1 year)"], "description": "People who have been married for less than a year", "real_time_cluster": false, "audience_size_lower_bound": 15281556, "audience_size_upper_bound": 17971111, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-02-26T02:19:03.76Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-02T13:30:33.571Z"}, {"_id": "67be7a17cb8a85c6757d8c34", "demographic_id": "6002714398372", "name": "Parents (All)", "type": "family_statuses", "path": ["Parents (All)"], "description": "People who are likely to be parents.", "real_time_cluster": false, "audience_size_lower_bound": 311861035, "audience_size_upper_bound": 366748578, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-02-26T02:19:03.797Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-02T13:30:33.581Z"}, {"_id": "67be7a17cb8a85c6757d8c36", "demographic_id": "6002714398772", "name": "Newly engaged (6 months)", "type": "life_events", "path": ["Newly engaged (6 months)"], "description": "People who have been engaged for less than 6 months.", "real_time_cluster": false, "audience_size_lower_bound": 2665346, "audience_size_upper_bound": 3134448, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-02-26T02:19:03.798Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-02T13:30:33.583Z"}, {"_id": "67be7a17cb8a85c6757d8c38", "demographic_id": "6002737124172", "name": "Upcoming birthday", "type": "life_events", "path": ["Date of birth", "Upcoming birthday"], "description": "People who are going to have their birthday within the next week.", "real_time_cluster": true, "audience_size_lower_bound": 66103898, "audience_size_upper_bound": 77738185, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-02-26T02:19:03.799Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-02T13:30:33.584Z"}, {"_id": "67be7a17cb8a85c6757d8c3a", "demographic_id": "6003054185372", "name": "Recently moved", "type": "life_events", "path": ["Recently moved"], "description": "People who have updated their profile with a new current city in the last 6 months", "real_time_cluster": false, "audience_size_lower_bound": 762859, "audience_size_upper_bound": 897123, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-02-26T02:19:03.8Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-02T13:30:33.585Z"}, {"_id": "67be7a17cb8a85c6757d8c3c", "demographic_id": "6003053984972", "name": "Long-distance relationship", "type": "life_events", "path": ["Long-distance relationship"], "description": "People who are in a long-distance relationship", "real_time_cluster": false, "audience_size_lower_bound": 8651807, "audience_size_upper_bound": 10174526, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-02-26T02:19:03.801Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-02T13:30:33.586Z"}, {"_id": "67be7a17cb8a85c6757d8c3e", "demographic_id": "6003053860372", "name": "Away from home town", "type": "life_events", "path": ["Away from home town"], "description": "People who are away from their home towns.", "real_time_cluster": false, "audience_size_lower_bound": 133157996, "audience_size_upper_bound": 156593804, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-02-26T02:19:03.802Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-02T13:30:33.586Z"}, {"_id": "67be7a17cb8a85c6757d8c40", "demographic_id": "6003053857372", "name": "Away from family", "type": "life_events", "path": ["Away from family"], "description": "People who are away from their family", "real_time_cluster": false, "audience_size_lower_bound": 133109595, "audience_size_upper_bound": 156536884, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-02-26T02:19:03.802Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-02T13:30:33.587Z"}, {"_id": "67be7a17cb8a85c6757d8c42", "demographic_id": "6003050226972", "name": "Newlywed (6 months)", "type": "life_events", "path": ["Newlywed (6 months)"], "description": "People who have been married for less than six months", "real_time_cluster": false, "audience_size_lower_bound": 8445611, "audience_size_upper_bound": 9932039, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-02-26T02:19:03.803Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-02T13:30:33.588Z"}, {"_id": "67be7a17cb8a85c6757d8c44", "demographic_id": "6003050210972", "name": "Newly engaged (1 year)", "type": "life_events", "path": ["Newly engaged (1 year)"], "description": "People who have been engaged for less than 1 year", "real_time_cluster": false, "audience_size_lower_bound": 4320590, "audience_size_upper_bound": 5081015, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-02-26T02:19:03.803Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-02T13:30:33.589Z"}, {"_id": "67be7a17cb8a85c6757d8c46", "demographic_id": "6005232221572", "name": "New relationship", "type": "life_events", "path": ["New relationship"], "description": "People who have updated their profile with a new relationship in the last six months", "real_time_cluster": false, "audience_size_lower_bound": 1827861, "audience_size_upper_bound": 2149565, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-02-26T02:19:03.804Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-02T13:30:33.59Z"}, {"_id": "67be7a17cb8a85c6757d8c48", "demographic_id": "6005149512172", "name": "New job", "type": "life_events", "path": ["New job"], "description": "People who have updated their profile with a new job position in the last 6 months.", "real_time_cluster": false, "audience_size_lower_bound": 105565, "audience_size_upper_bound": 124145, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-02-26T02:19:03.805Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-02T13:30:33.591Z"}, {"_id": "67be7a17cb8a85c6757d8c4a", "demographic_id": "*************", "name": "Business and finance", "type": "industries", "path": ["Business and finance"], "description": "People with roles in business and finance. Examples include: accountant, accounting manager, auditor, sales manager, financial adviser, chief financial officer, dealer, agent etc.", "real_time_cluster": false, "audience_size_lower_bound": 7330694, "audience_size_upper_bound": 8620897, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-02-26T02:19:03.805Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-02T13:30:33.592Z"}, {"_id": "67be7a17cb8a85c6757d8c4c", "demographic_id": "*************", "name": "Administrative services", "type": "industries", "path": ["Administrative services"], "description": "People with roles in administration. Examples include: secretary, administrative assistant, office manager, office assistant, flat locator, personal assistant, branch manager etc.", "real_time_cluster": false, "audience_size_lower_bound": ********, "audience_size_upper_bound": ********, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-02-26T02:19:03.806Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-02T13:30:33.592Z"}, {"_id": "67be7a17cb8a85c6757d8c4e", "demographic_id": "6008888998983", "name": "Education and libraries", "type": "industries", "path": ["Education and libraries"], "description": "People with roles in education or libraries. Examples include: educator, instructor, teacher, professor, lecturer, research assistant, tutor, librarian, principal etc.", "real_time_cluster": false, "audience_size_lower_bound": 10186107, "audience_size_upper_bound": 11978862, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-02-26T02:19:03.807Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-02T13:30:33.593Z"}, {"_id": "67be7a17cb8a85c6757d8c50", "demographic_id": "6009003311983", "name": "Management", "type": "industries", "path": ["Management"], "description": "People with roles in management. Examples include: manager, supervisor, director, president, chairperson etc.", "real_time_cluster": false, "audience_size_lower_bound": 16732943, "audience_size_upper_bound": 19677942, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-02-26T02:19:03.808Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-02T13:30:33.594Z"}, {"_id": "67be7a17cb8a85c6757d8c52", "demographic_id": "6008888980183", "name": "Sales", "type": "industries", "path": ["Sales"], "description": "People with roles in sales. Examples include: sales assistant, retail manager, realtor, consultant, customer service representative, cashier etc.", "real_time_cluster": false, "audience_size_lower_bound": 13317312, "audience_size_upper_bound": 15661159, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-02-26T02:19:03.809Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-02T13:30:33.594Z"}, {"_id": "67be7a17cb8a85c6757d8c54", "demographic_id": "6008888961983", "name": "IT and technical services", "type": "industries", "path": ["IT and technical services"], "description": "People with roles in IT and technical services. Examples include: IT technician, web developer, IT consultant etc.", "real_time_cluster": false, "audience_size_lower_bound": 6322892, "audience_size_upper_bound": 7435721, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-02-26T02:19:03.81Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-02T13:30:33.595Z"}, {"_id": "67be7a17cb8a85c6757d8c56", "demographic_id": "6008888972183", "name": "Legal services", "type": "industries", "path": ["Legal services"], "description": "People with roles in legal services. Examples include: lawyer, corporate counsel, partner, paralegal etc.", "real_time_cluster": false, "audience_size_lower_bound": 921262, "audience_size_upper_bound": 1083405, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-02-26T02:19:03.81Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-02T13:30:33.596Z"}, {"_id": "67be7a17cb8a85c6757d8c58", "demographic_id": "6012631862383", "name": "Newly engaged (3 months)", "type": "life_events", "path": ["Newly engaged (3 months)"], "description": "People who have been engaged for less than three months", "real_time_cluster": false, "audience_size_lower_bound": 1571596, "audience_size_upper_bound": 1848198, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-02-26T02:19:03.811Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-02T13:30:33.597Z"}, {"_id": "67be7a17cb8a85c6757d8c5a", "demographic_id": "6012901802383", "name": "Arts, entertainment, sport and media", "type": "industries", "path": ["Arts, entertainment, sport and media"], "description": "People with roles in the arts, entertainment, sport and media. Examples include: photographer, artist, actor, actress, singer etc.", "real_time_cluster": false, "audience_size_lower_bound": 9505086, "audience_size_upper_bound": 11177982, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-02-26T02:19:03.811Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-02T13:30:33.598Z"}, {"_id": "67be7a17cb8a85c6757d8c5c", "demographic_id": "6012903140583", "name": "Production", "type": "industries", "path": ["Production"], "description": "People with roles in production. Examples include: miner, blacksmith, lumberjack etc.", "real_time_cluster": false, "audience_size_lower_bound": 10527558, "audience_size_upper_bound": 12380409, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-02-26T02:19:03.812Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-02T13:30:33.599Z"}, {"_id": "67be7a17cb8a85c6757d8c5e", "demographic_id": "6012903320983", "name": "Transport and moving", "type": "industries", "path": ["Transport and moving"], "description": "People with roles in transport and moving. Examples include: driver, operator, sea captain, flight attendant, pilot etc.", "real_time_cluster": false, "audience_size_lower_bound": 5818507, "audience_size_upper_bound": 6842565, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-02-26T02:19:03.813Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-02T13:30:33.599Z"}, {"_id": "67be7a17cb8a85c6757d8c60", "demographic_id": "6012903126783", "name": "Architecture and engineering", "type": "industries", "path": ["Architecture and engineering"], "description": "People with roles in architecture and engineering. Examples include: software engineer, technician, electrician, machinist etc.", "real_time_cluster": false, "audience_size_lower_bound": 6030251, "audience_size_upper_bound": 7091576, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-02-26T02:19:03.814Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-02T13:30:33.6Z"}, {"_id": "67be7a17cb8a85c6757d8c62", "demographic_id": "6012903127583", "name": "Food and restaurants", "type": "industries", "path": ["Food and restaurants"], "description": "People with roles in food and restaurants. Examples include: cashier, server, waiter, waitress, chef, barista, line cook etc.", "real_time_cluster": false, "audience_size_lower_bound": 4050738, "audience_size_upper_bound": 4763668, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-02-26T02:19:03.814Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-02T13:30:33.601Z"}, {"_id": "67be7a17cb8a85c6757d8c64", "demographic_id": "6012903128783", "name": "Construction and extraction", "type": "industries", "path": ["Construction and extraction"], "description": "People with roles in construction and extraction. Examples include: service technician, electrician, brick layer, mechanic, machine operator etc.", "real_time_cluster": false, "audience_size_lower_bound": 3819434, "audience_size_upper_bound": 4491655, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-02-26T02:19:03.815Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-02T13:30:33.601Z"}, {"_id": "67be7a17cb8a85c6757d8c66", "demographic_id": "6012903159383", "name": "Healthcare and medical services", "type": "industries", "path": ["Healthcare and medical services"], "description": "People with roles in healthcare and medical services. Examples include: physician, dentist, cardiologist etc.", "real_time_cluster": false, "audience_size_lower_bound": 9601187, "audience_size_upper_bound": 11290996, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-02-26T02:19:03.816Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-02T13:30:33.602Z"}, {"_id": "67be7a17cb8a85c6757d8c68", "demographic_id": "6012903160983", "name": "Installation and repair services", "type": "industries", "path": ["Installation and repair services"], "description": "People with roles in installation and repair services. Examples include: technician, operator, mechanic, welder etc.", "real_time_cluster": false, "audience_size_lower_bound": 6127940, "audience_size_upper_bound": 7206458, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-02-26T02:19:03.816Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-02T13:30:33.602Z"}, {"_id": "67be7a17cb8a85c6757d8c6a", "demographic_id": "6012903167183", "name": "Life, physical and social sciences", "type": "industries", "path": ["Life, physical and social sciences"], "description": "People with roles in life, physical and social sciences. Examples include: professor, chemist, psychologist, geologist etc.", "real_time_cluster": false, "audience_size_lower_bound": 3912221, "audience_size_upper_bound": 4600773, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-02-26T02:19:03.817Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-02T13:30:33.603Z"}, {"_id": "67be7a17cb8a85c6757d8c6c", "demographic_id": "6012903167783", "name": "Computation and mathematics", "type": "industries", "path": ["Computation and mathematics"], "description": "People with roles in computation and mathematics. Examples include: computer scientist, analyst, mathematician etc.", "real_time_cluster": false, "audience_size_lower_bound": 5197852, "audience_size_upper_bound": 6112674, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-02-26T02:19:03.818Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-02T13:30:33.603Z"}, {"_id": "67be7a17cb8a85c6757d8c6e", "demographic_id": "6012903168383", "name": "Community and social services", "type": "industries", "path": ["Community and social services"], "description": "People with roles in community and social services. Examples include: minister, social worker, counsellor, politician etc.", "real_time_cluster": false, "audience_size_lower_bound": 7652659, "audience_size_upper_bound": 8999528, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-02-26T02:19:03.818Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-02T13:30:33.603Z"}, {"_id": "67be7a17cb8a85c6757d8c70", "demographic_id": "6012903299783", "name": "Protective services", "type": "industries", "path": ["Protective services"], "description": "People with roles in protective services. Examples include: security guard, sergeant, life guard, police officer, firefighter etc.", "real_time_cluster": false, "audience_size_lower_bound": 2225130, "audience_size_upper_bound": 2616753, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-02-26T02:19:03.819Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-02T13:30:33.604Z"}, {"_id": "67be7a17cb8a85c6757d8c72", "demographic_id": "6012903299983", "name": "Farming, fishing and forestry", "type": "industries", "path": ["Farming, fishing and forestry"], "description": "People with roles in farming, fishing and forestry. Examples include: farmer, rider, crew member, handyman etc.", "real_time_cluster": false, "audience_size_lower_bound": 3757515, "audience_size_upper_bound": 4418838, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-02-26T02:19:03.819Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-02T13:30:33.604Z"}, {"_id": "67be7a17cb8a85c6757d8c74", "demographic_id": "6012903317583", "name": "Cleaning and maintenance services", "type": "industries", "path": ["Cleaning and maintenance services"], "description": "People with roles in cleaning and maintenance. Examples include: caretaker, housekeeper, gardener, handyman etc.", "real_time_cluster": false, "audience_size_lower_bound": 2487684, "audience_size_upper_bound": 2925517, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-02-26T02:19:03.82Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-02T13:30:33.605Z"}, {"_id": "67be7a17cb8a85c6757d8c76", "demographic_id": "6012903320383", "name": "Military (global)", "type": "industries", "path": ["Military (global)"], "description": "People with active roles in the military.", "real_time_cluster": false, "audience_size_lower_bound": 715055, "audience_size_upper_bound": 840905, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-02-26T02:19:03.82Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-02T13:30:33.605Z"}, {"_id": "67be7a17cb8a85c6757d8c78", "demographic_id": "6013133420583", "name": "Newlywed (3 months)", "type": "life_events", "path": ["Newlywed (3 months)"], "description": "People who have been married for less than three months", "real_time_cluster": false, "audience_size_lower_bound": 4474363, "audience_size_upper_bound": 5261851, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-02-26T02:19:03.821Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-02T13:30:33.606Z"}, {"_id": "67be7a17cb8a85c6757d8c7a", "demographic_id": "6016671857383", "name": "Veterans (US)", "type": "industries", "path": ["Veterans (US)"], "description": "People who were previously employed by the US military", "real_time_cluster": false, "audience_size_lower_bound": 1119531, "audience_size_upper_bound": 1316569, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-02-26T02:19:03.822Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-02T13:30:33.606Z"}, {"_id": "67be7a17cb8a85c6757d8c7c", "demographic_id": "6017476616183", "name": "Anniversary within 30 days", "type": "life_events", "path": ["Anniversary", "Anniversary within 30 days"], "description": "People with a relationship anniversary (marriage, domestic partnership etc.) occurring within the next 30 days", "real_time_cluster": false, "audience_size_lower_bound": 5802062, "audience_size_upper_bound": 6823226, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-02-26T02:19:03.822Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-02T13:30:33.606Z"}, {"_id": "67be7a17cb8a85c6757d8c7e", "demographic_id": "6018399723983", "name": "Anniversary within 31-60 days", "type": "life_events", "path": ["Anniversary", "Anniversary within 31-60 days"], "description": "People with a relationship anniversary (marriage, domestic partnership etc.) occurring within the next 31-60 days", "real_time_cluster": false, "audience_size_lower_bound": 5651704, "audience_size_upper_bound": 6646404, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-02-26T02:19:03.823Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-02T13:30:33.607Z"}, {"_id": "67be7a17cb8a85c6757d8c80", "demographic_id": "6019621029983", "name": "Government employees (global)", "type": "industries", "path": ["Government employees (global)"], "description": "People with roles in the government", "real_time_cluster": false, "audience_size_lower_bound": 806210, "audience_size_upper_bound": 948104, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-02-26T02:19:03.823Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-02T13:30:33.607Z"}, {"_id": "67be7a17cb8a85c6757d8c82", "demographic_id": "6023005372383", "name": "Parents (up to 12 months)", "type": "family_statuses", "path": ["Parents (up to 12 months)"], "description": "People who are likely to be parents with children aged up to 12 months.", "real_time_cluster": false, "audience_size_lower_bound": 5568369, "audience_size_upper_bound": 6548402, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-02-26T02:19:03.824Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-02T13:30:33.607Z"}, {"_id": "67be7a17cb8a85c6757d8c84", "demographic_id": "6023005458383", "name": "Parents with toddlers (aged 1-2)", "type": "family_statuses", "path": ["Parents with toddlers (aged 1-2)"], "description": "People who are likely to be parents with toddlers.", "real_time_cluster": false, "audience_size_lower_bound": 3584647, "audience_size_upper_bound": 4215546, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-02-26T02:19:03.824Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-02T13:30:33.608Z"}, {"_id": "67be7a17cb8a85c6757d8c86", "demographic_id": "6023005529383", "name": "Parents with pre-schoolers (3-5 years)", "type": "family_statuses", "path": ["Parents with pre-schoolers (3-5 years)"], "description": "People who are likely to be parents with pre-schoolers.", "real_time_cluster": false, "audience_size_lower_bound": 5900086, "audience_size_upper_bound": 6938502, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-02-26T02:19:03.825Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-02T13:30:33.608Z"}, {"_id": "67be7a17cb8a85c6757d8c88", "demographic_id": "6023005570783", "name": "Parents with primary school-age children (6-8 years).", "type": "family_statuses", "path": ["Parents with primary school-age children (6-8 years)."], "description": "People who are likely to be parents with early school-age children.", "real_time_cluster": false, "audience_size_lower_bound": 10408983, "audience_size_upper_bound": 12240965, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-02-26T02:19:03.825Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-02T13:30:33.609Z"}, {"_id": "67be7a17cb8a85c6757d8c8a", "demographic_id": "6023005681983", "name": "Parents with teenagers (aged 13-17)", "type": "family_statuses", "path": ["Parents with teenagers (aged 13-17)"], "description": "People who are likely to be parents with teenagers.", "real_time_cluster": false, "audience_size_lower_bound": 27115366, "audience_size_upper_bound": 31887671, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-02-26T02:19:03.826Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-02T13:30:33.609Z"}, {"_id": "67be7a17cb8a85c6757d8c8c", "demographic_id": "6023005718983", "name": "Parents with adult children (aged 18-26)", "type": "family_statuses", "path": ["Parents with adult children (aged 18-26)"], "description": "People who are likely to be parents with adult children.", "real_time_cluster": false, "audience_size_lower_bound": 71128570, "audience_size_upper_bound": 83647199, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-02-26T02:19:03.826Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-02T13:30:33.609Z"}, {"_id": "67be7a17cb8a85c6757d8c8e", "demographic_id": "6023080302983", "name": "Parents with pre-teens (aged 9-12)", "type": "family_statuses", "path": ["Parents with pre-teens (aged 9-12)"], "description": "People who are likely to be parents with pre-teens.", "real_time_cluster": false, "audience_size_lower_bound": 12437164, "audience_size_upper_bound": 14626105, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-02-26T02:19:03.827Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-02T13:30:33.61Z"}, {"_id": "67be7a17cb8a85c6757d8c90", "demographic_id": "6048267235783", "name": "Birthday in January", "type": "life_events", "path": ["Date of birth", "Month of birth", "Birthday in January"], "description": "People who have a birthday in January", "real_time_cluster": false, "audience_size_lower_bound": 234344173, "audience_size_upper_bound": 275588748, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-02-26T02:19:03.828Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-02T13:30:33.611Z"}, {"_id": "67be7a17cb8a85c6757d8c92", "demographic_id": "6049083267183", "name": "Birthday in February", "type": "life_events", "path": ["Date of birth", "Month of birth", "Birthday in February"], "description": "People who have a birthday in February.", "real_time_cluster": false, "audience_size_lower_bound": 160908727, "audience_size_upper_bound": 189228664, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-02-26T02:19:03.828Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-02T13:30:33.611Z"}, {"_id": "67be7a17cb8a85c6757d8c94", "demographic_id": "6048026294583", "name": "Birthday in March", "type": "life_events", "path": ["Date of birth", "Month of birth", "Birthday in March"], "description": "People who have a birthday in March.", "real_time_cluster": false, "audience_size_lower_bound": 154876358, "audience_size_upper_bound": 182134598, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-02-26T02:19:03.829Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-02T13:30:33.612Z"}, {"_id": "67be7a17cb8a85c6757d8c96", "demographic_id": "6048026275783", "name": "Birthday in April", "type": "life_events", "path": ["Date of birth", "Month of birth", "Birthday in April"], "description": "People who have a birthday in April.", "real_time_cluster": false, "audience_size_lower_bound": 149901473, "audience_size_upper_bound": 176284133, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-02-26T02:19:03.83Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-02T13:30:33.612Z"}, {"_id": "67be7a17cb8a85c6757d8c98", "demographic_id": "6048026061783", "name": "Birthday in May", "type": "life_events", "path": ["Date of birth", "Month of birth", "Birthday in May"], "description": "People who have a birthday in May", "real_time_cluster": false, "audience_size_lower_bound": 163137529, "audience_size_upper_bound": 191849735, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-02-26T02:19:03.831Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-02T13:30:33.613Z"}, {"_id": "67be7a17cb8a85c6757d8c9a", "demographic_id": "6048026229983", "name": "Birthday in June", "type": "life_events", "path": ["Date of birth", "Month of birth", "Birthday in June"], "description": "People who have a birthday in June.", "real_time_cluster": false, "audience_size_lower_bound": 157404322, "audience_size_upper_bound": 185107483, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-02-26T02:19:03.831Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-02T13:30:33.613Z"}, {"_id": "67be7a17cb8a85c6757d8c9c", "demographic_id": "6048808449583", "name": "Birthday in July", "type": "life_events", "path": ["Date of birth", "Month of birth", "Birthday in July"], "description": "People who have a birthday in July.", "real_time_cluster": false, "audience_size_lower_bound": 159390273, "audience_size_upper_bound": 187442962, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-02-26T02:19:03.831Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-02T13:30:33.614Z"}, {"_id": "67be7a17cb8a85c6757d8c9e", "demographic_id": "6048810966183", "name": "Birthday in August", "type": "life_events", "path": ["Date of birth", "Month of birth", "Birthday in August"], "description": "People with a birthday in August.", "real_time_cluster": false, "audience_size_lower_bound": 163037165, "audience_size_upper_bound": 191731707, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-02-26T02:19:03.832Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-02T13:30:33.614Z"}, {"_id": "67be7a17cb8a85c6757d8ca0", "demographic_id": "6048810961183", "name": "Birthday in September", "type": "life_events", "path": ["Date of birth", "Month of birth", "Birthday in September"], "description": "People with a birthday in September", "real_time_cluster": false, "audience_size_lower_bound": 151897213, "audience_size_upper_bound": 178631123, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-02-26T02:19:03.833Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-02T13:30:33.615Z"}, {"_id": "67be7a17cb8a85c6757d8ca2", "demographic_id": "6048810950583", "name": "Birthday in October", "type": "life_events", "path": ["Date of birth", "Month of birth", "Birthday in October"], "description": "People with a birthday in October", "real_time_cluster": false, "audience_size_lower_bound": 158924954, "audience_size_upper_bound": 186895746, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-02-26T02:19:03.833Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-02T13:30:33.615Z"}, {"_id": "67be7a17cb8a85c6757d8ca4", "demographic_id": "6048810938183", "name": "Birthday in November", "type": "life_events", "path": ["Date of birth", "Month of birth", "Birthday in November"], "description": "People with a birthday in November", "real_time_cluster": false, "audience_size_lower_bound": 145796169, "audience_size_upper_bound": 171456295, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-02-26T02:19:03.834Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-02T13:30:33.615Z"}, {"_id": "67be7a17cb8a85c6757d8ca6", "demographic_id": "6048810914583", "name": "Birthday in December", "type": "life_events", "path": ["Date of birth", "Month of birth", "Birthday in December"], "description": "People with a birthday in December.", "real_time_cluster": false, "audience_size_lower_bound": 181329549, "audience_size_upper_bound": 213243550, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-02-26T02:19:03.834Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-02T13:30:33.616Z"}, {"_id": "67be7a17cb8a85c6757d8ca8", "demographic_id": "6075565069783", "name": "Large business-to-business enterprise employees (500+ employees)", "type": "industries", "path": ["Large business-to-business enterprise employees (500+ employees)"], "description": "A proxy for people who work in B2B companies with 501 or more employees, based on the number of users regularly logging in from the same IP location.", "real_time_cluster": false, "audience_size_lower_bound": 181865385, "audience_size_upper_bound": 213873693, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-02-26T02:19:03.835Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-02T13:30:33.616Z"}, {"_id": "67be7a17cb8a85c6757d8caa", "demographic_id": "6080792228383", "name": "Medium business-to-business enterprise employees (200-500 employees)", "type": "industries", "path": ["Medium business-to-business enterprise employees (200-500 employees)"], "description": "A proxy for people who work in B2B companies with 200-500 employees, based on the number of users regularly logging in from the same IP location.", "real_time_cluster": false, "audience_size_lower_bound": 43288837, "audience_size_upper_bound": 50907673, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-02-26T02:19:03.835Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-02T13:30:33.616Z"}, {"_id": "67be7a17cb8a85c6757d8cac", "demographic_id": "6080792282783", "name": "Small business-to-business enterprise employees (10-200 employees)", "type": "industries", "path": ["Small business-to-business enterprise employees (10-200 employees)"], "description": "A proxy for people who work in B2B companies with 10-199 employees, based on the number of users regularly logging in from the same IP location.", "real_time_cluster": false, "audience_size_lower_bound": 113840151, "audience_size_upper_bound": 133876018, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-02-26T02:19:03.836Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-02T13:30:33.617Z"}, {"_id": "67be7a17cb8a85c6757d8cae", "demographic_id": "6107813079183", "name": "Household income: top 5% of ZIP codes (US)", "type": "income", "path": ["Household income: top 5% of ZIP codes (US)"], "description": "People who live in the top 5% of US ZIP code areas by average household income based on publicly available information", "real_time_cluster": false, "audience_size_lower_bound": 12750901, "audience_size_upper_bound": 14995060, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-02-26T02:19:03.837Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-02T13:30:33.617Z"}, {"_id": "67be7a17cb8a85c6757d8cb0", "demographic_id": "6107813551783", "name": "Household income: top 10% of ZIP codes (US)", "type": "income", "path": ["Household income: top 10% of ZIP codes (US)"], "description": "People who live in the top 10% of US ZIP code areas by average household income based on publicly available information", "real_time_cluster": false, "audience_size_lower_bound": 26041011, "audience_size_upper_bound": 30624229, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-02-26T02:19:03.837Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-02T13:30:33.617Z"}, {"_id": "67be7a17cb8a85c6757d8cb2", "demographic_id": "6107813553183", "name": "Household income: top 10-25% of ZIP codes (US)", "type": "income", "path": ["Household income: top 10-25% of ZIP codes (US)"], "description": "People who live in the top 10-25% of US ZIP code areas by average household income based on publicly available information.", "real_time_cluster": false, "audience_size_lower_bound": 33272954, "audience_size_upper_bound": 39128994, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-02-26T02:19:03.838Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-02T13:30:33.618Z"}, {"_id": "67be7a17cb8a85c6757d8cb4", "demographic_id": "6107813554583", "name": "Household income: top 25-50% of ZIP codes (US)", "type": "income", "path": ["Household income: top 25-50% of ZIP codes (US)"], "description": "People who live in the top 25-50% of US ZIP code areas by average household income based on publicly available information.", "real_time_cluster": false, "audience_size_lower_bound": 39841909, "audience_size_upper_bound": 46854085, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-02-26T02:19:03.838Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-02T13:30:33.618Z"}, {"_id": "67be7a17cb8a85c6757d8cb6", "demographic_id": "6203619820983", "name": "Friends of people who recently moved", "type": "life_events", "path": ["Friends of", "Friends of people who recently moved"], "description": "Friends of people who have moved in the past 30 days", "real_time_cluster": false, "audience_size_lower_bound": 1088657882, "audience_size_upper_bound": 1280261670, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-02-26T02:19:03.839Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-02T13:30:33.619Z"}, {"_id": "67be7a17cb8a85c6757d8cb8", "demographic_id": "6203620854183", "name": "Friends of people with birthdays in a month", "type": "life_events", "path": ["Friends of", "Friends of people with birthdays in a month"], "description": "Friends of people with a birthday in 7-30 days", "real_time_cluster": false, "audience_size_lower_bound": 2040937154, "audience_size_upper_bound": 2400142094, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-02-26T02:19:03.839Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-02T13:30:33.619Z"}, {"_id": "67be7a17cb8a85c6757d8cba", "demographic_id": "6203621025983", "name": "Friends of women with a birthday in 7-30 days", "type": "life_events", "path": ["Friends of", "Friends of women with a birthday in 7-30 days"], "description": "Friends of women with a birthday in 7-30 days", "real_time_cluster": false, "audience_size_lower_bound": 1823049185, "audience_size_upper_bound": 2143905842, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-02-26T02:19:03.84Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-02T13:30:33.619Z"}, {"_id": "67be7a17cb8a85c6757d8cbc", "demographic_id": "6203621119983", "name": "Friends of men with a birthday in 7-30 days", "type": "life_events", "path": ["Friends of", "Friends of men with a birthday in 7-30 days"], "description": "Friends of men with a birthday in 7-30 days", "real_time_cluster": false, "audience_size_lower_bound": 1914778433, "audience_size_upper_bound": 2251779438, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-02-26T02:19:03.84Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-02T13:30:33.62Z"}, {"_id": "67be7a17cb8a85c6757d8cbe", "demographic_id": "6203621218383", "name": "Friends of people with birthdays in a week", "type": "life_events", "path": ["Friends of", "Friends of people with birthdays in a week"], "description": "Friends of people with a birthday in 0-7 days", "real_time_cluster": false, "audience_size_lower_bound": 1843435068, "audience_size_upper_bound": 2167879640, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-02-26T02:19:03.841Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-02T13:30:33.62Z"}, {"_id": "67be7a17cb8a85c6757d8cc0", "demographic_id": "6203621325983", "name": "Friends of women with a birthday in 0-7 days", "type": "life_events", "path": ["Friends of", "Friends of women with a birthday in 0-7 days"], "description": "Friends of women with a birthday in 0-7 days", "real_time_cluster": false, "audience_size_lower_bound": 1521570435, "audience_size_upper_bound": 1789366832, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-02-26T02:19:03.841Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-02T13:30:33.621Z"}, {"_id": "67be7a17cb8a85c6757d8cc2", "demographic_id": "6203621472783", "name": "Friends of men with a birthday in 0-7 days", "type": "life_events", "path": ["Friends of", "Friends of men with a birthday in 0-7 days"], "description": "Friends of men with a birthday in 0-7 days", "real_time_cluster": false, "audience_size_lower_bound": 1641293877, "audience_size_upper_bound": 1930161600, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-02-26T02:19:03.842Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-02T13:30:33.621Z"}, {"_id": "67be7a17cb8a85c6757d8cc4", "demographic_id": "6262428231783", "name": "Business decision makers", "type": "industries", "path": ["Business decision makers"], "description": "Business decision is a B2B audience segment that targets ads to people who are business decision makers in engineering/IT, operations, HR, strategy or marketing, based on their job titles.", "real_time_cluster": false, "audience_size_lower_bound": 40355, "audience_size_upper_bound": 47458, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-02-26T02:19:03.842Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-02T13:30:33.621Z"}, {"_id": "67be7a17cb8a85c6757d8cc6", "demographic_id": "6262428248783", "name": "IT decision makers", "type": "industries", "path": ["IT decision makers"], "description": "IT decision makers is a B2B audience segment that target ads to people who are IT decision makers based on their job titles.", "real_time_cluster": false, "audience_size_lower_bound": 13635, "audience_size_upper_bound": 16035, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-02-26T02:19:03.843Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-02T13:30:33.622Z"}, {"_id": "67be7a17cb8a85c6757d8cc8", "demographic_id": "6262428209783", "name": "Business decision maker titles and interests", "type": "industries", "path": ["Business decision maker titles and interests"], "description": "Business decision maker titles and interests is a B2B audience segment that targets ads to people who are business decision makers based on their job titles and interests.", "real_time_cluster": false, "audience_size_lower_bound": 40355, "audience_size_upper_bound": 47458, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-02-26T02:19:03.843Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-02T13:30:33.622Z"}, {"_id": "67be7a17cb8a85c6757d8cca", "demographic_id": "6377169550583", "name": "Company size: 1-10 employees", "type": "industries", "path": ["Company size: 1-10 employees"], "description": "People who work for companies with 1 to 10 employees.", "real_time_cluster": false, "audience_size_lower_bound": 608890, "audience_size_upper_bound": 716055, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-02-26T02:19:03.844Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-02T13:30:33.623Z"}, {"_id": "67be7a17cb8a85c6757d8ccc", "demographic_id": "6377134779583", "name": "Company size: 11-100 employees", "type": "industries", "path": ["Company size: 11-100 employees"], "description": "People who work for companies with 11 to 100 employees.", "real_time_cluster": false, "audience_size_lower_bound": 1175407, "audience_size_upper_bound": 1382279, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-02-26T02:19:03.844Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-02T13:30:33.623Z"}, {"_id": "67be7a17cb8a85c6757d8cce", "demographic_id": "6377169297783", "name": "Company size: 101-500 employees", "type": "industries", "path": ["Company size: 101-500 employees"], "description": "People who work for companies with 101 to 500 employees.", "real_time_cluster": false, "audience_size_lower_bound": 643938, "audience_size_upper_bound": 757272, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-02-26T02:19:03.845Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-02T13:30:33.623Z"}, {"_id": "67be7a17cb8a85c6757d8cd0", "demographic_id": "6377408290383", "name": "Company size: more than 500 employees", "type": "industries", "path": ["Company size: more than 500 employees"], "description": "People who work for companies with more than 500 employees.", "real_time_cluster": false, "audience_size_lower_bound": 737167, "audience_size_upper_bound": 866909, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-02-26T02:19:03.845Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-02T13:30:33.624Z"}, {"_id": "67be7a17cb8a85c6757d8cd2", "demographic_id": "6377168992983", "name": "Company revenue: $1M to $10M", "type": "industries", "path": ["Company revenue: $1M to $10M"], "description": "People who work for companies with revenue of $1M to $10M.", "real_time_cluster": false, "audience_size_lower_bound": 758342, "audience_size_upper_bound": 891811, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-02-26T02:19:03.845Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-02T13:30:33.624Z"}, {"_id": "67be7a17cb8a85c6757d8cd4", "demographic_id": "6377169088983", "name": "Company revenue: less than $1M", "type": "industries", "path": ["Company revenue: less than $1M"], "description": "People who work for companies with revenue of less than $1M.", "real_time_cluster": false, "audience_size_lower_bound": 310482, "audience_size_upper_bound": 365127, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-02-26T02:19:03.846Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-02T13:30:33.624Z"}, {"_id": "67be7a17cb8a85c6757d8cd6", "demographic_id": "6377408081983", "name": "Company revenue: more than $10M", "type": "industries", "path": ["Company revenue: more than $10M"], "description": "People who work for companies with revenue of more than $10M.", "real_time_cluster": false, "audience_size_lower_bound": 869928, "audience_size_upper_bound": 1023036, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-02-26T02:19:03.847Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-02T13:30:33.625Z"}, {"_id": "67be7a17cb8a85c6757d8cd8", "demographic_id": "6377408028783", "name": "Companies founded between 2000 and 2009", "type": "industries", "path": ["Companies founded between 2000 and 2009"], "description": "People who work for companies founded between 2000 and 2009.", "real_time_cluster": false, "audience_size_lower_bound": 662465, "audience_size_upper_bound": 779059, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-02-26T02:19:03.847Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-02T13:30:33.625Z"}, {"_id": "67be7a17cb8a85c6757d8cda", "demographic_id": "6377168689383", "name": "Companies founded between 2010 and now", "type": "industries", "path": ["Companies founded between 2010 and now"], "description": "People who work for companies founded between 2010 and now.", "real_time_cluster": false, "audience_size_lower_bound": 952430, "audience_size_upper_bound": 1120058, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-02-26T02:19:03.847Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-02T13:30:33.625Z"}, {"_id": "67be7a17cb8a85c6757d8cdc", "demographic_id": "6377134922183", "name": "Companies founded before 2000", "type": "industries", "path": ["Companies founded before 2000"], "description": "People who work for companies founded before 2000.", "real_time_cluster": false, "audience_size_lower_bound": 964905, "audience_size_upper_bound": 1134729, "created_by": "67864300b94c663e5d253cb5", "created_at": "2025-02-26T02:19:03.848Z", "updated_by": "67864300b94c663e5d253cb5", "updated_at": "2025-03-02T13:30:33.626Z"}]