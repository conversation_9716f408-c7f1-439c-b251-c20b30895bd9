package pipelines

import (
	"go.mongodb.org/mongo-driver/bson"
)

func PipelineListTableUsers() []bson.M {
	pipeline := []bson.M{
		{
			"$lookup": bson.M{
				"from":         "admin_roles",
				"localField":   "role_id",
				"foreignField": "_id",
				"as":           "role",
			},
		},
		{
			"$lookup": bson.M{
				"from":         "admin_users",
				"localField":   "created_by",
				"foreignField": "_id",
				"as":           "user_created",
			},
		},
		{
			"$lookup": bson.M{
				"from":         "admin_users",
				"localField":   "updated_by",
				"foreignField": "_id",
				"as":           "user_updated",
			},
		},
		{
			"$lookup": bson.M{
				"from":         "admin_client",
				"localField":   "client_id",
				"foreignField": "_id",
				"as":           "client",
			},
		},
		{
			"$unwind": bson.M{
				"path":                       "$client",
				"preserveNullAndEmptyArrays": true,
			},
		},
		{
			"$unwind": bson.M{
				"path":                       "$role",
				"preserveNullAndEmptyArrays": true,
			},
		},
		{
			"$unwind": bson.M{
				"path":                       "$user_created",
				"preserveNullAndEmptyArrays": true,
			},
		},
		{
			"$unwind": bson.M{
				"path":                       "$user_updated",
				"preserveNullAndEmptyArrays": true,
			},
		},
		{
			"$addFields": bson.M{
				"latest_time": bson.M{
					"$cond": bson.M{
						"if":   bson.M{"$gt": bson.A{"$updated_at", "$created_at"}},
						"then": "$updated_at",
						"else": "$created_at",
					},
				},
			},
		},
		{
			"$sort": bson.M{
				"latest_time": -1, // sort theo thời gian mới nhất
			},
		},

		// Optionally: remove `latest_time` nếu không muốn trả về
		{
			"$project": bson.M{
				"latest_time": 0, // ẩn field này
			},
		},
		// {
		// 	"$sort": bson.M{
		// 		"updated_at": -1,
		// 		"created_at": -1,
		// 	},
		// },
	}

	return pipeline
}
