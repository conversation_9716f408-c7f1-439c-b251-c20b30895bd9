package usecase

import (
	"context"
	"encoding/json"
	"fmt"
	"godsp/conf"
	clientE "godsp/modules/admin/client/entity"
	userRp "godsp/modules/admin/user/repository/mongo"
	adAccRes "godsp/modules/facebook/ad_account/transport/response"

	"godsp/modules/facebook/audience/common/enums"
	"godsp/modules/facebook/audience/common/errs"
	"godsp/modules/facebook/audience/entity"
	"godsp/modules/facebook/audience/mapping"
	"godsp/modules/facebook/audience/transport/requests"
	"godsp/modules/facebook/audience/transport/responses"
	"godsp/modules/facebook/common/fbenums"
	"godsp/modules/facebook/iface_repo"
	"godsp/pkg/fb-marketing/fb"
	v20 "godsp/pkg/fb-marketing/marketing/v20"
	"godsp/pkg/gos/utils"

	"github.com/dev-networldasia/dspgos/sctx"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo/options"
)

type ApiAudienceRepo interface {
	UpsertAudienceRepo(ctx context.Context, filter bson.M, update bson.M) error
	FindAudienceRepo(ctx context.Context, filter interface{}, opts ...*options.FindOptions) (*[]entity.CustomAudienceEntity, error)
	CountAudiencesRepo(ctx context.Context, filter interface{}) (int64, error)
	InsertAudienceRepo(ctx context.Context, customAudience *entity.CustomAudienceEntity) error
	FindOneAudienceRepo(ctx context.Context, filter interface{}) (*entity.CustomAudienceEntity, error)
	DeleteAudienceRepo(ctx context.Context, filter interface{}, update interface{}) error
}

type ApiUserRepo interface {
	GetRoleOfUser(ctx context.Context, userId primitive.ObjectID, opts ...*options.AggregateOptions) (*userRp.RoleInfo, error)
}

type ClientRepo interface {
	FindClientRepo(ctx context.Context, filter interface{}, opts ...*options.FindOptions) ([]*clientE.ClientEntity, error)
}

type apiAudienceUsc struct {
	fbService  *v20.Service
	repo       ApiAudienceRepo
	logger     sctx.Logger
	userRepo   ApiUserRepo
	clientRepo ClientRepo
	adAcc      iface_repo.AdAccountRepo
}

func NewApiAudienceUsc(fbService *v20.Service, repo ApiAudienceRepo, userRepo ApiUserRepo, clientRepo ClientRepo, adAcc iface_repo.AdAccountRepo, logger sctx.Logger) *apiAudienceUsc {
	return &apiAudienceUsc{
		fbService:  fbService,
		repo:       repo,
		logger:     logger,
		userRepo:   userRepo,
		clientRepo: clientRepo,
		adAcc:      adAcc}
}

func (usc *apiAudienceUsc) GetRoleByUserId(ctx context.Context, userId primitive.ObjectID) (*string, error) {
	role, err := usc.userRepo.GetRoleOfUser(ctx, userId)
	if err != nil {
		return nil, err
	}
	return &role.RoleName, nil
}

/**
 * Reload Audience by act
 */
func (usc *apiAudienceUsc) ReloadAudienceUsc(ctx context.Context, adAccountId string, userId primitive.ObjectID) []string {
	var errs []string
	audiences, err := usc.fbService.Audiences.ListCustom(ctx, adAccountId)
	if err != nil {
		usc.logger.Error(err)
		errs = append(errs, err.Error())
		return errs
	}
	saveAudiences, err := usc.fbService.Audiences.ListSavedAudiences(ctx, adAccountId)
	if err != nil {
		usc.logger.Error(err)
		errs = append(errs, err.Error())
		return errs
	}

	// filter
	filtering := []fb.Filter{}
	filtering = append(filtering, fb.Filter{
		Field:    "delete_time",
		Operator: "GREATER_THAN",
		Value:    0,
	})

	go usc.upsertCustomAudience(ctx, audiences, userId)
	go usc.upsertSaveAudience(ctx, saveAudiences, userId)

	return nil
}

// save custom audience
func (usc *apiAudienceUsc) upsertCustomAudience(ctx context.Context, audiences []v20.CustomAudience, userId primitive.ObjectID) {
	for _, audience := range audiences {
		audienceUpsert := mapping.MapperFBCustomAudienceToUpsert(&audience, userId)

		filter := bson.M{"custom_audience_id": audience.ID}
		if err := usc.repo.UpsertAudienceRepo(ctx, filter, audienceUpsert); err != nil {
			usc.logger.Error(err)
		}
	}
}

// save save audience
func (usc *apiAudienceUsc) upsertSaveAudience(ctx context.Context, audiences []v20.SavesavedAudience, userId primitive.ObjectID) {
	for _, audience := range audiences {
		saveAudienceUpsert := mapping.MapperFBSaveAudienceToUpsert(&audience, userId)

		filter := bson.M{"custom_audience_id": audience.ID}
		if err := usc.repo.UpsertAudienceRepo(ctx, filter, saveAudienceUpsert); err != nil {
			usc.logger.Error(err)
		}
	}
}

/**
 * Api list adset
 */
func (usc *apiAudienceUsc) ListAudiencesUsc(ctx context.Context, payload *requests.ListAudiencesReq) (*responses.ListAudiencesResp, error) {
	if payload == nil {
		return nil, errs.ErrListAudiencesEmpty
	}

	limit := payload.Length
	skip := payload.Start

	// Create filter (optional, add search or other conditions here)
	filter := bson.M{
		"data_source": bson.M{
			"$exists": true,
			"$ne":     []interface{}{nil, "N/A"},
		},
	}
	if payload.SearchValue != nil && *payload.SearchValue != "" {
		filter["name"] = bson.M{"$regex": *payload.SearchValue, "$options": "i"}
	}

	if payload.Filter.TypeSearch != "" && payload.Filter.TypeSearch == "other-source" {
		filter["$and"] = []bson.M{
			{"subtype": bson.M{"$in": []interface{}{enums.SUBTYPE_CUSTOM, enums.SUBTYPE_WEBSITE}}},
		}
	}

	// } else {
	// 	filter["type"] = bson.M{"$ne": enums.TYPE_SAVE_AUDIENCES}
	// }

	// filter["account_id"] = payload.AccountID
	// filter["account_id"] = utils.GetAdAccount(ctx)

	allowDiskUse := fbenums.ALLOW_DISK_USE_TRUE
	opts := &options.FindOptions{
		AllowDiskUse: &allowDiskUse,
		Skip:         &skip,
		Limit:        &limit,
	}

	// Handle sorting
	// sortOrder := payload.SortOrder
	// var sortField string
	// if payload.SortField == "" {
	// 	sortField = "created_at"
	// } else {
	// 	sortField = payload.SortField
	// }

	opts.SetSort(bson.M{"create_at": 1})

	adsets, err := usc.repo.FindAudienceRepo(ctx, filter, opts)

	if err != nil {
		return nil, err
	}

	total, err := usc.repo.CountAudiencesRepo(ctx, bson.M{})
	if err != nil {
		return nil, err
	}

	totalFiltered := total
	if len(filter) != 0 {
		totalFiltered, err = usc.repo.CountAudiencesRepo(ctx, filter)
		if err != nil {
			return nil, err
		}

		total = totalFiltered
	}

	data := mapping.MapperEntityToDatatable(adsets)

	return &responses.ListAudiencesResp{
		Draw:            payload.Draw,
		Data:            data,
		RecordsTotal:    total,
		RecordsFiltered: totalFiltered,
	}, nil
}

/**
 * Api list datatable
 */
func (usc *apiAudienceUsc) ListDatatableAudienceUsc(ctx context.Context, payload *requests.ListTableAudienceReq) (*responses.DatatableAudienceResq, error) {
	jsonData, _ := json.MarshalIndent(payload, "", "  ")
	fmt.Println("\n-------- payload audience -------> \n", string(jsonData))
	if payload == nil {
		return nil, errs.ErrAudienceDataTableEmpty
	}

	user, err := utils.GetInfoUserBasic(ctx)
	if err != nil {
		return nil, err
	}

	filter := utils.GetFilterFBAds(ctx, user)

	allowDiskUse := fbenums.ALLOW_DISK_USE_TRUE
	opts := &options.FindOptions{
		AllowDiskUse: &allowDiskUse,
		Sort: bson.M{
			"time_updated": -1,
		},
	}

	if user.RoleName == conf.SysConf.RoleAdmin {
		filter["account_id"] = payload.AccountID
	}
	if payload.AccountID == "" {
		delete(filter, "account_id")
	}
	if payload.ClientIDStr != "" {
		filter["client_id"] = payload.ClientID
	}

	audiences, err := usc.repo.FindAudienceRepo(ctx, filter, opts)

	if err != nil {
		return nil, err
	}

	data := mapping.MapperAudienceToDatatable(audiences)

	return &responses.DatatableAudienceResq{
		Draw: payload.Draw,
		Data: data,
	}, nil
}

func (usc *apiAudienceUsc) GetCustomAudienceUsc(ctx context.Context, payload requests.GetOneAudienceReq) (*entity.CustomAudienceEntity, error) {
	user, err := utils.GetInfoUserBasic(ctx)
	if err != nil {
		return nil, err
	}
	filter := utils.GetFilterFBAds(ctx, user)

	audience, err := usc.repo.FindOneAudienceRepo(ctx, filter)
	if err != nil {
		return nil, err
	}
	return audience, nil
}

func (usc *apiAudienceUsc) CreateCustomAudienceUsc(ctx context.Context, payload *requests.CreateCustomAudienceReq) (*entity.CustomAudienceEntity, error) {
	// user, err := utils.GetInfoUserBasic(ctx)
	// if err != nil {
	// 	return nil, err
	// }

	if payload == nil {
		return nil, errs.ErrCreateCustomAudienceEmpty
	}
	audienceEntity, audienceFB := mapping.MapperCreateAudienceReqToAundience(payload, payload.CreatedBy)

	audienceID, err := usc.fbService.Audiences.Create(ctx, payload.AccountID, *audienceFB)
	if err != nil {
		usc.logger.Error(err)
		return nil, err
	}

	audienceEntity.CustomAudienceID = audienceID

	if err := usc.repo.InsertAudienceRepo(ctx, audienceEntity); err != nil {
		usc.logger.Error(err)
		return nil, err
	}

	go func() {
		if err := usc.ReloadCustomAudienceUsc(ctx, audienceID, payload.CreatedBy); err != nil {
			usc.logger.Error(err)
		}
	}()

	return audienceEntity, nil
}

func (usc *apiAudienceUsc) UpdateCustomAudienceUcs(ctx context.Context, payload *requests.UpdateCustomAudienceReq) error {
	if payload == nil {
		return errs.ErrUpdateCustomAudienceEmpty
	}

	audienceID := payload.AudienceID
	audienceUpdate := mapping.MapperEntityToFBForUpdate(payload)

	err := usc.fbService.Audiences.UpdateCustomAudience(ctx, *audienceUpdate, payload.AudienceID)
	if err != nil {
		usc.logger.Error(err)
		return err
	}

	go func() {
		if err := usc.ReloadCustomAudienceUsc(ctx, audienceID, payload.UpdatedBy); err != nil {
			usc.logger.Error(err)
		}
	}()

	return nil
}

func (usc *apiAudienceUsc) DeleteCustomAudienceUsc(ctx context.Context, payload *requests.DeleteCustomAudienceReq) error {
	if payload == nil {
		return errs.ErrDeleteCustomAudienceEmpty
	}

	user, err := utils.GetInfoUserBasic(ctx)
	if err != nil {
		return err
	}
	filter := utils.GetFilterFBAds(ctx, user)
	filter["custom_audience_id"] = bson.M{"$in": payload.AudienceIDs}

	countAud, err := usc.repo.CountAudiencesRepo(ctx, filter)
	if err != nil {
		usc.logger.Error(err)
		return err
	}
	if countAud < int64(len(payload.AudienceIDs)) {
		return errs.ErrNotFoundAudienceID
	}

	audienceDelete, err := usc.repo.FindAudienceRepo(ctx, filter)
	if err != nil || len(*audienceDelete) == 0 {
		usc.logger.Error(err)
		return err
	}

	for _, audienceID := range payload.AudienceIDs {
		err := usc.fbService.Audiences.Delete(ctx, audienceID)
		if err != nil {
			usc.logger.Error(err)
			return err
		}
	}

	update := mapping.MapperDeleteAudienciesReq(payload)
	err = usc.repo.DeleteAudienceRepo(ctx, filter, update)
	if err != nil {
		usc.logger.Error(err)
		return err
	}

	return nil
}

func (usc *apiAudienceUsc) ReloadCustomAudienceUsc(c context.Context, audienceID string, userId primitive.ObjectID) error {
	customAudience, err := usc.fbService.Audiences.GetCustomAudience(c, audienceID)
	if err != nil {
		usc.logger.Error(err)
		return err
	}

	if err := usc.processCreateUpdateCustomAudience(c, customAudience, userId); err != nil {
		usc.logger.Error(err)
		return err

	}

	return nil
}

func (usc *apiAudienceUsc) processCreateUpdateCustomAudience(ctx context.Context, customAudience *v20.CustomAudience, userId primitive.ObjectID) error {

	filter := bson.M{"custom_audience_id": customAudience.ID}
	customAudienceUpsert := mapping.MapperCustomAudienceFBToUpsertAudience(customAudience, userId)

	err := usc.repo.UpsertAudienceRepo(ctx, filter, customAudienceUpsert)
	if err != nil {
		return err
	}
	return nil
}

func (usc *apiAudienceUsc) GetDetailCustomAudienceUsc(ctx context.Context, payload *requests.GetDetailCustomAudienceReq) (*entity.CustomAudienceEntity, error) {

	user, err := utils.GetInfoUserBasic(ctx)
	if err != nil {
		return nil, err
	}
	filter := utils.GetFilterFBAds(ctx, user)
	filter["custom_audience_id"] = payload.AudienceID

	customAudience, err := usc.repo.FindOneAudienceRepo(ctx, filter)

	if err != nil {
		return nil, err
	}

	return customAudience, nil
}

/**
 * List client
 */
func (usc *apiAudienceUsc) ListClientAudienceUsc(ctx context.Context) ([]*clientE.ClientEntity, error) {
	return usc.clientRepo.FindClientRepo(ctx, bson.M{})
}

/**
 * list ad account
 */
func (usc *apiAudienceUsc) ListAdAccountAudienceUsc(ctx context.Context) ([]*adAccRes.AdAccountEditClient, error) {
	allowDiskUse := fbenums.ALLOW_DISK_USE_TRUE
	opts := &options.FindOptions{
		AllowDiskUse: &allowDiskUse,
		Sort:         bson.D{{Key: "name", Value: -1}},
		Projection:   bson.M{"id": 1, "name": 1, "account_id": 1},
	}
	return usc.adAcc.FindAdAccountEditClientRepo(ctx, bson.M{}, opts)
}
