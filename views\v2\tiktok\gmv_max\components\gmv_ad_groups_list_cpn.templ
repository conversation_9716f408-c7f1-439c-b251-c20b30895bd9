package components

import "godsp/modules/tiktok/gmv_max_campaign/common/enums"

templ GmvAdGroupsListCpn() {
	<div class="row mt-4 fade-in">
		<div class="col-12">
			<div class="card">
				<div class="card-header">
					<div class="d-flex justify-content-between align-items-center">
						<h5 class="card-title mb-0">Ad Groups</h5>
						<div class="d-flex gap-2">
							<button type="button" class="btn btn-primary">
								<i class="ri-add-line align-bottom me-1"></i> Create Ad Group
							</button>
						</div>
					</div>
					<p class="text-muted mb-0 mt-2">
						Manage ad groups within this GMV Max campaign.
					</p>
				</div>
				<div class="card-body">
					<!-- Filters and Search -->
					<div class="row mb-3">
						<div class="col-lg-6">
							<div class="d-flex gap-2">
								<div class="search-box">
									<input type="text" class="form-control" id="adGroupSearchInput" placeholder="Search ad groups...">
									<i class="ri-search-line search-icon"></i>
								</div>
							</div>
						</div>
						<div class="col-lg-6">
							<div class="d-flex gap-2 justify-content-end">
								<div class="custom-select" id="adGroupStatusDropdown">
									<div class="selected">-- Select status --</div>
									<ul class="options">
										<li data-value={ enums.STATUS_DELIVERY_OK }>
											<span class="status-item-circle active"></span> Active
										</li>
										<li data-value={ enums.STATUS_DISABLE }>
											<span class="status-item-circle inactive"></span> Inactive
										</li>
										<li data-value={ enums.STATUS_DELETE }>
											<span class="status-item-circle deleted"></span> Deleted
										</li>
										<li data-value={ enums.STATUS_DELIVERY_NOT }>
											<span class="status-item-circle not-delivering"></span> Not delivering
										</li>
									</ul>
								</div>
								<input type="hidden" name="adGroupStatus" id="adGroupStatusFilter">
								
								<button class="btn btn-outline-secondary">
									<i class="ri-grid-line"></i>
								</button>
								<button class="btn btn-outline-secondary">
									<i class="ri-external-link-line"></i>
								</button>
								<button class="btn btn-outline-secondary">
									<i class="ri-file-copy-line"></i>
								</button>
							</div>
						</div>
					</div>
					
					<!-- Ad Groups Table Container -->
					<div id="ad-groups-table-container">
						<!-- Table will be loaded here -->
						<div class="text-center py-4">
							<div class="spinner-border text-primary" role="status">
								<span class="visually-hidden">Loading...</span>
							</div>
							<p class="mt-2 text-muted">Loading ad groups...</p>
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>
}
