package requests

import (
	"godsp/modules/facebook/ad/common/errs"
	"godsp/modules/facebook/common/fbenums"
	"godsp/modules/facebook/common/fbrules"
	v20 "godsp/pkg/fb-marketing/marketing/v20"
	"strings"

	"github.com/go-playground/validator/v10"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

type UpdateAdReq struct {
	AdID          string                      `json:"ad_id" validate:"required,numeric,gt=0"`
	AccountID     string                      `json:"account_id" validate:"omitempty,numeric,gt=0"`
	AdsetId       string                      `json:"adset_id" validate:"omitempty,numeric,gt=0"`
	Name          string                      `json:"name" validate:"required"`
	TrackingSpecs []v20.ConversionActionQuery `json:"tracking_specs" validate:"omitempty"`
	Status        string                      `json:"status"  validate:"omitempty,ruleStatus"`
	Creative      *UpdateCreativeReq          `json:"creative" validate:"omitempty"`

	UserId primitive.ObjectID `json:"-"`
}

type UpdateCreativeReq struct {
	CreativeID string `json:"creative_id" validate:"omitempty"`
}

func (req *UpdateAdReq) Validate() []string {
	validate := validator.New()

	validate.RegisterValidation("ruleStatus", fbrules.RuleFBStatus)
	var validationErrors []string
	req.Name = strings.TrimSpace(req.Name)

	err := validate.Struct(req)
	if err != nil {
		for _, err := range err.(validator.ValidationErrors) {
			switch err.Field() {
			case "AdID":
				validationErrors = append(validationErrors, errs.ErrAdID.Error())
			case "AccountID":
				validationErrors = append(validationErrors, errs.ErrAdAccountID.Error())
			case "AdsetId":
				validationErrors = append(validationErrors, errs.ErrAdAdsetId.Error())
			case "Name":
				validationErrors = append(validationErrors, errs.ErrAdName.Error())
			case "Status":
				validationErrors = append(validationErrors, errs.ErrAdStatus.Error())
			case "TrackingSpecs":
				validationErrors = append(validationErrors, errs.ErrAdTrackingSpecs.Error())
			}
		}
	}

	// set value when validate ok
	if validationErrors == nil {
		req.Status = fbenums.StatusFB[req.Status]
	}

	return validationErrors
}

type UpdateNameStatusAdReq struct {
	AdId   string             `json:"ad_id" validate:"required,numeric,gt=0"`
	Name   string             `json:"name" validate:"omitempty"`
	Status string             `json:"status"  validate:"omitempty,ruleStatus"`
	UserId primitive.ObjectID `json:"-"`
}

func (req *UpdateNameStatusAdReq) Validate() []string {
	validate := validator.New()

	validate.RegisterValidation("ruleStatus", fbrules.RuleFBStatus)
	var validationErrors []string
	req.Name = strings.TrimSpace(req.Name)
	err := validate.Struct(req)
	if err != nil {
		for _, err := range err.(validator.ValidationErrors) {
			switch err.Field() {
			case "Name":
				validationErrors = append(validationErrors, errs.ErrAdName.Error())
			case "Status":
				validationErrors = append(validationErrors, errs.ErrAdStatus.Error())
			}
		}
	}
	if req.Status == "" && req.Name == "" {
		validationErrors = append(validationErrors, errs.ErrFBAdUpdateInvalidate.Error())
	}
	return validationErrors
}
