package routes

import (
	"github.com/dev-networldasia/dspgos/sctx"

	"github.com/gofiber/fiber/v2"
)

func SetupRouterAdCreative(app *fiber.App, serviceCtx sctx.ServiceContext, midds ...fiber.Handler) {
	apiGroup := app.Group("/dsp/facebook/api/ad-creatives")
	{
		if len(midds) > 0 {
			for _, midd := range midds {
				apiGroup.Use(midd)
			}
		}
		compApi := ComposerAdCreativeApiService(serviceCtx)
		apiGroup.Patch("/reload", compApi.ReloadAdCreativeApi()).Name("fb.creatives.reload")
		apiGroup.Post("/detail", compApi.DetailAdCreativeApi()).Name("fb.creatives.detail")

		apiGroup.Post("/create", compApi.CreateAdCreativeApi()).Name("fb.creatives.create")
		apiGroup.Delete("/delete", compApi.DeleteAdCreativeApi()).Name("fb.creatives.delete")
		apiGroup.Post("/generate-preview", compApi.GenerateAdCreativePreviewApi()).Name("fb.api.creatives.generate_preview")
		apiGroup.Post("/generate-instagram-preview", compApi.GenerateInstagramAdCreativePreviewApi()).Name("fb.api.creatives.generate_instagram_preview")
	}
}
