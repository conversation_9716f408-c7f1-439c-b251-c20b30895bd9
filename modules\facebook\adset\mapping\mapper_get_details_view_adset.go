package mapping

import (
	"encoding/json"
	"godsp/modules/facebook/adset/entity"
	"godsp/modules/facebook/adset/transport/responses"
	v20 "godsp/pkg/fb-marketing/marketing/v20"
	"godsp/pkg/gos/utils"
)

/**
 * Mappper update adset req to adset
 */
// func MapperEntityToAdsetDetailsView(adset *responses.AdsetDetailView,) {
func MapperEntityToAdsetDetailsView(adset *entity.AdsetDetailViewEntity, flexibale *[][]v20.InterestResultCheckValidate) *responses.AdsetDetailView {
	st := utils.FormatTimeToTimeByLocation(adset.StartTime)
	adset.StartTime = st

	if adset.EndTime != nil {
		t := utils.FormatTimeToTimeByLocation(*adset.EndTime)
		adset.EndTime = &t
	}

	adsetView := responses.AdsetDetailView{}
	ConvertStruct(&adset, &adsetView)
	if flexibale != nil {
		adsetView.Targeting.FlexibleSpec = *flexibale
	}

	return &adsetView
}

func ConvertStruct(src, dest interface{}) error {
	data, err := json.Marshal(src) // Bước 1: Chuyển struct thành JSON
	if err != nil {
		return err
	}
	return json.Unmarshal(data, dest) // Bước 2: Parse JSON vào struct đích
}

/**
 * Mappper update adset req to adset Flexible
 */
// func MapperEntityToAdsetDetailsView(adset *responses.AdsetDetailView,) {
// func MapperEntityToAdsetDetailsViewFlexibaleSpec(adset *entity.AdsetDetailViewEntity, flexibleSpec *[][]v20.InterestResultCheckValidate) {
// 	st := utils.FormatTimeToTimeByLocation(adset.StartTime)
// 	adset.StartTime = st

// 	if adset.EndTime != nil {
// 		t := utils.FormatTimeToTimeByLocation(*adset.EndTime)
// 		adset.EndTime = &t
// 	}

// }
