package admrules

import (
	"godsp/modules/admin/common/admconst"

	"github.com/go-playground/validator/v10"
)

func RuleStatusCreate(fl validator.FieldLevel) bool {
	status := fl.Field().Interface().(string)
	_, ok := admconst.StatusCreateNameValue[status]
	return ok
}

func RuleStatusUpdate(fl validator.FieldLevel) bool {
	status := fl.Field().Interface().(int)
	_, ok := admconst.StatusFullName[status]
	return ok
}

func RuleStatus(fl validator.FieldLevel) bool {
	status := fl.Field().Interface().(string)
	_, ok := admconst.StatusFullNameValue[status]
	return ok
}
