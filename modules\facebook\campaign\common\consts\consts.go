package consts

const (
	CAMPAIGN_ORDER_DEFAULT = "updated_time"
)

var (
	//field camp sord data table
	FieldCampaignSort = map[int]string{
		1: "updated_time",
		2: "name",
		3: "status",
	}
	// GetDetailsCampaignPipeline_BK = []bson.D{
	// 	{
	// 		{
	// 			"$lookup", bson.D{
	// 				{"from", "fb_adsets"},
	// 				{"localField", "campaign_id"},
	// 				{"foreignField", "campaign_id"},
	// 				{"as", "adsets"},
	// 			},
	// 		},
	// 	},
	// 	{

	// 		{"$unwind", bson.D{
	// 			{"path", "$adsets"},
	// 			{"preserveNullAndEmptyArrays", true},
	// 		}},
	// 	},
	// 	{

	// 		{"$lookup", bson.D{
	// 			{"from", "fb_ads"},
	// 			{"localField", "adsets.adset_id"},
	// 			{"foreignField", "adset_id"},
	// 			{"as", "adsets.ads"},
	// 		}},
	// 	},
	// 	{

	// 		{"$addFields", bson.D{
	// 			{"adsets.ads", bson.D{
	// 				{"$map", bson.D{
	// 					{"input", "$adsets.ads"},
	// 					{"as", "ad"},
	// 					{"in", bson.D{
	// 						{"ad_id", "$$ad.ad_id"},
	// 						{"name", "$$ad.name"},
	// 					}},
	// 				}},
	// 			}},
	// 		}},
	// 	},
	// 	{
	// 		{"$group", bson.D{
	// 			{"_id", "$campaign_id"},
	// 			{"campaign", bson.D{{"$first", "$$ROOT"}}}, // Lấy toàn bộ dữ liệu của campaign
	// 			{"adsets", bson.D{
	// 				{"$push", bson.D{
	// 					{"adset_id", "$adsets.adset_id"},
	// 					{"name", "$adsets.name"},
	// 					{"ads", "$adsets.ads"},
	// 				}},
	// 			}},
	// 		}},
	// 	},
	// 	{ // AddFields: Thêm Adsets vào campaign
	// 		{"$addFields", bson.D{
	// 			{"campaign.adsets", "$adsets"},
	// 		}},
	// 	},
	// 	{ // Project: Chỉ lấy trường campaign
	// 		{"$replaceRoot", bson.D{
	// 			{"newRoot", "$campaign"},
	// 		}},
	// 	},
	// }
)
