package usecase

import (
	"context"
	"errors"
	"fmt"
	"time"

	"godsp/conf"
	userEt "godsp/modules/admin/user/entity"
	userRp "godsp/modules/admin/user/repository/mongo"
	adserverReportE "godsp/modules/facebook/adserver_report/entity"

	// reportRes "godsp/modules/facebook/adserver_report/transport/responses"
	campEnum "godsp/modules/facebook/campaign/common/enum"

	"godsp/modules/facebook/campaign/common/errs"
	"godsp/modules/facebook/campaign/common/pipelines"
	"godsp/modules/facebook/campaign/entity"
	"godsp/modules/facebook/campaign/mapping"
	"godsp/modules/facebook/campaign/transport/requests"
	"godsp/modules/facebook/campaign/transport/responses"
	"godsp/modules/facebook/common/fbenums"
	v20 "godsp/pkg/fb-marketing/marketing/v20"
	"godsp/pkg/gos/utils"
	"godsp/pkg/sctx/core"

	"github.com/dev-networldasia/dspgos/sctx"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo/options"
)

type ApiCampaignRepo interface {
	FindOneCampaignRepo(ctx context.Context, filter interface{}, opts ...*options.FindOneOptions) (*entity.CampaignEntity, error)
	UpsertCampaignRepo(ctx context.Context, filter bson.M, campaignBSon bson.M) error
	FindCampaignRepo(ctx context.Context, filter interface{}, opts ...*options.FindOptions) (*[]entity.CampaignEntity, error)
	AggregateCampaignRepo(ctx context.Context, pipeline []bson.M, opts ...*options.AggregateOptions) (*entity.CampaignDetailResponseEntity, error)
	CountCampaignRepo(ctx context.Context, filter interface{}) (int64, error)
	DeleteCampaignRepo(ctx context.Context, filter interface{}, opts ...*options.DeleteOptions) error
	DeleteCampaignManyTransactionRepo(ctx context.Context, filter interface{}, opts ...*options.DeleteOptions) error
	// UpdateManyCampignRepo(ctx context.Context, filter interface{}, update interface{}) error
}

type ApiFBReportDetailRepo interface {
	FindFBReportDetailRepo(ctx context.Context, filter bson.M, opts ...*options.FindOptions) (*[]adserverReportE.FBReportDetailEntity, error)
	GetMetricsColumnForCampaignsFBReportDetailRepo(ctx context.Context, filter bson.M) (*[]adserverReportE.FBReportDetailEntity, error)
	// GetMetricsColumnForCampaignsFBReportDetailRepo(ctx context.Context, filter bson.M) (*[]reportRes.FBReportDetailTableItem, error)
}
type ApiUserRepo interface {
	FindOneUserRepo(ctx context.Context, filter interface{}, opts ...*options.FindOneOptions) (*userEt.UserEntity, error)
	GetRoleOfUser(ctx context.Context, userId primitive.ObjectID, opts ...*options.AggregateOptions) (*userRp.RoleInfo, error)
}

type apiCampaignUsc struct {
	fbService          *v20.Service
	repo               ApiCampaignRepo
	fbReportDetailRepo ApiFBReportDetailRepo
	userRepo           ApiUserRepo
	logger             sctx.Logger
}

func NewApiCampaignUsc(fbService *v20.Service, repo ApiCampaignRepo, fbReportDetailRepo ApiFBReportDetailRepo, userRepo ApiUserRepo, logger sctx.Logger) *apiCampaignUsc {
	return &apiCampaignUsc{
		fbService:          fbService,
		repo:               repo,
		fbReportDetailRepo: fbReportDetailRepo,
		userRepo:           userRepo,
		logger:             logger,
	}
}

/**
 * Get Role By UserId
 * @return string
 */
func (usc *apiCampaignUsc) GetRoleByUserId(ctx context.Context, userId primitive.ObjectID) (*string, error) {
	role, err := usc.userRepo.GetRoleOfUser(ctx, userId)
	if err != nil {
		return nil, err
	}
	return &role.RoleName, nil
}

/**
 * Reload Campaign Facebook V20
 * - Nếu ko có tạo mới
 * - Nếu có rồi thì update
 */
func (usc *apiCampaignUsc) ReloadCampaignUsc(ctx context.Context, accountId string, userId primitive.ObjectID) []string {
	var errStr []string

	campaigns, err := usc.fbService.Campaigns.List(accountId).Do(ctx)
	// jsonData, _ := json.MarshalIndent(campaigns[0], "", "  ")
	// fmt.Println("upsert campaign data: ", string(jsonData))
	// jsonData, _ := json.MarshalIndent(campaigns, "", "  ")
	// fmt.Println("Campaigns: %v", string(jsonData))

	if err != nil {
		usc.logger.Error(err)
		errStr = append(errStr, errs.ErrFBCampaignList.Error())
		return errStr
	}

	if campaigns == nil {
		usc.logger.Error(errs.ErrFBCampaignList)
		errStr = append(errStr, errs.ErrFBCampaignList.Error())
		return errStr
	}

	for _, camp := range campaigns {
		filter := bson.M{"campaign_id": camp.ID}
		updateData := mapping.MapperCampaignsToUpsert(&camp, userId, primitive.ObjectID{}, nil)
		if err := usc.repo.UpsertCampaignRepo(ctx, filter, updateData); err != nil {
			usc.logger.Error(err)
			errStr = append(errStr, err.Error())
		}
	}

	return errStr
}

/**
 * Reload Campaign
 */
func (usc *apiCampaignUsc) ReloadCampaignDetail(ctx context.Context, campaignId string, userId primitive.ObjectID) error {

	camp, err := usc.fbService.Campaigns.Get(ctx, campaignId)
	if err != nil {
		usc.logger.Error(err)
		return err
	}
	if camp == nil {
		usc.logger.Error(errs.ErrReloadCampaignIdFBNotExist)
		return errs.ErrReloadCampaignIdFBNotExist
	}

	filter := bson.M{"campaign_id": campaignId}
	updateData := mapping.MapperCampaignsToUpsert(camp, userId, primitive.ObjectID{}, nil)

	if err := usc.repo.UpsertCampaignRepo(ctx, filter, updateData); err != nil {
		usc.logger.Error(err)
		return err
	}

	return nil
}

/**
 * Create Campaign
 */
func (usc *apiCampaignUsc) CreateCampaignUsc(ctx context.Context, payload *requests.CreateCampaignReq) (*entity.CampaignEntity, error) {
	// user, err := utils.GetInfoUserBasic(ctx)
	// if err != nil {
	// 	return nil, err
	// }

	// filter := utils.GetFilterFBAds(ctx, user)
	// userId := payload.UserId
	// roleName, err := usc.GetRoleByUserId(ctx, payload.UserId)
	// if err != nil {
	// 	usc.logger.Error(err)
	// }

	// isAdminUser := true
	// if *roleName != "ADMIN" {
	// 	isAdminUser = false
	// }

	campEntity, campFb := mapping.MapperCreateCampaignRequestToCampaigns(payload)

	campId, err := usc.fbService.Campaigns.Create(ctx, *campFb)
	if err != nil {
		usc.logger.Error(err)
		return nil, err
	}

	camp, err := usc.fbService.Campaigns.Get(ctx, campId)
	if err != nil {
		usc.logger.Error(err)
		return nil, err
	}
	if camp == nil {
		usc.logger.Error(errs.ErrReloadCampaignIdFBNotExist)
		return nil, errs.ErrReloadCampaignIdFBNotExist
	}

	filter := bson.M{"campaign_id": campId}
	updateData := mapping.MapperCampaignsToUpsert(camp, payload.UserId, payload.ClientID, &campEntity.Approve)

	if err := usc.repo.UpsertCampaignRepo(ctx, filter, updateData); err != nil {
		usc.logger.Error(err)
		return nil, err
	}

	campEntity, err = usc.FindOneCampaignUsc(ctx, campId)

	if err != nil {
		usc.logger.Error(err)
		return nil, err
	}
	return campEntity, nil
}

/**
 * Update Campaign
 */
func (usc *apiCampaignUsc) UpdateCampaignUsc(ctx context.Context, payload *requests.UpdateCampaignReq) error {
	filter := bson.M{
		"campaign_id": payload.CampaignID,
	}
	roleName, err := usc.GetRoleByUserId(ctx, payload.UserId)
	if err != nil {
		usc.logger.Error(err)
	}

	isAdminUser := true
	if *roleName != "ADMIN" {
		filter["account_id"] = payload.AccountID
		filter["list_user_ids"] = payload.UserId
		isAdminUser = false
	}

	// Find Campaign in DB
	campPre, err := usc.repo.FindOneCampaignRepo(ctx, filter)
	if err != nil {
		usc.logger.Error(err)
		return core.ErrRecordNotFound
	}

	campUpdate, campFb := mapping.MapperUpdateCampaignRequestToCampaigns(payload, campPre, &isAdminUser)

	fmt.Printf("campFB  %v", campFb)
	err = usc.fbService.Campaigns.Update(ctx, *campFb)
	if err != nil {
		usc.logger.Error(err)
		return err
	}

	err = usc.repo.UpsertCampaignRepo(ctx, filter, campUpdate)
	if err != nil {
		usc.logger.Error(err)
	}

	return nil
}

/**
 * Get One Campaign
 */
func (usc *apiCampaignUsc) FindOneCampaignUsc(ctx context.Context, campaignId string) (*entity.CampaignEntity, error) {
	filter := bson.M{
		"campaign_id": campaignId,
	}

	campEntity, err := usc.repo.FindOneCampaignRepo(ctx, filter)
	if err != nil {
		return nil, err
	}

	return campEntity, nil
}

/**
 * Get One Details Campaign have list adset and ads
 */
func (usc *apiCampaignUsc) FindOneDetailsCampaignUsc(ctx context.Context, payload *requests.GetDetailCampaignReq) (*entity.CampaignDetailResponseEntity, error) {
	user, _ := utils.GetInfoUserBasic(ctx)

	// if err != nil {
	// 	return nil, err
	// }

	// Lấy role của user
	roleName, err := usc.GetRoleByUserId(ctx, payload.UserId)
	if err != nil {
		return nil, core.ErrForbidden
	}

	// Khởi tạo filterConditions
	filterConditions := []bson.M{}

	// Thêm điều kiện vào filterConditions nếu giá trị không nil
	if payload.CampaignID != nil {
		filterConditions = append(filterConditions, bson.M{"campaign_id": *payload.CampaignID})
	}
	if payload.AdID != nil {
		filterConditions = append(filterConditions, bson.M{"ads.ad_id": *payload.AdID})
	}
	if payload.AdsetID != nil {
		filterConditions = append(filterConditions, bson.M{"adsets.adset_id": *payload.AdsetID})
	}
	filterConditions = append(filterConditions, bson.M{"status": bson.M{
		"$nin": []string{fbenums.FB_STATUS_ARCHIVED, fbenums.FB_STATUS_DELETED},
	}})

	// Nếu user không phải ADMIN, thêm điều kiện kiểm tra list_user_ids

	if *roleName != conf.SysConf.RoleAdmin {
		filterConditions = append(filterConditions, bson.M{"client_id": user.ClientId})
		filterConditions = append(filterConditions, bson.M{"account_id": user.AdAccountId})
	} else if *roleName == conf.SysConf.RoleClientUser {
		filterConditions = append(filterConditions, bson.M{"list_user_ids": bson.M{"$in": bson.A{payload.UserId}}})
	}

	isAdmin := true

	// if *roleName == conf.SysConf.RoleClientUser {
	// 	isAdmin = false
	// 	filterConditions = append(filterConditions, bson.M{"list_user_ids": bson.M{"$in": bson.A{payload.UserId}}})
	// }
	// if *roleName != conf.SysConf.RoleAdmin {
	// 	filterConditions = append(filterConditions, bson.M{"client_id": user.ClientId})
	// 	filterConditions = append(filterConditions, bson.M{"account_id": user.AdAccountId})
	// }

	// if *roleName == conf.SysConf.RoleClientAdmin || *roleName == conf.SysConf.RoleClientAdminViewer {
	// 	filterConditions = append(filterConditions, bson.M{"client_id": user.ClientId})
	// 	filterConditions = append(filterConditions, bson.M{"account_id": user.AdAccountId})
	// }

	// Tạo filter cuối cùng
	filter := bson.M{
		"$and": filterConditions,
	}

	pipeline := pipelines.GetDetailsCampaignPipeline(filter, payload.UserId, isAdmin)
	// var prettyDocs []bson.M
	// for _, doc := range pipeline {
	// 	bsonDoc, err := bson.Marshal(doc)
	// 	if err != nil {
	// 		panic(err)
	// 	}

	// 	var prettyDoc bson.M
	// 	err = bson.Unmarshal(bsonDoc, &prettyDoc)
	// 	if err != nil {
	// 		panic(err)
	// 	}

	// 	prettyDocs = append(prettyDocs, prettyDoc)
	// }

	campEntity, err := usc.repo.AggregateCampaignRepo(ctx, pipeline)

	if err != nil {
		return nil, err
	}

	if campEntity.CampaignID == "" {
		return nil, core.ErrBadRequest
	}

	return campEntity, nil
}

/**
 * Delete Campaign dung transactions
 */
func (usc *apiCampaignUsc) DeleteCampaignUsc(ctx context.Context, payload *requests.DeleteCampaignReq) ([]string, error) {

	userId, err := utils.GetUserId(ctx)
	if err != nil {
		return nil, err
	}

	roleName, err := usc.GetRoleByUserId(ctx, userId)
	if err != nil {
		return nil, err
	}

	var filter bson.M
	if *roleName == conf.SysConf.RoleAdmin {
		filter = bson.M{"campaign_id": bson.M{"$in": payload.CampaignIDs}}
	} else {
		filter = bson.M{"campaign_id": bson.M{"$in": payload.CampaignIDs}, "list_user_ids": userId}
	}

	err = usc.repo.DeleteCampaignManyTransactionRepo(ctx, filter)
	if err != nil {
		usc.logger.Error(err)
		return nil, err
	}

	var removedIds []string
	for _, id := range payload.CampaignIDs {
		if err := usc.fbService.Campaigns.Delete(ctx, id); err != nil {
			usc.logger.Error(err)
		} else {
			removedIds = append(removedIds, id)
		}
	}

	return removedIds, nil
}

/**
 * Handle payload list camp datable
 */
func HanldePayloutListDatatableCampaigns(ctx context.Context, payload *requests.ListTableCampaignReq, usc *apiCampaignUsc) (bson.M, error) {

	user, _ := utils.GetInfoUserBasic(ctx)

	filter := utils.GetFilterFBAds(ctx, user)
	fmt.Printf("GetFilterFBAds: filter: %v\n", filter)

	if payload.SearchValue != nil && *payload.SearchValue != "" {
		filter["name"] = bson.M{"$regex": *payload.SearchValue, "$options": "i"}
	}

	if payload.CampaignId != "" {
		filter["campaign_id"] = bson.M{"$in": []string{payload.CampaignId}}
	}

	// if payload.StartTime != nil && payload.EndTime != nil {
	// 	filter["updated_time"] = bson.M{"$gte": *payload.StartTime, "$lte": *payload.EndTime}
	// }

	if user.RoleName == conf.SysConf.RoleAdmin {
		filter["account_id"] = payload.AccountID
	}
	// if payload.AccountID == "" {
	// 	delete(filter, "account_id")
	// }
	if payload.ClientIDStr != "" {
		filter["client_id"] = payload.ClientID
	}

	// jsonData, _ := json.MarshalIndent(filter, "", "  ")
	// fmt.Printf("filter ------->: %v\n", string(jsonData))

	return filter, nil
}

func (usc *apiCampaignUsc) getMetricCampaignReport(ctx context.Context, payload *requests.ListTableCampaignReq, campaignIds []string) (*[]adserverReportE.FBReportDetailEntity, error) {
	filter := bson.M{"campaign_id": bson.M{"$in": campaignIds}}

	if payload.StartTime != nil && payload.EndTime != nil {
		startTimeStr := (*payload.StartTime).Format("2006-01-02")
		endTimeStr := (*payload.EndTime).Format("2006-01-02")
		filter["date"] = bson.M{"$gte": startTimeStr, "$lte": endTimeStr}
	} else {
		yesterday := time.Now().AddDate(0, 0, -1)
		endOfYesterdayStr := yesterday.Format("2006-01-02")
		startOfYesterdayStr := yesterday.AddDate(0, 0, -30).Format("2006-01-02")
		filter["date"] = bson.M{"$gte": startOfYesterdayStr, "$lte": endOfYesterdayStr}
	}

	// jsonData, _ := json.MarshalIndent(filter, "", "  ")
	// fmt.Printf("getMetricCampaignReport: filter: %v\n", string(jsonData))

	return usc.fbReportDetailRepo.GetMetricsColumnForCampaignsFBReportDetailRepo(ctx, filter)

}

/**
 * Api list camp datatable
 */
func (usc *apiCampaignUsc) ListDatatableCampaignUsc(ctx context.Context, payload *requests.ListTableCampaignReq) (*responses.DatatableCampaignResq, error) {

	filter, err := HanldePayloutListDatatableCampaigns(ctx, payload, usc)
	if err != nil {
		return nil, err
	}

	limit := payload.Length
	skip := payload.Start

	allowDiskUse := fbenums.ALLOW_DISK_USE_TRUE
	opts := &options.FindOptions{
		AllowDiskUse: &allowDiskUse,
		Skip:         &skip,
		Limit:        &limit,
	}

	sortOrder := payload.SortOrder
	if payload.SortField == "" {
		opts.SetSort(bson.M{"updated_time": 1})
	} else {
		opts.SetSort(bson.M{payload.SortField: sortOrder})
	}

	campaigns, err := usc.repo.FindCampaignRepo(ctx, filter, opts)
	if err != nil {
		return nil, err
	}

	if len(*campaigns) == 0 {
		fmt.Println("not found campaign")
		return nil, errors.New("not found campaign")
	}

	var campaignIds []string
	for _, campaign := range *campaigns {
		campaignIds = append(campaignIds, campaign.CampaignID)
	}
	// jsonData, _ := json.MarshalIndent(campaignIds, "", "  ")
	// fmt.Printf("------> campaignIds: %v\n", string(jsonData))

	// fbReportDetails, err := usc.fbReportDetailRepo.GetMetricsColumnForCampaignsFBReportDetailRepo(ctx, bson.M{"campaign_id": bson.M{"$in": campaignIds}})
	// jsonData, _ := json.MarshalIndent(fbReportDetails, "", "  ")
	// fmt.Printf("------> fbReportDetails: %v\n", string(jsonData))
	fbReportDetails, err := usc.getMetricCampaignReport(ctx, payload, campaignIds)
	if err != nil {
		usc.logger.Error(err)
		return nil, err
	}

	// jsonData, _ := json.MarshalIndent(fbReportDetails, "", "  ")
	// fmt.Printf("------> fbReportDetails: %v\n", string(jsonData))

	//total all
	total, err := usc.repo.CountCampaignRepo(ctx, bson.M{})
	if err != nil {
		return nil, err
	}

	// total filtered
	totalFiltered := total
	if len(filter) != 0 {
		totalFiltered, err = usc.repo.CountCampaignRepo(ctx, filter)
		if err != nil {
			return nil, err
		}
	}

	data := mapping.MapperCampaignToDatatable(campaigns, fbReportDetails)

	return &responses.DatatableCampaignResq{
		Draw:            payload.Draw,
		Data:            data,
		RecordsTotal:    total,
		RecordsFiltered: totalFiltered,
	}, nil
}

/**
 * Api list camps
 */
func (usc *apiCampaignUsc) ListCampaignUsc(ctx context.Context, payload *requests.CampaignReq) (*[]entity.CampaignEntity, error) {

	if payload == nil {
		return nil, errs.ErrCampaignDataTableEmpty
	}

	user, err := utils.GetInfoUserBasic(ctx)
	if err != nil {
		return nil, err
	}

	filter := utils.GetFilterFBAds(ctx, user)

	campaigns, err := usc.repo.FindCampaignRepo(ctx, filter)
	if err != nil {
		return nil, err
	}

	return campaigns, nil
}

/**
 * Api toggle status camps
 */
func (usc *apiCampaignUsc) UpdateNameOrStatusCampaignUsc(ctx context.Context, payload *requests.UpdateNameStatusCampaignReq) error {
	user, _ := utils.GetInfoUserBasic(ctx)
	// if err != nil {
	// 	return err
	// }

	filter := utils.GetFilterFBAds(ctx, user)
	filter["account_id"] = payload.AccountID
	filter["campaign_id"] = payload.CampaignID

	campPre, err := usc.repo.FindOneCampaignRepo(ctx, filter)
	if err != nil {
		usc.logger.Error(err)
		return err
	}
	fmt.Println("Status: ", payload.Status)
	if fbenums.FB_STATUS_ACTIVE == payload.Status && campPre.Approve == campEnum.Review {
		return errs.ErrCampaignMustBeApprovalBeforeActive
	}

	campUpdate, campFB := mapping.MapperUpdateNameStatusCampaign(payload, campPre)

	// jsonData, _ := json.MarshalIndent(campFB, "", "  ")
	// fmt.Printf("campFB  %v", string(jsonData))

	err = usc.fbService.Campaigns.Update(ctx, *campFB)
	if err != nil {
		usc.logger.Error(err)
		return err
	}
	fmt.Println("err: ", err)

	go func() {
		err := usc.repo.UpsertCampaignRepo(ctx, filter, campUpdate)
		if err != nil {
			usc.logger.Error(err)
		}
	}()

	return nil
}

func (usc *apiCampaignUsc) ApproveCampaignUsc(ctx context.Context, payload *requests.ApproveCampaignReq, userId primitive.ObjectID) error {
	user, _ := utils.GetInfoUserBasic(ctx)

	filter := utils.GetFilterFBAds(ctx, user)
	filter["campaign_id"] = payload.CampaignID
	filter["account_id"] = payload.AccountID
	filter["approve"] = campEnum.Review

	campPre, err := usc.repo.FindOneCampaignRepo(ctx, filter)
	if err != nil {
		usc.logger.Error(err)
		return err
	}

	campUpdate, campFB := mapping.MapperApproveCampaign(payload, campPre, userId)
	err = usc.fbService.Campaigns.Update(ctx, *campFB)
	if err != nil {
		usc.logger.Error(err)
		return err
	}

	go func() {
		err := usc.repo.UpsertCampaignRepo(ctx, filter, campUpdate)
		if err != nil {
			usc.logger.Error(err)
		}
	}()
	return nil

}
