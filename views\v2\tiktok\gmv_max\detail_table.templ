package gmv_max

import (
	"github.com/dev-networldasia/dspgos/gos/templates"
	"godsp/views/v2/layouts"
	layoutCops "godsp/views/v2/layouts/components"
	"godsp/views/v2/layouts/masters"
	cop "godsp/views/v2/tiktok/components"
	cpn "godsp/views/v2/tiktok/gmv_max/components"
	"godsp/views/v2/tiktok/tableAds/components"
)

func getDetailDataLayoutMaster(data *DetailTableGmvMaxLayoutData) masters.LayoutMasterData {
	return masters.LayoutMasterData{
		AuthPermission: data.AuthPermission,
		UserInfo:       data.UserInfo,
	}
}

func getDetailPathBreadcrumb() []layoutCops.PathBreadcrumb {
	return []layoutCops.PathBreadcrumb{
		{Title: "GMV Max", DataKey: "t-gmv-max-campaigns", Url: "/dsp/tiktok/gmv-max-campaign/list"},
		{Title: "Detail", DataKey: "t-gmv-max-campaigns", Url: "/dsp/tiktok/gmv-max-campaign/detail#"},
	}
}

func getDetailDataLayoutTable(data *DetailTableGmvMaxLayoutData) components.LayoutTableData {
	return components.LayoutTableData{
		AuthPermission: data.AuthPermission,
		UserInfo:       data.UserInfo,
		Clients:        data.Clients,
	}
}

templ DetailDatatableGmvMaxCamp(data *DetailTableGmvMaxLayoutData) {
	{{
		dataLayoutMaster := getDetailDataLayoutMaster(data)
		pathBreadcrumb := getDetailPathBreadcrumb()
		// dataLayoutTable := getDetailDataLayoutTable(data)
	}}
	@layouts.Master(dataLayoutMaster, []templ.Component{cssDetailHeader()}, scriptGmvMaxDetailTable()) {
		@layoutCops.ListBreadcrumdCpn("GMV Max Detail", pathBreadcrumb)
		if data != nil && data.FlashMsg != "" {
			@cop.FlashMsgCop(data.FlashMsg)
		}
		<!-- Page Header -->
		@cpn.GmvDetailPageHeaderCpn()
		<!-- Campaign Detail Section -->
		@cpn.GmvCampaignDetailCpn()
		<!-- Performance Overview Section with Chart -->
		@cpn.GmvDetailOverviewChartCpn()
		<!-- Ad Groups Section -->
		@cpn.GmvAdGroupsListCpn()
	}
}

templ cssDetailHeader() {
	<link href={ templates.AssetURL("/static/css/pages/tiktok/gmv-max.css") } rel="stylesheet" type="text/css"/>
}

templ scriptGmvMaxDetailTable() {
	<script src="https://cdn.jsdelivr.net/npm/apexcharts@latest"></script>
	<script type="module" src={ templates.AssetURL("/static/js/pages/tiktok/gmv_max/detail-table.js") }></script>
	<script>
		document.addEventListener("DOMContentLoaded", function () {
			// Chart data for detail view
			const detailChartData = {
				cost: [8230801, 7500000, 9200000, 6900000, 10100000, 8800000, 7700000, 8230801],
				gross_revenue: [38509851, 35000000, 42000000, 31000000, 45000000, 39000000, 36000000, 38509851],
				orders: [168, 150, 190, 130, 210, 170, 140, 168],
				cost_per_order: [48930, 50000, 48421, 53077, 48095, 51765, 55000, 48930],
				roi: [4.68, 4.67, 4.57, 4.49, 4.46, 4.43, 4.68, 4.68]
			};

			// Chart configuration
			const chartOptions = {
				series: [{
					name: 'Cost',
					data: detailChartData.cost
				}],
				chart: {
					type: 'line',
					height: 350,
					toolbar: {
						show: true
					}
				},
				stroke: {
					curve: 'smooth',
					width: 3
				},
				xaxis: {
					categories: ['Day 1', 'Day 2', 'Day 3', 'Day 4', 'Day 5', 'Day 6', 'Day 7', 'Day 8']
				},
				yaxis: {
					labels: {
						formatter: function (val) {
							return val.toLocaleString();
						}
					}
				},
				dataLabels: {
					enabled: true,
					formatter: function (val) {
						return val.toLocaleString();
					}
				},
				colors: ['#405189'],
				grid: {
					borderColor: '#f1f1f1'
				}
			};

			// Initialize chart
			if (document.querySelector("#detail-performance-chart")) {
				const chart = new ApexCharts(document.querySelector("#detail-performance-chart"), chartOptions);
				chart.render();

				// Metric selection functionality
				let activeMetrics = ['cost']; // Default active metric
				const maxActiveMetrics = 2;

				// Handle metric selection
				document.querySelectorAll('.metric-card').forEach(card => {
					card.addEventListener('click', function() {
						const metric = this.getAttribute('data-metric');
						
						if (this.classList.contains('active')) {
							// Deactivate metric
							this.classList.remove('active');
							activeMetrics = activeMetrics.filter(m => m !== metric);
						} else {
							// Activate metric
							if (activeMetrics.length >= maxActiveMetrics) {
								// Remove first active metric
								const firstActive = activeMetrics.shift();
								document.querySelector(`[data-metric="${firstActive}"]`).classList.remove('active');
							}
							this.classList.add('active');
							activeMetrics.push(metric);
						}

						// Update chart
						updateChart();
					});
				});

				function updateChart() {
					const series = activeMetrics.map(metric => ({
						name: metric.charAt(0).toUpperCase() + metric.slice(1).replace('_', ' '),
						data: detailChartData[metric]
					}));

					chart.updateSeries(series);
				}

				// Set initial active state
				document.querySelector('[data-metric="cost"]').classList.add('active');
			}

			// Helper function to create status display HTML
			function createDetailStatusDisplay(statusValue) {
				let circleClass = 'status-item-circle';
				let displayText = '';
				
				switch (statusValue) {
					case 'STATUS_DELIVERY_OK':
						circleClass += ' active';
						displayText = 'Active';
						break;
					case 'STATUS_DISABLE':
						circleClass += ' inactive';
						displayText = 'Inactive';
						break;
					case 'STATUS_DELETE':
						circleClass += ' deleted';
						displayText = 'Deleted';
						break;
					case 'STATUS_DELIVERY_NOT':
						circleClass += ' not-delivering';
						displayText = 'Not delivering';
						break;
					default:
						circleClass += ' not-delivering';
						displayText = 'Unknown';
				}
				
				return `<span class="${circleClass}"></span><span>${displayText}</span>`;
			}

			// Initialize custom dropdowns for detail view
			initDetailCustomDropdowns();

			// Load initial ad groups table
			loadAdGroupsTable();

			// Search functionality for ad groups
			document.getElementById('adGroupSearchInput').addEventListener('input', function (e) {
				console.log('Ad group search:', e.target.value);
				// Add your search logic here
			});

			// Initialize custom dropdown functionality for detail view
			function initDetailCustomDropdowns() {
				// Ad Group Status Dropdown
				initSingleDropdown('adGroupStatusDropdown', 'adGroupStatusFilter', function(value, text) {
					console.log('Ad group status changed:', value, text);
					handleAdGroupStatusFilter(value);
				});
			}

			// Initialize single custom dropdown
			function initSingleDropdown(dropdownId, hiddenInputId, onChangeCallback) {
				const dropdown = document.getElementById(dropdownId);
				if (!dropdown) return;

				const selected = dropdown.querySelector('.selected');
				const options = dropdown.querySelectorAll('.options li');
				const hiddenInput = document.getElementById(hiddenInputId);

				if (!selected || !hiddenInput) {
					console.error(`Dropdown elements not found for ${dropdownId}`);
					return;
				}

				// Toggle dropdown open/close
				selected.addEventListener('click', function() {
					dropdown.classList.toggle('open');
				});

				// Handle option selection
				options.forEach(option => {
					option.addEventListener('click', function() {
						const value = this.getAttribute('data-value');
						const text = this.textContent.trim();
						
						// Update selected display
						selected.innerHTML = this.innerHTML;
						
						// Update hidden input
						hiddenInput.value = value;
						
						// Close dropdown
						dropdown.classList.remove('open');
						
						// Trigger callback
						if (onChangeCallback) {
							onChangeCallback(value, text);
						}
					});
				});

				// Close dropdown when clicking outside
				document.addEventListener('click', function(e) {
					if (!dropdown.contains(e.target)) {
						dropdown.classList.remove('open');
					}
				});
			}

			// Handle ad group status filter functionality
			function handleAdGroupStatusFilter(statusValue) {
				console.log(`Filtering ad groups by status: "${statusValue}"`);
				
				if (statusValue) {
					console.log(`Status enum value: ${statusValue}`);
					// loadAdGroupsTable();
				}
			}

			// Load ad groups table
			function loadAdGroupsTable() {
				const container = document.getElementById('ad-groups-table-container');
				
				if (!container) {
					console.error('Ad groups table container not found');
					return;
				}

				// Show loading state
				container.innerHTML = `
					<div class="text-center py-4">
						<div class="spinner-border text-primary" role="status">
							<span class="visually-hidden">Loading...</span>
						</div>
						<p class="mt-2 text-muted">Loading ad groups...</p>
					</div>
				`;

				// Simulate API call (replace with actual implementation)
				setTimeout(() => {
					container.innerHTML = `
						<div class="text-center py-4">
							<p class="text-muted">No ad groups found for this campaign.</p>
						</div>
					`;
				}, 1000);
			}
		});
	</script>
}
