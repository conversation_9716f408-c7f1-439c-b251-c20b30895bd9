package gmv_max

import (
"github.com/dev-networldasia/dspgos/gos/templates"
"godsp/views/v2/layouts"
layoutCops "godsp/views/v2/layouts/components"
"godsp/views/v2/layouts/masters"
cop "godsp/views/v2/tiktok/components"
cpn "godsp/views/v2/tiktok/gmv_max/components"
"godsp/views/v2/tiktok/tableAds/components"
)

func getDetailDataLayoutMaster(data *DetailTableGmvMaxLayoutData) masters.LayoutMasterData {
return masters.LayoutMasterData{
AuthPermission: data.AuthPermission,
UserInfo: data.UserInfo,
}
}

func getDetailPathBreadcrumb() []layoutCops.PathBreadcrumb {
return []layoutCops.PathBreadcrumb{
{Title: "GMV Max", DataKey: "t-gmv-max-campaigns", Url: "/dsp/tiktok/gmv-max-campaign/list"},
{Title: "Detail", DataKey: "t-gmv-max-campaign-detail", Url: "/dsp/tiktok/gmv-max-campaign/detail#"},
}
}

func getDetailDataLayoutTable(data *DetailTableGmvMaxLayoutData) components.LayoutTableData {
return components.LayoutTableData{
AuthPermission: data.AuthPermission,
UserInfo: data.UserInfo,
Clients: data.Clients,
}
}

templ DetailDatatableGmvMaxCamp(data *DetailTableGmvMaxLayoutData) {
{{
dataLayoutMaster := getDetailDataLayoutMaster(data)
pathBreadcrumb := getDetailPathBreadcrumb()
// dataLayoutTable := getDetailDataLayoutTable(data)
}}
@layouts.Master(dataLayoutMaster, []templ.Component{header()}, scriptGmvMaxDetailTable()) {
@layoutCops.ListBreadcrumdCpn("GMV Max Detail", pathBreadcrumb)
if data != nil && data.FlashMsg != "" {
@cop.FlashMsgCop(data.FlashMsg)
}
<!-- Page Header -->
@cpn.GmvDetailPageHeaderCpn()
<!-- Overview Section with Chart -->
@cpn.GmvDetailOverviewChartCpn()
<!-- Product Table Section -->
@cpn.GmvProductTableCpn()
}
}

templ header() {
<link href={ templates.AssetURL("/static/css/tiktok.css") } rel="stylesheet" type="text/css" />
<link href={ templates.AssetURL("/static/css/pages/tiktok/gmv-max.css") } rel="stylesheet" type="text/css" />
<link href={ templates.AssetURL("/static/css/pages/tiktok/gmv-max-detail.css") } rel="stylesheet" type="text/css" />
}

templ scriptGmvMaxDetailTable() {
<script src="https://cdn.jsdelivr.net/npm/apexcharts@latest"></script>
<script type="module" src={ templates.AssetURL("/static/js/pages/tiktok/gmv_max/list-table.js") }></script>
<script>
	document.addEventListener("DOMContentLoaded", function () {
		// Detail page specific initialization
		console.log('GMV Max Detail page loaded');

		// Add fade-in animation to cards
		const cards = document.querySelectorAll('.card');
		cards.forEach((card, index) => {
			setTimeout(() => {
				card.classList.add('fade-in');
			}, index * 100);
		});

		// Initialize detail chart
		initDetailChart();

		// Detail page specific functionality
		initDetailPageFeatures();
	});

	function initDetailChart() {
		// Chart data for detail page
		const chartData = {
			cost: [5397173, 4800000, 5200000, 4500000, 5800000, 5100000, 4900000, 5397173],
			gross_revenue: [22494198, 21000000, 24000000, 19000000, 26000000, 23000000, 21500000, 22494198],
			orders: [106, 95, 115, 85, 125, 105, 90, 106],
			cost_per_order: [50917, 50526, 45217, 52941, 46400, 48571, 54444, 50917],
			roi: [4.17, 4.38, 4.62, 4.22, 4.48, 4.51, 4.39, 4.17]
		};

		const dates = ['Jun 27', 'Jun 28', 'Jun 29', 'Jun 30', 'Jul 01', 'Jul 02', 'Jul 03', 'Jul 04'];

		let detailChart;
		let activeMetrics = ['cost', 'orders']; // Default active metrics

		// Initialize chart
		function initChart() {
			const series = activeMetrics.map(metric => ({
				name: getMetricDisplayName(metric),
				data: chartData[metric],
				type: 'line'
			}));

			const options = {
				series: series,
				chart: {
					height: 350,
					type: 'line',
					toolbar: {
						show: true
					},
					zoom: {
						enabled: false
					}
				},
				dataLabels: {
					enabled: true,
					formatter: function (val, opts) {
						return formatValue(val, activeMetrics[opts.seriesIndex]);
					}
				},
				stroke: {
					curve: 'smooth',
					width: 3
				},
				colors: ['#405189', '#0ab39c', '#f7b84b', '#f06548', '#6c757d'],
				xaxis: {
					categories: dates,
					labels: {
						style: {
							colors: '#8c9097',
							fontSize: '13px'
						}
					}
				},
				yaxis: activeMetrics.map((metric, index) => ({
					seriesName: getMetricDisplayName(metric),
					opposite: index > 0,
					axisTicks: {
						show: true,
					},
					axisBorder: {
						show: true,
						color: series[index]?.color || '#405189'
					},
					labels: {
						style: {
							colors: series[index]?.color || '#405189',
						},
						formatter: function (val) {
							return formatValue(val, metric);
						}
					},
					title: {
						text: getMetricDisplayName(metric),
						style: {
							color: series[index]?.color || '#405189',
						}
					}
				})),
				grid: {
					borderColor: '#f1f1f1',
					strokeDashArray: 3
				},
				legend: {
					position: 'top',
					horizontalAlign: 'right',
					floating: true,
					offsetY: -25,
					offsetX: -5
				},
				tooltip: {
					shared: true,
					intersect: false,
					y: {
						formatter: function (val, opts) {
							const metric = activeMetrics[opts.seriesIndex];
							return formatValue(val, metric);
						}
					}
				}
			};

			if (detailChart) {
				detailChart.destroy();
			}

			detailChart = new ApexCharts(document.querySelector("#gmv_max_detail_chart"), options);
			detailChart.render();
		}

		// Helper functions
		function getMetricDisplayName(metric) {
			const names = {
				cost: 'Cost',
				gross_revenue: 'Gross Revenue',
				orders: 'Orders',
				cost_per_order: 'Cost per Order',
				roi: 'ROI'
			};
			return names[metric] || metric;
		}

		function formatValue(val, metric) {
			if (metric === 'cost' || metric === 'gross_revenue' || metric === 'cost_per_order') {
				return new Intl.NumberFormat('vi-VN').format(val) + ' VND';
			} else if (metric === 'roi') {
				return val.toFixed(2);
			} else {
				return new Intl.NumberFormat('vi-VN').format(val);
			}
		}

		// Handle metric checkbox changes
		document.querySelectorAll('.metric-checkbox').forEach(checkbox => {
			checkbox.addEventListener('change', function () {
				const metricCard = this.closest('.metric-card');
				const metric = metricCard.getAttribute('data-metric');

				if (this.checked) {
					// If we already have 2 active metrics, remove the oldest one
					if (activeMetrics.length >= 2) {
						const oldestMetric = activeMetrics.shift(); // Remove first (oldest) metric
						// Uncheck the oldest metric's checkbox
						const oldestCheckbox = document.querySelector(`[data-metric="${oldestMetric}"] .metric-checkbox`);
						if (oldestCheckbox) {
							oldestCheckbox.checked = false;
						}
					}
					// Add new metric to active list
					activeMetrics.push(metric);
				} else {
					// Remove metric from active list
					activeMetrics = activeMetrics.filter(m => m !== metric);
				}

				// Update chart
				initChart();
			});
		});

		// Initialize chart on page load
		initChart();
	}

	function initDetailPageFeatures() {
		// Add any detail page specific features here
		console.log('Detail page features initialized');

		// Handle metric cards click
		const metricCards = document.querySelectorAll('.metric-card');
		metricCards.forEach(card => {
			card.addEventListener('click', function () {
				// Toggle selected state
				this.classList.toggle('selected');
			});
		});

		// Handle "View creatives" button clicks
		const viewCreativesButtons = document.querySelectorAll('.view-creatives-btn');
		viewCreativesButtons.forEach(button => {
			button.addEventListener('click', function () {
				const productId = this.getAttribute('data-product-id');
				console.log('View creatives for product:', productId);

				// Switch to creatives tab
				const creativesTab = document.querySelector('a[href="#creatives-tab"]');
				const creativesTabPane = document.querySelector('#creatives-tab');
				const productTab = document.querySelector('a[href="#product-tab"]');
				const productTabPane = document.querySelector('#product-tab');

				if (creativesTab && creativesTabPane && productTab && productTabPane) {
					// Remove active class from product tab
					productTab.classList.remove('active');
					productTab.setAttribute('aria-selected', 'false');
					productTabPane.classList.remove('show', 'active');

					// Add active class to creatives tab
					creativesTab.classList.add('active');
					creativesTab.setAttribute('aria-selected', 'true');
					creativesTabPane.classList.add('show', 'active');

					// Load creatives data for the selected product
					loadCreativesData(productId);
				}
			});
		});
	}

	function loadCreativesData(productId) {
		// Update creatives tab content with product-specific data
		const creativesTabPane = document.querySelector('#creatives-tab');
		if (creativesTabPane) {
			creativesTabPane.innerHTML = `
				<div class="d-flex justify-content-between align-items-center mb-3">
					<h6 class="mb-0">Creatives for Product ID: ${productId}</h6>
					<button type="button" class="btn btn-outline-secondary btn-sm" onclick="switchBackToProductTab()">
						<i class="ri-arrow-left-line me-1"></i> Back to Products
					</button>
				</div>
				<div class="table-responsive">
					<table class="table table-nowrap align-middle mb-0">
						<thead class="table-light">
							<tr>
								<th scope="col">Creative</th>
								<th scope="col">Type</th>
								<th scope="col">Performance</th>
								<th scope="col">Cost</th>
								<th scope="col">Orders</th>
								<th scope="col">Action</th>
							</tr>
						</thead>
						<tbody>
							<tr>
								<td>
									<div class="d-flex align-items-center">
										<div class="avatar-sm me-3">
											<div class="avatar-title bg-light rounded">
												<i class="ri-image-line"></i>
											</div>
										</div>
										<div>
											<h6 class="mb-1">Creative Image 1</h6>
											<small class="text-muted">1920x1080</small>
										</div>
									</div>
								</td>
								<td><span class="badge bg-info-subtle text-info">Image</span></td>
								<td><span class="text-success">Good</span></td>
								<td><strong>150,000 VND</strong></td>
								<td>25</td>
								<td>
									<button type="button" class="btn btn-outline-primary btn-sm">
										View Details
									</button>
								</td>
							</tr>
							<tr>
								<td>
									<div class="d-flex align-items-center">
										<div class="avatar-sm me-3">
											<div class="avatar-title bg-light rounded">
												<i class="ri-video-line"></i>
											</div>
										</div>
										<div>
											<h6 class="mb-1">Creative Video 1</h6>
											<small class="text-muted">30s duration</small>
										</div>
									</div>
								</td>
								<td><span class="badge bg-primary-subtle text-primary">Video</span></td>
								<td><span class="text-warning">Average</span></td>
								<td><strong>300,000 VND</strong></td>
								<td>45</td>
								<td>
									<button type="button" class="btn btn-outline-primary btn-sm">
										View Details
									</button>
								</td>
							</tr>
						</tbody>
					</table>
				</div>
			`;
		}
	}

	// Function to switch back to product tab
	window.switchBackToProductTab = function () {
		const creativesTab = document.querySelector('a[href="#creatives-tab"]');
		const creativesTabPane = document.querySelector('#creatives-tab');
		const productTab = document.querySelector('a[href="#product-tab"]');
		const productTabPane = document.querySelector('#product-tab');

		if (creativesTab && creativesTabPane && productTab && productTabPane) {
			// Remove active class from creatives tab
			creativesTab.classList.remove('active');
			creativesTab.setAttribute('aria-selected', 'false');
			creativesTabPane.classList.remove('show', 'active');

			// Add active class to product tab
			productTab.classList.add('active');
			productTab.setAttribute('aria-selected', 'true');
			productTabPane.classList.add('show', 'active');

			// Reset creatives tab content
			creativesTabPane.innerHTML = `
				<div class="text-center py-5">
					<i class="ri-image-line fs-1 mb-3 d-block text-muted"></i>
					<h6 class="mb-2">No creatives data</h6>
					<p class="mb-0 text-muted">Select a product and click "View creatives" to see creative performance data.</p>
				</div>
			`;
		}
	}
</script>
}