package gmv_max

import (
	"github.com/dev-networldasia/dspgos/gos/templates"
	"godsp/views/v2/layouts"
	layoutCops "godsp/views/v2/layouts/components"
	"godsp/views/v2/layouts/masters"
	cop "godsp/views/v2/tiktok/components"
	cpn "godsp/views/v2/tiktok/gmv_max/components"
	"godsp/views/v2/tiktok/tableAds/components"
)

func getDetailDataLayoutMaster(data *DetailTableGmvMaxLayoutData) masters.LayoutMasterData {
	return masters.LayoutMasterData{
		AuthPermission: data.AuthPermission,
		UserInfo:       data.UserInfo,
	}
}

func getDetailPathBreadcrumb() []layoutCops.PathBreadcrumb {
	return []layoutCops.PathBreadcrumb{
		{Title: "GMV Max", DataKey: "t-gmv-max-campaigns", Url: "/dsp/tiktok/gmv-max-campaign/list"},
		{Title: "Detail", DataKey: "t-gmv-max-campaign-detail", Url: "/dsp/tiktok/gmv-max-campaign/detail#"},
	}
}

func getDetailDataLayoutTable(data *DetailTableGmvMaxLayoutData) components.LayoutTableData {
	return components.LayoutTableData{
		AuthPermission: data.AuthPermission,
		UserInfo:       data.UserInfo,
		Clients:        data.Clients,
	}
}

templ DetailDatatableGmvMaxCamp(data *DetailTableGmvMaxLayoutData) {
	{{
		dataLayoutMaster := getDetailDataLayoutMaster(data)
		pathBreadcrumb := getDetailPathBreadcrumb()
		// dataLayoutTable := getDetailDataLayoutTable(data)
	}}
	@layouts.Master(dataLayoutMaster, []templ.Component{header()}, scriptGmvMaxDetailTable()) {
		@layoutCops.ListBreadcrumdCpn("GMV Max Detail", pathBreadcrumb)
		if data != nil && data.FlashMsg != "" {
			@cop.FlashMsgCop(data.FlashMsg)
		}
		<!-- Page Header -->
		@cpn.GmvDetailPageHeaderCpn()
		<!-- Overview Section with Chart -->
		@cpn.GmvOverviewChartCpn()
		<!-- Ad Groups List Section -->
		@cpn.GmvAdGroupsListCpn()
	}
}

templ header() {
	<link href={ templates.AssetURL("/static/css/tiktok.css") } rel="stylesheet" type="text/css"/>
	<link href={ templates.AssetURL("/static/css/pages/tiktok/gmv-max.css") } rel="stylesheet" type="text/css"/>
	<link href={ templates.AssetURL("/static/css/pages/tiktok/gmv-max-detail.css") } rel="stylesheet" type="text/css"/>
}

templ scriptGmvMaxDetailTable() {
	<script src="https://cdn.jsdelivr.net/npm/apexcharts@latest"></script>
	<script type="module" src={ templates.AssetURL("/static/js/pages/tiktok/gmv_max/list-table.js") }></script>
	<script>
	document.addEventListener("DOMContentLoaded", function () {
		// Detail page specific initialization
		console.log('GMV Max Detail page loaded');

		// Add fade-in animation to cards
		const cards = document.querySelectorAll('.card');
		cards.forEach((card, index) => {
			setTimeout(() => {
				card.classList.add('fade-in');
			}, index * 100);
		});

		// Ensure chart container exists and is visible
		const chartContainer = document.querySelector('#gmv_max_line_chart');
		if (chartContainer) {
			console.log('Chart container found, chart should initialize from list-table.js');

			// Add observer to ensure chart renders when container is visible
			const observer = new IntersectionObserver((entries) => {
				entries.forEach(entry => {
					if (entry.isIntersecting) {
						console.log('Chart container is visible');
						// Trigger chart resize if needed
						setTimeout(() => {
							if (window.ApexCharts && window.gmvChart) {
								window.gmvChart.resize();
							}
						}, 100);
					}
				});
			});
			observer.observe(chartContainer);
		} else {
			console.warn('Chart container #gmv_max_line_chart not found');
		}

		// Detail page specific functionality
		initDetailPageFeatures();
	});

	function initDetailPageFeatures() {
		// Add any detail page specific features here
		console.log('Detail page features initialized');

		// Example: Add click handlers for metric cards if they exist
		const metricCards = document.querySelectorAll('.metric-card');
		metricCards.forEach(card => {
			card.addEventListener('click', function () {
				// Toggle selected state
				this.classList.toggle('selected');
			});
		});
	}
</script>
}
