package gmv_max

import (
	"github.com/dev-networldasia/dspgos/gos/templates"
	"godsp/views/v2/layouts"
	layoutCops "godsp/views/v2/layouts/components"
	"godsp/views/v2/layouts/masters"
	cop "godsp/views/v2/tiktok/components"
	cpn "godsp/views/v2/tiktok/gmv_max/components"
	"godsp/views/v2/tiktok/tableAds/components"
)

func getDataLayoutMaster(data *DetailTableGmvMaxLayoutData) masters.LayoutMasterData {
	return masters.LayoutMasterData{
		AuthPermission: data.AuthPermission,
		UserInfo:       data.UserInfo,
	}
}

func getPathBreadcrumb() []layoutCops.PathBreadcrumb {
	return []layoutCops.PathBreadcrumb{
		{Title: "GMV Max", DataKey: "t-gmv-max-campaigns", Url: "/dsp/tiktok/gmv-max-campaign/list"},
		{Title: "Detail", DataKey: "t-gmv-max-campaign-detail", Url: "/dsp/tiktok/gmv-max-campaign/detail#"},
	}
}

func getDataLayoutTable(data *DetailTableGmvMaxLayoutData) components.LayoutTableData {
	return components.LayoutTableData{
		AuthPermission: data.AuthPermission,
		UserInfo:       data.UserInfo,
		Clients:        data.Clients,
	}
}

templ DetailDatatableGmvMaxCamp(data *DetailTableGmvMaxLayoutData) {
	{{
		dataLayoutMaster := getDataLayoutMaster(data)
		pathBreadcrumb := getPathBreadcrumb()
		// dataLayoutTable := getDataLayoutTable(data)
	}}
	@layouts.Master(dataLayoutMaster, []templ.Component{header()}, scriptGmvMaxDetailTable()) {
		@layoutCops.ListBreadcrumdCpn("GMV Max Detail", pathBreadcrumb)
		if data != nil && data.FlashMsg != "" {
			@cop.FlashMsgCop(data.FlashMsg)
		}
		<!-- Page Header -->
		@cpn.GmvDetailPageHeaderCpn()
		<!-- Campaign Detail Section -->
		@cpn.GmvCampaignDetailCpn()
		<!-- Overview Section with Chart (reused from list page) -->
		@cpn.GmvOverviewChartCpn()
		<!-- Ad Groups List Section -->
		@cpn.GmvAdGroupsListCpn()
	}
}

templ header() {
	<link href={ templates.AssetURL("/static/css/tiktok.css") } rel="stylesheet" type="text/css"/>
	<link href={ templates.AssetURL("/static/css/pages/tiktok/gmv-max.css") } rel="stylesheet" type="text/css"/>
}

templ scriptGmvMaxDetailTable() {
	<script src="https://cdn.jsdelivr.net/npm/apexcharts@latest"></script>
	<script type="module" src={ templates.AssetURL("/static/js/pages/tiktok/gmv_max/list-table.js") }></script>
}
