package usecase

import (
	"context"
	userE "godsp/modules/admin/user/entity"
	adAccRes "godsp/modules/facebook/ad_account/transport/response"
	pixelE "godsp/modules/facebook/ad_pixel/entity"
	catalogueE "godsp/modules/facebook/catalogue/entity"
	pageE "godsp/modules/facebook/pages/entity"
	tikPresetColEnt "godsp/modules/tiktok/custom_column_table/entity"

	"godsp/modules/tiktok/advertiser/entity"

	"github.com/dev-networldasia/dspgos/sctx"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
)

type AdAccountRepo interface {
	// FindAdAccountAggregateRepo(ctx context.Context, filter interface{}, opts ...*options.FindOptions) ([]*adAccRes.AdAccountWithFullname, error)
	FindAdAccountEditClientRepo(ctx context.Context, filter interface{}, opts ...*options.FindOptions) ([]*adAccRes.AdAccountEditClient, error)
	UpdateManyAdAccountRepo(ctx context.Context, filter interface{}, update interface{}) error
}
type PageRepo interface {
	FindPageRepo(ctx context.Context, filter interface{}, opts ...*options.FindOptions) ([]*pageE.PageEntity, error)
	UpdateManyPageRepo(ctx context.Context, filter interface{}, update interface{}) error
}
type PixelRepo interface {
	FindAdPixelRepo(ctx context.Context, filter interface{}, opts ...*options.FindOptions) ([]*pixelE.AdPixelEntity, error)
	UpdateManyPixelRepo(ctx context.Context, filter interface{}, update interface{}) error
}

type CatalogueRepo interface {
	FindCataloguesRepo(ctx context.Context, filter interface{}, opts ...*options.FindOptions) ([]*catalogueE.CatalogueEntity, error)
	UpdateManyCatalogueRepo(ctx context.Context, filter interface{}, update interface{}) error
}

type UserRepo interface {
	FindUsersWithPipelineRepo(ctx context.Context, pipeline []bson.M, opts ...*options.AggregateOptions) (*[]userE.UserEntity, error)
}

type TiktokAdvertiserRepo interface {
	FindListAdaccountsRepo(ctx context.Context, filter bson.M, opts ...*options.FindOptions) (*[]entity.AdvertiserEntity, error)
	UpdateManyAdvertiserRepo(ctx context.Context, filter interface{}, update interface{}) error
}
type TiktokPresetColumnRepo interface {
	FindListPresetColumnRepo(ctx context.Context, filter any, opts ...*options.FindOptions) (*[]tikPresetColEnt.TiktokPresetColumnTableEntity, error)
	UpdatePresetRepo(ctx context.Context, filter any, update any, opts ...*options.UpdateOptions) error
	UpdateManyPresetColumnRepo(ctx context.Context, filter interface{}, update interface{}) error
}

type ApiClientUscDeps struct {
	Mongo             *mongo.Database
	Logger            sctx.Logger
	Repo              ApiClientRepo
	UserRepo          UserRepo
	AdAccRepo         AdAccountRepo
	PageRepo          PageRepo
	PixelRepo         PixelRepo
	CatalogueRepo     CatalogueRepo
	TikAdvertiserRepo TiktokAdvertiserRepo
	TikPresetColRepo  TiktokPresetColumnRepo
}
