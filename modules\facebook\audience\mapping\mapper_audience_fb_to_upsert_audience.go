package mapping

import (
	v20 "godsp/pkg/fb-marketing/marketing/v20"
	"time"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

func MapperCustomAudienceFBToUpsertAudience(customAudience *v20.CustomAudience, userId primitive.ObjectID) bson.M {
	now := time.Now()
	customAudience.DataSource.CreationParams = "{\"prefill\":\"true\"}"

	customAudienceUpsert := bson.M{
		"custom_audience_id": customAudience.ID,
		"name":               customAudience.Name,
		// "account_id":                    customAudience.AccountID,
		"description":                   customAudience.Description,
		"subtype":                       customAudience.Subtype,
		"approximate_count_upper_bound": customAudience.ApproximateCountUpperBound,
		"approximate_count_lower_bound": customAudience.ApproximateCountLowerBound,

		"rule":                   customAudience.Rule,
		"customer_file_source":   customAudience.CustomerFileSource,
		"lookalike_audience_ids": customAudience.Lookalikes,
		"adaccounts":             customAudience.Adaccounts,
		"lookalike_spec":         customAudience.LookalikeSpec,
		"origin_audience_id":     customAudience.OriginAudienceID,

		"pixel_id":         customAudience.PixelId,
		"data_source":      customAudience.DataSource,
		"delivery_status":  customAudience.DeliveryStatus,
		"retention_days":   customAudience.RetentionDays,
		"time_created":     now,
		"time_updated":     now,
		"operation_status": customAudience.OperationStatus,
		"is_value_based":   customAudience.IsValueBased,

		// "created_by": userId,
		// "created_at": now,
		"updated_by": userId,
		"updated_at": now,
	}

	CustomAudienceSetOnInsert := bson.M{
		"created_by": userId,
		"created_at": now,
	}

	return bson.M{
		"$set":         customAudienceUpsert,
		"$setOnInsert": CustomAudienceSetOnInsert,
		"$addToSet":    bson.M{"list_user_ids": userId},
	}

}
