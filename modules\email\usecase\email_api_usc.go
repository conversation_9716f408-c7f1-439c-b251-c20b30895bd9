package usecase

import (
	"bytes"
	"fmt"
	"godsp/modules/email/ifaces"
	"godsp/modules/email/service"
	"html/template"

	"github.com/dev-networldasia/dspgos/sctx"
)

type EmailApiUsecase struct {
	service  *service.EmailService
	logger   sctx.Logger
	jobQueue chan ifaces.EmailInfoSend
	userRepo UserRepo
}

func NewEmailApiUsecase(srv *service.EmailService, logger sctx.Logger, userRepo UserRepo) *EmailApiUsecase {
	u := &EmailApiUsecase{
		service:  srv,
		logger:   logger,
		jobQueue: make(chan ifaces.EmailInfoSend, 100),
		userRepo: userRepo,
	}

	go u.startWorker()

	return u
}

func (u *EmailApiUsecase) startWorker() {
	for emailJob := range u.jobQueue {
		err := u.service.SendBK(emailJob)
		if err != nil {
			if u.logger != nil {
				u.logger.Error(err)
			} else {
				fmt.Printf("Error sending email to %s: %v\n", emailJob.To, err)
			}
		}
	}
}

func (u *EmailApiUsecase) SendEmail(to, subject, body string) error {
	email := ifaces.EmailInfoSend{
		To:      to,
		Subject: subject,
		Body:    body,
	}

	select {
	case u.jobQueue <- email:
		// enqueued successfully
		return nil
	default:
		err := fmt.Errorf("email queue is full, failed to enqueue email to %s", to)
		if u.logger != nil {
			u.logger.Error(err.Error())
		}
		return err
	}
}

// SendLoginNotificationEmail sends a specific notification email upon successful login.
func (u *EmailApiUsecase) SendLoginNotificationEmail(userEmail, userName string) error {
	templatePath := "modules/email/templates/login_notification.html"

	tmpl, err := template.ParseFiles(templatePath)
	if err != nil {
		if u.logger != nil {
			u.logger.Error(fmt.Sprintf("Failed to parse email template %s: %v", templatePath, err))
		}
		return fmt.Errorf("failed to parse email template: %w", err)
	}

	data := struct {
		UserName string
	}{
		UserName: "Test Username",
	}

	var body bytes.Buffer
	if err := tmpl.Execute(&body, data); err != nil {
		if u.logger != nil {
			u.logger.Error(fmt.Sprintf("Failed to execute email template %s: %v", templatePath, err))
		}
		return fmt.Errorf("failed to execute email template: %w", err)
	}

	emailSubject := fmt.Sprintf("Successful Login Notification - %s", userName)

	return u.SendEmail(userEmail, emailSubject, body.String())
}

// Example email no template
func (u *EmailApiUsecase) SendExampleEmail(toEmail, recipientName string) error {
	emailSubject := fmt.Sprintf("Example Email for %s", recipientName)
	emailBody := fmt.Sprintf(
		"<p>Hello %s,</p><p>This is a generic example email sent from the GODSP platform.</p><p>This demonstrates the example email sending functionality.</p><p>Best regards,<br/>The GODSP Team</p>",
		recipientName,
	)

	return u.SendEmail(toEmail, emailSubject, emailBody)
}
