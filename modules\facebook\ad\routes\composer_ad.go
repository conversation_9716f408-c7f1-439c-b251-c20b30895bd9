package routes

import (
	clientR "godsp/modules/admin/client/repository/mongo"
	userR "godsp/modules/admin/user/repository/mongo"

	"godsp/modules/facebook/ad/repository/mongo"
	"godsp/modules/facebook/ad/transport/handlers"
	"godsp/modules/facebook/ad/usecase"
	adAccR "godsp/modules/facebook/ad_account/repository/mongo"
	"godsp/pkg/sctx/component/facebook/fbmarketing"

	"github.com/dev-networldasia/dspgos/sctx"
	"github.com/dev-networldasia/dspgos/sctx/component/mongodb"
	"github.com/dev-networldasia/dspgos/sctx/configs"

	"github.com/gofiber/fiber/v2"
)

type ComposerAds interface {
	ListAdsHdl() fiber.Handler
}

func ComposerAdsService(serviceCtx sctx.ServiceContext) ComposerAds {
	mongoDB := serviceCtx.MustGet(configs.KeyCompMongoDB).(mongodb.MongoComponent).GetDatabase()
	logger := serviceCtx.MustGet(configs.KeyLoggerDaily).(sctx.AppLoggerDaily).GetLogger("webfb")
	fbService := serviceCtx.MustGet(configs.KeyFBMarketingV20).(fbmarketing.FBMarketingServices).GetFBMarketingService()

	// Init api
	repo := mongo.NewAdRepo(mongoDB)
	adAccRepo := adAccR.NewAdAccountRepo(mongoDB)
	clientRepo := clientR.NewClientRepo(mongoDB)
	userRepo := userR.NewUserRepo(mongoDB)
	usc := usecase.NewAdUsc(fbService, repo, userRepo, logger, adAccRepo, clientRepo)
	hdl := handlers.NewAdsHdl(usc)
	return hdl
}
