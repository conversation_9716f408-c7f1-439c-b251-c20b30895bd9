**Cấu trúc và Tổ chức Code JavaScript cho Dashboard Quản Lý Qu<PERSON> C<PERSON> (Facebook Ads Manager)**

---

### 1. <PERSON><PERSON><PERSON> tiêu

- Tổ chức lại code JavaScript theo module cho dự án Ads Manager
- <PERSON><PERSON> bảo trì, debug, scale
- Sử dụng **jQuery** cho thao tác DOM, AJAX

---

### 2. <PERSON><PERSON><PERSON> trúc thư mục `assets/js/`

```bash
assets/js/
├── core/              # Hàm dụng chung toàn hệ thống
├── components/        # Component giao diện tái sử dụng
├── services/          # Gọi API chia theo tài nguyên
├── pages/             # Logic JS cho từng trang
├── router.js          # Điều hướng page -> file js
└── main.js            # Khởi tạo JS chung
```

---

### 3. Chi tiết từng folder

#### ▶ `core/`

```bash
core/
├── ajax.js              # Wrapper $.ajax có sẵn headers/token
├── toast.js             # Thông báo toast (SweetAlert hoặc custom)
├── loading.js           # Show/hide loading overlay
├── validator.js         # Validate form
├── format.js            # Định dạng số, tiền, ngày tháng
├── helpers.js           # Tiện ích chung (debounce, clone,...)
├── constants.js         # Trạng thái, enum,...
├── config.js            # Cấu hình chung (baseURL,...)
├── modal.js             # Xử lý modal
├── auth.js              # Kiểm tra token, login state
```

#### ▶ `components/`

```bash
components/
├── modal.js             # Mở/đóng modal chung
├── toast.js             # Thông báo
├── datatable.js         # Tạo bảng từ data
├── pagination.js        # Phân trang
├── filterPanel.js       # Bộ lọc
├── dropdownMenu.js      # Dropdown tùy chọn
```

#### ▶ `services/`

```bash
services/
├── authService.js       # Login/logout
├── adAccountService.js  # Tài khoản quảng cáo
├── campaignService.js   # Chiến dịch
├── adsetService.js      # Nhóm quảng cáo
├── adCreativeService.js # Quảng cáo (hình/đoạn)
├── reportService.js     # Báo cáo
├── insightService.js    # Thông tin chi tiết
├── pixelService.js      # Pixel tracking
```

#### ▶ `pages/`

```bash
pages/
├── dashboard.js             # Trang tổng quan
├── ad-accounts.js           # Danh sách tài khoản
├── ad-campaigns.js          # Chiến dịch
├── adsets.js                # Nhóm quảng cáo
├── creatives.js             # Creative
├── analytics.js             # Phân tích
├── audience.js              # Tệp khách hàng
├── conversion-events.js     # Sự kiện chuyển đổi
├── billing.js               # Hóa đơn
├── settings.js              # Cài đặt
```

---

### 4. Cách load JS theo trang

Trong HTML:

```html
<body data-page="ad-campaigns"> <!-- -> Load ad-campaigns.js -->
```

Trong `router.js`:

```js
const page = document.body.dataset.page;
if (page) import(`./pages/${page}.js`).then(m => m.init?.());
```

---

### 5. Build/minify JS

- Dùng Vite/Webpack (hoặc esbuild) để:
  - Minify code
  - Bundle theo trang
  - Tree-shake code không dùng

---

### 6. Tips bổ sung

| Vấn đề               | Giải pháp                             |
| -------------------- | ------------------------------------- |
| Code tràn lan        | Phân theo module + chia trang         |
| Khó debug            | Sử dụng console debug theo module     |
| Load chậm            | Import theo trang, không load toàn bộ |
| Lộ đồng bộ giao diện | Dùng component jQuery cho modal/toast |
| Bị leak token        | Gối API qua `ajax.js` gắn sẵn token   |

---

### 7. Tài liệu tham khảo

- [https://developer.mozilla.org/en-US/](https://developer.mozilla.org/en-US/)
- [jQuery AJAX Guide](https://api.jquery.com/jquery.ajax/)
- [SweetAlert2](https://sweetalert2.github.io/)
- [Bootstrap 5 Docs](https://getbootstrap.com/)
- [Vite](https://vitejs.dev/)

---

**Henry có thể dùng cấu trúc này làm base cho toàn bộ dashboard quảng cáo của mình.**
