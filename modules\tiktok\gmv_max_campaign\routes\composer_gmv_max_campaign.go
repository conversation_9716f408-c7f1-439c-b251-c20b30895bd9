package routes

import (
	clientR "godsp/modules/admin/client/repository/mongo"
	"godsp/modules/tiktok/campaign/repository/mongo"
	"godsp/modules/tiktok/common/services"
	"godsp/modules/tiktok/gmv_max_campaign/transport/handlers"
	"godsp/modules/tiktok/gmv_max_campaign/usecase"
	"godsp/pkg/sctx/configs"

	"github.com/dev-networldasia/dspgos/sctx"
	"github.com/dev-networldasia/dspgos/sctx/component/mongodb"
	"github.com/gofiber/fiber/v2"
	"github.com/gofiber/fiber/v2/middleware/session"
)

type ComposerGmvMaxCampaignHdl interface {
	ListGmvMaxCampaignHdl(store *session.Store) fiber.Handler
}

func ComposerGmvMaxCampaignService(serviceCtx sctx.ServiceContext) ComposerGmvMaxCampaignHdl {
	mongoDB := serviceCtx.MustGet(configs.KeyCompMongoDB).(mongodb.MongoComponent).GetDatabase()
	logger := serviceCtx.MustGet(configs.KeyLoggerDaily).(sctx.AppLoggerDaily).GetLogger("web_tiktok.gmv_max_campaign")

	clientRepo := clientR.NewClientRepo(mongoDB)
	tiktokService := services.GetTiktokServices(logger)

	params := usecase.ParamApiCampaignUsc{
		TiktokService: tiktokService,
		Repo:          mongo.NewCampaignRepo(mongoDB),
		Logger:        logger,
		ClientRepo:    clientRepo,
	}

	usc := usecase.NewCampaignUsc(params)
	hdl := handlers.NewCampaignHdl(usc)
	return hdl
}
