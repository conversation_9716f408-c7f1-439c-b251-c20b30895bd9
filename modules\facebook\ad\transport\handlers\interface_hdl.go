package handlers

import (
	"context"
	clientE "godsp/modules/admin/client/entity"
	userRes "godsp/modules/admin/user/transport/responses"
	adAccRes "godsp/modules/facebook/ad_account/transport/response"

	"go.mongodb.org/mongo-driver/bson/primitive"
)

type AdUsc interface {
	GetUserInfo(ctx context.Context, userId primitive.ObjectID) (*userRes.DetailsUser, error)
	ListAdAccountAdUsc(ctx context.Context) ([]*adAccRes.AdAccountEditClient, error)
	ListClientAdUsc(ctx context.Context) ([]*clientE.ClientEntity, error)
}

type adsHdl struct {
	usc AdUsc
}

func NewAdsHdl(usc AdUsc) *adsHdl {
	return &adsHdl{
		usc: usc,
	}
}
