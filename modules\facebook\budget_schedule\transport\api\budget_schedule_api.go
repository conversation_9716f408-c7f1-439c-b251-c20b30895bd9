package api

import (
	"context"
	"fmt"
	"godsp/modules/facebook/budget_schedule/transport/requests"
	"godsp/modules/facebook/budget_schedule/transport/responses"
	"godsp/pkg/gos/utils"
	"godsp/pkg/sctx/core"
	"net/http"

	"github.com/gofiber/fiber/v2"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

type ApiBudgetScheduleUsc interface {
	ReloadByCampaignAdsetIdApiBudgetScheduleForAdminUsc(ctx context.Context, payload *requests.ReloadBudgetScheduleByCampaignAdsetIdReq, userId primitive.ObjectID) error
	GetByCampaignAdsetIdApiBudgetScheduleUsc(ctx context.Context, campaignAdsetId string, typeAction string, userId primitive.ObjectID) (*[]responses.HighDemandPeriodEntity, error)
	CreateBudgetScheduleUsc(ctx context.Context, payload *requests.CreateBudgetScheduleReq) (*[]responses.HighDemandPeriodEntity, error)
	CreateListBudgetScheduleUsc(ctx context.Context, payload *requests.CreateListBudgetScheduleReq, userId primitive.ObjectID) (*[]responses.HighDemandPeriodEntity, []string)
	DeleteListByIdBudgetScheduleUsc(ctx context.Context, payload *requests.DeleteBudgetScheduleReq) (*[]string, []error)
	UpdateListBudgetScheduleUsc(ctx context.Context, payload *requests.UpdateListScheduleReq, userId primitive.ObjectID) (*[]responses.HighDemandPeriodEntity, error)
	// UpdateBudgetScheduleUsc(ctx context.Context, payload *requests.UpdateBudgetScheduleReq) error
}

type budgetScheduleApi struct {
	usc ApiBudgetScheduleUsc
}

func NewBudgetScheduleApi(usc ApiBudgetScheduleUsc) *budgetScheduleApi {
	return &budgetScheduleApi{
		usc: usc,
	}
}

/***
 * Api reload budget schedule
 */
func (a *budgetScheduleApi) ReloadBudgetScheduleByCampaignAdsetIdApi() fiber.Handler {
	return func(c *fiber.Ctx) error {

		var payload requests.ReloadBudgetScheduleByCampaignAdsetIdReq
		if err := c.BodyParser(&payload); err != nil {
			return core.ReturnErrsForApi(c, err)
		}

		//validate
		if err := payload.Validate(); err != nil {
			return core.ReturnErrForApi(c, err.Error())
		}

		userId, err := utils.GetUserId(c.Context())
		if err != nil {
			return core.ReturnErrsForApi(c, err)
		}

		if errs := a.usc.ReloadByCampaignAdsetIdApiBudgetScheduleForAdminUsc(c.Context(), &payload, userId); errs != nil {
			return core.ReturnErrsForApi(c, errs)
		}

		return c.Status(http.StatusOK).JSON(core.ResponseData(map[string]interface{}{
			"msg": "Reload Budget schedules successfully",
		}))
	}
}

/***
 * Api Get budget schedule
 */
func (a *budgetScheduleApi) GetByCampaignAdsetIdApiBudgetScheduleApi() fiber.Handler {
	return func(c *fiber.Ctx) error {

		var payload requests.ReloadBudgetScheduleByCampaignAdsetIdReq
		if err := c.BodyParser(&payload); err != nil {
			return core.ReturnErrsForApi(c, err)
		}

		//validate
		if err := payload.Validate(); err != nil {
			return core.ReturnErrForApi(c, err.Error())
		}

		userId, err := utils.GetUserId(c.Context())
		if err != nil {
			// fmt.Println("Error:", err)
			return core.ReturnErrsForApi(c, err)
		}

		var campAdsetId string
		var typeAction = "campaign"
		if payload.AdsetId != "" {
			typeAction = "adset"
			campAdsetId = payload.AdsetId
		} else {
			campAdsetId = payload.CampaignId
		}

		// camp, err :=
		buds, errs := a.usc.GetByCampaignAdsetIdApiBudgetScheduleUsc(c.Context(), campAdsetId, typeAction, userId)
		// fmt.Printf("xxxxxxx----------> %v \n", errs)
		if errs != nil {
			return core.ErrNotFound
		}

		return c.Status(http.StatusOK).JSON(core.ResponseData(map[string]interface{}{
			"msg":  "Get Budget schedules successfully",
			"data": buds,
		}))
	}
}

/***
 * Api create budget schedule
 */
func (a *budgetScheduleApi) CreateBudgetScheduleApi() fiber.Handler {
	return func(c *fiber.Ctx) error {

		var payload requests.CreateListBudgetScheduleReq

		if c.Body() != nil && len(c.Body()) > 0 {
			if err := c.BodyParser(&payload); err != nil {
				return core.ReturnErrsForApi(c, err)
			}
		}
		if err := c.BodyParser(&payload); err != nil {
			return core.ReturnErrsForApi(c, err)
		}

		// Validate từng phần tử trong slice
		for i, schedule := range payload {
			if errs := schedule.Validate(); errs != nil {
				return core.ReturnErrsForApi(c, fmt.Errorf("validation error at index %d: %v", i, errs))
			}
		}

		userId, err := utils.GetUserId(c.Context())
		if err != nil {
			fmt.Println("Error:", err)
			return core.ReturnErrsForApi(c, err)
		}

		buds, errs := a.usc.CreateListBudgetScheduleUsc(c.Context(), &payload, userId)

		if len(errs) > 0 {
			return c.Status(http.StatusBadRequest).JSON(core.ResponseData(map[string]interface{}{
				"data":   []interface{}{},
				"errors": errs,
				"msg":    "Create Budget schedules fail!",
			}))
		}

		return c.Status(http.StatusOK).JSON(core.ResponseData(map[string]interface{}{
			"data": buds,
			"msg":  "Create Budget schedules successfully",
		}))
	}
}

/***
 * Api update budget schedule
 */
func (a *budgetScheduleApi) UpdateListBudgetScheduleApi() fiber.Handler {
	return func(c *fiber.Ctx) error {

		var payload requests.UpdateListScheduleReq

		if c.Body() != nil && len(c.Body()) > 0 {
			if err := c.BodyParser(&payload); err != nil {
				return core.ReturnErrsForApi(c, err)
			}
		}

		// Validate for schedule item
		for i, schedule := range payload {
			if errs := schedule.Validate(); errs != nil {
				return core.ReturnErrsForApi(c, fmt.Errorf("validation error at index %d: %v", i, errs))
			}
		}

		// Get schedule of campaign to validate

		if err := payload.Validate(&payload); err != nil {
			return c.Status(http.StatusBadRequest).JSON(core.ResponseData(map[string]interface{}{
				"error": err.Error(),
				"msg":   "Update Budget schedules fail",
			}))
		}

		userId, err := utils.GetUserId(c.Context())
		if err != nil {
			fmt.Println("Error:", err)
			return core.ReturnErrsForApi(c, err)
		}

		buds, err := a.usc.UpdateListBudgetScheduleUsc(c.Context(), &payload, userId)
		if err != nil {
			return c.Status(http.StatusBadRequest).JSON(core.ResponseData(map[string]interface{}{
				"error": err.Error(),
				"msg":   "Update Budget schedules fail",
			}))
		}

		return c.Status(http.StatusOK).JSON(core.ResponseData(map[string]interface{}{
			"data": buds,
			"msg":  "Update Budget schedules successfully",
		}))
	}
}

/***
 * Api create budget schedule
 */
func (a *budgetScheduleApi) DeleteByIdBudgetScheduleApi() fiber.Handler {
	return func(c *fiber.Ctx) error {

		var payload requests.DeleteBudgetScheduleReq
		if err := c.BodyParser(&payload); err != nil {
			return core.ReturnErrsForApi(c, err)
		}

		//validate
		if errs := payload.Validate(); errs != nil {
			return core.ReturnErrsForApi(c, errs)
		}

		userId, err := utils.GetUserId(c.Context())
		if err != nil {
			fmt.Println("Error:", err)
			return core.ReturnErrsForApi(c, err)
		}
		payload.UserId = userId

		deleted, errs := a.usc.DeleteListByIdBudgetScheduleUsc(c.Context(), &payload)
		if len(errs) > 0 {
			return c.Status(http.StatusBadRequest).JSON(core.ResponseData(map[string]interface{}{
				"data":    nil,
				"deleted": deleted,
				"errors":  errs,
				"msg":     "Delete Budget schedules fail!",
			}))
		}

		return c.Status(http.StatusOK).JSON(core.ResponseData(map[string]interface{}{
			"data": nil,
			"msg":  "Delete Budget schedules successfully",
		}))
	}
}
