package mapping

import (
	campEnum "godsp/modules/facebook/campaign/common/enum"
	"godsp/modules/facebook/campaign/entity"
	"godsp/modules/facebook/campaign/transport/requests"
	"godsp/modules/facebook/common/fbenums"
	v20 "godsp/pkg/fb-marketing/marketing/v20"
	"time"

	"go.mongodb.org/mongo-driver/bson"
)

func MapperPayloadUpdateToUpdateEntity(payload *requests.UpdateCampaignReq, campPre *entity.CampaignEntity, isAdmin *bool) (bson.M, string) {
	now := time.Now()

	statusToFB := fbenums.FB_STATUS_PAUSED
	updateData := bson.M{
		"name": payload.Name,
		// "objective":  payload.Objective,
		"updated_at": now,
		"updated_by": payload.UserId,
	}

	if len(payload.SpecialAdCategories) > 0 {
		updateData["special_ad_categories"] = payload.SpecialAdCategories
	}

	if payload.BidStrategy != nil {
		updateData["bid_strategy"] = payload.BidStrategy
	}

	if payload.DailyBudget != nil {
		if *payload.DailyBudget > 0 {
			updateData["daily_budget"] = *payload.DailyBudget
			updateData["lifetime_budget"] = 0
		}
	}

	if payload.LifeTimeBudget != nil {
		if *payload.LifeTimeBudget > 0 {
			updateData["daily_budget"] = 0
			updateData["lifetime_budget"] = *payload.LifeTimeBudget
		}
	}

	if payload.DailyBudget == nil && payload.LifeTimeBudget == nil {
		updateData["daily_budget"] = 0
	}

	if payload.IsBudgetScheduleEnabled != nil {
		updateData["is_budget_schedule_enabled"] = *payload.IsBudgetScheduleEnabled
	}

	if payload.CanUseSpendCap != nil {
		updateData["can_use_spend_cap"] = payload.CanUseSpendCap
	}

	if payload.SpendCap != nil {
		if *payload.SpendCap > 0 {
			updateData["spend_cap"] = *payload.SpendCap
		}
		if payload.Status == fbenums.FB_STATUS_ACTIVE {
			updateData["approve"] = campEnum.Review
		}
		statusToFB = fbenums.FB_STATUS_PAUSED
		updateData["status"] = fbenums.FB_STATUS_PAUSED
	}

	if payload.Status != "" {
		// User Action
		if payload.Status == fbenums.FB_STATUS_ACTIVE && (campPre.Approve == campEnum.Review || campPre.Approve == campEnum.None) {
			updateData["status"] = fbenums.FB_STATUS_PAUSED
			updateData["approve"] = campEnum.Review
			statusToFB = fbenums.FB_STATUS_PAUSED
		} else {
			if campPre.Approve == campEnum.Review {
				updateData["approve"] = campEnum.None
			}
			updateData["status"] = payload.Status
			statusToFB = payload.Status
		}
	}

	if payload.BidStrategy != nil {
		updateData["bid_strategy"] = *payload.BidStrategy
	}

	// Admin Action
	// if isAdmin != nil && *isAdmin {
	// 	updateData["status"] = payload.Status
	// 	if payload.Status == fbenums.FB_STATUS_ACTIVE {
	// 		updateData["approve"] = campEnum.Approve
	// 	}

	// 	return bson.M{
	// 		"$set": updateData,
	// 	}, payload.Status
	// }

	return bson.M{
		"$set": updateData,
		// "$addToSet": bson.M{"list_user_ids": payload.UserId},
	}, statusToFB

}

func MapperUpdateCampaignRequestToCampaigns(payload *requests.UpdateCampaignReq, campPre *entity.CampaignEntity, isAdmin *bool) (bson.M, *v20.Campaign) {
	campFB := v20.Campaign{
		ID:        payload.CampaignID,
		AccountID: payload.AccountID,
		Name:      payload.Name,
		// Objective:           payload.Objective,
		Status:              fbenums.FB_STATUS_PAUSED,
		SpecialAdCategories: payload.SpecialAdCategories,
	}
	if payload.BidStrategy != nil {
		campFB.BidStrategy = *payload.BidStrategy
	}

	if payload.IsBudgetScheduleEnabled != nil {
		campFB.IsBudgetScheduleEnabled = *payload.IsBudgetScheduleEnabled
	}

	if payload.DailyBudget != nil {
		if *payload.DailyBudget > 0 {
			campFB.DailyBudget = *payload.DailyBudget
			campFB.LifeTimeBudget = 0
		}
	}

	if payload.LifeTimeBudget != nil {
		campFB.LifeTimeBudget = *payload.LifeTimeBudget
		campFB.DailyBudget = 0
	}

	if payload.CanUseSpendCap != nil {
		campFB.CanUseSpendCap = *payload.CanUseSpendCap
	}

	// if isAdmin != nil && *isAdmin {
	// 	campFB.Status = payload.Status
	// }

	if payload.SpendCap != nil {
		if *payload.SpendCap > 0 {
			campFB.SpendCap = *payload.SpendCap
		}
	}

	if payload.BidStrategy != nil {
		campFB.BidStrategy = *payload.BidStrategy
	}

	campUpdate, statusToFB := MapperPayloadUpdateToUpdateEntity(payload, campPre, isAdmin)

	campFB.Status = statusToFB
	return campUpdate, &campFB
}

func MapperUpdateNameStatusCampaignRequestToCampaigns(payload *requests.UpdateNameStatusCampaignReq) *v20.Campaign {
	campFB := v20.Campaign{
		ID:        payload.CampaignID,
		AccountID: payload.AccountID,
		Name:      payload.Name,
		Status:    fbenums.FB_STATUS_PAUSED,
	}
	return &campFB

}

func MapperUpdateNameStatusCampaign(payload *requests.UpdateNameStatusCampaignReq, campPre *entity.CampaignEntity) (bson.M, *v20.Campaign) {
	now := time.Now()
	campFB := v20.Campaign{
		ID:        payload.CampaignID,
		AccountID: payload.AccountID,
		Name:      payload.Name,
	}

	campUpdate := bson.M{
		"updated_at": now,
		"updated_by": payload.UserId,
	}
	if payload.Name != "" {
		campUpdate["name"] = payload.Name
		campFB.Name = payload.Name
	}
	if payload.Status == "" {
		return campUpdate, &campFB
	}

	if payload.Status == fbenums.FB_STATUS_ACTIVE && (campPre.Approve == campEnum.Review || campPre.Approve == campEnum.None) {
		campUpdate["status"] = fbenums.FB_STATUS_PAUSED
		campUpdate["approve"] = campEnum.Review
		campFB.Status = fbenums.FB_STATUS_PAUSED
	} else {
		if campPre.Approve == campEnum.Review {
			campUpdate["approve"] = campEnum.None
		}
		campUpdate["status"] = payload.Status
		campFB.Status = payload.Status
	}

	return bson.M{
		"$set": campUpdate,
	}, &campFB

}

// func MapperUpdateNameStatusCampaign(payload *requests.UpdateNameStatusCampaignReq, campPre *entity.CampaignEntity, isAdmin *bool) (bson.M, *v20.Campaign) {
// 	now := time.Now()
// 	campFB := v20.Campaign{
// 		ID:        payload.CampaignID,
// 		AccountID: payload.AccountID,
// 		Name:      payload.Name,
// 	}

// 	campUpdate := bson.M{
// 		"updated_at": now,
// 		"updated_by": payload.UserId,
// 	}
// 	if payload.Name != "" {
// 		campUpdate["name"] = payload.Name
// 		campFB.Name = payload.Name
// 	}
// 	if payload.Status == "" {
// 		return campUpdate, &campFB
// 	}

// 	// Admin Action
// 	// if isAdmin != nil && *isAdmin {
// 	// 	campFB.Status = payload.Status
// 	// 	campUpdate["status"] = payload.Status
// 	// 	if payload.Status == fbenums.FB_STATUS_ACTIVE {
// 	// 		campUpdate["approve"] = campEnum.Approve
// 	// 		return bson.M{
// 	// 			"$set": campUpdate,
// 	// 		}, &campFB
// 	// 	}

// 	// 	return bson.M{
// 	// 		"$set": campUpdate,
// 	// 	}, &campFB
// 	// }

// 	if payload.Status == fbenums.FB_STATUS_ACTIVE && (campPre.Approve == campEnum.Review || campPre.Approve == campEnum.None) {
// 		campUpdate["status"] = fbenums.FB_STATUS_PAUSED
// 		campUpdate["approve"] = campEnum.Review
// 		campFB.Status = fbenums.FB_STATUS_PAUSED
// 	} else {
// 		if campPre.Approve == campEnum.Review {
// 			campUpdate["approve"] = campEnum.None
// 		}
// 		campUpdate["status"] = payload.Status
// 		campFB.Status = payload.Status
// 	}

// 	return bson.M{
// 		"$set": campUpdate,
// 	}, &campFB
// 	// User Action
// 	// if payload.Status == fbenums.FB_STATUS_ACTIVE {
// 	// 	if campPre.Approve == campEnum.Approve {
// 	// 		campUpdate["status"] = fbenums.FB_STATUS_ACTIVE
// 	// 		campFB.Status = fbenums.FB_STATUS_ACTIVE
// 	// 	} else if campPre.Approve == campEnum.Review {
// 	// 		campUpdate["status"] = fbenums.FB_STATUS_PAUSED
// 	// 		campFB.Status = fbenums.FB_STATUS_PAUSED
// 	// 	} else {
// 	// 		campUpdate["status"] = fbenums.FB_STATUS_PAUSED
// 	// 		campUpdate["approve"] = campEnum.Review
// 	// 	}

// 	// 	return bson.M{
// 	// 		"$set": campUpdate,
// 	// 	}, &campFB
// 	// }

// 	// campFB.Status = payload.Status
// 	// campUpdate["status"] = payload.Status
// 	// if campPre.Approve == campEnum.Review {
// 	// 	campUpdate["approve"] = campEnum.None
// 	// }

// 	// return bson.M{
// 	// 	"$set": campUpdate,
// 	// }, &campFB
// }
