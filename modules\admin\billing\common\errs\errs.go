package errs

import "errors"

var (
	ErrNameBillingValidate         = errors.New("name of billing is not empty")
	ErrDescriptionBillingValidate  = errors.New("description of billing is not empty")
	ErrAmountBillingValidate       = errors.New("amount of billing is not empty")
	ErrCurrencyBillingValidate     = errors.New("currency of billing is not empty")
	ErrBillNumberValidate          = errors.New("bill number of billing is not empty")
	ErrBillingIDValidate           = errors.New("billing id is not empty")
	ErrAdvertiserIDBillingValidate = errors.New("advertiser id of billing is not empty")
	ErrTypeBillingValidate         = errors.New("type of billing is not empty")
	ErrClientIdBillingValidate     = errors.New("client id of billing is not empty")
	ErrOrderingBillingValidate     = errors.New("ordering of billing must be greater than 0")
	ErrData                        = errors.New("invalid form format")
	ErrIDNotFound                  = errors.New("id billing not found")
)
