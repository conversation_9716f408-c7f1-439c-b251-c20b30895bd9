package mapping

import (
	"godsp/modules/facebook/ad_pixel/entity"
	v20 "godsp/pkg/fb-marketing/marketing/v20"
	"time"

	"go.mongodb.org/mongo-driver/bson/primitive"
)

func MapperAdPixelFBToEntity(ap *v20.AdPixel, accountId string, userId primitive.ObjectID) *entity.AdPixelEntity {
	now := time.Now()

	return &entity.AdPixelEntity{
		ID:        primitive.NewObjectID(),
		AccountID: accountId,
		PixelID:   ap.ID,
		Name:      ap.Name,

		CreatedBy: userId,
		CreatedAt: now,
		UpdatedAt: now,
		UpdatedBy: userId,
	}
}
