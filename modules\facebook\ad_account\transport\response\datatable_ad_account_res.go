package response

import (
	"godsp/pkg/gos/goconst"
	"time"

	"go.mongodb.org/mongo-driver/bson/primitive"
)

type AdAccountListResponse struct {
	AdAccountDataTables []*AdAccountDataTable
	Count               int64
	CountFilter         int64
}

type AdAccountEditClient struct {
	ID              primitive.ObjectID   `json:"_id"`
	AccountID       string               `json:"account_id" bson:"account_id"`
	Name            string               `json:"name" bson:"name"`
	Currency        string               `json:"currency" bson:"currency"`
	TimeZoneName    string               `json:"timezone_name" bson:"timezone_name"`
	ListUserIDs     []primitive.ObjectID `json:"list_user_ids" bson:"list_user_ids"`
	ClientIDs       []primitive.ObjectID `json:"client_ids" bson:"client_ids"`
	CreatedBy       primitive.ObjectID   `json:"created_by" bson:"created_by"`
	CreatorFullName string               `json:"creator_full_name" bson:"creator_full_name"`
	CreatedAt       time.Time            `json:"created_at" bson:"created_at"`
	UpdatedBy       primitive.ObjectID   `json:"updated_by" bson:"updated_by"`
	UpdaterFullName string               `json:"updater_full_name" bson:"updater_full_name"`
	UpdatedAt       time.Time            `json:"updated_at" bson:"updated_at"`
}
type AdAccountWithFullname struct {
	ID              primitive.ObjectID `json:"_id"`
	AccountID       string             `json:"account_id" bson:"account_id"`
	Name            string             `json:"name" bson:"name"`
	Currency        string             `json:"currency" bson:"currency"`
	TimeZoneName    string             `json:"timezone_name" bson:"timezone_name"`
	CreatedBy       primitive.ObjectID `json:"created_by" bson:"created_by"`
	CreatorFullName string             `json:"creator_full_name" bson:"creator_full_name"`
	CreatedAt       time.Time          `json:"created_at" bson:"created_at"`
	UpdatedBy       primitive.ObjectID `json:"updated_by" bson:"updated_by"`
	UpdaterFullName string             `json:"updater_full_name" bson:"updater_full_name"`
	UpdatedAt       time.Time          `json:"updated_at" bson:"updated_at"`
}

type AdAccountDataTable struct {
	ID              primitive.ObjectID `json:"_id"`
	Name            string             `json:"name" bson:"name"`
	Currency        string             `json:"currency" bson:"currency"`
	TimeZoneName    string             `json:"timezone_name" bson:"timezone_name"`
	CreatedBy       primitive.ObjectID `json:"created_by" bson:"created_by"`
	CreatorFullName string             `json:"creator_full_name" bson:"creator_full_name"`
	CreatedAt       string             `json:"created_at" bson:"created_at"`
	UpdatedBy       primitive.ObjectID `json:"updated_by" bson:"updated_by"`
	UpdaterFullName string             `json:"updater_full_name" bson:"updater_full_name"`
	UpdatedAt       string             `json:"updated_at" bson:"updated_at"`
}

func MapperDatatable(data AdAccountWithFullname) *AdAccountDataTable {
	createAt := data.CreatedAt.Format(goconst.YYYY_MM_DD_HH_MM_SS)
	updateAt := data.UpdatedAt.Format(goconst.YYYY_MM_DD_HH_MM_SS)

	return &AdAccountDataTable{
		ID:              data.ID,
		Name:            data.Name,
		Currency:        data.Currency,
		TimeZoneName:    data.TimeZoneName,
		CreatedBy:       data.CreatedBy,
		CreatorFullName: data.CreatorFullName,
		CreatedAt:       createAt,
		UpdatedBy:       data.UpdatedBy,
		UpdaterFullName: data.UpdaterFullName,
		UpdatedAt:       updateAt,
	}
}
