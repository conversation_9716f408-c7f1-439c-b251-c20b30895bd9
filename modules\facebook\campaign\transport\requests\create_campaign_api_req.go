package requests

import (
	"godsp/modules/facebook/campaign/common/enum"
	"godsp/modules/facebook/campaign/common/errs"
	"godsp/modules/facebook/campaign/entity"
	"godsp/modules/facebook/campaign/transport/rules"
	"godsp/modules/facebook/common/fbconst"
	"godsp/modules/facebook/common/fbenums"
	"godsp/modules/facebook/common/fbrules"
	"strings"

	"github.com/go-playground/validator/v10"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

type CreateCampaignReq struct {
	AccountID string `json:"-"`
	// AccountID string `json:"account_id" validate:"required"`

	Name                    string   `json:"name" validate:"required"`
	Objective               string   `json:"objective" validate:"required,ruleObjective"`
	Status                  string   `json:"status"  validate:"required,ruleStatus"`
	SpecialAdCategories     []string `json:"special_ad_categories" validate:"required"`
	BidStrategy             *string  `json:"bid_strategy,omitempty"`
	DailyBudget             *uint64  `json:"daily_budget,omitempty"`
	LifeTimeBudget          *uint64  `json:"lifetime_budget,omitempty"`
	IsBudgetScheduleEnabled *bool    `json:"is_budget_schedule_enabled,omitempty" validate:"omitempty,boolean"`

	CanUseSpendCap *bool   `json:"can_use_spend_cap,omitempty" validate:"omitempty,boolean"`
	SpendCap       *uint64 `json:"spend_cap,omitempty" validate:"omitempty,numeric,gt=0"`

	PromotedObject *entity.AdPromotedObject `json:"promoted_object,omitempty"`

	ClientID primitive.ObjectID `json:"-"`
	UserId   primitive.ObjectID `json:"-"`
}

func (req *CreateCampaignReq) Validate() []string {
	validate := validator.New()

	validate.RegisterValidation("ruleStatus", fbrules.RuleFBStatus)
	validate.RegisterValidation("ruleObjective", rules.RuleFBObjective)
	var validationErrors []string
	req.Name = strings.TrimSpace(req.Name)

	err := validate.Struct(req)

	if err != nil {
		for _, err := range err.(validator.ValidationErrors) {
			switch err.Field() {
			case "AccountID":
				validationErrors = append(validationErrors, errs.ErrCampaignAccountID.Error())
			case "Name":
				validationErrors = append(validationErrors, errs.ErrCampaignName.Error())
			case "Objective":
				validationErrors = append(validationErrors, errs.ErrCampaignObjective.Error())
			case "Status":
				validationErrors = append(validationErrors, errs.ErrCampaignStatus.Error())
			case "CanUseSpendCap":
				validationErrors = append(validationErrors, errs.ErrCampaignSpendActiveFor.Error())
			case "SpendCap":
				validationErrors = append(validationErrors, errs.ErrCampaignSpendCapFor.Error())
			case "SpecialAdCategories":
				validationErrors = append(validationErrors, errs.ErrCampaignSpecialAdCategories.Error())
			}
		}
	}

	// validate SpecialAdCategories
	if req.SpecialAdCategories != nil {
		for _, cate := range req.SpecialAdCategories {
			if ok := rules.RuleSpecialAdCategories(cate); !ok {
				validationErrors = append(validationErrors, errs.ErrCampaignSpecialAdCategories.Error())
			}
		}
	}

	// Bid Strategy
	if req.BidStrategy != nil {
		if bidStrategy := strings.TrimSpace(*req.BidStrategy); bidStrategy == "" {
			req.BidStrategy = nil
		} else if ok := rules.RuleFBidStrategy(bidStrategy); !ok {
			validationErrors = append(validationErrors, errs.ErrCampaignBidStrategy.Error())
		} else {
			req.BidStrategy = &bidStrategy
		}

		if req.DailyBudget == nil && req.LifeTimeBudget == nil {
			validationErrors = append(validationErrors, errs.ErrCampaignBudget.Error())
		} else {
			//validate daily_budget
			if req.DailyBudget != nil {
				if *req.DailyBudget > fbconst.DAILY_BUDGET_MAX {
					*req.DailyBudget = fbconst.DAILY_BUDGET_MAX
				} else if *req.DailyBudget < fbconst.DAILY_BUDGET_MIN {
					req.DailyBudget = nil
					validationErrors = append(validationErrors, errs.ErrCampaignBudgetToSmall.Error())
				}
			}

			//validate lifetime_budget
			if req.LifeTimeBudget != nil {
				if *req.LifeTimeBudget < fbconst.LIFETIME_BUDGET_MIN {
					req.LifeTimeBudget = nil
					validationErrors = append(validationErrors, errs.ErrCampaignBudgetToSmall.Error())
				}
			}
		}
	} else {
		req.DailyBudget = nil
		req.LifeTimeBudget = nil
	}

	if req.CanUseSpendCap != nil {
		if req.SpendCap == nil {
			validationErrors = append(validationErrors, errs.ErrCampaignSpendCapReq.Error())
			req.CanUseSpendCap = nil
		}
	} else if req.SpendCap != nil {
		validationErrors = append(validationErrors, errs.ErrCampaignSpendActiveIsEnabled.Error())
		req.SpendCap = nil
	}

	// set value when validate ok
	if validationErrors == nil {
		req.Status = fbenums.StatusFB[req.Status]
		req.Objective = enum.Objective[req.Objective]
	}

	return validationErrors
}
