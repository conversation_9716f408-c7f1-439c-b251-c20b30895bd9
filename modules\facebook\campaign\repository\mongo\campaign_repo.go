package mongo

import (
	"context"
	"errors"
	"fmt"
	"godsp/modules/facebook/campaign/entity"
	"godsp/modules/facebook/common/fberrs"
	"godsp/pkg/sctx/core"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
)

type campaignRepo struct {
	DB         *mongo.Database
	Collection *mongo.Collection
}

func NewCampaignRepo(DB *mongo.Database) *campaignRepo {
	return &campaignRepo{
		DB:         DB,
		Collection: DB.Collection(entity.CampaignEntity{}.CollectionName()),
	}
}

/***
 * Upsert campaign
 */
func (r *campaignRepo) UpsertCampaignRepo(ctx context.Context, filter bson.M, campaignBSon bson.M) error {
	// opts := options.Update().SetUpsert(true)
	// fmt.Printf("budget remaining------> %v \n", campaignBSon)
	// _, err := r.Collection.UpdateOne(ctx, filter, campaignBSon, opts)

	_, err := r.Collection.UpdateOne(ctx, filter, campaignBSon, options.Update().SetUpsert(true))

	if err != nil {
		return err
	}

	return nil
}

/***
 * update one Campaign
 */
func (r *campaignRepo) UpdateOneCampaignRepo(ctx context.Context, filter interface{}, campaignEntity *entity.CampaignEntity, opts ...*options.UpdateOptions) error {
	update := bson.M{
		"$set": *campaignEntity,
	}
	_, err := r.Collection.UpdateOne(ctx, filter, update)
	if err != nil {
		return err
	}

	return nil
}

/**
 * FindOne campaign
 */
func (r *campaignRepo) FindOneCampaignRepo(ctx context.Context, filter interface{}, opts ...*options.FindOneOptions) (*entity.CampaignEntity, error) {

	fmt.Printf("\n -----------  filter ----------- %+v \n", filter)

	var campaign entity.CampaignEntity
	err := r.Collection.FindOne(ctx, filter, opts...).Decode(&campaign)
	if errors.Is(err, mongo.ErrNoDocuments) {
		return nil, core.ErrNotFound
	}

	return &campaign, nil
}

/**
 * Find One campaign have Adset And Ad
 */
func (r *campaignRepo) AggregateCampaignRepo(ctx context.Context, pipeline []bson.M, opts ...*options.AggregateOptions) (*entity.CampaignDetailResponseEntity, error) {
	var campaign entity.CampaignDetailResponseEntity
	cursor, err := r.Collection.Aggregate(ctx, pipeline, opts...)
	if errors.Is(err, mongo.ErrNoDocuments) {
		return nil, core.ErrNotFound
	}

	defer cursor.Close(ctx)

	if cursor.Next(ctx) {
		if err := cursor.Decode(&campaign); err != nil {
			return nil, err
		}
	}

	if errors.Is(err, mongo.ErrNoDocuments) {
		return nil, core.ErrNotFound
	}

	return &campaign, nil
}

/***
 *
 *  skip := (page * limit) - limit
	opts := &options.FindOptions{
		AllowDiskUse: &fbenums.ALLOW_DISK_USE_TRUE,
		Skip:         &skip,
		Limit:        &limit,
	}
	if sort != nil {
		opts.SetSort(sort)
	}
*/

func (r *campaignRepo) FindCampaignRepo(ctx context.Context, filter interface{}, opts ...*options.FindOptions) (*[]entity.CampaignEntity, error) {
	var campaigns []entity.CampaignEntity

	cursor, err := r.Collection.Find(ctx, filter, opts...)
	if err != nil {
		return nil, err
	}
	defer cursor.Close(ctx)

	if cursor == nil {
		return nil, fberrs.ErrNilCursorValue
	}

	err = cursor.All(ctx, &campaigns)
	if err != nil {
		return nil, err
	}

	return &campaigns, nil
}

/**
 * count Ad Account
 */
func (r *campaignRepo) CountCampaignRepo(ctx context.Context, filter interface{}) (int64, error) {
	return r.Collection.CountDocuments(ctx, filter)
	// return r.Collection.CountDocuments(ctx, filter)
}

/**
 * Delete Ad Campaign */
func (r *campaignRepo) DeleteCampaignRepo(ctx context.Context, filter interface{}, opts ...*options.DeleteOptions) error {

	if _, err := r.Collection.DeleteMany(ctx, filter, opts...); err != nil {
		return err
	}
	return nil
}

/**
 * Delete Ad Campaign */
func (r *campaignRepo) DeleteCampaignManyTransactionRepo(ctx context.Context, filter interface{}, opts ...*options.DeleteOptions) error {

	if _, err := r.Collection.DeleteMany(ctx, filter, opts...); err != nil {
		return err
	}

	return nil

	// Start a new session to perform database operations in a transaction.
	// session, err := r.Collection.Database().Client().StartSession()
	// if err != nil {
	// 	return err
	// }
	// defer session.EndSession(ctx)

	// err = mongo.WithSession(ctx, session, func(sc mongo.SessionContext) error {
	// 	// Begin a new transaction.
	// 	if err := session.StartTransaction(); err != nil {
	// 		return err
	// 		return core.ErrInternalServerError
	// 	}

	// 	// Remove multiple campaign
	// 	result, err := r.Collection.DeleteMany(sc, filter)
	// 	if err != nil {
	// 		session.AbortTransaction(sc)
	// 		return err
	// 		return core.ErrBadRequest
	// 	}

	// 	// Commit the transaction if no error occurred.
	// 	if result.DeletedCount == 0 {
	// 		return core.ErrBadRequest
	// 	}

	// 	// Commit transaction If we reach here, no errors occurred and the transaction was successful.
	// 	if err := session.CommitTransaction(sc); err != nil {
	// 		session.AbortTransaction(sc)
	// 		return err
	// 		return core.ErrBadRequest
	// 	}
	// 	return nil
	// })
	// return err
}

/**
 * Update UserId for Campaign
 */
// func (r *campaignRepo) UpdateManyCampignRepo(ctx context.Context, filter interface{}, update interface{}) error {

// 	result, err := r.Collection.UpdateMany(ctx, filter, update, options.Update().SetUpsert(false))
// 	if err != nil {
// 		return err
// 	}

// 	if result.MatchedCount == 0 && result.ModifiedCount == 0 {
// 		return core.ErrBadRequest
// 	}

// 	return nil
// }
