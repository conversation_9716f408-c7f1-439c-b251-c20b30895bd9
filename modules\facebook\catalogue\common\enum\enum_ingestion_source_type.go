package enum

const (
	INGESTION_SOURCE_TYPE_ALL           = "ALL"
	INGESTION_SOURCE_TYPE_PRIMARY       = "PRIMARY" //Default
	INGESTION_SOURCE_TYPE_SUPPLEMENTARY = "SUPPLEMENTARY"
)

var IngestionSourceType = map[string]string{
	INGESTION_SOURCE_TYPE_ALL:           INGESTION_SOURCE_TYPE_ALL,
	INGESTION_SOURCE_TYPE_PRIMARY:       INGESTION_SOURCE_TYPE_PRIMARY,
	INGESTION_SOURCE_TYPE_SUPPLEMENTARY: INGESTION_SOURCE_TYPE_SUPPLEMENTARY,
}
