package api

import (
	"fmt"
	"godsp/modules/facebook/ad/transport/requests"
	"godsp/pkg/gos/utils"
	"godsp/pkg/sctx/core"
	"net/http"

	"github.com/gofiber/fiber/v2"
)

/***
 * Update camp api
 */

func (a *adApi) UpdateAdApi() fiber.Handler {
	return func(c *fiber.Ctx) error {
		var payload requests.UpdateAdReq
		if err := c.BodyParser(&payload); err != nil {
			return core.ReturnErrsForApi(c, err)
		}

		//validate
		validationErrors := payload.Validate()
		if len(validationErrors) > 0 {
			return core.ReturnErrsForApi(c, validationErrors)
		}

		ad, err := a.usc.UpdateAdUsc(c.Context(), &payload)
		if err != nil {
			fmt.Println("Error:", err)
			return core.ReturnErrsForApi(c, err)
		}

		return c.Status(http.StatusOK).JSON(core.ResponseData(map[string]interface{}{
			"msg":  "Update Ad successfully",
			"data": ad,
		}))
	}
}

/**
 * Toggle active or edit name ad
 */

func (a *adApi) UpdateNameStatusAdApi() fiber.Handler {
	return func(c *fiber.Ctx) error {
		var payload requests.UpdateNameStatusAdReq
		if err := c.BodyParser(&payload); err != nil {
			return core.ReturnErrsForApi(c, err)
		}

		//validate
		validationErrors := payload.Validate()
		if len(validationErrors) > 0 {
			return core.ReturnErrsForApi(c, validationErrors)
		}

		if payload.Name == "" && payload.Status == "" {
			return core.ErrBadRequest
		}

		userId, errUser := utils.GetUserId(c.Context())
		if errUser != nil {
			return core.ReturnErrsForApi(c, errUser)
		}

		payload.UserId = userId

		err := a.usc.UpdateNameStatusAdUsc(c.Context(), &payload)
		if err != nil {
			fmt.Println("Error:", err)
			return core.ReturnErrsForApi(c, err)
		}

		return c.Status(http.StatusOK).JSON(core.ResponseData(map[string]interface{}{
			"msg": "Update name and status successfully",
		}))
	}
}
