package handlers

import (
	"godsp/pkg/gos/utils"
	"godsp/pkg/sctx/core"

	"github.com/gofiber/fiber/v2"
)

func (h *adsHdl) ListAdsHdl() fiber.Handler {
	return func(c *fiber.Ctx) error {
		adAccounts, _ := h.usc.ListAdAccountAdUsc(c.Context())
		clients, _ := h.usc.ListClientAdUsc(c.Context())

		userInfo, err := utils.GetInfoUserAuth(c.Context())
		if err != nil {
			c.Redirect("/page-permission-denied")
		}

		return c.Render("facebook/camp-adset-ad/index", fiber.Map{
			"authPermission": core.GetPermission(c.Context()).GetPermissions(),
			"userInfo":       userInfo,
			"Numbers":        generateNumbers(),
			"adAccounts":     adAccounts,
			"clients":        clients,
		})

		// return c.Render("facebook/ads/index", fiber.Map{})
	}
}

func generateNumbers() []int {
	var numbers []int
	for i := 18; i <= 65; i++ {
		numbers = append(numbers, i)
	}

	return numbers
}
