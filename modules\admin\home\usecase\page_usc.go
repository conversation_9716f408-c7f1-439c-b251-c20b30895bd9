package usecase

import (
	"context"
	"fmt"
	"godsp/conf"
	userEt "godsp/modules/admin/user/entity"
	userRp "godsp/modules/admin/user/repository/mongo"
	"godsp/modules/facebook/common/fbenums"
	"godsp/modules/facebook/pages/common/errs"
	"godsp/modules/facebook/pages/entity"

	"godsp/modules/facebook/pages/mapping"
	v20 "godsp/pkg/fb-marketing/marketing/v20"
	"godsp/pkg/gos/utils"

	"github.com/dev-networldasia/dspgos/sctx"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo/options"
)

type ApiPageRepo interface {
	UpsertPageRepo(ctx context.Context, filter bson.M, update bson.M) error
	FindPageRepo(ctx context.Context, filter interface{}, opts ...*options.FindOptions) ([]*entity.PageEntity, error)
}

type ApiUserRepo interface {
	FindOneUserRepo(ctx context.Context, filter interface{}, opts ...*options.FindOneOptions) (*userEt.UserEntity, error)
	GetRoleOfUser(ctx context.Context, userId primitive.ObjectID, opts ...*options.AggregateOptions) (*userRp.RoleInfo, error)
}

type apiPageUsc struct {
	fbService *v20.Service
	repo      ApiPageRepo
	userRepo  ApiUserRepo
	logger    sctx.Logger
}

func NewApiPageUsc(fbService *v20.Service, repo ApiPageRepo, userRepo ApiUserRepo, logger sctx.Logger) *apiPageUsc {
	return &apiPageUsc{
		fbService: fbService,
		repo:      repo,
		userRepo:  userRepo,
		logger:    logger,
	}
}

/**
 * Reload Api page owned
 */
func (usc *apiPageUsc) ReloadOwnedPage(ctx context.Context, userId primitive.ObjectID) []string {
	var errStr []string
	pages, err := usc.fbService.Pages.GetOwnedPages(ctx, conf.FBConf.BusinessID)
	if err != nil {
		usc.logger.Error(err)
		errStr = append(errStr, err.Error())
		return errStr
	}

	if pages == nil {
		errStr = append(errStr, errs.ErrFBPageListNotFound.Error())
		return errStr
	}

	for _, page := range pages {
		filter := bson.M{"page_id": page.ID}

		// Call GetInstagramAccountsForPage to fetch the Instagram accounts
		instagramAccounts, err := usc.fbService.Pages.GetInstagramAccountsForPage(ctx, page.ID)
		if err != nil {
			usc.logger.Error("failed to get Instagram accounts for page %s: %w", page.ID, err)
			errStr = append(errStr, err.Error())
			continue
		}

		if instagramAccounts != nil {
			page.InstagramAccounts.Data = instagramAccounts
			// Map page data along with Instagram accounts
			updateData := mapping.MapperPageFBToUpsert(&page, userId)

			if err := usc.repo.UpsertPageRepo(ctx, filter, updateData); err != nil {
				usc.logger.Error(err)
				errStr = append(errStr, err.Error())
			}
		} else {
			pageBackedInstagramAccounts, err := usc.fbService.Pages.GetPageBackedInstagramAccounts(ctx, page.ID)
			if err != nil {
				usc.logger.Error("failed to get Page Backed Instagram accounts for page %s: %w", page.ID, err)
				errStr = append(errStr, err.Error())
				continue
			}
			page.PageBackedInstagramAccounts.Data = pageBackedInstagramAccounts
			updateData := mapping.MapperPageFBToUpsert(&page, userId)

			if err := usc.repo.UpsertPageRepo(ctx, filter, updateData); err != nil {
				usc.logger.Error(err)
				errStr = append(errStr, err.Error())
			}
		}

	}

	return errStr
}

/**
 * Reload Api page owned
 */
func (usc *apiPageUsc) ReloadClientPage(ctx context.Context, userId primitive.ObjectID) []string {
	var errStr []string
	pages, err := usc.fbService.Pages.GetClientPages(ctx, conf.FBConf.BusinessID)
	if err != nil {
		usc.logger.Error(err)
		errStr = append(errStr, err.Error())
		return errStr
	}

	if pages == nil {
		errStr = append(errStr, errs.ErrFBPageListNotFound.Error())
		return errStr
	}

	for _, page := range pages {
		filter := bson.M{"page_id": page.ID}

		// Call GetInstagramAccountsForPage to fetch the Instagram accounts
		instagramAccounts, err := usc.fbService.Pages.GetInstagramAccountsForPage(ctx, page.ID)
		if err != nil {
			usc.logger.Error("failed to get Instagram accounts for page %s: %w", page.ID, err)
			errStr = append(errStr, err.Error())
			continue
		}

		// Add Instagram accounts to the page object
		page.InstagramAccounts.Data = instagramAccounts

		// Map page data along with Instagram accounts
		updateData := mapping.MapperPageFBToUpsert(&page, userId)

		if err := usc.repo.UpsertPageRepo(ctx, filter, updateData); err != nil {
			usc.logger.Error(err)
			errStr = append(errStr, err.Error())
		}
	}

	return errStr
}

/**
 * reload api page detail
 */
func (usc *apiPageUsc) ReloadPageDetail(ctx context.Context, page_id string, userId primitive.ObjectID) error {
	page, err := usc.fbService.Pages.Get(ctx, page_id)
	if err != nil {
		return err
	}
	if page == nil {
		return errs.ErrFBPageNotFound
	}

	filter := bson.M{"page_id": page.ID}
	updateData := mapping.MapperPageFBToUpsert(page, userId)
	if err := usc.repo.UpsertPageRepo(ctx, filter, updateData); err != nil {
		usc.logger.Error(err)
		return err
	}

	return nil
}

/**
 * get list page from mongo
 */
func (usc *apiPageUsc) GetPages(ctx context.Context) ([]*entity.PageEntity, error) {
	userId, err := utils.GetUserId(ctx)

	if err != nil {
		return nil, err
	}

	filter := bson.M{
		"status": bson.M{"$nin": []string{fbenums.FB_STATUS_ARCHIVED, fbenums.FB_STATUS_DELETED}},
	}

	userFilter := bson.M{
		"_id": userId,
	}

	// var userPages []string
	role, err := usc.userRepo.GetRoleOfUser(ctx, userId)
	if err != nil {
		return nil, err
	}

	if role.RoleName != "ADMIN" {
		pages, err := usc.userRepo.FindOneUserRepo(ctx, userFilter)
		if err != nil {
			return nil, err
		}
		filter["page_id"] = bson.M{
			"$in": pages.ListPageIds,
		}
	}

	ops := &options.FindOptions{}
	ops.SetSort(bson.M{
		"created_at": -1, // sort by created_at in descending order
	})

	pages, err := usc.repo.FindPageRepo(ctx, filter)
	if err != nil {
		usc.logger.Error(err)
		return nil, err
	}

	fmt.Printf("pages %+v \n", pages)
	return pages, nil
}

/**
 * Use Update Avatar for Page
 */
func (p *apiPageUsc) ReloadAvatarPage(ctx context.Context, pageID string) (string, error) {

	avatarRes, err := p.fbService.Pages.GetAvatarPage(ctx, pageID)
	if err != nil {
		return "", err
	}

	filter := bson.M{"page_id": pageID}
	updateData := bson.M{
		"$set": bson.M{
			"picture": avatarRes,
		},
	}

	err = p.repo.UpsertPageRepo(ctx, filter, updateData)

	if err != nil {
		return "", nil
	}

	return avatarRes.URL, nil
}
