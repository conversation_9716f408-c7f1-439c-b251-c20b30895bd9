package mapping

import (
	"encoding/json"
	"fmt"
	"godsp/modules/facebook/adset/transport/requests"
	"godsp/modules/facebook/common/fbenums"
	v20 "godsp/pkg/fb-marketing/marketing/v20"

	"go.mongodb.org/mongo-driver/bson"
)

/**
 * Mappper update adset req to adset
 */
func MapperUpdateAdsetReqToAdset(payload *requests.UpdateAdsetReq, adsetFB *v20.Adset) {

	jsonData, err := json.Marshal(payload)
	if err != nil {
		fmt.Println("Lỗi khi convert slice sang JSON:", err)
	}
	fmt.Println("\n\n >>-----Payload----<< %v \n \n ", string(jsonData))

	if payload.Name != nil {
		adsetFB.Name = *payload.Name
	}

	// Daily budget
	if payload.DailyBudget != nil {
		adsetFB.DailyBudget = *payload.DailyBudget
	}
	if payload.DailyMinSpendTarget != nil {
		adsetFB.DailyMinSpendTarget = *payload.DailyMinSpendTarget
	}

	if payload.DailySpendCap != nil {
		adsetFB.DailySpendCap = *payload.DailySpendCap
	}

	// Life Time budget
	if payload.LifetimeBudget != nil {
		adsetFB.LifetimeBudget = *payload.LifetimeBudget
	}

	if payload.LifetimeMinSpendTarget != nil {
		adsetFB.LifetimeMinSpendTarget = *payload.LifetimeMinSpendTarget
	}
	if payload.LifeTimeSpendCap != nil {
		adsetFB.LifeTimeSpendCap = *payload.LifeTimeSpendCap
	}

	if payload.BidAmount != nil {
		adsetFB.BidAmount = *payload.BidAmount
	}

	if payload.BidStrategy != nil {
		adsetFB.BidStrategy = *payload.BidStrategy
	}

	if payload.BillingEvent != nil {
		adsetFB.BillingEvent = *payload.BillingEvent
	}

	if payload.Status != nil {
		adsetFB.Status = *payload.Status
	}

	if payload.EndTime != nil {
		adsetFB.EndTime = payload.EndTime
	} else {
		adsetFB.EndTime = nil
	}

	if payload.Targeting != nil {
		adsetFB.Targeting = payload.Targeting
	}

	adsetFB.IsBudgetScheduleEnabled = payload.IsBudgetScheduleEnabled
	// adsetFB.FrequencyControlSpecs = payload.FrequencyControlSpecs

	fmt.Println("\n\n >>-----Payload Update After Mapping ----<< %v \n \n ", adsetFB)

	jsonData, err2 := json.Marshal(adsetFB)
	if err2 != nil {
		fmt.Println("Lỗi khi convert slice sang JSON:", err2)
	}
	fmt.Println("\n\n >>-----Payload Update After Mapping ----<< %v \n \n ", string(jsonData))
}

func MapperUpdateNameStatusAdsetReqToAdset(payload *requests.UpdateNameStatusAdsetReq, accountId string) (bson.M, bson.M) {
	// adsetFB := &v20.Adset{
	// 	ID:        payload.AdsetId,
	// 	AccountID: accountId,
	// }

	adsetFB := make(bson.M)
	dataUpdate := make(bson.M)

	if payload.Name != "" {
		adsetFB["name"] = payload.Name
		dataUpdate["$set"] = bson.M{"name": payload.Name}
	}

	if payload.Status != "" {
		adsetFB["status"] = fbenums.StatusFB[payload.Status]
		if len(dataUpdate) == 0 {
			dataUpdate["$set"] = bson.M{"status": fbenums.StatusFB[payload.Status]}
		} else {
			dataUpdate["$set"].(bson.M)["status"] = fbenums.StatusFB[payload.Status]
		}

	}

	return adsetFB, dataUpdate
}
