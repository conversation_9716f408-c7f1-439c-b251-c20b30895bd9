package mongo

import (
	"context"
	"errors"
	"fmt"
	"godsp/modules/admin/user/common/pipelines"
	"godsp/modules/admin/user/entity"
	"godsp/modules/admin/user/transport/responses"
	"godsp/modules/facebook/common/fberrs"
	"godsp/pkg/sctx/core"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
)

type userRepo struct {
	DB         *mongo.Database
	Collection *mongo.Collection
}

func NewUserRepo(DB *mongo.Database) *userRepo {
	return &userRepo{
		DB:         DB,
		Collection: DB.Collection(entity.UserEntity{}.CollectionName()),
	}
}

/**
 * Create index
 */
func (r *userRepo) CreateUserIndex(ctx context.Context) error {
	cursor, err := r.Collection.Indexes().List(ctx)
	if err != nil {
		return err
	}
	defer cursor.Close(ctx)

	for cursor.Next(ctx) {
		var index bson.M
		if err := cursor.Decode(&index); err != nil {
			return err
		}
		if indexName, ok := index["email"].(string); ok && indexName == "user_email_index" {
			continue
		}

		if indexName, ok := index["username"].(string); ok && indexName == "user_username_index" {
			continue
		}

		if indexName, ok := index["phone"].(string); ok && indexName == "user_phone_index" {
			return nil
		}
	}

	emailIndexModel := mongo.IndexModel{
		Keys:    bson.M{"email": 1},
		Options: options.Index().SetUnique(true).SetName("user_email_index"),
	}

	usernameIndexModel := mongo.IndexModel{
		Keys: bson.M{"username": 1},
		Options: options.Index().SetUnique(true).SetName("user_username_index").
			SetPartialFilterExpression(bson.M{
				"username": bson.M{"$exists": true},
			}),
	}

	phoneIndexModel := mongo.IndexModel{
		Keys: bson.M{"phone": 1},
		Options: options.Index().SetUnique(true).SetName("user_phone_index").
			SetPartialFilterExpression(bson.M{
				"phone": bson.M{"$exists": true},
			}),
	}

	_, err = r.Collection.Indexes().CreateOne(ctx, emailIndexModel)
	if err != nil {
		return err
	}
	_, err = r.Collection.Indexes().CreateOne(ctx, usernameIndexModel)
	if err != nil {
		return err
	}

	_, err = r.Collection.Indexes().CreateOne(ctx, phoneIndexModel)
	return err
}

/**
 * FindOne user
 */
func (r *userRepo) FindOneUserRepo(ctx context.Context, filter interface{}, opts ...*options.FindOneOptions) (*entity.UserEntity, error) {
	var userEntity entity.UserEntity
	err := r.Collection.FindOne(ctx, filter, opts...).Decode(&userEntity)
	if errors.Is(err, mongo.ErrNoDocuments) {
		return nil, core.ErrNotFound
	}

	return &userEntity, nil
}

/**
 * Find one user with pipeline
 */
func (r *userRepo) FindOneUserWithPipelineRepo(ctx context.Context, pipeline []bson.M, opts ...*options.AggregateOptions) (*entity.UserEntity, error) {
	cursor, err := r.Collection.Aggregate(ctx, pipeline, opts...)
	if err != nil {
		return nil, err
	}
	defer cursor.Close(ctx)

	if cursor == nil {
		return nil, fberrs.ErrNilCursorValue
	}

	if cursor.Next(ctx) {
		var userEntity entity.UserEntity
		if err := cursor.Decode(&userEntity); err != nil {
			return nil, err
		}

		return &userEntity, nil
	}

	return nil, core.ErrNotFound
}

/**
 * Find one user with pipeline
 */
func (r *userRepo) FindOneUserProfileWithPipelineRepo(ctx context.Context, pipeline []bson.M, opts ...*options.AggregateOptions) (*responses.DetailsUserProfile, error) {
	cursor, err := r.Collection.Aggregate(ctx, pipeline, opts...)
	if err != nil {
		return nil, err
	}
	defer cursor.Close(ctx)

	if cursor == nil {
		return nil, fberrs.ErrNilCursorValue
	}

	if cursor.Next(ctx) {
		var userProfile responses.DetailsUserProfile
		if err := cursor.Decode(&userProfile); err != nil {
			return nil, err
		}

		return &userProfile, nil
	}

	return nil, core.ErrNotFound
}

/**
 * Find user by user
 */
func (r *userRepo) FindOneByUserRepo(ctx context.Context, filter interface{}) (*entity.UserEntity, error) {
	var userEntity entity.UserEntity

	err := r.Collection.FindOne(ctx, filter).Decode(&userEntity)
	if errors.Is(err, mongo.ErrNoDocuments) {
		return nil, core.ErrNotFound
	}

	return &userEntity, nil
}

/**
 * Find user
 */
func (r *userRepo) FindUserRepo(ctx context.Context, filter interface{}, opts ...*options.FindOptions) (*[]entity.UserEntity, error) {
	var users []entity.UserEntity

	cursor, err := r.Collection.Find(ctx, filter, opts...)
	if err != nil {
		return nil, err
	}
	defer cursor.Close(ctx)

	if cursor == nil {
		return nil, fberrs.ErrNilCursorValue
	}
	err = cursor.All(ctx, &users)
	if err != nil {
		return nil, err
	}

	return &users, nil
}

/**
 * Find users with pipeline
 */
func (r *userRepo) FindUsersWithPipelineRepo(ctx context.Context, pipeline []bson.M, opts ...*options.AggregateOptions) (*[]entity.UserEntity, error) {
	var users []entity.UserEntity

	cursor, err := r.Collection.Aggregate(ctx, pipeline, opts...)
	if err != nil {
		return nil, err
	}
	defer cursor.Close(ctx)

	if cursor == nil {
		return nil, fberrs.ErrNilCursorValue
	}

	if err = cursor.All(ctx, &users); err != nil {
		return nil, err
	}

	return &users, nil
}

/**
 * Find One Detail user with pipeline
 */
func (r *userRepo) FindOneDetailUserWithPipelineRepo(ctx context.Context, pipeline []bson.M, opts ...*options.AggregateOptions) (*responses.DetailsUser, error) {
	var userDetails responses.DetailsUser

	cursor, err := r.Collection.Aggregate(ctx, pipeline, opts...)
	if err != nil {
		return nil, err
	}
	defer cursor.Close(ctx)

	if cursor.Next(ctx) {
		if err := cursor.Decode(&userDetails); err != nil {
			return nil, err
		}
	}

	return &userDetails, nil
}

/***
 * insert user
 */
func (r *userRepo) InsertUserRepo(ctx context.Context, user *entity.UserCreation) (*primitive.ObjectID, error) {
	user.BeforeSave()
	result, err := r.Collection.InsertOne(ctx, user)
	if err != nil {
		if mongo.IsDuplicateKeyError(err) {
			return nil, core.ErrDuplicate
		}
		return nil, err
	}
	userId := result.InsertedID.(primitive.ObjectID)
	return &userId, nil
}

/***
 * update one filter interface{}, update interface{}, opts ...*options.UpdateOptions
 */
func (r *userRepo) UpdateOneUserRepo(ctx context.Context, filter interface{}, user *entity.UserUpdate, opts ...*options.UpdateOptions) error {
	user.BeforeUpdate()
	update := bson.M{
		"$set": *user,
	}

	_, err := r.Collection.UpdateOne(ctx, filter, update)
	if err != nil {
		if mongo.IsDuplicateKeyError(err) {
			return core.ErrNotFound
		}
		return err
	}

	return nil
}

/***
 * update one filter interface{}, update interface{}, opts ...*options.UpdateOptions
 */
func (r *userRepo) UpdateOneUserByBsonMRepo(ctx context.Context, filter interface{}, data bson.M, opts ...*options.UpdateOptions) error {

	_, err := r.Collection.UpdateOne(ctx, filter, data)
	if err != nil {
		if mongo.IsDuplicateKeyError(err) {
			return core.ErrNotFound
		}
		return err
	}

	return nil
}

/**
 * Get Role Of User
 */

type RoleInfo struct {
	RoleName string             `json:"role_name" bson:"role_name"`
	ID       primitive.ObjectID `json:"_id" bson:"_id"`
}

func (r *userRepo) GetRoleOfUser(ctx context.Context, userId primitive.ObjectID, opts ...*options.AggregateOptions) (*RoleInfo, error) {
	var role RoleInfo

	pipeline := pipelines.PipelineGetRoleInfo(userId)
	cursor, err := r.Collection.Aggregate(ctx, pipeline, opts...)
	if err != nil {
		return nil, err
	}
	defer cursor.Close(ctx)
	if cursor == nil {
		return nil, fberrs.ErrNilCursorValue
	}

	if cursor.Next(ctx) {
		if err := cursor.Decode(&role); err != nil {
			return nil, err
		}
	}

	fmt.Printf("Role %+v \n", &role)
	return &role, nil
}
